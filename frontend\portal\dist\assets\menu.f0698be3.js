/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{_ as e,B as l,r as a,j as t,o,a as u,b as d,i,w as n,k as p,t as s,d as r,z as m,e as v,a0 as c,P as f,aa as h,M as b,ab as _,ac as y,ad as w}from"./index.bfaf04e1.js";import g from"./icon.992d35c5.js";import{W as V}from"./warningBar.0cee9251.js";import{c as k}from"./authorityBtn.a62ac8bd.js";const I={class:"gva-table-box"},x={class:"gva-btn-list"},D={key:0,class:"icon-column"},U={style:{display:"inline-flex"}},C={class:"dialog-footer"},z=e(Object.assign({name:"Men<PERSON>"},{setup(e){const z=l({path:[{required:!0,message:"请输入菜单name",trigger:"blur"}],component:[{required:!0,message:"请输入文件路径",trigger:"blur"}],"meta.title":[{required:!0,message:"请输入菜单展示名称",trigger:"blur"}]}),B=a(1),q=a(0),A=a(999),T=a([]),j=a({}),S=async()=>{const e=await c({page:B.value,pageSize:A.value,...j.value});0===e.code&&(T.value=e.data.list,q.value=e.data.total,B.value=e.data.page,A.value=e.data.pageSize)};S();const M=()=>{N.value.component=N.value.component.replace(/\\/g,"/")},N=a({ID:0,path:"",name:"",hidden:"",parentId:"",component:"",meta:{title:"",icon:"",defaultMenu:!1,closeTab:!1,keepAlive:!1},parameters:[],menuBtn:[]}),P=()=>{N.value.path=N.value.name},F=e=>{H(),e()},$=a(null),E=a(!1),H=()=>{E.value=!1,$.value.resetFields(),N.value={ID:0,path:"",name:"",hidden:"",parentId:"",component:"",meta:{title:"",icon:"",defaultMenu:!1,keepAlive:""}}},K=a(!1),O=()=>{H(),K.value=!1},W=async()=>{$.value.validate((async e=>{if(e){let e;e=Q.value?await _(N.value):await y(N.value),0===e.code&&(b({type:"success",message:Q.value?"编辑成功":"添加成功!"}),S()),H(),K.value=!1}}))},G=a([{ID:"0",title:"根菜单"}]),J=()=>{G.value=[{ID:"0",title:"根目录"}],L(T.value,G.value,!1)},L=(e,l,a)=>{e&&e.forEach((e=>{if(e.children&&e.children.length){const t={title:e.meta.title,ID:String(e.ID),disabled:a||e.ID===N.value.ID,children:[]};L(e.children,t.children,a||e.ID===N.value.ID),l.push(t)}else{const t={title:e.meta.title,ID:String(e.ID),disabled:a||e.ID===N.value.ID};l.push(t)}}))},Q=a(!1),R=a("新增菜单"),X=e=>{R.value="新增菜单",N.value.parentId=String(e),Q.value=!1,J(),K.value=!0};return(e,l)=>{const a=t("base-button"),c=t("el-table-column"),_=t("el-icon"),y=t("el-table"),q=t("base-input"),A=t("base-form-item"),j=t("base-checkbox"),H=t("base-option"),L=t("base-select"),Y=t("el-cascader"),Z=t("base-form"),ee=t("el-dialog");return o(),u("div",null,[d("div",I,[d("div",x,[i(a,{size:"small",type:"primary",icon:"plus",onClick:l[0]||(l[0]=e=>X("0"))},{default:n((()=>l[15]||(l[15]=[p("新增根菜单")]))),_:1,__:[15]})]),i(y,{data:T.value,"row-key":"ID"},{default:n((()=>[i(c,{align:"left",label:"ID","min-width":"100",prop:"ID"}),i(c,{align:"left",label:"展示名称","min-width":"120",prop:"authorityName"},{default:n((e=>[d("span",null,s(e.row.meta.title),1)])),_:1}),i(c,{align:"left",label:"图标","min-width":"140",prop:"authorityName"},{default:n((e=>[e.row.meta.icon?(o(),u("div",D,[i(_,null,{default:n((()=>[(o(),r(m(e.row.meta.icon)))])),_:2},1024),d("span",null,s(e.row.meta.icon),1)])):v("",!0)])),_:1}),i(c,{align:"left",label:"路由Name","show-overflow-tooltip":"","min-width":"160",prop:"name"}),i(c,{align:"left",label:"路由Path","show-overflow-tooltip":"","min-width":"160",prop:"path"}),i(c,{align:"left",label:"是否隐藏","min-width":"100",prop:"hidden"},{default:n((e=>[d("span",null,s(e.row.hidden?"隐藏":"显示"),1)])),_:1}),i(c,{align:"left",label:"父节点","min-width":"90",prop:"parentId"}),i(c,{align:"left",label:"排序","min-width":"70",prop:"sort"}),i(c,{align:"left",label:"文件路径","min-width":"360",prop:"component"}),i(c,{align:"left",fixed:"right",label:"操作",width:"300"},{default:n((e=>[i(a,{size:"small",type:"primary",link:"",icon:"plus",onClick:l=>X(e.row.ID)},{default:n((()=>l[16]||(l[16]=[p("添加子菜单")]))),_:2,__:[16]},1032,["onClick"]),i(a,{size:"small",type:"primary",link:"",icon:"edit",onClick:l=>(async e=>{R.value="编辑菜单";const l=await w({id:e});N.value=l.data.menu,Q.value=!0,J(),K.value=!0})(e.row.ID)},{default:n((()=>l[17]||(l[17]=[p("编辑")]))),_:2,__:[17]},1032,["onClick"]),i(a,{size:"small",type:"primary",link:"",icon:"delete",onClick:l=>{return a=e.row.ID,void f.confirm("此操作将永久删除所有角色下该菜单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await h({ID:a})).code&&(b({type:"success",message:"删除成功!"}),1===T.value.length&&B.value>1&&B.value--,S())})).catch((()=>{b({type:"info",message:"已取消删除"})}));var a}},{default:n((()=>l[18]||(l[18]=[p("删除")]))),_:2,__:[18]},1032,["onClick"])])),_:1})])),_:1},8,["data"])]),i(ee,{modelValue:K.value,"onUpdate:modelValue":l[14]||(l[14]=e=>K.value=e),"before-close":F,title:R.value},{footer:n((()=>[d("div",C,[i(a,{size:"small",onClick:O},{default:n((()=>l[27]||(l[27]=[p("取 消")]))),_:1,__:[27]}),i(a,{size:"small",type:"primary",onClick:W},{default:n((()=>l[28]||(l[28]=[p("确 定")]))),_:1,__:[28]})])])),default:n((()=>[i(V,{title:"新增菜单，需要在角色管理内配置权限才可使用"}),K.value?(o(),r(Z,{key:0,ref_key:"menuForm",ref:$,inline:!0,model:N.value,rules:z,"label-position":"top","label-width":"85px"},{default:n((()=>[i(A,{label:"路由Name",prop:"path",style:{width:"30%"}},{default:n((()=>[i(q,{modelValue:N.value.name,"onUpdate:modelValue":l[1]||(l[1]=e=>N.value.name=e),autocomplete:"off",placeholder:"唯一英文字符串",onChange:P},null,8,["modelValue"])])),_:1}),i(A,{prop:"path",style:{width:"30%"}},{label:n((()=>[d("div",U,[l[20]||(l[20]=p(" 路由Path ")),i(j,{modelValue:E.value,"onUpdate:modelValue":l[2]||(l[2]=e=>E.value=e),style:{float:"right","margin-left":"20px"}},{default:n((()=>l[19]||(l[19]=[p("添加参数")]))),_:1,__:[19]},8,["modelValue"])])])),default:n((()=>[i(q,{modelValue:N.value.path,"onUpdate:modelValue":l[3]||(l[3]=e=>N.value.path=e),disabled:!E.value,autocomplete:"off",placeholder:"建议只在后方拼接参数"},null,8,["modelValue","disabled"])])),_:1}),i(A,{label:"是否隐藏",style:{width:"30%"}},{default:n((()=>[i(L,{modelValue:N.value.hidden,"onUpdate:modelValue":l[4]||(l[4]=e=>N.value.hidden=e),placeholder:"是否在列表隐藏"},{default:n((()=>[i(H,{value:!1,label:"否"}),i(H,{value:!0,label:"是"})])),_:1},8,["modelValue"])])),_:1}),i(A,{label:"父节点ID",style:{width:"30%"}},{default:n((()=>[i(Y,{modelValue:N.value.parentId,"onUpdate:modelValue":l[5]||(l[5]=e=>N.value.parentId=e),style:{width:"100%"},disabled:!Q.value,options:G.value,props:{checkStrictly:!0,label:"title",value:"ID",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])])),_:1}),i(A,{label:"文件路径",prop:"component",style:{width:"60%"}},{default:n((()=>[i(q,{modelValue:N.value.component,"onUpdate:modelValue":l[6]||(l[6]=e=>N.value.component=e),autocomplete:"off",placeholder:"页面:view/xxx/xx.vue 插件:plugin/xx/xx.vue",onBlur:M},null,8,["modelValue"]),l[22]||(l[22]=d("span",{style:{"font-size":"12px","margin-right":"12px"}},"如果菜单包含子菜单，请创建router-view二级路由页面或者",-1)),i(a,{style:{"margin-top":"4px"},size:"small",onClick:l[7]||(l[7]=e=>N.value.component="view/routerHolder.vue")},{default:n((()=>l[21]||(l[21]=[p("点我设置")]))),_:1,__:[21]})])),_:1,__:[22]}),i(A,{label:"展示名称",prop:"meta.title",style:{width:"30%"}},{default:n((()=>[i(q,{modelValue:N.value.meta.title,"onUpdate:modelValue":l[8]||(l[8]=e=>N.value.meta.title=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),i(A,{label:"图标",prop:"meta.icon",style:{width:"30%"}},{default:n((()=>[i(g,{meta:N.value.meta,style:{width:"100%"}},null,8,["meta"])])),_:1}),i(A,{label:"排序标记",prop:"sort",style:{width:"30%"}},{default:n((()=>[i(q,{modelValue:N.value.sort,"onUpdate:modelValue":l[9]||(l[9]=e=>N.value.sort=e),modelModifiers:{number:!0},autocomplete:"off"},null,8,["modelValue"])])),_:1}),i(A,{label:"KeepAlive",prop:"meta.keepAlive",style:{width:"30%"}},{default:n((()=>[i(L,{modelValue:N.value.meta.keepAlive,"onUpdate:modelValue":l[10]||(l[10]=e=>N.value.meta.keepAlive=e),style:{width:"100%"},placeholder:"是否keepAlive缓存页面"},{default:n((()=>[i(H,{value:!1,label:"否"}),i(H,{value:!0,label:"是"})])),_:1},8,["modelValue"])])),_:1}),i(A,{label:"CloseTab",prop:"meta.closeTab",style:{width:"30%"}},{default:n((()=>[i(L,{modelValue:N.value.meta.closeTab,"onUpdate:modelValue":l[11]||(l[11]=e=>N.value.meta.closeTab=e),style:{width:"100%"},placeholder:"是否自动关闭tab"},{default:n((()=>[i(H,{value:!1,label:"否"}),i(H,{value:!0,label:"是"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])):v("",!0),d("div",null,[i(a,{size:"small",type:"primary",icon:"edit",onClick:l[12]||(l[12]=e=>{return(l=N.value).parameters||(l.parameters=[]),void l.parameters.push({type:"query",key:"",value:""});var l})},{default:n((()=>l[23]||(l[23]=[p("新增菜单参数")]))),_:1,__:[23]}),i(y,{data:N.value.parameters,style:{width:"100%"}},{default:n((()=>[i(c,{align:"left",prop:"type",label:"参数类型",width:"180"},{default:n((e=>[i(L,{modelValue:e.row.type,"onUpdate:modelValue":l=>e.row.type=l,placeholder:"请选择"},{default:n((()=>[i(H,{key:"query",value:"query",label:"query"}),i(H,{key:"params",value:"params",label:"params"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),i(c,{align:"left",prop:"key",label:"参数key",width:"180"},{default:n((e=>[d("div",null,[i(q,{modelValue:e.row.key,"onUpdate:modelValue":l=>e.row.key=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(c,{align:"left",prop:"value",label:"参数值"},{default:n((e=>[d("div",null,[i(q,{modelValue:e.row.value,"onUpdate:modelValue":l=>e.row.value=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(c,{align:"left"},{default:n((e=>[d("div",null,[i(a,{type:"danger",size:"small",icon:"delete",onClick:l=>{return a=N.value.parameters,t=e.$index,void a.splice(t,1);var a,t}},{default:n((()=>l[24]||(l[24]=[p("删除")]))),_:2,__:[24]},1032,["onClick"])])])),_:1})])),_:1},8,["data"]),i(a,{style:{"margin-top":"12px"},size:"small",type:"primary",icon:"edit",onClick:l[13]||(l[13]=e=>{return(l=N.value).menuBtn||(l.menuBtn=[]),void l.menuBtn.push({name:"",desc:""});var l})},{default:n((()=>l[25]||(l[25]=[p("新增可控按钮")]))),_:1,__:[25]}),i(y,{data:N.value.menuBtn,style:{width:"100%"}},{default:n((()=>[i(c,{align:"left",prop:"name",label:"按钮名称",width:"180"},{default:n((e=>[d("div",null,[i(q,{modelValue:e.row.name,"onUpdate:modelValue":l=>e.row.name=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(c,{align:"left",prop:"name",label:"备注",width:"180"},{default:n((e=>[d("div",null,[i(q,{modelValue:e.row.desc,"onUpdate:modelValue":l=>e.row.desc=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(c,{align:"left"},{default:n((e=>[d("div",null,[i(a,{type:"danger",size:"small",icon:"delete",onClick:l=>(async(e,l)=>{const a=e[l];if(0===a.ID)return void e.splice(l,1);0!==(await k({id:a.ID})).code||e.splice(l,1)})(N.value.menuBtn,e.$index)},{default:n((()=>l[26]||(l[26]=[p("删除")]))),_:2,__:[26]},1032,["onClick"])])])),_:1})])),_:1},8,["data"])])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-635be047"]]);export{z as default};
