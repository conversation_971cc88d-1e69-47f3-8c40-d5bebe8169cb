/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{s as a}from"./authority.8a89f398.js";import{W as t}from"./warningBar.4338ec87.js";import{r as e,h as l,o as r,d as o,e as u,j as s,w as i,k as n,F as d,i as c,f as h,t as y,M as f}from"./index.74d1ee23.js";const m={class:"clearfix sticky-button",style:{margin:"18px"}},p={class:"tree-content"},v=Object.assign({name:"Datas"},{props:{row:{default:function(){return{}},type:Object},authority:{default:function(){return[]},type:Array}},emits:["changeRow"],setup(v,{expose:I,emit:w}){const _=v,g=e([]),b=e(!1),k=a=>{a&&a.forEach((a=>{const t={};t.authorityId=a.authorityId,t.authorityName=a.authorityName,g.value.push(t),a.children&&a.children.length&&k(a.children)}))},x=e([]);k(_.authority),_.row.dataAuthorityId&&_.row.dataAuthorityId.forEach((a=>{const t=g.value&&g.value.filter((t=>t.authorityId===a.authorityId))&&g.value.filter((t=>t.authorityId===a.authorityId))[0];x.value.push(t)}));const A=w,j=()=>{x.value=[...g.value],A("changeRow","dataAuthorityId",x.value),b.value=!0},C=()=>{x.value=g.value.filter((a=>a.authorityId===_.row.authorityId)),A("changeRow","dataAuthorityId",x.value),b.value=!0},R=()=>{const a=[];z(_.row,a),x.value=g.value.filter((t=>a.indexOf(t.authorityId)>-1)),A("changeRow","dataAuthorityId",x.value),b.value=!0},z=(a,t)=>{t.push(a.authorityId),a.children&&a.children.forEach((a=>{z(a,t)}))},N=async()=>{0===(await a(_.row)).code&&f({type:"success",message:"资源设置成功"})},E=()=>{A("changeRow","dataAuthorityId",x.value),b.value=!0};return I({enterAndNext:()=>{N()},needConfirm:b}),(a,e)=>{const f=l("base-button"),v=l("base-checkbox"),I=l("el-checkbox-group");return r(),o("div",null,[u("div",m,[s(f,{class:"fl-right",size:"small",type:"primary",onClick:N},{default:i((()=>e[1]||(e[1]=[n("确 定")]))),_:1,__:[1]}),s(f,{class:"fl-left",size:"small",type:"primary",onClick:j},{default:i((()=>e[2]||(e[2]=[n("全选")]))),_:1,__:[2]}),s(f,{class:"fl-left",size:"small",type:"primary",onClick:C},{default:i((()=>e[3]||(e[3]=[n("本角色")]))),_:1,__:[3]}),s(f,{class:"fl-left",size:"small",type:"primary",onClick:R},{default:i((()=>e[4]||(e[4]=[n("本角色及子角色")]))),_:1,__:[4]})]),u("div",p,[s(I,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=a=>x.value=a),onChange:E},{default:i((()=>[(r(!0),o(d,null,c(g.value,((a,t)=>(r(),h(v,{key:t,label:a},{default:i((()=>[n(y(a.authorityName),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])]),s(t,{title:"此功能仅用于创建角色和角色的many2many关系表，具体使用还须自己结合表实现业务，详情参考示例代码（客户示例）"})])}}});export{v as default};
