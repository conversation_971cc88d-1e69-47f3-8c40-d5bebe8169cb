/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{i as e,b as a,j as l}from"./autoCode.a94c47f9.js";import{W as t}from"./warningBar.4338ec87.js";import{_ as o,r as s,h as d,o as i,d as u,j as c,e as n,w as r,k as p,M as m,P as f}from"./index.74d1ee23.js";const b={class:"gva-table-box"},g={class:"gva-btn-list"},v={class:"dialog-footer"},k=o(Object.assign({name:"AutoPkg"},{setup(o){const k=s({packageName:"",label:"",desc:""}),_=s({packageName:[{required:!0,message:"请输入包名",trigger:"blur"},{validator:(e,a,l)=>{/^\d+$/.test(a[0])?l(new Error("不能够以数字开头")):l()},trigger:"blur"}]}),w=s(!1),y=()=>{w.value=!1,k.value={packageName:"",label:"",desc:""}},V=s(null),h=async()=>{V.value.validate((async a=>{if(a){0===(await e(k.value)).code&&m({type:"success",message:"添加成功",showClose:!0}),N(),y()}}))},C=s([]),N=async()=>{const e=await a();0===e.code&&(C.value=e.data.pkgs)};return N(),(e,a)=>{const o=d("base-button"),s=d("el-table-column"),j=d("el-table"),x=d("base-input"),B=d("base-form-item"),z=d("base-form"),P=d("el-dialog");return i(),u("div",null,[c(t,{href:"https://www.bilibili.com/video/BV1kv4y1g7nT?p=3",title:"此功能为开发环境使用，不建议发布到生产，具体使用效果请看视频https://www.bilibili.com/video/BV1kv4y1g7nT?p=3"}),n("div",b,[n("div",g,[c(o,{size:"small",type:"primary",icon:"plus",onClick:a[0]||(a[0]=e=>{w.value=!0})},{default:r((()=>a[5]||(a[5]=[p("新增")]))),_:1,__:[5]})]),c(j,{data:C.value},{default:r((()=>[c(s,{align:"left",label:"id",width:"60",prop:"ID"}),c(s,{align:"left",label:"包名",width:"150",prop:"packageName"}),c(s,{align:"left",label:"展示名",width:"150",prop:"label"}),c(s,{align:"left",label:"描述","min-width":"150",prop:"desc"}),c(s,{align:"left",label:"操作",width:"200"},{default:r((e=>[c(o,{icon:"delete",size:"small",type:"primary",link:"",onClick:a=>(async e=>{f.confirm("此操作仅删除数据库中的pkg存储，后端相应目录结构请自行删除与数据库保持一致！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await l(e)).code&&(m({type:"success",message:"删除成功!"}),N())}))})(e.row)},{default:r((()=>a[6]||(a[6]=[p("删除")]))),_:2,__:[6]},1032,["onClick"])])),_:1})])),_:1},8,["data"])]),c(P,{modelValue:w.value,"onUpdate:modelValue":a[4]||(a[4]=e=>w.value=e),"before-close":y,title:"创建Package"},{footer:r((()=>[n("div",v,[c(o,{size:"small",onClick:y},{default:r((()=>a[7]||(a[7]=[p("取 消")]))),_:1,__:[7]}),c(o,{size:"small",type:"primary",onClick:h},{default:r((()=>a[8]||(a[8]=[p("确 定")]))),_:1,__:[8]})])])),default:r((()=>[c(t,{title:"新增Pkg用于自动化代码使用"}),c(z,{ref_key:"pkgForm",ref:V,model:k.value,rules:_.value,"label-width":"80px"},{default:r((()=>[c(B,{label:"包名",prop:"packageName"},{default:r((()=>[c(x,{modelValue:k.value.packageName,"onUpdate:modelValue":a[1]||(a[1]=e=>k.value.packageName=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),c(B,{label:"展示名",prop:"label"},{default:r((()=>[c(x,{modelValue:k.value.label,"onUpdate:modelValue":a[2]||(a[2]=e=>k.value.label=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),c(B,{label:"描述",prop:"desc"},{default:r((()=>[c(x,{modelValue:k.value.desc,"onUpdate:modelValue":a[3]||(a[3]=e=>k.value.desc=e),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-4d2dfb69"]]);export{k as default};
