/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register([],(function(o,e){"use strict";return{execute:function(){o("default",Object.assign({name:"<PERSON>lientLog<PERSON>"},{setup:function(o){var e=function(o){console.log("1");var e=new RegExp("(^|&)"+o+"=([^&]*)(&|$)","i");console.log(2);var n=window.location.search.substr(1).match(e);return console.log(n),null!=n?decodeURI(n[2]):null}("type");console.log("type"),console.log(e);var n=window.localStorage.getItem("token")||"";return console.log(11),console.log(n),n&&"client"===e&&(window.location.href="asecagent://?token=".concat(n)),function(o,e){return null}}}))}}}));
