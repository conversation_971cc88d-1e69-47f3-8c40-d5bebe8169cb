<template>
  <base-row class="custom-transfer">
    <base-col :span="12" style="border-right: 1px #E4E4E4 solid">
      <div>
        <base-radio-group v-model="type" size="large">
          <el-radio-button
              v-for="button in treeLabel"
              :key="button.value"
              class="tree-label"
              :label="button.value"
              @change="getDate"
          >
            {{ button.text }}
          </el-radio-button>
        </base-radio-group>
      </div>
      <div style="border-bottom: 1px #E4E4E4 solid;border-top: 1px #E4E4E4 solid;">
        <base-checkbox style="margin: 2px 5px 5px 22px" v-model="all" @change="handleCheckAllChange"></base-checkbox>
        <base-input
            style="
        width: calc(100% - 41px);height: 23px"
            v-model="search"
            :placeholder="searchNotes"
            @change="getDate"
        />
      </div>
      <el-scrollbar height="350px" wrapRef="scroll">
        <el-tree
            :data="data"
            ref="treeRef"
            :expand-on-click-node=false
            check-on-click-node
            :props="defaultProps"
            show-checkbox
            node-key="id"
            @check="check"
        />
      </el-scrollbar>
      <el-pagination
          small
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          layout="prev, pager, next"
          v-model:total="treeTotal"
          @current-change="getDate"
      />
    </base-col>
    <base-col :span="12">
      <el-scrollbar wrapRef="scroll" style="height: 400px;">
        <div
            style="width: 100%;height: 25px;border-bottom: 1px #E4E4E4 solid;"
        >
          <el-link :underline="false" style="margin: 5px;float: right" @click="removeAll">清空</el-link>
        </div>
        <div>
          <el-tree
              :data="checkedData"
              :props="defaultProps"
          >
            <template #default="{node,data}">
            <span class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span>
                <a @click="remove(node, data)">删除</a>
              </span>
            </span>
            </template>
          </el-tree>
        </div>
      </el-scrollbar>
    </base-col>
  </base-row>
</template>

<script>
export default {
  name: 'CustomTransfer',
}
</script>
<script setup>
import { inject, nextTick, ref, watch } from 'vue'

const treeRef = ref()
const all = ref(false)
const searchNotes = ref('')
const groupCheck = inject('groupCheck')
const userCheck = inject('userCheck')
const appGroupCheck = inject('appGroupCheck')
const appCheck = inject('appCheck')
const getTreeData = inject('getTreeData')
const defaultProps = inject('defaultProps')
const data = inject('treeData')
const type = inject('treeType')
const treeTotal = inject('treeTotal')
const currentPage = inject('treeCurrentPage')
const pageSize = inject('treePageSize')
const dataType = inject('dataType')

const checkedParent = async(data) => {
  console.log(data)
  if (!data.parent.parent) {
    return data.parent
  }
  return await checkedParent(data.parent)
}

const checkedData = inject('checkedData')
const search = inject('treeSearch')
const treeLabel = inject('treeLabel')
// 添加选中项
const setCheck = async(data) => {
  console.log('add check')
  console.log(data)
  for (const item of data.childNodes) {
    if (item.checked) {
      console.log('chec')
      console.log(item)
      const checkData = {
        id: item.data.id,
        name: item.data.name,
        type: type.value,
        dataType: dataType.value,
      }
      if (type.value === 'group' && dataType.value === 'user') {
        groupCheck.value.push(checkData)
      }
      if (type.value === 'user' && dataType.value === 'user') {
        userCheck.value.push(checkData)
      }
      if (type.value === 'group' && dataType.value === 'resource') {
        appGroupCheck.value.push(checkData)
      }
      if (type.value === 'user' && dataType.value === 'resource') {
        appCheck.value.push(checkData)
      }
      checkedData.value.push(checkData)
    } else {
      console.log(item)
      if (item.childNodes.length > 0) {
        await setCheck(item)
      }
    }
  }
}

const check = async(nodeData) => {
  const node = treeRef.value.getNode(nodeData)
  console.log(treeRef.value.getCheckedKeys().length)
  console.log(data.value)
  if (treeRef.value.getCheckedKeys().length === data.value.length) {
    all.value = true
  } else {
    all.value = false
  }
  checkedData.value.length = 0
  if (type.value === 'group' && dataType.value === 'user') {
    groupCheck.value.length = 0
    checkedData.value.push(...userCheck.value)
  }
  if (type.value === 'user' && dataType.value === 'user') {
    userCheck.value.length = 0
    checkedData.value.push(...groupCheck.value)
  }
  if (type.value === 'group' && dataType.value === 'resource') {
    appGroupCheck.value.length = 0
    checkedData.value.push(...appCheck.value)
  }
  if (type.value === 'user' && dataType.value === 'resource') {
    appCheck.value.length = 0
    checkedData.value.push(...appGroupCheck.value)
  }

  // 判断父节点是否选中
  const parent = await checkedParent(node)
  console.log('父节点')
  console.log(parent)
  await setCheck(parent)
}

const updateAll = () => {
  console.log('修改全选状态')
  console.log(treeRef.value.getCheckedKeys())
  if (treeRef.value.getCheckedKeys().length === data.value.length) {
    all.value = true
  } else {
    all.value = false
  }
}

const setCheckedKeys = async() => {
  console.log('setCheckedKeys')
  console.log(dataType.value)
  console.log(type.value)
  if (dataType.value === 'user') {
    let ids = []
    switch (type.value) {
      case 'user':
        console.log(userCheck.value)
        ids = userCheck.value.map(i => i.id)
        // if (ids.length > 0) {
        await treeRef.value.setCheckedKeys(ids, false)
        // }
        break
      case 'group':
        console.log(groupCheck.value)
        ids = groupCheck.value.map(i => i.id)
        // if (ids.length > 0) {
        await treeRef.value.setCheckedKeys(ids, false)
        // }
        break
    }
  }
  if (dataType.value === 'resource') {
    let ids = []
    switch (type.value) {
      case 'user':
        console.log(appCheck.value)
        ids = appCheck.value.map(i => i.id)
        // if (ids.length > 0) {
        await treeRef.value.setCheckedKeys(ids, false)
        // }
        break
      case 'group':
        console.log(appGroupCheck.value)
        ids = appGroupCheck.value.map(i => i.id)
        console.log(ids)
        console.log(ids.length)
        // if (ids.length > 0) {
        await treeRef.value.setCheckedKeys(ids, false)
        // }
        break
    }
  }
  updateAll()
}

const getDate = () => {
  getTreeData()
  // setCheckedKeys()
}

nextTick(() => {
  console.log('nextTick')
})

watch(data, (newData, oldData) => {
  console.log('watch')
  console.log(groupCheck.value)
  console.log(userCheck.value)
  console.log(appGroupCheck.value)
  console.log(appCheck.value)
  console.log(treeRef)
  if (dataType.value === 'user') {
    checkedData.value.push(...groupCheck.value, ...userCheck.value)
  } else {
    checkedData.value.push(...appGroupCheck.value, ...appCheck.value)
  }

  if (type.value === 'group' && dataType.value === 'user') {
    searchNotes.value = '组织名称'
  }
  if (type.value === 'user' && dataType.value === 'user') {
    searchNotes.value = '用户名'
  }
  if (type.value === 'group' && dataType.value === 'resource') {
    searchNotes.value = '资源分组名称'
  }
  if (type.value === 'user' && dataType.value === 'resource') {
    searchNotes.value = '资源名称'
  }
  setCheckedKeys()
}, {
  immediate: true, deep: true,
})

const handleCheckAllChange = (val) => {
  if (all.value) {
    console.log(data.value)
    treeRef.value.setCheckedNodes(data.value, false)
  } else {
    treeRef.value.setCheckedNodes([])
  }
  check(data.value[0])
}

const remove = (node, data) => {
  console.log('remove')
  const parent = node.parent
  const children = parent.data.children || parent.data
  console.log(data)
  const index = children.findIndex((d) => d.id === data.id)
  children.splice(index, 1)
  checkedData.value = [...checkedData.value]
  console.log(type.value)
  console.log(dataType.value)
  if (data.type === 'group' && data.dataType === 'user') {
    groupCheck.value.splice(groupCheck.value.indexOf(data), 1)
  }
  if (data.type === 'user' && data.dataType === 'user') {
    userCheck.value.splice(userCheck.value.indexOf(data), 1)
  }
  if (data.type === 'group' && data.dataType === 'resource') {
    appGroupCheck.value.splice(appGroupCheck.value.indexOf(data), 1)
  }
  if (data.type === 'user' && data.dataType === 'resource') {
    appCheck.value.splice(appCheck.value.indexOf(data), 1)
  }
  setCheckedKeys()
}

const removeAll = () => {
  console.log('removeAll')
  if (dataType.value === 'user') {
    groupCheck.value.length = 0
    userCheck.value.length = 0
  }
  if (dataType.value === 'resource') {
    appGroupCheck.value.length = 0
    appCheck.value.length = 0
  }
  checkedData.value.length = 0
  setCheckedKeys()
}

defineExpose({ groupCheck, userCheck, appGroupCheck, appCheck })
</script>
<style scoped>

</style>
<style lang="scss">
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.el-tree-node__content {
  width: 100% !important;
}

.custom-transfer {
  border: 1px #E4E4E4 solid;
  border-radius: 4px;
  padding: 0px !important;

  .el-radio-group {
    border: none;
    --el-border-radius-base: 0 !important;
  }

  .tree-label {
    height: 25px;
    font-size: 12px;
    margin: 0px;

    * {
      height: 25px;
      font-size: 12px;
    }

    span {
      padding: 6px 10px;
    }

    .el-radio-button__inner {
      border: none;
      border-right: 1px #dcdfe6 solid !important;
    }
  }

  .el-input {
    height: 25px;
  }

  .el-input__wrapper {
    border-radius: 0px !important;
    box-shadow: 1px 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset !important;
  }

  .el-scrollbar {
    height: 350px;
  }

  .el-memu-user {
    height: 25px;
    width: 100%;

    li * {
      width: 50px;
      font-size: 12px !important;
    }
  }

  .el-pagination {
    padding: 0 !important;

    ul li {
      margin: 0 2px !important;
    }

    ul li.is-active {
      color: #FFFFFF !important;
      border-color: #4E8DDA !important;
      background: #4E8DDA !important;
    }
  }
}
</style>
