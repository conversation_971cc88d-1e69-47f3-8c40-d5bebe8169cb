/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.fcd86654.js","./index-legacy.dbc04544.js"],(function(e,t){"use strict";var n,i,r,u,a;return{setters:[function(e){n=e.T},function(e){i=e.r,r=e.H,u=e.o,a=e.d}],execute:function(){e("default",Object.assign({name:"TinyLine"},{props:{data:{default:function(){return[]},type:Array}},setup:function(e){var t=e,c=i(null);return r((function(){new n(c.value,{height:35,autoFit:!0,data:t.data,smooth:!1,step:5,point:{size:1,shape:"circle",style:{fill:"#2972C8",stroke:"#2972C8",lineWidth:1}},lineStyle:{lineWidth:1}}).render()})),function(e,t){return u(),a("div",{ref_key:"container",ref:c},null,512)}}}))}}}));
