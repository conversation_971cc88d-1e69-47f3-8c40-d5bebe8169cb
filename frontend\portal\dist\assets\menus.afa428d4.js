/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{r as e,y as a,j as t,o as l,a as o,b as s,i as u,w as n,k as d,t as r,J as c,e as i,a7 as m,a8 as y,M as p,a9 as f,S as h}from"./index.bfaf04e1.js";import{u as v}from"./authority.8d6791c2.js";import{g as k,s as w}from"./authorityBtn.a62ac8bd.js";const I={class:"clearfix sticky-button"},b={class:"tree-content"},_={class:"custom-tree-node"},g={key:0},C={class:"dialog-footer"},R=Object.assign({name:"Menus"},{props:{row:{default:function(){return{}},type:Object}},emits:["changeRow"],setup(R,{expose:x,emit:D}){const j=R,V=D,z=e(""),N=e([]),A=e([]),B=e(!1),E=e({children:"children",label:function(e){return e.meta.title}});(async()=>{const e=await m();N.value=e.data.menus;const a=(await y({authorityId:j.row.authorityId})).data.menus,t=[];a.forEach((e=>{a.some((a=>a.parentId===e.menuId))||t.push(Number(e.menuId))})),A.value=t})();const O=()=>{B.value=!0},S=e(null),M=async()=>{const e=S.value.getCheckedNodes(!1,!0);0===(await f({menus:e,authorityId:j.row.authorityId})).code&&p({type:"success",message:"菜单设置成功!"})};x({enterAndNext:()=>{M()},needConfirm:B});const T=e(!1),U=e([]),J=e([]),q=e();let F="";const G=e=>{J.value=e},H=e=>{T.value=!0,U.value=e.menuBtn},K=()=>{T.value=!1},L=async()=>{const e=J.value.map((e=>e.ID));0===(await w({menuID:F,selected:e,authorityId:j.row.authorityId})).code&&(p({type:"success",message:"设置成功"}),T.value=!1)},P=(e,a)=>!e||-1!==a.meta.title.indexOf(e);return a(z,(e=>{S.value.filter(e)})),(e,a)=>{const m=t("base-input"),y=t("base-button"),f=t("el-tree"),w=t("el-table-column"),x=t("el-table"),D=t("el-dialog");return l(),o("div",null,[s("div",I,[u(m,{modelValue:z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>z.value=e),class:"fitler",placeholder:"筛选"},null,8,["modelValue"]),u(y,{class:"fl-right",size:"small",type:"primary",onClick:M},{default:n((()=>a[2]||(a[2]=[d("确 定")]))),_:1,__:[2]})]),s("div",b,[u(f,{ref_key:"menuTree",ref:S,data:N.value,"default-checked-keys":A.value,props:E.value,"default-expand-all":"","highlight-current":"","node-key":"ID","show-checkbox":"","filter-node-method":P,onCheck:O},{default:n((({node:e,data:t})=>[s("span",_,[s("span",null,r(e.label),1),s("span",null,[u(y,{type:"primary",link:"",size:"small",style:c({color:R.row.defaultRouter===t.name?"#E6A23C":"#85ce61"}),disabled:!e.checked,onClick:()=>(async e=>{const a=await v({authorityId:j.row.authorityId,AuthorityName:j.row.authorityName,parentId:j.row.parentId,defaultRouter:e.name});0===a.code&&(p({type:"success",message:"设置成功"}),V("changeRow","defaultRouter",a.data.authority.defaultRouter))})(t)},{default:n((()=>[d(r(R.row.defaultRouter===t.name?"总览":"设为总览"),1)])),_:2},1032,["style","disabled","onClick"])]),t.menuBtn.length?(l(),o("span",g,[u(y,{type:"primary",link:"",size:"small",onClick:()=>(async e=>{F=e.ID;const a=await k({menuID:F,authorityId:j.row.authorityId});0===a.code&&(H(e),await h(),a.data.selected&&a.data.selected.forEach((e=>{U.value.some((a=>{a.ID===e&&q.value.toggleRowSelection(a,!0)}))})))})(t)},{default:n((()=>a[3]||(a[3]=[d(" 分配按钮 ")]))),_:2,__:[3]},1032,["onClick"])])):i("",!0)])])),_:1},8,["data","default-checked-keys","props"])]),u(D,{modelValue:T.value,"onUpdate:modelValue":a[1]||(a[1]=e=>T.value=e),title:"分配按钮","destroy-on-close":""},{footer:n((()=>[s("div",C,[u(y,{size:"small",onClick:K},{default:n((()=>a[4]||(a[4]=[d("取 消")]))),_:1,__:[4]}),u(y,{size:"small",type:"primary",onClick:L},{default:n((()=>a[5]||(a[5]=[d("确 定")]))),_:1,__:[5]})])])),default:n((()=>[u(x,{ref_key:"btnTableRef",ref:q,data:U.value,"row-key":"ID",onSelectionChange:G},{default:n((()=>[u(w,{type:"selection",width:"55"}),u(w,{label:"按钮名称",prop:"name"}),u(w,{label:"按钮备注",prop:"desc"})])),_:1},8,["data"])])),_:1},8,["modelValue"])])}}});export{R as default};
