/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as e,u as a,a as s,b as t,r as n,p as r,c as o,E as i,M as l,h as c,o as u,d,j as p,w as m,e as v,t as y,f as h}from"./index.4982c0f9.js";import f from"./secondaryAuth.95bf0c1b.js";import"./verifyCode.0c196ff0.js";const g={class:"oauth-result-container"},_={key:0,class:"loading-box"},w={class:"message"},b={key:1,class:"secondary-auth-container"},S=e(Object.assign({name:"OAuth2Result"},{setup(e){const S=a(),q=s(),I=t(),T=n("正在处理认证信息..."),C=n(!1),j=n(""),P=n(""),x=n(""),A=n(""),L=n(""),k=n("phone"),E=n(!0);r("userName",x),r("last_id",A),r("isSecondary",n(!0)),r("contactType",k),r("hasContactInfo",E);const O=o((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===k.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===k.value}])),R=async e=>{T.value="认证成功，正在跳转...";let a=decodeURIComponent(L.value||"/");if(e.clientParams){const s=new URLSearchParams;s.set("type",e.clientParams.type),e.clientParams.wp&&s.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+s.toString()}window.location.href=a},N=()=>{C.value=!1,T.value="已取消验证，正在返回登录页...";const e=new URLSearchParams;S.query.idp_id&&e.set("idp_id",S.query.idp_id),S.query.redirect_url&&e.set("redirect",S.query.redirect_url),"client"===S.query.type&&(e.set("type","client"),S.query.wp&&e.set("wp",S.query.wp));const a=e.toString()?`/login?${e.toString()}`:"/login";q.push(a)};return i((async()=>{var e;try{const{auth_token:e,idp_id:a,redirect_url:s,login_type:t,auth_error:n}=S.query;if("is_test"===t)return T.value="测试完成",l.success("测试完成，正在关闭窗口..."),void setTimeout((()=>window.close()),2e3);if(n)throw new Error(n);if(!e)throw new Error("缺少有效的认证令牌");localStorage.setItem("loginType",t);const r={clientId:"client_portal",grantType:"implicit",redirect_uri:s,idpId:a,authWeb:{authWebToken:e}};T.value="验证登录信息...";const o=await I.LoginIn(r,t,a);if("object"==typeof o&&null!==o&&o.isSecondary){T.value="需要进行二次认证...",k.value=o.contactType||"phone",E.value=o.hasContactInfo||!1;const e=o.secondary&&Array.isArray(o.secondary)&&o.secondary.length>0?o.secondary[0].id:a;j.value=o.uniqKey,P.value=o.user_id,x.value=o.userName,A.value=e,L.value=s||"/",C.value=!0}else{if(!0!==o)throw new Error("登录处理失败");T.value="认证成功，正在跳转...",L.value=s||"/"}}catch(a){console.error("处理错误:",a);let t="认证失败，请稍后再试";try{if(null==(e=a.response)?void 0:e.data)t=a.response.data.error_description||a.response.data.message||a.response.data.error||t;else if(a.message){let e=a.message;if(e.includes("msg=登录失败")){const a=e.split("msg=登录失败:")[1]||e;e=a.trim()}if(e.includes("{"))try{const a=e.indexOf("{"),s=e.substring(a),n=JSON.parse(s);n&&n.message&&(t=n.message)}catch(s){}if("认证失败，请稍后再试"===t&&e.includes("message =")){const a=/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/,s=e.match(a);s&&s[1]&&(t=s[1].trim())}if("认证失败，请稍后再试"===t&&e.includes("reason =")){const a=/reason\s*=\s*(\w+)/,s=e.match(a);if(s&&s[1]){t=`认证失败: ${s[1].replace(/_/g," ").toLowerCase()}`}}"认证失败，请稍后再试"===t&&(t=e.split("\n")[0],t.length>100&&(t=t.substring(0,97)+"..."))}}catch(s){console.error("处理错误消息时发生异常:",s)}T.value=t,l.error(t),setTimeout((()=>{q.push({name:"Login"})}),2e3)}})),(e,a)=>{const s=c("base-icon"),t=c("el-icon");return u(),d("div",g,[C.value?h("v-if",!0):(u(),d("div",_,[p(t,{class:"loading-icon",size:40},{default:m((()=>[p(s,{name:"loading"})])),_:1}),v("div",w,y(T.value),1)])),h(" 使用SecondaryAuth组件代替Sms组件 "),C.value?(u(),d("div",b,[p(f,{"auth-info":{uniqKey:j.value,contactType:k.value,hasContactInfo:E.value},"auth-id":A.value,"user-name":x.value,"last-id":A.value,"auth-methods":O.value,onVerificationSuccess:R,onCancel:N},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])])):h("v-if",!0)])}}}),[["__scopeId","data-v-fe7893b9"],["__file","D:/asec-platform/frontend/portal/src/view/login/oauth2/oauth2_result.vue"]]);export{S as default};
