/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{r as e,l as a,h as t,o as l,d as n,j as o,w as i,f as s,g as r,e as d,F as p,i as u,O as y,J as f,t as m,Q as g,m as c,k as h,R as x}from"./index.74d1ee23.js";const C={style:{"font-size":"14px","font-family":"HarmonyOS_Medium"}},_={style:{"font-size":"12px","font-family":"HarmonyOS_Medium","font-weight":"500"}},b={name:"CustomTable",props:{tableData:{type:Array,required:!0},propList:{type:Array,required:!0},isIndexColumn:{type:Boolean,default:!1},isSelectColumn:{type:Boolean,default:!1},isOperationColumn:{type:Boolean,default:!1}}},v=Object.assign(b,{setup(b,{expose:v}){const w=e(),k=a("handleEdit"),S=a("handleDelete");e(!0);const z=e(!1),O=e(!0),H=a("currentPage"),M=a("pageSize"),j=a("total"),D=a("getTableData");return v({getSelectionRows:()=>(console.log("handleSelectionChange"),w.value.getSelectionRows())}),(e,a)=>{const v=t("el-table-column"),P=t("el-link"),R=t("el-table"),B=t("el-pagination");return l(),n("div",null,[o(R,{ref_key:"tableRef",ref:w,data:b.tableData,style:{width:"100%","min-height":"calc(100vh - 220px)"},height:"calc(100vh - 220px)","class-name":"table-row-style","row-class-name":"app-table-style"},{default:i((()=>[b.isSelectColumn?(l(),s(v,{key:0,type:"selection",width:"55"})):r("",!0),b.isIndexColumn?(l(),s(v,{key:1,label:"序号",type:"index",width:"60"},{header:i((()=>a[3]||(a[3]=[d("div",{style:{"text-align":"center"}},[d("span",{style:{"font-size":"14px","font-family":"HarmonyOS_Medium"}},"序号")],-1)]))),_:1})):r("",!0),(l(!0),n(p,null,u(b.propList,(a=>(l(),n(p,{key:a.prop},[!a.isHidden||a.isHidden.value?(l(),s(v,y({key:0,ref_for:!0},a,{"show-overflow-tooltip":""}),{header:i((()=>[d("div",{style:f([{"text-align":a.isCenter?a.isCenter:"left"}])},[d("span",C,m(a.label),1)],4)])),default:i((t=>[g(e.$slots,a.slotName,{row:t.row},(()=>[d("div",{style:f([{"text-align":a.isCenter?a.isCenter:"left"}])},[d("span",_,m(t.row[a.prop]),1)],4)]))])),_:2},1040)):r("",!0)],64)))),128)),b.isOperationColumn?(l(),s(v,{key:2,fixed:"right",label:"操作"},{header:i((()=>a[4]||(a[4]=[d("div",null,[d("span",{style:{"font-size":"14px","font-weight":"500","font-family":"HarmonyOS_Medium"}},"操作")],-1)]))),default:i((e=>[d("div",null,[o(P,{type:"primary",underline:!1,style:{"font-family":"HarmonyOS_Medium","margin-right":"20px","font-size":"12px","font-style":"normal"},onClick:a=>c(k)(e.$index,e.row)},{default:i((()=>a[5]||(a[5]=[h(" 编 辑 ")]))),_:2,__:[5]},1032,["onClick"]),o(P,{type:"primary",underline:!1,style:{"font-family":"HarmonyOS_Medium","font-size":"12px","font-style":"normal"},onClick:a=>c(S)(e.$index,e.row)},{default:i((()=>a[6]||(a[6]=[h(" 删 除 ")]))),_:2,__:[6]},1032,["onClick"])])])),_:1})):r("",!0)])),_:3},8,["data"]),o(B,{currentPage:c(H),"onUpdate:currentPage":a[0]||(a[0]=e=>x(H)?H.value=e:null),"page-size":c(M),"onUpdate:pageSize":a[1]||(a[1]=e=>x(M)?M.value=e:null),total:c(j),"onUpdate:total":a[2]||(a[2]=e=>x(j)?j.value=e:null),"page-sizes":[50,100,150,200],disabled:z.value,background:O.value,layout:"total, sizes, prev, pager, next, jumper",style:{float:"right"},class:"risk-pagination",onSizeChange:c(D),onCurrentChange:c(D)},null,8,["currentPage","page-size","total","disabled","background","onSizeChange","onCurrentChange"])])}}});export{v as _};
