/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{g as a,d as e,c as t,u as l,a as u}from"./authority.8a89f398.js";import r from"./menus.4c8535e3.js";import o from"./apis.89d8b6fa.js";import i from"./datas.e3f9a3db.js";import{W as d}from"./warningBar.4338ec87.js";import{r as s,h as n,o as y,d as h,j as v,e as m,w as c,k as p,f,g as I,P as b,M as g}from"./index.74d1ee23.js";import"./authorityBtn.6d1599aa.js";import"./api.34c114d1.js";const _={class:"authority"},w={class:"gva-table-box"},k={class:"gva-btn-list"},C={class:"dialog-footer"},N=Object.assign({name:"Authority"},{setup(N){const V=s([{authorityId:0,authorityName:"根角色"}]),z=s(!1),j=s("add"),x=s({}),A=s("新增角色"),U=s(!1),B=s(!1),S=s({}),q=s({authorityId:0,authorityName:"",parentId:0}),D=s({authorityId:[{required:!0,message:"请输入角色ID",trigger:"blur"},{validator:(a,e,t)=>/^[0-9]*[1-9][0-9]*$/.test(e)?t():t(new Error("请输入正整数")),trigger:"blur",message:"必须为正整数"}],authorityName:[{required:!0,message:"请输入角色名",trigger:"blur"}],parentId:[{required:!0,message:"请选择父角色",trigger:"blur"}]}),R=s(1),E=s(0),F=s(999),P=s([]),T=s({}),M=async()=>{const e=await a({page:R.value,pageSize:F.value,...T.value});0===e.code&&(P.value=e.data.list,E.value=e.data.total,R.value=e.data.page,F.value=e.data.pageSize)};M();const O=(a,e)=>{x.value[a]=e},W=s(null),$=s(null),G=s(null),H=(a,e)=>{const t=[W,$,G];e&&t[e].value.needConfirm&&(t[e].value.enterAndNext(),t[e].value.needConfirm=!1)},J=s(null),K=()=>{J.value&&J.value.resetFields(),q.value={authorityId:0,authorityName:"",parentId:0}},L=()=>{K(),U.value=!1,B.value=!1},Q=()=>{if(q.value.authorityId=Number(q.value.authorityId),0===q.value.authorityId)return g({type:"error",message:"角色id不能为0"}),!1;J.value.validate((async a=>{if(a){switch(j.value){case"add":0===(await u(q.value)).code&&(g({type:"success",message:"添加成功!"}),M(),L());break;case"edit":0===(await l(q.value)).code&&(g({type:"success",message:"添加成功!"}),M(),L());break;case"copy":{const a={authority:{authorityId:0,authorityName:"",datauthorityId:[],parentId:0},oldAuthorityId:0};a.authority.authorityId=q.value.authorityId,a.authority.authorityName=q.value.authorityName,a.authority.parentId=q.value.parentId,a.authority.dataAuthorityId=S.value.dataAuthorityId,a.oldAuthorityId=S.value.authorityId;0===(await t(a)).code&&(g({type:"success",message:"复制成功！"}),M())}}K(),U.value=!1}}))},X=()=>{V.value=[{authorityId:0,authorityName:"根角色"}],Y(P.value,V.value,!1)},Y=(a,e,t)=>{q.value.authorityId=String(q.value.authorityId),a&&a.forEach((a=>{if(a.children&&a.children.length){const l={authorityId:a.authorityId,authorityName:a.authorityName,disabled:t||a.authorityId===q.value.authorityId,children:[]};Y(a.children,l.children,t||a.authorityId===q.value.authorityId),e.push(l)}else{const l={authorityId:a.authorityId,authorityName:a.authorityName,disabled:t||a.authorityId===q.value.authorityId};e.push(l)}}))},Z=a=>{K(),A.value="新增角色",j.value="add",q.value.parentId=a,X(),U.value=!0};return(a,t)=>{const l=n("base-button"),u=n("el-table-column"),s=n("el-table"),N=n("el-cascader"),B=n("base-form-item"),E=n("base-input"),F=n("base-form"),T=n("el-dialog"),K=n("el-tab-pane"),Y=n("el-tabs"),aa=n("el-drawer");return y(),h("div",_,[v(d,{title:"注：右上角头像下拉可切换角色"}),m("div",w,[m("div",k,[v(l,{size:"small",type:"primary",icon:"plus",onClick:t[0]||(t[0]=a=>Z(0))},{default:c((()=>t[6]||(t[6]=[p("新增角色")]))),_:1,__:[6]})]),v(s,{data:P.value,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"authorityId",style:{width:"100%"}},{default:c((()=>[v(u,{label:"角色ID","min-width":"180",prop:"authorityId"}),v(u,{align:"left",label:"角色名称","min-width":"180",prop:"authorityName"}),v(u,{align:"left",label:"操作",width:"460"},{default:c((a=>[v(l,{icon:"setting",size:"small",type:"primary",link:"",onClick:e=>{return t=a.row,z.value=!0,void(x.value=t);var t}},{default:c((()=>t[7]||(t[7]=[p("设置权限")]))),_:2,__:[7]},1032,["onClick"]),v(l,{icon:"plus",size:"small",type:"primary",link:"",onClick:e=>Z(a.row.authorityId)},{default:c((()=>t[8]||(t[8]=[p("新增子角色")]))),_:2,__:[8]},1032,["onClick"]),v(l,{icon:"copy-document",size:"small",type:"primary",link:"",onClick:e=>(a=>{X(),A.value="拷贝角色",j.value="copy";for(const e in q.value)q.value[e]=a[e];S.value=a,U.value=!0})(a.row)},{default:c((()=>t[9]||(t[9]=[p("拷贝")]))),_:2,__:[9]},1032,["onClick"]),v(l,{icon:"edit",size:"small",type:"primary",link:"",onClick:e=>(a=>{X(),A.value="编辑角色",j.value="edit";for(const e in q.value)q.value[e]=a[e];X(),U.value=!0})(a.row)},{default:c((()=>t[10]||(t[10]=[p("编辑")]))),_:2,__:[10]},1032,["onClick"]),v(l,{icon:"delete",size:"small",type:"primary",link:"",onClick:t=>{return l=a.row,void b.confirm("此操作将永久删除该角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await e({authorityId:l.authorityId})).code&&(g({type:"success",message:"删除成功!"}),1===P.value.length&&R.value>1&&R.value--,M())})).catch((()=>{g({type:"info",message:"已取消删除"})}));var l}},{default:c((()=>t[11]||(t[11]=[p("删除")]))),_:2,__:[11]},1032,["onClick"])])),_:1})])),_:1},8,["data"])]),v(T,{modelValue:U.value,"onUpdate:modelValue":t[4]||(t[4]=a=>U.value=a),title:A.value},{footer:c((()=>[m("div",C,[v(l,{size:"small",onClick:L},{default:c((()=>t[12]||(t[12]=[p("取 消")]))),_:1,__:[12]}),v(l,{size:"small",type:"primary",onClick:Q},{default:c((()=>t[13]||(t[13]=[p("确 定")]))),_:1,__:[13]})])])),default:c((()=>[v(F,{ref_key:"authorityForm",ref:J,model:q.value,rules:D.value,"label-width":"80px"},{default:c((()=>[v(B,{label:"父级角色",prop:"parentId"},{default:c((()=>[v(N,{modelValue:q.value.parentId,"onUpdate:modelValue":t[1]||(t[1]=a=>q.value.parentId=a),style:{width:"100%"},disabled:"add"==j.value,options:V.value,props:{checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])])),_:1}),v(B,{label:"角色ID",prop:"authorityId"},{default:c((()=>[v(E,{modelValue:q.value.authorityId,"onUpdate:modelValue":t[2]||(t[2]=a=>q.value.authorityId=a),disabled:"edit"==j.value,autocomplete:"off"},null,8,["modelValue","disabled"])])),_:1}),v(B,{label:"角色姓名",prop:"authorityName"},{default:c((()=>[v(E,{modelValue:q.value.authorityName,"onUpdate:modelValue":t[3]||(t[3]=a=>q.value.authorityName=a),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),z.value?(y(),f(aa,{key:0,modelValue:z.value,"onUpdate:modelValue":t[5]||(t[5]=a=>z.value=a),"custom-class":"auth-drawer","with-header":!1,size:"40%",title:"角色配置"},{default:c((()=>[v(Y,{"before-leave":H,type:"border-card"},{default:c((()=>[v(K,{label:"角色菜单"},{default:c((()=>[v(r,{ref_key:"menus",ref:W,row:x.value,onChangeRow:O},null,8,["row"])])),_:1}),v(K,{label:"角色api"},{default:c((()=>[v(o,{ref_key:"apis",ref:$,row:x.value,onChangeRow:O},null,8,["row"])])),_:1}),v(K,{label:"资源权限"},{default:c((()=>[v(i,{ref_key:"datas",ref:G,authority:P.value,row:x.value,onChangeRow:O},null,8,["authority","row"])])),_:1})])),_:1})])),_:1},8,["modelValue"])):I("",!0)])}}});export{N as default};
