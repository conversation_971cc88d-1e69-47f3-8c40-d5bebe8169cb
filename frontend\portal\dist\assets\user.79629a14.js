/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{r as e,y as a,j as l,o as t,a as i,i as o,b as s,w as u,k as r,d,e as n,ae as m,P as p,af as c,M as v,ag as h,ah as g,ai as y,S as b,aj as f}from"./index.bfaf04e1.js";import{g as w}from"./authority.8d6791c2.js";import{C as I}from"./index.2ff0c44c.js";import{_}from"./index.ed6a63ad.js";import{W as V}from"./warningBar.0cee9251.js";import"./noBody.e1ae38ec.js";import"./index-browser-esm.c2d3b5c9.js";import"./common.a1b58fdb.js";const k={class:"gva-table-box"},N={class:"gva-btn-list"},C={style:{"text-align":"right","margin-top":"8px"}},x={class:"gva-pagination"},z={style:{height:"60vh",overflow:"auto",padding:"0 12px"}},U=["src"],j={key:1,class:"header-img-box"},D={class:"dialog-footer"},S=Object.assign({name:"User"},{setup(S){const O=e("/auth/"),q=(e,a)=>{e&&e.forEach((e=>{if(e.children&&e.children.length){const l={authorityId:e.authorityId,authorityName:e.authorityName,children:[]};q(e.children,l.children),a.push(l)}else{const l={authorityId:e.authorityId,authorityName:e.authorityName};a.push(l)}}))},B=e(1),J=e(0),T=e(10),P=e([]),E=e=>{T.value=e,R()},F=e=>{B.value=e,R()},R=async()=>{const e=await m({page:B.value,pageSize:T.value});0===e.code&&(P.value=e.data.list,J.value=e.data.total,B.value=e.data.page,T.value=e.data.pageSize)};a((()=>P.value),(()=>{M()}));(async()=>{R();const e=await w({page:1,pageSize:999});H(e.data.list)})();const M=()=>{P.value&&P.value.forEach((e=>{const a=e.authorities&&e.authorities.map((e=>e.authorityId));e.authorityIds=a}))},W=e(null),A=()=>{W.value.open()},G=e([]),H=e=>{G.value=[],q(e,G.value)},K=e({username:"",password:"",nickName:"",headerImg:"",authorityId:"",authorityIds:[],enable:1}),L=e({userName:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:5,message:"最低5位字符",trigger:"blur"}],password:[{required:!0,message:"请输入用户密码",trigger:"blur"},{min:6,message:"最低6位字符",trigger:"blur"}],nickName:[{required:!0,message:"请输入用户昵称",trigger:"blur"}],authorityId:[{required:!0,message:"请选择用户角色",trigger:"blur"}]}),Q=e(null),X=async()=>{K.value.authorityId=K.value.authorityIds[0],Q.value.validate((async e=>{if(e){const e={...K.value};if("add"===$.value){0===(await g(e)).code&&(v({type:"success",message:"创建成功"}),await R(),Z())}if("edit"===$.value){0===(await y(e)).code&&(v({type:"success",message:"编辑成功"}),await R(),Z())}}}))},Y=e(!1),Z=()=>{Q.value.resetFields(),K.value.headerImg="",K.value.authorityIds=[],Y.value=!1},$=e("add"),ee=()=>{$.value="add",Y.value=!0},ae={},le=async(e,a,l)=>{if(a)return void(l||(ae[e.ID]=[...e.authorityIds]));await b();0===(await f({ID:e.ID,authorityIds:e.authorityIds})).code?v({type:"success",message:"角色设置成功"}):l?e.authorityIds=[l,...e.authorityIds]:(e.authorityIds=[...ae[e.ID]],delete ae[e.ID])};return(e,a)=>{const m=l("base-button"),g=l("el-table-column"),f=l("el-cascader"),w=l("el-switch"),S=l("el-popover"),q=l("el-table"),M=l("el-pagination"),H=l("base-input"),ae=l("base-form-item"),te=l("base-form"),ie=l("el-dialog");return t(),i("div",null,[o(V,{title:"注：右上角头像下拉可切换角色"}),s("div",k,[s("div",N,[o(m,{size:"small",type:"primary",icon:"plus",onClick:ee},{default:u((()=>a[8]||(a[8]=[r("新增用户")]))),_:1,__:[8]})]),o(q,{data:P.value,"row-key":"ID"},{default:u((()=>[o(g,{align:"left",label:"头像","min-width":"75"},{default:u((e=>[o(I,{style:{"margin-top":"8px"},"pic-src":e.row.headerImg},null,8,["pic-src"])])),_:1}),o(g,{align:"left",label:"ID","min-width":"50",prop:"ID"}),o(g,{align:"left",label:"用户名","min-width":"150",prop:"userName"}),o(g,{align:"left",label:"昵称","min-width":"150",prop:"nickName"}),o(g,{align:"left",label:"手机号","min-width":"180",prop:"phone"}),o(g,{align:"left",label:"邮箱","min-width":"180",prop:"email"}),o(g,{align:"left",label:"用户角色","min-width":"200"},{default:u((e=>[o(f,{modelValue:e.row.authorityIds,"onUpdate:modelValue":a=>e.row.authorityIds=a,options:G.value,"show-all-levels":!1,"collapse-tags":"",props:{multiple:!0,checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},clearable:!1,onVisibleChange:a=>{le(e.row,a,0)},onRemoveTag:a=>{le(e.row,!1,a)}},null,8,["modelValue","onUpdate:modelValue","options","onVisibleChange","onRemoveTag"])])),_:1}),o(g,{align:"left",label:"启用","min-width":"150"},{default:u((e=>[o(w,{modelValue:e.row.enable,"onUpdate:modelValue":a=>e.row.enable=a,"inline-prompt":"","active-value":1,"inactive-value":2,onChange:()=>{(async e=>{K.value=JSON.parse(JSON.stringify(e)),await b();const a={...K.value};0===(await y(a)).code&&(v({type:"success",message:(2===a.enable?"禁用":"启用")+"成功"}),await R(),K.value.headerImg="",K.value.authorityIds=[])})(e.row)}},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),o(g,{label:"操作","min-width":"250",fixed:"right"},{default:u((e=>[o(S,{modelValue:e.row.visible,"onUpdate:modelValue":a=>e.row.visible=a,placement:"top",width:"160"},{reference:u((()=>[o(m,{type:"primary",link:"",icon:"delete",size:"small"},{default:u((()=>a[11]||(a[11]=[r("删除")]))),_:1,__:[11]})])),default:u((()=>[a[12]||(a[12]=s("p",null,"确定要删除此用户吗",-1)),s("div",C,[o(m,{size:"small",type:"primary",link:"",onClick:a=>e.row.visible=!1},{default:u((()=>a[9]||(a[9]=[r("取消")]))),_:2,__:[9]},1032,["onClick"]),o(m,{type:"primary",size:"small",onClick:a=>(async e=>{0===(await h({id:e.ID})).code&&(v.success("删除成功"),e.visible=!1,await R())})(e.row)},{default:u((()=>a[10]||(a[10]=[r("确定")]))),_:2,__:[10]},1032,["onClick"])])])),_:2,__:[12]},1032,["modelValue","onUpdate:modelValue"]),o(m,{type:"primary",link:"",icon:"edit",size:"small",onClick:a=>{return l=e.row,$.value="edit",K.value=JSON.parse(JSON.stringify(l)),void(Y.value=!0);var l}},{default:u((()=>a[13]||(a[13]=[r("编辑")]))),_:2,__:[13]},1032,["onClick"]),o(m,{type:"primary",link:"",icon:"magic-stick",size:"small",onClick:a=>{return l=e.row,void p.confirm("是否将此用户密码重置为123456?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=await c({ID:l.ID});0===e.code?v({type:"success",message:e.msg}):v({type:"error",message:e.msg})}));var l}},{default:u((()=>a[14]||(a[14]=[r("重置密码")]))),_:2,__:[14]},1032,["onClick"])])),_:1})])),_:1},8,["data"]),s("div",x,[o(M,{"current-page":B.value,"page-size":T.value,"page-sizes":[10,30,50,100],total:J.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:F,onSizeChange:E},null,8,["current-page","page-size","total"])])]),o(ie,{modelValue:Y.value,"onUpdate:modelValue":a[7]||(a[7]=e=>Y.value=e),"custom-class":"user-dialog",title:"用户","show-close":!1,"close-on-press-escape":!1,"close-on-click-modal":!1},{footer:u((()=>[s("div",D,[o(m,{size:"small",onClick:Z},{default:u((()=>a[15]||(a[15]=[r("取 消")]))),_:1,__:[15]}),o(m,{size:"small",type:"primary",onClick:X},{default:u((()=>a[16]||(a[16]=[r("确 定")]))),_:1,__:[16]})])])),default:u((()=>[s("div",z,[o(te,{ref_key:"userForm",ref:Q,rules:L.value,model:K.value,"label-width":"80px"},{default:u((()=>["add"===$.value?(t(),d(ae,{key:0,label:"用户名",prop:"userName"},{default:u((()=>[o(H,{modelValue:K.value.userName,"onUpdate:modelValue":a[0]||(a[0]=e=>K.value.userName=e)},null,8,["modelValue"])])),_:1})):n("",!0),"add"===$.value?(t(),d(ae,{key:1,label:"密码",prop:"password"},{default:u((()=>[o(H,{modelValue:K.value.password,"onUpdate:modelValue":a[1]||(a[1]=e=>K.value.password=e)},null,8,["modelValue"])])),_:1})):n("",!0),o(ae,{label:"昵称",prop:"nickName"},{default:u((()=>[o(H,{modelValue:K.value.nickName,"onUpdate:modelValue":a[2]||(a[2]=e=>K.value.nickName=e)},null,8,["modelValue"])])),_:1}),o(ae,{label:"手机号",prop:"phone"},{default:u((()=>[o(H,{modelValue:K.value.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>K.value.phone=e)},null,8,["modelValue"])])),_:1}),o(ae,{label:"邮箱",prop:"email"},{default:u((()=>[o(H,{modelValue:K.value.email,"onUpdate:modelValue":a[4]||(a[4]=e=>K.value.email=e)},null,8,["modelValue"])])),_:1}),o(ae,{label:"用户角色",prop:"authorityId"},{default:u((()=>[o(f,{modelValue:K.value.authorityIds,"onUpdate:modelValue":a[5]||(a[5]=e=>K.value.authorityIds=e),style:{width:"100%"},options:G.value,"show-all-levels":!1,props:{multiple:!0,checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},clearable:!1},null,8,["modelValue","options"])])),_:1}),o(ae,{label:"启用",prop:"disabled"},{default:u((()=>[o(w,{modelValue:K.value.enable,"onUpdate:modelValue":a[6]||(a[6]=e=>K.value.enable=e),"inline-prompt":"","active-value":1,"inactive-value":2},null,8,["modelValue"])])),_:1}),o(ae,{label:"头像","label-width":"80px"},{default:u((()=>[s("div",{style:{display:"inline-block"},onClick:A},[K.value.headerImg?(t(),i("img",{key:0,class:"header-img-box",src:K.value.headerImg&&"http"!==K.value.headerImg.slice(0,4)?O.value+K.value.headerImg:K.value.headerImg},null,8,U)):(t(),i("div",j,"从媒体库选择"))])])),_:1})])),_:1},8,["rules","model"])])])),_:1},8,["modelValue"]),o(_,{ref_key:"chooseImg",ref:W,target:K.value,"target-key":"headerImg"},null,8,["target"])])}}});export{S as default};
