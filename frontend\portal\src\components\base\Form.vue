<template>
  <form :class="formClass" @submit.prevent="handleSubmit">
    <slot></slot>
  </form>
</template>

<script setup>
import { computed, provide, ref, nextTick } from 'vue'

const props = defineProps({
  model: {
    type: Object,
    default: () => ({})
  },
  rules: {
    type: Object,
    default: () => ({})
  },
  labelPosition: {
    type: String,
    default: 'right',
    validator: (value) => ['left', 'right', 'top'].includes(value)
  },
  labelWidth: {
    type: String,
    default: '100px'
  },
  inline: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'validate'])

const formItems = ref([])

const formClass = computed(() => {
  const classes = ['base-form']

  if (props.inline) {
    classes.push('base-form--inline')
  }

  classes.push(`base-form--label-${props.labelPosition}`)

  return classes.join(' ')
})

const handleSubmit = (event) => {
  emit('submit', event)
}

// 注册表单项
const addFormItem = (formItem) => {
  formItems.value.push(formItem)
}

// 移除表单项
const removeFormItem = (formItem) => {
  const index = formItems.value.indexOf(formItem)
  if (index > -1) {
    formItems.value.splice(index, 1)
  }
}

// 验证表单
const validate = (callback) => {
  return new Promise((resolve, reject) => {
    let valid = true
    let count = 0
    const errors = []

    if (formItems.value.length === 0) {
      if (callback) callback(true)
      resolve(true)
      return
    }

    formItems.value.forEach(item => {
      item.validate('', (message) => {
        count++
        if (message) {
          valid = false
          errors.push(message)
        }

        if (count === formItems.value.length) {
          if (callback) callback(valid, errors)
          if (valid) {
            resolve(true)
          } else {
            reject(errors)
          }
        }
      })
    })
  })
}

// 验证指定字段
const validateField = (props, callback) => {
  const propsArray = Array.isArray(props) ? props : [props]
  const items = formItems.value.filter(item => propsArray.includes(item.prop))

  if (items.length === 0) {
    if (callback) callback()
    return
  }

  let valid = true
  let count = 0

  items.forEach(item => {
    item.validate('', (message) => {
      count++
      if (message) {
        valid = false
      }

      if (count === items.length) {
        if (callback) callback(valid)
      }
    })
  })
}

// 重置表单
const resetFields = () => {
  formItems.value.forEach(item => {
    item.resetField()
  })
}

// 清除验证
const clearValidate = (props) => {
  if (props) {
    const propsArray = Array.isArray(props) ? props : [props]
    formItems.value.forEach(item => {
      if (propsArray.includes(item.prop)) {
        item.clearValidate()
      }
    })
  } else {
    formItems.value.forEach(item => {
      item.clearValidate()
    })
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  validateField,
  resetFields,
  clearValidate
})

// 提供给子组件的数据
provide('baseForm', {
  model: props.model,
  rules: props.rules,
  labelPosition: props.labelPosition,
  labelWidth: props.labelWidth,
  addFormItem,
  removeFormItem
})
</script>

<style scoped>
.form-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}

.form-inline .form-item {
  margin-bottom: 0;
  margin-right: 16px;
}

.form-label-left .form-label {
  text-align: left;
}

.form-label-right .form-label {
  text-align: right;
}

.form-label-top .form-label {
  text-align: left;
  margin-bottom: 4px;
}
</style>
