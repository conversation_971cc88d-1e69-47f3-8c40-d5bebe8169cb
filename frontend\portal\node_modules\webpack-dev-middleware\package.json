{"name": "webpack-dev-middleware", "version": "3.7.3", "description": "A development middleware for webpack", "license": "MIT", "repository": "webpack/webpack-dev-middleware", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack/webpack-dev-middleware", "bugs": "https://github.com/webpack/webpack-dev-middleware/issues", "main": "index.js", "engines": {"node": ">= 6"}, "scripts": {"commitlint": "commitlint --from=master", "lint": "eslint --cache lib test", "release": "standard-version", "security": "npm audit", "test:only": "jest", "test:coverage": "npm run test:only -- --coverage", "test:watch": "npm run test:only --watch", "pretest": "npm run lint", "test": "npm run test:coverage", "defaults": "webpack-defaults"}, "files": ["lib", "index.js"], "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"memory-fs": "^0.4.1", "mime": "^2.4.4", "mkdirp": "^0.5.1", "range-parser": "^1.2.1", "webpack-log": "^2.0.0"}, "devDependencies": {"@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.1", "del": "^4.1.1", "del-cli": "^1.1.0", "eslint": "^6.4.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "express": "^4.17.1", "file-loader": "^4.2.0", "husky": "^3.0.7", "jest": "^24.9.0", "jest-junit": "^8.0.0", "lint-staged": "^9.4.0", "prettier": "^1.18.2", "standard-version": "^7.0.0", "supertest": "^4.0.2", "webpack": "^4.41.0"}, "keywords": ["webpack", "middleware", "develompent"]}