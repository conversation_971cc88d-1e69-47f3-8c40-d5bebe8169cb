/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./verifyCode-legacy.787644ec.js","./index-legacy.dbc04544.js"],(function(e,t){"use strict";var a,n,r,i,u,o,c,l,s,d,h,f,p,v,m,y,x=document.createElement("style");return x.textContent='@charset "UTF-8";.secondary-auth-overlay[data-v-3e719deb]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.5);z-index:1000;display:flex;justify-content:center;align-items:center}.secondary-auth-container[data-v-3e719deb]{background:#fff;padding:40px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,.1);min-width:340px;max-width:90%}.auth-selector .title[data-v-3e719deb]{height:60px;font-size:24px;text-align:center;margin-bottom:20px}.auth-selector .auth-methods[data-v-3e719deb]{display:flex;justify-content:center;flex-wrap:wrap;gap:20px;margin-bottom:20px}.auth-selector .auth-method-card[data-v-3e719deb]{width:120px;height:120px;cursor:pointer;transition:all .3s}.auth-selector .auth-method-card[data-v-3e719deb]:hover{transform:translateY(-5px);box-shadow:0 5px 15px rgba(0,0,0,.1)}.auth-selector .auth-method-content[data-v-3e719deb]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.auth-selector .auth-method-name[data-v-3e719deb]{margin-top:10px;font-size:14px}.auth-selector .selector-footer[data-v-3e719deb]{text-align:center;margin-top:20px}\n',document.head.appendChild(x),{setters:[function(e){a=e.default},function(e){n=e.r,r=e.c,i=e.h,u=e.o,o=e.d,c=e.e,l=e.F,s=e.i,d=e.j,h=e.w,f=e.g,p=e.f,v=e._,m=e.t,y=e.k}],execute:function(){var t={class:"secondary-auth-overlay"},x={class:"secondary-auth-container"},b={key:0,class:"auth-selector"},g={class:"auth-methods"},_={class:"auth-method-content"},k={class:"icon","aria-hidden":"true"},w=["xlink:href"],j={class:"auth-method-name"},C={class:"selector-footer"},I=Object.assign({name:"SecondaryAuth"},{props:{authMethods:{type:Array,default:function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:!0},{type:"email",name:"邮箱验证",icon:"email",available:!0}]}},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup:function(e,v){var I=v.emit,S=e,q=n(!0),z=n(null),A=r((function(){return S.authMethods.filter((function(e){return e.available}))})),F=function(e){z.value=e,q.value=!1};1===A.value.length&&F(A.value[0]);var M=I,N=function(){M("cancel")},O=function(e){"client"===route.query.type&&(e.clientParams={type:"client",wp:route.query.wp||"50001"}),M("verification-success",e)};return function(n,r){var v=i("base-avatar"),I=i("base-card"),S=i("base-button");return u(),o("div",t,[c("div",x,[q.value?(u(),o("div",b,[r[3]||(r[3]=c("h2",{class:"title"},"请选择二次认证方式",-1)),c("div",g,[(u(!0),o(l,null,s(A.value,(function(e){return u(),p(I,{key:e.type,class:"auth-method-card",onClick:function(t){return F(e)}},{default:h((function(){return[c("div",_,[d(v,null,{default:h((function(){return[(u(),o("svg",k,[c("use",{"xlink:href":"#icon-auth-"+e.icon},null,8,w)]))]})),_:2},1024),c("div",j,m(e.name),1)])]})),_:2},1032,["onClick"])})),128))]),c("div",C,[d(S,{type:"info",onClick:r[0]||(r[0]=function(){return N()})},{default:h((function(){return r[2]||(r[2]=[y("取消")])})),_:1,__:[2]})])])):f("",!0),!q.value&&z.value?(u(),p(a,{key:1,auth_info:e.authInfo,auth_id:e.authId,"user-name":e.userName,last_id:e.lastId,"secondary-type":z.value.type,onVerificationSuccess:O,onBack:r[1]||(r[1]=function(e){return q.value=!0}),onCancel:N},null,8,["auth_info","auth_id","user-name","last_id","secondary-type"])):f("",!0)])])}}});e("default",v(I,[["__scopeId","data-v-3e719deb"]]))}}}));
