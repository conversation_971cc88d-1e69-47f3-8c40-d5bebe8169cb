<template xmlns="http://www.w3.org/1999/html">
  <div class="layout-page">
    <!--公共顶部菜单--->
    <ClientHeader />
    <div class="layout-wrap">
      <!--公共侧边栏菜单-->
      <ClientMenu />

      <div id="layoutMain" class="layout-main">
        <!--主流程路由渲染点-->
        <router-view :key="$route.fullPath" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Client',
}
</script>

<script setup>
import ClientHeader from './header.vue'
import ClientMenu from './menu.vue'
// 使用轻量级 SVG 图标，已在 main.js 中全局加载
</script>

<style lang="scss">
.layout-page {
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  background: #fff;

  .layout-wrap{
    width: 100%;
    height: calc(100% - 0px);
    display: flex;
  }

  .layout-header {
    width: 100%;
    height: 42px;
    z-index: 10;
  }

  .layout-main {
    width: 100%;
    height: 100%;
    overflow: hidden;
    flex:1;
    background: #fff;
  }
}
</style>
