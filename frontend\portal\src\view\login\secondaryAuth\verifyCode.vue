<template>
  <div class="verify-code">
    <div style="top: 10px;margin-bottom: 25px;text-align: center">
      <span class="title">{{ secondaryType === 'email' ? '邮件认证' : '短信认证' }}</span>
    </div>
    <div>
      <div v-if="canVerify" class="message-text">验证码已发送至您账号({{ userName || user_name }})关联的{{ secondaryType === 'email' ? '邮箱' : '手机' }}，请注意查收</div>
      <div v-else class="message-text">您的账号({{ userName || user_name }})未关联{{ secondaryType === 'email' ? '邮箱' : '手机号码' }}，请联系管理员！</div>
      <div v-if="canVerify" class="mt-4" style="margin-bottom: 25px">
        <base-input
          v-model="auth_code"
          :placeholder="secondaryType === 'email' ? '邮箱验证码' : '短信验证码'"
          class="input-with-select"
        >
          <template #append>
            <base-button type="info" :disabled="count_down>0" @click="sendVerifyCode">重新发送
              {{ count_down > 0 ? `(${count_down}秒)` : '' }}
            </base-button>
          </template>
        </base-input>
      </div>
      <div style="text-align: center;">
        <base-button
          v-if="canVerify"
          type="primary"
          size="large"
          :disabled="!auth_code"
          @click="verify"
        >确 定
        </base-button>
        <base-button
          type="info"
          size="large"
          @click="cancel"
        >取 消
        </base-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VerifyCode'
}
</script>

<script setup>
import { inject, ref, computed } from 'vue'
import { post_send_sms } from '@/api/sms'
import { useUserStore } from '@/pinia/modules/user'
import { ElMessage } from 'element-plus'

const auth_code = ref('')
const user_name = inject('userName')
const last_id = inject('last_id')
const isSecondary = inject('isSecondary')

const props = defineProps({
  auth_info: {
    type: Object,
    default: function() {
      return {}
    }
  },
  auth_id: {
    type: String,
    default: function() {
      return ''
    }
  },
  userName: {
    type: String,
    default: function() {
      return ''
    }
  },
  lastId: {
    type: String,
    default: function() {
      return ''
    }
  },
  secondaryType: {
    type: String,
    default: 'sms' // 'sms' 或 'email'
  }
})

console.log('verifyCode组件接收到的属性:', {
  secondaryType: props.secondaryType,
  authInfo: props.auth_info,
  canVerify: props.secondaryType === 'email' ? props.auth_info.hasEmail !== false : props.auth_info.notPhone !== false
})


const emit = defineEmits(['verification-success', 'back', 'cancel'])

// 判断是否能进行验证（有手机号或邮箱）
const canVerify = computed(() => {
  // 如果auth_info中明确提供了hasContactInfo，使用它
  if (props.auth_info.hasContactInfo !== undefined) {
    return props.auth_info.hasContactInfo
  }
  
  // 否则根据secondaryType和相应的字段判断
  if (props.secondaryType === 'email') {
    return props.auth_info.hasContactInfo !== false
  } else {
    return props.auth_info.hasContactInfo !== false
  }
})

const count_down = ref(60)

let timerId
const startCountDown = () => {
  count_down.value = 60
  timerId = setInterval(() => {
    count_down.value--
    if (count_down.value === 0) {
      stopCountDown()
    }
  }, 1000)
}

const stopCountDown = () => {
  clearInterval(timerId)
}

// 发送验证码，无论是短信还是邮箱，都使用相同的后端接口
const sendVerifyCode = async() => {
  if (!canVerify.value) return
  
  const query = {
    uniq_key: props.auth_info.uniqKey,
    idp_id: props.auth_id
  }
  
  try {
    // 根据文件中看到的情况，无论是短信还是邮箱，都使用post_send_sms接口
    const res = await post_send_sms(query)
    
    if (res.status === 200 && res.data.code !== -1) {
      startCountDown()
    } else {
      ElMessage({
        showClose: true,
        message: res.data.msg,
        type: 'error',
      })
      count_down.value = 0
    }
  } catch (error) {
    ElMessage({
      showClose: true,
      message: '发送验证码失败',
      type: 'error',
    })
    count_down.value = 0
  }
}

// 页面加载时自动发送验证码
sendVerifyCode()

const userStore = useUserStore()
const verify = async() => {
  const query = {
    uniq_key: props.auth_info.uniqKey,
    auth_code: auth_code.value,
    user_name: props.userName || user_name.value,
    idp_id: props.auth_id,
    redirect_uri: 'hello world',
    grant_type: 'implicit',
    client_id: 'client_portal'
  }

  const res = await userStore.LoginIn(query, 'accessory')
  if (res.code === -1) {
    return
  }
  
  // 验证成功时发出事件
  emit('verification-success', res)
}

const cancel = () => {
  emit('cancel')
  // emit('back')
  // if (isSecondary && isSecondary.value !== undefined) {
  //   isSecondary.value = false
  // }
}
</script>

<style lang="scss" scoped>
.verify-code {
  .title {
    height: 60px;
    font-size: 24px;
    text-align: center;
  }
  .message-text {
    margin-bottom: 40px;
    text-align: center;
    line-height: 1.5;
  }
}
</style>