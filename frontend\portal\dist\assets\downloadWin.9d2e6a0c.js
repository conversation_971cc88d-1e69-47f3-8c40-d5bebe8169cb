/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{_ as e,K as a,r as o,H as l,N as t,h as n,o as s,f as i,w as d,j as r,E as c,e as u,J as v,T as m,d as p,k as g,g as w,M as f}from"./index.74d1ee23.js";import{_ as h}from"./ASD.492c8837.js";import"./iconfont.2d75af05.js";import{g as b}from"./system.2850f217.js";import"./browser.58a2c47c.js";const y={style:{background:"'#273444'"}},x={class:"downloadWin"},_={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},k={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-bottom":"42px","margin-top":"60px",display:"none"}},F={key:1,class:"download-complete"},T=e(Object.assign({name:"downloadWin"},{setup(e){a((e=>({"465db246":e.activeBackground,"70712b7d":e.normalText})));const T=o(!1),j=o(!0),E=o(!1),z=o("1"),L=o({});L.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};const S=o(!1),B=o(0),C=o(!1);let R=0;const M=()=>{const e=document.body.clientWidth;e<1e3||e>=1e3&&e<1200?(E.value=!1,j.value=!1,T.value=!0):(E.value=!1,j.value=!0,T.value=!1)};M();const O=o(!1);l((()=>{t.emit("collapse",T.value),t.emit("mobile",E.value),t.on("showLoading",(()=>{O.value=!0})),t.on("closeLoading",(()=>{O.value=!1})),window.onresize=()=>(M(),t.emit("collapse",T.value),void t.emit("mobile",E.value))}));const U=o("#1f2a36"),W=o(!1),D=()=>{T.value=!T.value,j.value=!T.value,W.value=!T.value,t.emit("collapse",T.value)},$=e=>100===e?"下载完成":`${e}%`,q=async(e,a)=>{try{const o=await H(e);P(o,a)}catch(o){if(R<3&&"网络连接超时"===o.message)return R++,q(e,a);throw new Error(`安装包下载失败，请检查网络连接或联系管理员。错误: ${o.message}`)}},H=e=>new Promise(((a,o)=>{const l=new XMLHttpRequest;l.open("GET",e,!0),l.responseType="blob",l.timeout=3e5;let t=Date.now();l.onprogress=e=>{if(e.lengthComputable){const a=e.loaded/e.total*100;B.value=Math.round(a)}else{const a=(Date.now()-t)/1e3,o=60*(e.loaded/a),l=e.loaded/o*100;B.value=Math.min(99,Math.round(l))}},l.onload=()=>{200===l.status?a(l.response):o(new Error(`HTTP 错误: ${l.status}`))},l.onerror=()=>{o(new Error("网络错误"))},l.ontimeout=()=>{o(new Error("网络连接超时"))},l.send()})),P=(e,a)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,a);else{const o=document.createElement("a"),l=document.querySelector("body");o.href=window.URL.createObjectURL(e),o.download=a,o.style.display="none",l.appendChild(o),o.click(),l.removeChild(o),window.URL.revokeObjectURL(o.href)}};return(e,a)=>{const o=n("base-row"),l=n("component"),t=n("el-icon"),M=n("el-menu-item"),O=n("el-menu"),H=n("el-scrollbar"),P=n("Expand"),A=n("Fold"),G=n("base-aside"),I=n("el-link"),J=n("el-progress"),K=n("base-main"),N=n("base-container");return s(),i(N,{class:"layout-cont"},{default:d((()=>[r(N,{class:c([j.value?"openside":"hideside",E.value?"mobile":""])},{default:d((()=>[r(o,{class:c([W.value?"shadowBg":""]),onClick:a[0]||(a[0]=e=>(W.value=!W.value,j.value=!!T.value,void D()))},null,8,["class"]),r(G,{class:"main-cont main-left gva-aside"},{default:d((()=>[u("div",{class:c(["tilte",[j.value?"openlogoimg":"hidelogoimg"]]),style:v({background:U.value})},a[2]||(a[2]=[u("img",{alt:"",class:"logoimg",src:h},null,-1)]),6),u("div",y,[r(H,{style:{height:"calc(100vh - 110px)"}},{default:d((()=>[r(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:d((()=>[r(O,{collapse:T.value,"collapse-transition":!1,"default-active":z.value,"background-color":L.value.background,"active-text-color":L.value.activeText,class:"el-menu-vertical","unique-opened":""},{default:d((()=>[r(M,{index:"1"},{default:d((()=>[r(t,null,{default:d((()=>[r(l,{class:"iconfont icon-zuhu-kehuduanxiazai"})])),_:1}),a[3]||(a[3]=u("span",null,"客户端下载",-1))])),_:1,__:[3]})])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})]),u("div",{class:"footer",style:v({background:U.value})},[u("div",{class:"menu-total",onClick:D},[T.value?(s(),i(t,{key:0,color:"#FFFFFF",size:"14px"},{default:d((()=>[r(P)])),_:1})):(s(),i(t,{key:1,color:"#FFFFFF",size:"14px"},{default:d((()=>[r(A)])),_:1}))])],4)])),_:1}),r(K,{class:"main-cont main-right client"},{default:d((()=>[u("div",x,[u("div",{style:{"margin-bottom":"5%",float:"left","margin-right":"5%",width:"205px",height:"209px",background:"#F1F8FF",position:"relative"},onClick:a[1]||(a[1]=e=>(async e=>{if("windows"===e){S.value=!0,B.value=0,C.value=!1,R=0;try{const a=await b({platform:e});if(0!==a.data.code)throw new Error(a.data.msg);{const e=window.location.port,o=new URL(a.data.data.download_url);let l;e?o.toString().includes("asec-deploy")?l=a.data.data.download_url:(o.port=e,l=o.toString()):(o.port="",l=o.toString());const t=e?a.data.data.latest_filename.replace(/@(\d+)/,`@${e}`):a.data.data.latest_filename;await q(l,t),C.value=!0,f({type:"success",message:"下载完成"})}}catch(a){f({type:"error",message:a.message||"下载失败，请联系管理员"})}finally{S.value=!1,setTimeout((()=>{C.value=!1}),3e3)}}})("windows"))},[(s(),p("svg",_,a[4]||(a[4]=[u("use",{"xlink:href":"#icon-windows"},null,-1)]))),(s(),p("svg",k,a[5]||(a[5]=[u("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),a[8]||(a[8]=u("br",null,null,-1)),r(I,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:d((()=>a[6]||(a[6]=[g(" Windows客户端 ")]))),_:1,__:[6]}),r(I,{class:"window-hidden",underline:!1,style:{"margin-top":"42px",display:"none"}},{default:d((()=>a[7]||(a[7]=[g(" 点击下载Windows客户端 ")]))),_:1,__:[7]}),S.value?(s(),i(J,{key:0,percentage:B.value,format:$,"stroke-width":10,style:{"margin-top":"20px"}},null,8,["percentage"])):w("",!0),C.value?(s(),p("div",F,"下载完成")):w("",!0)])])])),_:1})])),_:1},8,["class"])])),_:1})}}}),[["__scopeId","data-v-42c65cb6"]]);export{T as default};
