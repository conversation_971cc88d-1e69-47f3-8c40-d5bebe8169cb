/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{r as e,B as a,G as l,H as s,b as t,u as n,h as o,o as c,d as r,e as i,j as u,w as p,_ as d,F as v,i as m,f as b,k as f,t as g,E as k,g as y,I as h,J as _,M as w}from"./index.74d1ee23.js";import{g as W}from"./resource.29b70792.js";const S={class:"person"},U={class:"el-search"},x={class:"category-title"},C={class:"apps-container"},D={key:0,class:"status-badge"},I={class:"icon-wrapper"},O={class:"tooltip-content text-center"},T={key:0},A={key:1},E={class:"app-info"},F={class:"app-name"},P=d(Object.assign({name:"AppPage"},{setup(d){const P=e(""),V=e(null),B=e([]),J=e([]),N=e("1"),$=e(!1),j=e("standard"),L=a([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),M=e(null),q=e(!1),z=(e,a="success",l=3e3)=>{w({message:e,type:a,duration:l})},H=async e=>new Promise(((a,l)=>{let s,t=!1;(async()=>{try{const n=await new Promise(((e,a)=>{if(M.value&&M.value.readyState===WebSocket.OPEN)return void e(M.value);const l=new WebSocket("ws://localhost:50001");q.value=!0,l.onopen=()=>{console.log("WebSocket Connected"),M.value=l,q.value=!1,e(l)},l.onmessage=e=>{const a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&z(a,"error")},l.onclose=()=>{console.log("WebSocket Disconnected"),M.value=null,q.value=!1},l.onerror=e=>{console.error("WebSocket Error:",e),q.value=!1,a(e)},setTimeout((()=>{q.value&&(q.value=!1,l.close(),a(new Error("连接超时")))}),5e3)})),o={action:3,msg:e};s=setTimeout((()=>{t||(n.close(),l(new Error("启动超时：未收到响应")))}),3e3),n.onmessage=e=>{t=!0,clearTimeout(s);const n=e.data;n.startsWith("Ok")?a():l(new Error(n))},n.send(JSON.stringify(o)),console.log("发送消息:",o)}catch(n){clearTimeout(s),l(n)}})()}));l((()=>{M.value&&(M.value.close(),M.value=null)}));const R=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let l=0;for(let s=0;s<e.length;s++)l+=e.charCodeAt(s);return a[l%a.length]},G=()=>{$.value=!0},X=e=>{V.value=parseInt(e),J.value=e?B.value.filter((a=>a.id===parseInt(e))):B.value},K=()=>{if(!P.value)return void(J.value=B.value);const e=P.value.toLowerCase().trim();J.value=B.value.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))};s((()=>{(async()=>{try{const{data:e}=await W();if(console.log("API返回数据:",e),0===e.code&&e.data){const a=e.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl})))})));console.log("格式化后的数据:",a),B.value=a,J.value=a,a.length>0&&(V.value=a[0].id,N.value=a[0].id.toString())}}catch(e){console.error("API调用出错:",e)}})()}));const Q=t(),Y=n().query,Z=new XMLHttpRequest;Z.open("GET",document.location,!1),Z.send(null);const ee=Z.getResponseHeader("X-Corp-ID"),ae={action:0,msg:{token:Q.token.accessToken,refreshToken:Q.token.refreshToken,realm:ee||"default"},platform:document.location.hostname};{const a=Y.wp||50001,l=e({}),s=e(`ws://127.0.0.1:${a}`),t=navigator.platform;0!==t.indexOf("Mac")&&"MacIntel"!==t||(s.value=`wss://127.0.0.1:${a}`);const n=()=>{l.value=new WebSocket(s.value),l.value.onopen=()=>{console.log("socket连接成功"),o(JSON.stringify(ae))},l.value.onmessage=e=>{console.log(e),c()},l.value.onerror=()=>{console.log("socket连接错误:"+s.value),window.location.href=`asecagent://?web=${JSON.stringify(ae)}`}},o=e=>{console.log(e,"0"),l.value.send(e)},c=()=>{console.log("socket断开链接"),l.value.close()};console.log(`asecagent://?web=${JSON.stringify(ae)}`),n()}return(e,a)=>{const l=o("base-input"),s=o("base-button"),t=o("base-option"),n=o("base-select"),d=o("el-header"),w=o("el-menu-item"),W=o("el-menu"),V=o("base-aside"),M=o("base-avatar"),q=o("el-tooltip"),Q=o("el-link"),Y=o("base-main"),Z=o("base-container");return c(),r("div",null,[i("div",S,[u(d,null,{default:p((()=>[a[3]||(a[3]=i("span",{class:"el-title"},"我的应用",-1)),i("span",U,[u(l,{class:"el-search-input",modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),placeholder:"搜索应用","prefix-icon":"Search",onInput:K,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),u(s,{class:"el-search-btn",icon:"Refresh",size:"small"}),u(n,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),placeholder:"Select",size:"small"},{default:p((()=>[(c(!0),r(v,null,m(L,(e=>(c(),b(t,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])])),_:1,__:[3]}),u(Z,null,{default:p((()=>[u(V,{width:"96px",class:"category-aside"},{default:p((()=>[u(W,{class:"category-menu",onSelect:X,"default-active":N.value},{default:p((()=>[u(w,{index:"0",onClick:a[2]||(a[2]=e=>X(null))},{default:p((()=>a[4]||(a[4]=[f(" 全部 ")]))),_:1,__:[4]}),(c(!0),r(v,null,m(B.value,(e=>(c(),b(w,{key:e.id,index:e.id.toString()},{default:p((()=>[f(g(e.name),1)])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),u(Y,{class:"app-main"},{default:p((()=>[(c(!0),r(v,null,m(J.value,(e=>(c(),r("div",{key:e.id,class:"category-section"},[i("h3",x,g(e.name),1),i("div",C,[(c(!0),r(v,null,m(e.apps,(e=>(c(),r("div",{key:e.id,class:k(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(c(),r("div",D," 维护中 ")):y("",!0),u(Q,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:h((a=>(async e=>{if(e.WebUrl&&!e.maint)if(e.WebUrl.toLowerCase().startsWith("cs:")){const l=e.WebUrl.substring(3);try{z("正在启动爱尔企业浏览器...","info"),await H(l),z("启动成功","success")}catch(a){z("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else window.open(e.WebUrl,"_blank")})(e)),["prevent"])},{default:p((()=>[i("div",I,[u(q,{effect:"light",placement:"bottom"},{content:p((()=>[i("div",O,[e.WebUrl?(c(),r("span",T,g(e.WebUrl),1)):(c(),r("span",A,"暂无访问地址"))])])),default:p((()=>[u(M,{shape:"square",size:48,src:e.icon,onError:G,style:_(!e.icon||$.value?`background-color: ${R(e.app_name)} !important`:"")},{default:p((()=>[f(g(!e.icon||$.value?e.app_name.slice(0,2):""),1)])),_:2},1032,["src","style"])])),_:2},1024)]),i("div",E,[i("div",F,g(e.app_name),1),a[5]||(a[5]=i("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])])),_:2},1032,["disabled","onClick"])],2)))),128))])])))),128))])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-1225b8c9"]]);export{P as default};
