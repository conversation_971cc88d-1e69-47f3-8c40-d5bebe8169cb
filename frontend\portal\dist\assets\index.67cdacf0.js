/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as e,r as s,b as a,h as t,o as l,d as i,j as c,w as n,e as o,m as d,g as r,f as u}from"./index.4982c0f9.js";const m={class:"setting_body"},v={class:"setting_card"},g={class:"setting_content"},p={class:"theme-box"},f={class:"item-top"},k={class:"item-top"},y=e(Object.assign({name:"Setting"},{setup(e){const y=s(!1),b=s("rtl"),h=a(),_=()=>{y.value=!1},w=()=>{y.value=!0},j=e=>{null!==e?h.changeSideMode(e):h.changeSideMode("dark")};return(e,s)=>{const a=t("base-button"),C=t("check"),S=t("el-icon"),x=t("el-drawer");return l(),i("div",null,[c(a,{type:"primary",class:"drawer-container",icon:"setting",onClick:w}),c(x,{modelValue:y.value,"onUpdate:modelValue":s[2]||(s[2]=e=>y.value=e),title:"系统配置",direction:b.value,"before-close":_},{default:n((()=>[o("div",m,[o("div",v,[o("div",g,[o("div",p,[o("div",{class:"item",onClick:s[0]||(s[0]=e=>j("light"))},[o("div",f,["light"===d(h).mode?(l(),r(S,{key:0,class:"check"},{default:n((()=>[c(C)])),_:1})):u("v-if",!0),s[3]||(s[3]=o("img",{src:"https://gw.alipayobjects.com/zos/antfincdn/NQ%24zoisaD2/jpRkZQMyYRryryPNtyIC.svg"},null,-1))]),s[4]||(s[4]=o("p",null," 简约白 ",-1))]),o("div",{class:"item",onClick:s[1]||(s[1]=e=>j("dark"))},[o("div",k,["dark"===d(h).mode?(l(),r(S,{key:0,class:"check"},{default:n((()=>[c(C)])),_:1})):u("v-if",!0),s[5]||(s[5]=o("img",{src:"https://gw.alipayobjects.com/zos/antfincdn/XwFOFbLkSM/LCkqqYNmvBEbokSDscrm.svg"},null,-1))]),s[6]||(s[6]=o("p",null," 商务黑 ",-1))])])])])])])),_:1},8,["modelValue","direction"])])}}}),[["__scopeId","data-v-75466d12"],["__file","D:/asec-platform/frontend/portal/src/view/layout/setting/index.vue"]]);export{y as default};
