/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{a as e,t as l}from"./stringFun.2b3a18f6.js";import{g as a}from"./sysDictionary.2a470765.js";import{W as u}from"./warningBar.4338ec87.js";import{_ as d,r as o,h as t,o as i,d as m,j as r,w as s,e as n,F as p,i as f,f as v}from"./index.74d1ee23.js";const b=d(Object.assign({name:"FieldDialog"},{props:{dialogMiddle:{type:Object,default:function(){return{}}}},setup(d,{expose:b}){const c=d,y=o({}),V=o([]),g=o([{label:"=",value:"="},{label:"<>",value:"<>"},{label:">",value:">"},{label:"<",value:"<"},{label:"LIKE",value:"LIKE"},{label:"BETWEEN",value:"BETWEEN"},{label:"NOT BETWEEN",value:"NOT BETWEEN"}]),T=o([{label:"字符串",value:"string"},{label:"整型",value:"int"},{label:"布尔值",value:"bool"},{label:"浮点型",value:"float64"},{label:"时间",value:"time.Time"},{label:"枚举",value:"enum"}]),_=o({fieldName:[{required:!0,message:"请输入field英文名",trigger:"blur"}],fieldDesc:[{required:!0,message:"请输入field中文名",trigger:"blur"}],fieldJson:[{required:!0,message:"请输入field格式化json",trigger:"blur"}],columnName:[{required:!0,message:"请输入数据库字段",trigger:"blur"}],fieldType:[{required:!0,message:"请选择field数据类型",trigger:"blur"}]});(async()=>{y.value=c.dialogMiddle;const e=await a({page:1,pageSize:999999});V.value=e.data.list})();const E=()=>{y.value.fieldJson=e(y.value.fieldName),y.value.columnName=l(y.value.fieldJson)},N=()=>{y.value.fieldSearchType="",y.value.dictType=""},h=o(null);return b({fieldDialogFrom:h}),(e,l)=>{const a=t("base-input"),d=t("base-button"),o=t("base-form-item"),b=t("base-option"),c=t("base-select"),U=t("el-switch"),F=t("base-form");return i(),m("div",null,[r(u,{title:"id , created_at , updated_at , deleted_at 会自动生成请勿重复创建。搜索时如果条件为LIKE只支持字符串"}),r(F,{ref_key:"fieldDialogFrom",ref:h,model:y.value,"label-width":"120px","label-position":"right",rules:_.value,class:"grid-form"},{default:s((()=>[r(o,{label:"Field名称",prop:"fieldName"},{default:s((()=>[r(a,{modelValue:y.value.fieldName,"onUpdate:modelValue":l[0]||(l[0]=e=>y.value.fieldName=e),autocomplete:"off",style:{width:"80%"}},null,8,["modelValue"]),r(d,{size:"small",style:{width:"18%","margin-left":"2%"},onClick:E},{default:s((()=>l[12]||(l[12]=[n("span",{style:{"font-size":"12px"}},"自动填充",-1)]))),_:1,__:[12]})])),_:1}),r(o,{label:"Field中文名",prop:"fieldDesc"},{default:s((()=>[r(a,{modelValue:y.value.fieldDesc,"onUpdate:modelValue":l[1]||(l[1]=e=>y.value.fieldDesc=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),r(o,{label:"FieldJSON",prop:"fieldJson"},{default:s((()=>[r(a,{modelValue:y.value.fieldJson,"onUpdate:modelValue":l[2]||(l[2]=e=>y.value.fieldJson=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),r(o,{label:"数据库字段名",prop:"columnName"},{default:s((()=>[r(a,{modelValue:y.value.columnName,"onUpdate:modelValue":l[3]||(l[3]=e=>y.value.columnName=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),r(o,{label:"数据库字段描述",prop:"comment"},{default:s((()=>[r(a,{modelValue:y.value.comment,"onUpdate:modelValue":l[4]||(l[4]=e=>y.value.comment=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),r(o,{label:"Field数据类型",prop:"fieldType"},{default:s((()=>[r(c,{modelValue:y.value.fieldType,"onUpdate:modelValue":l[5]||(l[5]=e=>y.value.fieldType=e),style:{width:"100%"},placeholder:"请选择field数据类型",clearable:"",onChange:N},{default:s((()=>[(i(!0),m(p,null,f(T.value,(e=>(i(),v(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),r(o,{label:"enum"===y.value.fieldType?"枚举值":"类型长度",prop:"dataTypeLong"},{default:s((()=>[r(a,{modelValue:y.value.dataTypeLong,"onUpdate:modelValue":l[6]||(l[6]=e=>y.value.dataTypeLong=e),placeholder:"enum"===y.value.fieldType?"例:'北京','天津'":"数据库类型长度"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),r(o,{label:"Field查询条件",prop:"fieldSearchType"},{default:s((()=>[r(c,{modelValue:y.value.fieldSearchType,"onUpdate:modelValue":l[7]||(l[7]=e=>y.value.fieldSearchType=e),style:{width:"100%"},placeholder:"请选择Field查询条件",clearable:""},{default:s((()=>[(i(!0),m(p,null,f(g.value,(e=>(i(),v(b,{key:e.value,label:e.label,value:e.value,disabled:"string"!==y.value.fieldType&&"LIKE"===e.value||"int"!==y.value.fieldType&&"time.Time"!==y.value.fieldType&&"float64"!==y.value.fieldType&&("BETWEEN"===e.value||"NOT BETWEEN"===e.value)},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1}),r(o,{label:"关联字典",prop:"dictType"},{default:s((()=>[r(c,{modelValue:y.value.dictType,"onUpdate:modelValue":l[8]||(l[8]=e=>y.value.dictType=e),style:{width:"100%"},disabled:"int"!==y.value.fieldType,placeholder:"请选择字典",clearable:""},{default:s((()=>[(i(!0),m(p,null,f(V.value,(e=>(i(),v(b,{key:e.type,label:`${e.type}(${e.name})`,value:e.type},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),r(o,{label:"是否必填"},{default:s((()=>[r(U,{modelValue:y.value.require,"onUpdate:modelValue":l[9]||(l[9]=e=>y.value.require=e)},null,8,["modelValue"])])),_:1}),r(o,{label:"是否可清空"},{default:s((()=>[r(U,{modelValue:y.value.clearable,"onUpdate:modelValue":l[10]||(l[10]=e=>y.value.clearable=e)},null,8,["modelValue"])])),_:1}),r(o,{label:"校验失败文案"},{default:s((()=>[r(a,{modelValue:y.value.errorText,"onUpdate:modelValue":l[11]||(l[11]=e=>y.value.errorText=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])}}}),[["__scopeId","data-v-fd10f5c0"]]);export{b as default};
