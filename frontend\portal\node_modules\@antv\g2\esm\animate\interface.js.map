{"version": 3, "file": "interface.js", "sourceRoot": "", "sources": ["../../src/animate/interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Coordinate } from '../dependents';\n\n/**\n * @ignore\n */\nexport interface AnimateExtraCfg {\n  /**\n   * @title 当前坐标系\n   */\n  coordinate: Coordinate;\n  /**\n   * @title 图形最终样式\n   */\n  toAttrs: object;\n  /**\n   * @title 其他信息\n   */\n  [key: string]: any;\n}\n"]}