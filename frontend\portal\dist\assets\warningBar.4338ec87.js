/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{_ as a,h as e,o as n,d as t,j as s,w as r,e as i,t as l,E as o}from"./index.74d1ee23.js";const d=a({__name:"warningBar",props:{title:{type:String,default:""},href:{type:String,default:""}},setup(a){const d=a,c=()=>{d.href&&window.open(d.href)};return(d,p)=>{const f=e("warning-filled"),u=e("el-icon");return n(),t("div",{class:o(["warning-bar",a.href&&"can-click"]),onClick:c},[s(u,null,{default:r((()=>[s(f)])),_:1}),i("span",null,l(a.title),1)],2)}}},[["__scopeId","data-v-64538dc2"]]);export{d as W};
