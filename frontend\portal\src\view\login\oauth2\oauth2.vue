<template>
  <div class="sso-warpper">
    <base-button
      v-if="isForceBrowser"
      type="primary"
      size="large"
      class="login_submit_button"
      @click="clickSubmit"
    >授权登录</base-button>
    <iframe v-else :src="iframeSrc" frameborder="0" class="sso-iframe" />
  </div>
</template>
<script setup>
import { useRoute } from 'vue-router'
</script>
<script>

// 生成随机字符串作为 code_verifier
function generateCodeVerifier(length = 64) {
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'
  let text = ''
  for (let i = 0; i < length; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length))
  }
  return text
}

// 计算 code_challenge
async function generateCodeChallenge(codeVerifier) {
  const encoder = new TextEncoder()
  const data = encoder.encode(codeVerifier)
  const digest = await window.crypto.subtle.digest('SHA-256', data)

  return btoa(String.fromCharCode(...new Uint8Array(digest)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '')
}

export default {
  props: {
    auth_id: {
      type: String,
      default: function() {
        return ''
      },
    },
    auth_info: {
      type: Object,
      default: function() {
        return {}
      },
    }
  },
  data() {
    return {
      iframeSrc: '',
      isListen: false,
      isThirdBack: false,
      isAutoLogin: false,
      isListenShowApp: false,
      route: useRoute(),
      loading: false
    }
  },
  computed: {
    /**
     * 是否强制打开浏览器认证
     */
    isForceBrowser() {
      if (this.auth_info.authType === 'cas') {
        return parseInt(this.auth_info.casOpenType) === 1
      }
      return parseInt(this.auth_info.oauth2OpenType) === 1
    }
  },
  watch: {
    auth_id: {
      handler(newVal) {
        this.init()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
  },
  destroyed() {
    this.unListenGoBack()
    this.clearLoading()
    if (this.isListen) {
      this.removeEvent(window, 'message', this.listenHandle)
    }
  },
  methods: {
    init() {
      if (!this.isForceBrowser) {
        this.clickSubmit()
      }
    },
    async clickSubmit() {
      // 生成并存储 code_verifier
      const codeVerifier = generateCodeVerifier()
      sessionStorage.setItem('oauth2_code_verifier', codeVerifier)
      // 计算安全加强 code_challenge
      const codeChallenge = await generateCodeChallenge(codeVerifier)
      this.submit({ code_challenge: encodeURIComponent(codeChallenge), code_challenge_method: 'S256' })
    },
    async submit(data) {
      // let authType = 'oauth2'
      // if (this.auth_info.authType === 'cas') {
      //   authType = 'cas'
      // }
      let url = '/auth/login/v1/callback/' + this.auth_id
      // url后拼接data字典的参数
      if (data) {
        const urlList = []
        for (const key in data) {
          urlList.push(key + '=' + encodeURIComponent(data[key]))
        }
        url += '?' + urlList.join('&')
        if (this.route.query?.redirect) {
          const redirect = this.route.query?.redirect
          url += '&redirect=/' + encodeURIComponent(redirect)
        }
      }
      if (this.isForceBrowser) {
        window.location.href = url
      } else {
        if (this.isListen) {
          // 回调
          if (url.includes('code=') || url.includes('token=') || url.includes('auth_success=true')) {
            window.location.href = url // 认证成功，执行全局跳转
          } else {
            this.iframeSrc = url // 认证过程中，保持在iframe内
          }
          return
        }
        this.iframeSrc = url
        console.log('iframe初始地址', this.iframeSrc)
        this.isListen = true
        this.addEvent(window, 'message', this.listenHandle)
      }
    },
    async listenHandle(e) {
      console.log('sso触发监听')
      const eventName = e.data.event
      if (this.isThirdAppWakeup(eventName)) { // 第三发唤起app做授权认证
        this.wakeupApp(e)
        return
      }
      if (e.data) {
        this.submit(e.data)
      }
    },
    addEvent(element, event, handler) {
      if (element.addEventListener) { // 现代浏览器
        element.addEventListener(event, handler, false)
      } else if (element.attachEvent) { // IE8及以下
        element.attachEvent('on' + event, function() {
          // 修复this指向问题
          handler.call(element, window.event)
        })
      }
    },
    removeEvent(element, event, handlerWrapper) {
      if (element.removeEventListener) {
        element.removeEventListener(event, handlerWrapper)
      } else if (element.detachEvent) {
        element.detachEvent('on' + event, handlerWrapper)
      }
    },
    // ios浏览器iframe里面唤起第三方app失败所以逻辑放到外面
    isThirdAppWakeup(eventName) {
      return eventName === 'wakeup-app'
    },
    wakeupApp(e) {
      const url = e.data.params.url
      if (url) {
        window.location.href = url
      }
    },
    clearLoading() {
      if (this.loading) {
        this.loading.clear()
        this.loading = false
      }
    }
  }
}
</script>
<style scoped>
.sso-warpper{
  padding: 30px 60px 0 60px;
  overflow: visible;
  position: relative;
  .sso-img {
    width: 120px;
    height: 120px;
    display: block;
    margin: 0 auto;
  }
  .sso-iframe {
    margin-left: -60px;
    height: 300px;
    transform: translateZ(0); /* 强制硬件加速 */
    backface-visibility: hidden;
  }
  .login_submit_button {
    margin-bottom: 30px;
  }
}
</style>

<style>
.sso-iframe {
  height: 300px;
}
</style>
