/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{x as e,f as l,y as a,r as t,j as u,o as s,a as i,b as o,i as r,w as d,k as n,t as p,l as c,M as m}from"./index.bfaf04e1.js";import{f as v,a as y}from"./format.5148d109.js";import"./date.23f5a973.js";import"./dictionary.20c9ce22.js";import"./sysDictionary.cef56f98.js";const f=l=>e({url:"/sysDictionaryDetail/createSysDictionaryDetail",method:"post",data:l}),b={class:"gva-search-box"},_={class:"gva-table-box"},g={class:"gva-btn-list"},D={style:{"text-align":"right","margin-top":"8px"}},h={class:"gva-pagination"},w={class:"dialog-footer"},V=Object.assign({name:"SysDictionaryDetail"},{setup(V){const k=l();a((()=>k.params.id),(e=>{j.value.sysDictionaryID=Number(e),F()}));const z=t({label:null,value:null,status:!0,sort:null}),C=t({label:[{required:!0,message:"请输入展示值",trigger:"blur"}],value:[{required:!0,message:"请输入字典值",trigger:"blur"}],sort:[{required:!0,message:"排序标记",trigger:"blur"}]}),x=t(1),I=t(0),S=t(10),U=t([]),j=t({sysDictionaryID:Number(k.params.id)}),q=()=>{j.value={sysDictionaryID:Number(k.params.id)}},N=()=>{x.value=1,S.value=10,""===j.value.status&&(j.value.status=null),F()},M=e=>{S.value=e,F()},A=e=>{x.value=e,F()},F=async()=>{const l=await(a={page:x.value,pageSize:S.value,...j.value},e({url:"/sysDictionaryDetail/getSysDictionaryDetailList",method:"get",params:a}));var a;0===l.code&&(U.value=l.data.list,I.value=l.data.total,x.value=l.data.page,S.value=l.data.pageSize)};F();const L=t(""),O=t(!1),T=async l=>{const a=await(t={ID:l.ID},e({url:"/sysDictionaryDetail/findSysDictionaryDetail",method:"get",params:t}));var t;L.value="update",0===a.code&&(z.value=a.data.reSysDictionaryDetail,O.value=!0)},B=()=>{O.value=!1,z.value={label:null,value:null,status:!0,sort:null,sysDictionaryID:""}},E=async l=>{l.visible=!1;var a;0===(await(a={ID:l.ID},e({url:"/sysDictionaryDetail/deleteSysDictionaryDetail",method:"delete",data:a}))).code&&(m({type:"success",message:"删除成功"}),1===U.value.length&&x.value>1&&x.value--,F())},G=t(null),H=async()=>{z.value.sysDictionaryID=Number(k.params.id),G.value.validate((async l=>{if(!l)return;let a;switch(L.value){case"create":default:a=await f(z.value);break;case"update":a=await(t=z.value,e({url:"/sysDictionaryDetail/updateSysDictionaryDetail",method:"put",data:t}))}var t;0===a.code&&(m({type:"success",message:"创建/更改成功"}),B(),F())}))},J=()=>{L.value="create",O.value=!0};return(e,l)=>{const a=u("base-input"),t=u("base-form-item"),m=u("base-option"),f=u("base-select"),V=u("base-button"),k=u("base-form"),F=u("el-table-column"),L=u("el-popover"),K=u("el-table"),P=u("el-pagination"),Q=u("el-input-number"),R=u("el-switch"),W=u("el-dialog");return s(),i("div",null,[o("div",b,[r(k,{inline:!0,model:j.value},{default:d((()=>[r(t,{label:"展示值"},{default:d((()=>[r(a,{modelValue:j.value.label,"onUpdate:modelValue":l[0]||(l[0]=e=>j.value.label=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),r(t,{label:"字典值"},{default:d((()=>[r(a,{modelValue:j.value.value,"onUpdate:modelValue":l[1]||(l[1]=e=>j.value.value=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),r(t,{label:"启用状态",prop:"status"},{default:d((()=>[r(f,{modelValue:j.value.status,"onUpdate:modelValue":l[2]||(l[2]=e=>j.value.status=e),placeholder:"请选择"},{default:d((()=>[r(m,{key:"true",label:"是",value:"true"}),r(m,{key:"false",label:"否",value:"false"})])),_:1},8,["modelValue"])])),_:1}),r(t,null,{default:d((()=>[r(V,{size:"small",type:"primary",icon:"search",onClick:N},{default:d((()=>l[8]||(l[8]=[n("查询")]))),_:1,__:[8]}),r(V,{size:"small",icon:"refresh",onClick:q},{default:d((()=>l[9]||(l[9]=[n("重置")]))),_:1,__:[9]})])),_:1})])),_:1},8,["model"])]),o("div",_,[o("div",g,[r(V,{size:"small",type:"primary",icon:"plus",onClick:J},{default:d((()=>l[10]||(l[10]=[n("新增字典项")]))),_:1,__:[10]})]),r(K,{ref:"multipleTable",data:U.value,style:{width:"100%"},"tooltip-effect":"dark","row-key":"ID"},{default:d((()=>[r(F,{type:"selection",width:"55"}),r(F,{align:"left",label:"日期",width:"180"},{default:d((e=>[n(p(c(v)(e.row.CreatedAt)),1)])),_:1}),r(F,{align:"left",label:"展示值",prop:"label",width:"120"}),r(F,{align:"left",label:"字典值",prop:"value",width:"120"}),r(F,{align:"left",label:"启用状态",prop:"status",width:"120"},{default:d((e=>[n(p(c(y)(e.row.status)),1)])),_:1}),r(F,{align:"left",label:"排序标记",prop:"sort",width:"120"}),r(F,{align:"left",label:"按钮组"},{default:d((e=>[r(V,{size:"small",type:"primary",link:"",icon:"edit",onClick:l=>T(e.row)},{default:d((()=>l[11]||(l[11]=[n("变更")]))),_:2,__:[11]},1032,["onClick"]),r(L,{modelValue:e.row.visible,"onUpdate:modelValue":l=>e.row.visible=l,placement:"top",width:"160"},{reference:d((()=>[r(V,{type:"primary",link:"",icon:"delete",size:"small",onClick:l=>e.row.visible=!0},{default:d((()=>l[14]||(l[14]=[n("删除")]))),_:2,__:[14]},1032,["onClick"])])),default:d((()=>[l[15]||(l[15]=o("p",null,"确定要删除吗？",-1)),o("div",D,[r(V,{size:"small",type:"primary",link:"",onClick:l=>e.row.visible=!1},{default:d((()=>l[12]||(l[12]=[n("取消")]))),_:2,__:[12]},1032,["onClick"]),r(V,{type:"primary",size:"small",onClick:l=>E(e.row)},{default:d((()=>l[13]||(l[13]=[n("确定")]))),_:2,__:[13]},1032,["onClick"])])])),_:2,__:[15]},1032,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"]),o("div",h,[r(P,{"current-page":x.value,"page-size":S.value,"page-sizes":[10,30,50,100],total:I.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:A,onSizeChange:M},null,8,["current-page","page-size","total"])])]),r(W,{modelValue:O.value,"onUpdate:modelValue":l[7]||(l[7]=e=>O.value=e),"before-close":B,title:"弹窗操作"},{footer:d((()=>[o("div",w,[r(V,{size:"small",onClick:B},{default:d((()=>l[16]||(l[16]=[n("取 消")]))),_:1,__:[16]}),r(V,{size:"small",type:"primary",onClick:H},{default:d((()=>l[17]||(l[17]=[n("确 定")]))),_:1,__:[17]})])])),default:d((()=>[r(k,{ref_key:"dialogForm",ref:G,model:z.value,rules:C.value,size:"medium","label-width":"110px"},{default:d((()=>[r(t,{label:"展示值",prop:"label"},{default:d((()=>[r(a,{modelValue:z.value.label,"onUpdate:modelValue":l[3]||(l[3]=e=>z.value.label=e),placeholder:"请输入展示值",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),r(t,{label:"字典值",prop:"value"},{default:d((()=>[r(Q,{modelValue:z.value.value,"onUpdate:modelValue":l[4]||(l[4]=e=>z.value.value=e),modelModifiers:{number:!0},"step-strictly":"",step:1,placeholder:"请输入字典值",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),r(t,{label:"启用状态",prop:"status",required:""},{default:d((()=>[r(R,{modelValue:z.value.status,"onUpdate:modelValue":l[5]||(l[5]=e=>z.value.status=e),"active-text":"开启","inactive-text":"停用"},null,8,["modelValue"])])),_:1}),r(t,{label:"排序标记",prop:"sort"},{default:d((()=>[r(Q,{modelValue:z.value.sort,"onUpdate:modelValue":l[6]||(l[6]=e=>z.value.sort=e),modelModifiers:{number:!0},placeholder:"排序标记"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}});export{V as default};
