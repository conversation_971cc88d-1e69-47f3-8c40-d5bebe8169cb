/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
System.register(["./menuItem-legacy.72954c0c.js","./asyncSubmenu-legacy.0a4da314.js","./index-legacy.04f34b53.js"],(function(e,n){"use strict";var t,r,u,o,i,f,c,l,a,s,d,h;return{setters:[function(e){t=e.default},function(e){r=e.default},function(e){u=e.c,o=e.h,i=e.o,f=e.f,c=e.w,l=e.d,a=e.F,s=e.i,d=e.g,h=e.x}],execute:function(){e("default",Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:function(){return null}},isCollapse:{default:function(){return!1},type:<PERSON>olean},theme:{default:function(){return{}},type:Object}},setup:function(e){var n=e,m=u((function(){return n.routerInfo.children&&n.routerInfo.children.filter((function(e){return!e.hidden})).length?r:t}));return function(n,t){var r=o("AsideComponent");return e.routerInfo.hidden?d("",!0):(i(),f(h(m.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:c((function(){return[e.routerInfo.children&&e.routerInfo.children.length?(i(!0),l(a,{key:0},s(e.routerInfo.children,(function(n){return i(),f(r,{key:n.name,"is-collapse":!1,"router-info":n,theme:e.theme},null,8,["router-info","theme"])})),128)):d("",!0)]})),_:1},8,["is-collapse","theme","router-info"]))}}}))}}}));
