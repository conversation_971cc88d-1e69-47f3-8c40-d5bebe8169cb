/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js"],(function(t,e){"use strict";var o;return{setters:[function(t){o=t.x}],execute:function(){t("p",(function(t){return o({url:"/autoCode/preview",method:"post",data:t})})),t("c",(function(t){return o({url:"/autoCode/createTemp",method:"post",data:t,responseType:"blob"})})),t("d",(function(){return o({url:"/autoCode/getDB",method:"get"})})),t("g",(function(t){return o({url:"/autoCode/getTables",method:"get",params:t})})),t("a",(function(t){return o({url:"/autoCode/getColumn",method:"get",params:t})})),t("f",(function(t){return o({url:"/autoCode/getSysHistory",method:"post",data:t})})),t("r",(function(t){return o({url:"/autoCode/rollback",method:"post",data:t})})),t("e",(function(t){return o({url:"/autoCode/getMeta",method:"post",data:t})})),t("h",(function(t){return o({url:"/autoCode/delSysHistory",method:"post",data:t})})),t("i",(function(t){return o({url:"/autoCode/createPackage",method:"post",data:t})})),t("b",(function(){return o({url:"/autoCode/getPackage",method:"post"})})),t("j",(function(t){return o({url:"/autoCode/delPackage",method:"post",data:t})})),t("k",(function(t){return o({url:"/autoCode/createPlug",method:"post",data:t})}))}}}));
