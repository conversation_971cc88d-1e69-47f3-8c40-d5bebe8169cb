/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{b as a,r as e,h as s,o as l,d as o,j as t,w as d,e as n,k as u,m as i,M as r}from"./index.74d1ee23.js";const c={__name:"index",setup(c){const p=a(),_=e("/auth"),m=a=>{if(0===a.code){let e="";a.data&&a.data.forEach(((a,s)=>{e+=`${s+1}.${a.msg}\n`})),alert(e)}else r.error(a.msg)};return(a,e)=>{const r=s("upload-filled"),c=s("el-icon"),f=s("el-upload");return l(),o("div",null,[t(f,{class:"upload-demo",drag:"",action:`${_.value}/autoCode/installPlugin`,headers:{"x-token":i(p).token},"show-file-list":!1,"on-success":m,"on-error":m,name:"plug"},{tip:d((()=>e[0]||(e[0]=[n("div",{class:"el-upload__tip"}," 请把安装包的zip拖拽至此处上传 ",-1)]))),default:d((()=>[t(c,{class:"el-icon--upload"},{default:d((()=>[t(r)])),_:1}),e[1]||(e[1]=n("div",{class:"el-upload__text"},[u(" 拖拽或"),n("em",null,"点击上传")],-1))])),_:1,__:[1]},8,["action","headers"])])}}};export{c as default};
