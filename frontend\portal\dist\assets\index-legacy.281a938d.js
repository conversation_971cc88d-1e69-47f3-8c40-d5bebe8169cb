/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?t(Object(i),!0).forEach((function(t){a(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):t(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function a(t,n,a){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var i=a.call(t,n||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a,t}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function l(n,a,i,o){var l=a&&a.prototype instanceof c?a:c,s=Object.create(l.prototype);return r(s,"_invoke",function(n,a,i){var r,o,l,c=0,s=i||[],f=!1,p={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,o=0,l=e,p.n=n,u}};function v(n,a){for(o=n,l=a,t=0;!f&&c&&!i&&t<s.length;t++){var i,r=s[t],v=p.p,d=r[2];n>3?(i=d===a)&&(l=r[(o=r[4])?5:(o=3,3)],r[4]=r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(o=0,p.v=a,p.n=r[1]):v<d&&(i=n<3||r[0]>a||a>d)&&(r[4]=n,r[5]=a,p.n=d,o=0))}if(i||n>1)return u;throw f=!0,a}return function(i,s,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===s&&v(s,d),o=s,l=d;(t=o<2?e:l)||!f;){r||(o?o<3?(o>1&&(p.n=-1),v(o,l)):p.n=l:p.v=l);try{if(c=2,r){if(o||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,o<2&&(o=0)}else 1===o&&(t=r.return)&&t.call(r),o<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),o=1);r=e}else if((t=(f=p.n<0)?l:n.call(a,p))!==u)break}catch(t){r=e,o=1,l=t}finally{c=1}}return{value:t,done:f}}}(n,i,o),!0),s}var u={};function c(){}function s(){}function f(){}t=Object.getPrototypeOf;var p=[][a]?t(t([][a]())):(r(t={},a,(function(){return this})),t),v=f.prototype=c.prototype=Object.create(p);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,r(e,o,"GeneratorFunction")),e.prototype=Object.create(v),e}return s.prototype=f,r(v,"constructor",f),r(f,"constructor",s),s.displayName="GeneratorFunction",r(f,o,"GeneratorFunction"),r(v),r(v,o,"Generator"),r(v,a,(function(){return this})),r(v,"toString",(function(){return"[object Generator]"})),(i=function(){return{w:l,m:d}})()}function r(e,t,n,a){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}r=function(e,t,n,a){if(t)i?i(e,t,{value:n,enumerable:!a,configurable:!a,writable:!a}):e[t]=n;else{var o=function(t,n){r(e,t,(function(e){return this._invoke(t,n,e)}))};o("next",0),o("throw",1),o("return",2)}},r(e,t,n,a)}function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,i=function(){};return{s:i,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,r=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw r}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function u(e,t,n,a,i,r,o){try{var l=e[r](o),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(a,i)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(a,i){var r=e.apply(t,n);function o(e){u(r,a,i,o,l,"next",e)}function l(e){u(r,a,i,o,l,"throw",e)}o(void 0)}))}}System.register(["./index-legacy.11b10372.js","./localLogin-legacy.71333390.js","./wechat-legacy.3141f631.js","./feishu-legacy.c4965493.js","./dingtalk-legacy.3fd18da2.js","./oauth2-legacy.187f689d.js","./iconfont-legacy.37c53566.js","./sms-legacy.8b1ef0cc.js","./secondaryAuth-legacy.7277650a.js","./verifyCode-legacy.2153456a.js"],(function(e,t){"use strict";var a,r,l,u,s,f,p,v,d,g,h,y,m,b,x,w,j,k,O,_,S,P,T,z,q,L,E,C,I,A=document.createElement("style");return A.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+");background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}\n",document.head.appendChild(A),{setters:[function(e){a=e.s,r=e._,l=e.u,u=e.r,s=e.c,f=e.b,p=e.v,v=e.p,d=e.h,g=e.o,h=e.d,y=e.e,m=e.f,b=e.k,x=e.t,w=e.g,j=e.x,k=e.j,O=e.w,_=e.F,S=e.L,P=e.i},function(e){T=e.default},function(e){z=e.default},function(e){q=e.default},function(e){L=e.default},function(e){E=e.default},function(){},function(e){C=e.default},function(e){I=e.default},function(){}],execute:function(){var t={class:"login-page"},A={class:"content"},D={class:"right-panel"},G={key:0},U={key:0,class:"title"},F={key:1,class:"title"},N={style:{"text-align":"center"}},R={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},B={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},K=["xlink:href"],J={key:2,class:"login_panel_form"},M={key:3},V=["onClick"],$={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},H=["xlink:href"],Q={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},W={class:"auth-waiting"},X={class:"waiting-icon"},Y={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},Z=["xlink:href"],ee={class:"waiting-title"},te=Object.assign({name:"Login"},{setup:function(e){var r=l(),te=u(0),ne=u([]),ae=u("local"),ie=u(""),re=u(""),oe=u(""),le=u([]),ue=u([]),ce=u(!1),se=u(),fe=u(""),pe=u(!1),ve=u(""),de=u(!1),ge=u(""),he=u(""),ye=u(""),me=u({}),be=s((function(){var e=ce.value?ge.value:re.value;return ne.value.filter((function(t){return t.id!==e}))})),xe=f();s((function(){return ue.value.filter((function(e){return e.id!==re.value}))}));var we=function(){var e={};if(r.query.type&&(e.type=r.query.type),r.query.wp&&(e.wp=r.query.wp),r.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(r.query.redirect);if(t.includes("?")){var n=t.substring(t.indexOf("?")+1),a=new URLSearchParams(n);a.get("type")&&(e.type=a.get("type")),a.get("wp")&&(e.wp=a.get("wp"))}}catch(i){console.warn("解析redirect参数失败:",i)}return e},je=function(){var e=c(i().m((function e(){var t,n,l,u,c,s,f,p,v,d,g,h,y,m,b,x,w,j,k,O;return i().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,t=we(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),e.n=1,a({url:"/auth/login/v1/user/main_idp/list",method:"get"});case 1:if(200===(n=e.v).status){if(ne.value=n.data.idpList,(l=r.query.idp_id||xe.loginType)&&"undefined"!==l){u=!1,c=o(n.data.idpList);try{for(c.s();!(s=c.n()).done;)f=s.value,l===f.id&&(u=!0,re.value=f.id,ae.value=f.type,ie.value=f.templateType,le.value=f.attrs,le.value.name=f.name,le.value.authType=f.type)}catch(i){c.e(i)}finally{c.f()}u||(oe.value=null===(p=ne.value[0])||void 0===p?void 0:p.id,re.value=null===(v=ne.value[0])||void 0===v?void 0:v.id,ae.value=null===(d=ne.value[0])||void 0===d?void 0:d.type,ie.value=null===(g=ne.value[0])||void 0===g?void 0:g.templateType,le.value=null===(h=ne.value[0])||void 0===h?void 0:h.attrs,le.value.name=ne.value[0].name,le.value.authType=null===(y=ne.value[0])||void 0===y?void 0:y.type)}else oe.value=null===(m=ne.value[0])||void 0===m?void 0:m.id,re.value=null===(b=ne.value[0])||void 0===b?void 0:b.id,ae.value=null===(x=ne.value[0])||void 0===x?void 0:x.type,ie.value=null===(w=ne.value[0])||void 0===w?void 0:w.templateType,le.value=null===(j=ne.value[0])||void 0===j?void 0:j.attrs,le.value.name=ne.value[0].name,le.value.authType=null===(k=ne.value[0])||void 0===k?void 0:k.type;++te.value}e.n=3;break;case 2:e.p=2,O=e.v,console.error(O);case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}();je();var ke=s((function(){switch(ae.value){case"local":case"msad":case"ldap":case"web":case"email":return T;case"qiyewx":return z;case"feishu":return q;case"dingtalk":return L;case"oauth2":case"cas":return E;case"sms":return C;default:return"oauth2"===ie.value?E:"local"}})),Oe=s((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===ve.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===ve.value}]})),_e=function(){ce.value=!1,ue.value=[],se.value="",fe.value="",ve.value="",de.value=!1,ge.value&&(re.value=ge.value,ae.value=he.value,ie.value=ye.value,le.value=n({},me.value),ge.value="",he.value="",ye.value="",me.value={}),++te.value,console.log("取消后恢复的状态:",{isSecondary:ce.value,auth_id:re.value,auth_type:ae.value})},Se=function(){var e=c(i().m((function e(t){var n,a,o;return i().w((function(e){for(;;)switch(e.n){case 0:n=S.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{a=r.query.redirect_url||"/",t.clientParams&&((o=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&o.set("wp",t.clientParams.wp),a+=(a.includes("?")?"&":"?")+o.toString()),window.location.href=a}finally{null==n||n.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),Pe=s((function(){return!["dingtalk","feishu","qiyewx"].includes(ae.value)&&("oauth2"!==ie.value&&"cas"!==ae.value||("cas"===ae.value?1===parseInt(le.value.casOpenType):"oauth2"===ie.value&&1===parseInt(le.value.oauth2OpenType)))})),Te=function(e){oe.value=e.id,le.value=e.attrs||{},le.value.name=e.name,le.value.authType=e.type,ce.value&&(le.value.uniqKey=se.value,le.value.notPhone=pe.value),re.value=e.id,ae.value=e.type,ie.value=e.templateType,++te.value};return p(ce,function(){var e=c(i().m((function e(t,a){return i().w((function(e){for(;;)switch(e.n){case 0:ce.value&&(ge.value=re.value,he.value=ae.value,ye.value=ie.value,me.value=n({},le.value),console.log("二次认证数据:",{secondary:ue.value,secondaryLength:ue.value.length}),ue.value.length>0&&Te(ue.value[0]));case 1:return e.a(2)}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),v("secondary",ue),v("isSecondary",ce),v("uniqKey",se),v("userName",fe),v("notPhone",pe),v("last_id",oe),v("contactType",ve),v("hasContactInfo",de),function(e,n){var a=d("base-divider"),i=d("base-avatar"),r=d("base-carousel-item"),o=d("base-carousel");return g(),h("div",t,[y("div",A,[n[3]||(n[3]=y("div",{class:"left-panel"},[m(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),m('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),m(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),y("div",D,[m(" 正常登录状态 "),ce.value?(g(),h(_,{key:1},[m(" 二次认证等待状态 "),y("div",W,[y("div",X,[(g(),h("svg",Y,[y("use",{"xlink:href":"#icon-auth-".concat(he.value||ae.value)},null,8,Z)]))]),y("h4",ee,x(me.value.name||le.value.name)+" 登录成功",1),n[1]||(n[1]=y("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),n[2]||(n[2]=y("div",{class:"security-tips"},[y("i",{class:"el-icon-shield",style:{color:"#67c23a"}}),y("span",null,"为了您的账户安全，请完成二次身份验证")],-1))])],2112)):(g(),h("div",G,["local"===ae.value?(g(),h("span",U,"本地账号登录")):Pe.value?(g(),h("span",F,[y("div",N,[y("span",R,[(g(),h("svg",B,[y("use",{"xlink:href":"#icon-auth-"+ae.value},null,8,K)])),b(" "+x(le.value.name),1)])])])):m("v-if",!0),re.value?(g(),h("div",J,[m(' <component :is="getLoginType"></component> '),(g(),w(j(ke.value),{auth_id:re.value,auth_info:le.value},null,8,["auth_id","auth_info"])),m(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):m("v-if",!0),be.value.length>0?(g(),h("div",M,[k(a,null,{default:O((function(){return n[0]||(n[0]=[y("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)])})),_:1,__:[0]}),(g(),w(o,{key:te.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:O((function(){return[(g(!0),h(_,null,P(Math.ceil(be.value.length/2),(function(e){return g(),w(r,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:O((function(){return[(g(!0),h(_,null,P(be.value.slice(2*(e-1),2*(e-1)+2),(function(e){return g(),h("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:function(t){return Te(e)}},[y("div",null,[k(i,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:O((function(){return[(g(),h("svg",$,[y("use",{"xlink:href":"#icon-auth-"+e.type},null,8,H)]))]})),_:2},1024)]),y("div",Q,x(e.name),1)],8,V)})),128))]})),_:2},1024)})),128))]})),_:1}))])):m("v-if",!0)]))])]),m(" 二次认证弹窗 "),ce.value?(g(),w(I,{key:0,"auth-info":{uniqKey:se.value,contactType:ve.value,hasContactInfo:de.value},"auth-id":re.value,"user-name":fe.value,"last-id":oe.value,"auth-methods":Oe.value,onVerificationSuccess:Se,onCancel:_e},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):m("v-if",!0)])}}});e("default",r(te,[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]))}}}))}();
