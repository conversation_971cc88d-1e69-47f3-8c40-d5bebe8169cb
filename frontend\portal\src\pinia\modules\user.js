import {
  addSubgroup,
  createOrganize,
  createUser,
  deleteUser,
  delOrganize,
  getGroupMembers,
  getOrganize,
  getOrganizeCount,
  getOrganizeDetails,
  getRoles,
  getUserGroups,
  getUserInfo,
  getUserList,
  getUserListCount,
  getUserOrigin,
  login,
  setSelfInfo,
  updateOrganize,
  updateUser,
} from '@/api/user'
import { logout } from '@/api/logout'
import router from '@/router/index'
import { Loading, Message } from '@/components/base'
import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { useRouterStore } from './router'
import parse from 'url-parse'
import { auth_check, oauth2_check, handleOAuth2Callback } from '@/api/auth_wx'
import startRefreshToken from '@/utils/timer'
import stopRefreshToken from '@/utils/timer'
import { post_verify } from '@/api/sms'
import { refreshToken } from '@/api/token'

export const useUserStore = defineStore('user', () => {
  const loadingInstance = ref(null)

  const userInfo = ref({
    id: '',
    name: '',
    groupId: '',
    groupName: '',
    corpId: '',
    sourceId: '',
    phone: '',
    email: '',
    avatar: '',
    roles: [],
    sideMode: 'dark',
    activeColor: '#4D70FF',
    baseColor: '#fff',
  })

  const token = ref(window.localStorage.getItem('token') || '')
  const loginType = ref(window.localStorage.getItem('loginType') || '')
  try {
    token.value = token.value ? JSON.parse(token.value) : ''
  } catch (e) {
    console.log('---清理localStorage中的token---')
    window.localStorage.removeItem('token')
    token.value = ''
  }

  const setUserInfo = (val) => {
    userInfo.value = val
  }

  const setToken = (val) => {
    token.value = val
  }

  const setLoginType = (val) => {
    loginType.value = val
  }

  const NeedInit = () => {
    token.value = ''
    window.localStorage.removeItem('token')
    router.push({ name: 'Init', replace: true })
  }

  const ResetUserInfo = (value = {}) => {
    userInfo.value = {
      ...userInfo.value, ...value,
    }
  }
  /* 获取用户信息*/
  const GetUserInfo = async(id) => {
    const res = await getUserInfo(id)
    if (res.status === 200) {
      setUserInfo(res.data.userInfo)
    }
    return res
  }

  /* 修改用户信息 */
  const UpdateUser = async(user) => {
    const res = await updateUser(user)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 删除用户 */
  const DeleteUser = async(id) => {
    const res = await deleteUser(id)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 登录*/
  const LoginIn = async(loginInfo, auth_type, auth_id) => {
    loadingInstance.value = Loading.service({
      fullscreen: true,
      text: '登录中，请稍候...',
    })
    try {
      let res = ''
      switch (auth_type) {
        case 'qiyewx':
        case 'qiyewx_oauth':
        case 'feishu':
        case 'dingtalk':
        case 'oauth2':
        case 'cas':
        case 'msad':
        case 'ldap':
          res = await auth_check(loginInfo)
          setLoginType(auth_id)
          break
        case 'accessory':
          res = await post_verify(loginInfo)
          break
        default:
          res = await login(loginInfo)
          setLoginType(auth_id)
          break
      }
      const msg = res.data.msg
      if (res.status === 200) {
        if (res.data.code === -1 || res.data?.data?.status === 1) {
          Message({
            showClose: true,
            message: msg,
            type: 'error',
          })
          loadingInstance.value.close()
          return { code: -1 }
        } else {
          if (res.data.data) {
            if (res.data.data.secondary) {
              loadingInstance.value.close()
              return {
                isSecondary: true,
                secondary: res.data.data.secondary,
                uniqKey: res.data.data.uniqKey,
                contactType: res.data.data.contactType,
                hasContactInfo: res.data.data.hasContactInfo,
                secondaryType: res.data.secondaryType,
                userName: res.data.data.userName,
                user_id: res.data.data.userID
              }
            }
            setToken(res.data.data)
          }
          await GetUserInfo()
          startRefreshToken(LoginOut, setToken)
          const routerStore = useRouterStore()
          await routerStore.SetAsyncRouter()
          const asyncRouters = routerStore.asyncRouters
          asyncRouters.forEach(asyncRouter => {
            router.addRoute(asyncRouter)
          })
          const href = window.location.href.replace(/#/g, '&')
          const url = parse(href, true)
          let data = {}
          let clientType = null
          let clientWp = null
          try {
            const storedParams = localStorage.getItem('client_params')
            if (storedParams) {
              const params = JSON.parse(storedParams)
              clientType = params.type
              clientWp = params.wp
            }
          } catch (e) {
            console.warn('LoginIn: 获取localStorage参数失败:', e)
          }
          const queryString = window.location.search
          const urlParams = new URLSearchParams(queryString)
          const type = urlParams.get('type')
          if (url.query?.redirect || url.query?.redirect_url) {
            let params = ''
            if (url.query?.redirect) {
              params = url.query?.redirect.indexOf('?') > -1 ? url.query?.redirect.substring(url.query?.redirect.indexOf('?') + 1) : ''
            } else if (url.query?.redirect_url) {
              params = url.query?.redirect_url.indexOf('?') > -1 ? url.query?.redirect_url.substring(url.query?.redirect_url.indexOf('?') + 1) : ''
            }
            params.split('&').forEach(function(item) {
              const pair = item.split('=')
              data[pair[0]] = pair[1]
            })
            if (clientType) data.type = clientType
            if (clientWp) data.wp = clientWp
            loadingInstance.value.close()
            window.localStorage.setItem('refresh_times', 0)

            if(auth_type === 'qiyewx_oauth'){
              return true
            }else{
              window.location.href = url.query?.redirect || url.query?.redirect_url
              return true
            }
          } else {
            data = { type: clientType || url.query.type }
            if (clientWp || url.query.wp) {
              data.wp = clientWp || url.query.wp
            }
          }
          if (url.query.wp) {
            data.wp = url.query.wp
          }
          await router.push({
            name: 'dashboard',
            query: data
          })
          loadingInstance.value.close()
          return true
        }
      } else {
        Message({
          showClose: true,
          message: msg,
          type: 'error',
        })
        loadingInstance.value.close()
      }
    } catch (e) {
      Message({
        showClose: true,
        message: '服务异常，请联系管理员！',
        type: 'error',
      })
      loadingInstance.value.close()
    }
  }

// 处理OAuth2登录
const handleOAuth2Login = async (idpId, code, state) => {
  try {
    loadingInstance.value = Loading.service({
      fullscreen: true,
      text: '处理登录中...',
    })

    const response = await handleOAuth2Callback(idpId, code, state)

    if (response.status === 200 && response.data) {
      const data = response.data

      // 检查是否需要二次认证
      if (data.needSecondary) {
        loadingInstance.value.close()
        return {
          isSecondary: true,
          uniqKey: data.uniqKey
        }
      } else if (data.token) {
        // 设置token
        setToken({
          accessToken: data.token,
          refreshToken: data.refresh_token,
          expireIn: data.expires_in,
          tokenType: data.token_type || 'Bearer'
        })

        // 获取用户信息
        await GetUserInfo()

        loadingInstance.value.close()
        return true
      }
    }

    loadingInstance.value.close()
    return false
  } catch (error) {
    console.error('OAuth2登录处理失败:', error)
    loadingInstance.value.close()
    Message({
      showClose: true,
      message: error.message || '登录失败，请重试',
      type: 'error',
    })
    return false
  }
}

  /* 登出*/
  const LoginOut = async() => {
    stopRefreshToken() // 停止定时器
    const res = await logout() // 调用登出接口
    console.log('登出res', res)
    if (res.status === 200) {
      if (res.data.code === -1) {
        Message({
          showClose: true,
          message: res.data.msg,
          type: 'error',
        })
      } else {
        // 检查是否有OAuth2登出重定向URL
        if (res.data.redirectUrl) {
          console.log('检测到OAuth2登出URL，正在重定向:', res.data.redirectUrl)
          
          // 先清理本地存储
          ClearStorage()
          
          // 执行重定向到IdP登出页面
          window.location.href = res.data.redirectUrl
      }else {
          router.push({ name: 'Login', replace: true })
          ClearStorage()
          // window.location.reload()
        }
      }
    } else {
      Message({
        showClose: true,
        message: '服务异常，请联系管理员！',
        type: 'error',
      })
    }
  }

  /* 401认证失败登出*/
  const authFailureLoginOut = async() => {
    stopRefreshToken() // 停止定时器
    ClearStorage()
    router.push({ name: 'Login', replace: true })
    window.location.reload()
  }

  /* 清理数据 */
  const ClearStorage = async() => {
    sessionStorage.clear()
    window.localStorage.removeItem('userInfo')
    window.localStorage.removeItem('token')
    token.value = ''
  }
  /* 设置侧边栏模式*/
  const changeSideMode = async(data) => {
    const res = await setSelfInfo({ sideMode: data })
    if (res.code === 0) {
      userInfo.value.sideMode = data
      Message({
        type: 'success', message: '设置成功',
      })
    }
  }

  /* 获取角色列表 */
  const GetRoles = async(data) => {
    const res = await getRoles(data)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取用户组织 */
  const GetUserGroups = async(id) => {
    const res = await getUserGroups(id)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取用户角色 */
  const GetUserRole = async(id) => {
    const res = await getUserRole(id)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取组织信息 */
  const GetOrganize = async(data) => {
    const res = await getOrganize(data)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  // 获取用户来源
  const GetUserOrigin = async() => {
    const res = await getUserOrigin()
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取组织总数 */
  const GetOrganizeCount = async(data) => {
    const res = await getOrganizeCount(data)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取组织详情 */
  const GetOrganizeDetails = async(id) => {
    const res = await getOrganizeDetails(id)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取组织下用户 */
  const GetGroupMembers = async(id, data) => {
    const res = await getGroupMembers(id, data)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 新增子组 */
  const AddSubgroup = async(organize) => {
    const res = await addSubgroup(organize)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 新增组织 */
  const CreateOrganize = async(organize) => {
    const res = await createOrganize(organize)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 修改组织 */
  const UpdateOrganize = async(organize) => {
    const res = await updateOrganize(organize)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 删除组织 */
  const DelOrganize = async(organizeId) => {
    const res = await delOrganize(organizeId)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 新建用户 */
  const CreateUser = async(user) => {
    delete user.id
    const res = await createUser(user)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取用户列表 */
  const GetUserList = async(data) => {
    const res = await getUserList(data)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  /* 获取用户总数 */
  const GetUserListCount = async(data) => {
    const res = await getUserListCount(data)
    if (res.code === 0) {
      return ''
    }
    return res
  }

  // const mode = computed(() => userInfo.value.attributes.sideMode[0])
  const mode = 'dark'
  const sideMode = '#273444'
  // const sideMode = computed(() => {
  //   if (userInfo.value.attributes.sideMode[0] === 'dark') {
  //     return '#191a23'
  //   } else if (userInfo.value.attributes.sideMode[0] === 'light') {
  //     return '#fff'
  //   } else {
  //     return userInfo.value.attributes.sideMode[0]
  //   }
  // })
  const baseColor = '#fff'
  // const baseColor = computed(() => {
  //   if (userInfo.value.attributes.sideMode[0] === 'dark') {
  //     return '#fff'
  //   } else if (userInfo.value.attributes.sideMode[0] === 'light') {
  //     return '#191a23'
  //   } else {
  //     return userInfo.value.baseColor
  //   }
  // })
  const activeColor = '#4D70FF'
  // const activeColor = computed(() => {
  //   if (userInfo.value.attributes.sideMode[0] === 'dark' || userInfo.value.attributes.sideMode[0] === 'light') {
  //     return '#4D70FF'
  //   }
  //   return userInfo.attributes.activeColor[0]
  // })

  watch(() => token.value, () => {
    window.localStorage.setItem('token', JSON.stringify(token.value))
  })

  watch(() => loginType.value, () => {
    window.localStorage.setItem('loginType', loginType.value)
  })

  return {
    userInfo,
    token,
    loginType,
    NeedInit,
    ResetUserInfo,
    GetUserInfo,
    LoginIn,
    LoginOut,
    authFailureLoginOut,
    changeSideMode,
    mode,
    sideMode,
    setToken,
    baseColor,
    activeColor,
    loadingInstance,
    ClearStorage,
    GetOrganize,
    GetOrganizeDetails,
    UpdateOrganize,
    CreateOrganize,
    DelOrganize,
    AddSubgroup,
    CreateUser,
    GetUserList,
    GetUserListCount,
    UpdateUser,
    DeleteUser,
    GetRoles,
    GetGroupMembers,
    GetOrganizeCount,
    GetUserOrigin,
    GetUserGroups,
    GetUserRole,
    handleOAuth2Login,
  }
})
