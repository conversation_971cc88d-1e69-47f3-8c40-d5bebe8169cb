/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",l=o.toStringTag||"@@toStringTag";function i(e,o,a,l){var i=o&&o.prototype instanceof c?o:c,s=Object.create(i.prototype);return t(s,"_invoke",function(e,t,o){var a,l,i,c=0,s=o||[],p=!1,d={p:0,n:0,v:n,a:f,f:f.bind(n,4),d:function(e,t){return a=e,l=0,i=n,d.n=t,u}};function f(e,t){for(l=e,i=t,r=0;!p&&c&&!o&&r<s.length;r++){var o,a=s[r],f=d.p,m=a[2];e>3?(o=m===t)&&(i=a[(l=a[4])?5:(l=3,3)],a[4]=a[5]=n):a[0]<=f&&((o=e<2&&f<a[1])?(l=0,d.v=t,d.n=a[1]):f<m&&(o=e<3||a[0]>t||t>m)&&(a[4]=e,a[5]=t,d.n=m,l=0))}if(o||e>1)return u;throw p=!0,t}return function(o,s,m){if(c>1)throw TypeError("Generator is already running");for(p&&1===s&&f(s,m),l=s,i=m;(r=l<2?n:i)||!p;){a||(l?l<3?(l>1&&(d.n=-1),f(l,i)):d.n=i:d.v=i);try{if(c=2,a){if(l||(o="next"),r=a[o]){if(!(r=r.call(a,i)))throw TypeError("iterator result is not an object");if(!r.done)return r;i=r.value,l<2&&(l=0)}else 1===l&&(r=a.return)&&r.call(a),l<2&&(i=TypeError("The iterator does not provide a '"+o+"' method"),l=1);a=n}else if((r=(p=d.n<0)?i:e.call(t,d))!==u)break}catch(r){a=n,l=1,i=r}finally{c=1}}return{value:r,done:p}}}(e,a,l),!0),s}var u={};function c(){}function s(){}function p(){}r=Object.getPrototypeOf;var d=[][a]?r(r([][a]())):(t(r={},a,(function(){return this})),r),f=p.prototype=c.prototype=Object.create(d);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,t(e,l,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=p,t(f,"constructor",p),t(p,"constructor",s),s.displayName="GeneratorFunction",t(p,l,"GeneratorFunction"),t(f),t(f,l,"Generator"),t(f,a,(function(){return this})),t(f,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:i,m:m}})()}function t(e,n,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,r,o){if(n)a?a(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var l=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};l("next",0),l("throw",1),l("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,a,l){try{var i=e[a](l),u=i.value}catch(e){return void n(e)}i.done?t(u):Promise.resolve(u).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var l=e.apply(t,r);function i(e){n(l,o,a,i,u,"next",e)}function u(e){n(l,o,a,i,u,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.dbc04544.js","./customTable-legacy.8e1d28b3.js","./customFrom-legacy.fd23a917.js","./agents-legacy.d4bdf688.js"],(function(t,n){"use strict";var o,a,l,i,u,c,s,p,d,f,m,v,y,_,g,w,b,h,x,k,V,C,F,j=document.createElement("style");return j.textContent='@charset "UTF-8";.titleClass[data-v-763e9f26]{font-size:18px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#252631;margin-left:15px}.el-drawer__header{margin-bottom:5px!important;border-bottom:1px solid #EBEBEB;padding-top:10px!important;padding-bottom:10px!important}.custom-popover{min-width:130px;width:130px!important;box-shadow:1px 4px 15px rgba(0,0,0,.11)!important;padding:0!important}\n',document.head.appendChild(j),{setters:[function(e){o=e.r,a=e._,l=e.B,i=e.p,u=e.h,c=e.o,s=e.d,p=e.e,d=e.j,f=e.w,m=e.k,v=e.t,y=e.g,_=e.f,g=e.O,w=e.m,b=e.M},function(e){h=e._},function(e){x=e.u,k=e._},function(e){V=e.g,C=e.u,F=e.c}],execute:function(){var n=function(e){return[{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"desc",label:"描述：",type:"input",placeholder:"组件描述"},{field:"type",label:"组件类型：",type:"select",placeholder:"请选择",options:o([{value:3,label:"gateway"},{value:2,label:"connector"}]),optionsLabe:"label",optionsValue:"value",optionsKey:"value",rules:[{required:!0,message:"组件类型不能为空",trigger:"blur"}]},{field:"platform",label:"设备类型：",type:"select",placeholder:"请选择",options:o([{value:"1",label:"windows"},{value:"2",label:"linux"}]),optionsLabe:"label",optionsValue:"value",optionsKey:"value",rules:[{required:!0,message:"设备类型不能为空",trigger:"blur"}]},{field:"installCommand",label:"安装命令：",type:"input",disabled:!0,isHidden:e,placeholder:"保存后生成安装命令"}]},j={class:"agents"},O={style:{"background-color":"#FFFFFF",padding:"22px 20px 60px 20px"}},E={class:"header"},P={style:{float:"right"}},S={style:{"padding-left":"17px"}},T={style:{"text-align":"center"}},N={key:0,style:{color:"#252631"}},z={key:1,style:{color:"#252631"}},B={style:{"text-align":"center"}},U={key:0,style:{color:"#252631"}},G={key:1,style:{color:"#252631"}},I={style:{"text-align":"center"}},L={key:0},R={style:{"text-align":"center"}},q={style:{width:"15px","max-width":"20px",float:"left"}},A=["id"],H=Object.assign({name:"Agents"},{setup:function(t){var a=x().toClipboard,H=o(),D=o(),K=o(""),M=o([]),J=o(1),Q=o(50),W=o(0),X=o(!1),Y={formItems:[],formValues:l({})},Z=o(!1),$=o(!1),ee=o(!1),te=o(!1),ne=o(!1),re={propList:[{prop:"app_name",label:"名称",slotName:"app_name"},{prop:"platform",label:"系统类型",slotName:"platform",isHidden:$},{prop:"display_app_type",label:"组件类型",slotName:"display_app_type"},{prop:"app_version",label:"版本",slotName:"app_version",isHidden:ee},{prop:"desc",label:"描述",slotName:"desc"},{prop:"app_status",label:"状态",slotName:"app_status"},{prop:"operate",label:"操作",slotName:"operate"}],isIndexColumn:!0,isOperationColumn:!1},oe=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:return n={limit:Q.value,offset:(J.value-1)*Q.value},e.n=1,V(n);case 1:r=e.v,console.log("getAgentsList"),console.log(r),0===r.data.code&&(M.value=r.data.data.rows,W.value=r.data.data.total_rows);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),ae=function(){var t=r(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,oe();case 1:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();ae();var le=o("add"),ie=function(){var e=Object.keys(Y.formValues),t={};e.forEach((function(e){t[e]=""})),Object.assign(Y.formValues,t)},ue=function(){ie(),X.value=!1},ce=function(){var t=r(e().m((function t(n){return e().w((function(t){for(;;)switch(t.n){case 0:return console.log("agents"),console.log(Y.formValues),console.log(H.value),console.log(n),t.n=1,H.value.ruleFormRef.validate(function(){var t=r(e().m((function t(n,r){var o,a;return e().w((function(e){for(;;)switch(e.n){case 0:if(n){e.n=1;break}return b({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),e.a(2,"");case 1:if(console.log(Y.formValues),o={name:Y.formValues.name,desc:Y.formValues.desc,type:Number(Y.formValues.type),platform:1===Y.formValues.platform?"windows":"linux"},a="","edit"!==le.value){e.n=3;break}return e.n=2,C(o);case 2:a=e.v,e.n=5;break;case 3:return e.n=4,F(o);case 4:a=e.v;case 5:if(200!==a.status||0!==a.data.code){e.n=7;break}return b.success({message:"生成命令成功"}),ie(),e.n=6,oe();case 6:return X.value=!1,e.a(2);case 7:b.error({message:"生成命令失败，请重试"});case 8:return e.a(2)}}),t)})));return function(e,n){return t.apply(this,arguments)}}());case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),se=function(){var t=r(e().m((function t(n){return e().w((function(e){for(;;)switch(e.n){case 0:if(console.log(n),n){e.n=1;break}return b.error({message:"命令未生成，请保存后再复制"}),e.a(2);case 1:return e.p=1,e.n=2,a(n.toString());case 2:b.success({message:"复制成功"}),e.n=4;break;case 3:e.p=3,e.v,b.error({message:"复制失败请重试"});case 4:return e.a(2)}}),t,null,[[1,3]])})));return function(e){return t.apply(this,arguments)}}();return i("currentPage",J),i("pageSize",Q),i("total",W),i("getTableData",oe),function(e,t){var r=u("base-button"),o=u("base-checkbox"),a=u("el-popover"),l=u("SuccessFilled"),i=u("el-icon"),b=u("CircleCloseFilled"),x=u("el-link"),V=u("base-divider"),C=u("Close"),F=u("el-drawer");return c(),s("div",j,[p("div",O,[p("div",E,[d(r,{style:{width:"100px"},icon:e.Plus,onClick:t[0]||(t[0]=function(e){return le.value="add",K.value="添加组件",Y.formItems=n(!0),void(X.value=!0)})},{default:f((function(){return t[7]||(t[7]=[m("添加组件")])})),_:1,__:[7]},8,["icon"]),d(r,{style:{width:"77px"},icon:e.RefreshRight,onClick:oe},{default:f((function(){return t[8]||(t[8]=[m("刷新")])})),_:1,__:[8]},8,["icon"]),p("div",P,[d(a,{"popper-class":"custom-popover","show-arrow":!1,width:20,trigger:"click"},{reference:f((function(){return[d(r,{icon:e.Filter},{default:f((function(){return t[9]||(t[9]=[m("筛选")])})),_:1,__:[9]},8,["icon"])]})),default:f((function(){return[t[15]||(t[15]=p("div",{style:{color:"#252631","font-weight":"400","border-bottom":"1px #EBEBEB solid","text-align":"center",height:"38px","line-height":"38px"}}," 选择显示项 ",-1)),p("div",S,[d(o,{style:{"margin-top":"10px",width:"100%"},modelValue:Z.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return Z.value=e})},{default:f((function(){return t[10]||(t[10]=[m("MAC")])})),_:1,__:[10]},8,["modelValue"]),d(o,{style:{"margin-top":"10px",width:"100%"},modelValue:$.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return $.value=e})},{default:f((function(){return t[11]||(t[11]=[m("系统类型")])})),_:1,__:[11]},8,["modelValue"]),d(o,{style:{"margin-top":"10px",width:"100%"},modelValue:ee.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return ee.value=e})},{default:f((function(){return t[12]||(t[12]=[m("版本")])})),_:1,__:[12]},8,["modelValue"]),d(o,{style:{"margin-top":"10px",width:"100%"},modelValue:te.value,"onUpdate:modelValue":t[4]||(t[4]=function(e){return te.value=e})},{default:f((function(){return t[13]||(t[13]=[m("CPU")])})),_:1,__:[13]},8,["modelValue"]),d(o,{style:{"margin-top":"10px","margin-bottom":"10px",width:"100%"},modelValue:ne.value,"onUpdate:modelValue":t[5]||(t[5]=function(e){return ne.value=e})},{default:f((function(){return t[14]||(t[14]=[m("内存 ")])})),_:1,__:[14]},8,["modelValue"])])]})),_:1,__:[15]})])]),d(h,g({"table-data":M.value},re,{ref_key:"customTable",ref:D}),{app_name:f((function(e){return[p("div",T,[e.row.app_name?(c(),s("span",N,v(e.row.app_name),1)):(c(),s("span",z,v(e.row.name),1))])]})),display_app_type:f((function(e){return[p("div",B,[3===e.row.display_app_type?(c(),s("span",U,"gateway")):y("",!0),2===e.row.display_app_type?(c(),s("span",G,"Connector")):y("",!0)])]})),app_status:f((function(e){return[p("div",I,[e.row.installed?e.row.online?(c(),_(i,{key:1,size:16,style:{color:"#52c41a"}},{default:f((function(){return[d(l)]})),_:1})):(c(),_(i,{key:2,size:16},{default:f((function(){return[d(b,{color:"#D23030"})]})),_:1})):(c(),s("span",L,"---"))])]})),operate:f((function(e){return[p("div",R,[d(x,{underline:!1,style:{"font-size":"14px",color:"#2972C8"},onClick:function(t){return r=e.row,console.log("handleEdit"),console.log(r),le.value="edit",K.value="编辑组件",Y.formItems=n(!1),Y.formValues.id=r.id,Y.formValues.name=r.app_name||r.name,Y.formValues.desc=r.desc,Y.formValues.type=r.display_app_type,Y.formValues.platform="windows"===r.app_plat?"1":"2",Y.formValues.installCommand="windows"===r.platform?r.command_windows:r.command_linux,void(X.value=!0);var r}},{default:f((function(){return t[16]||(t[16]=[m(" 编辑 ")])})),_:2,__:[16]},1032,["onClick"]),d(V,{direction:"vertical"}),d(x,{underline:!1,style:{"font-size":"14px",color:"#2972C8"},onClick:function(t){return delete e.row.id}},{default:f((function(){return t[17]||(t[17]=[m("删除 ")])})),_:2,__:[17]},1032,["onClick"]),e.row.command_windows||e.row.command_linux?(c(),_(V,{key:0,direction:"vertical"})):y("",!0),e.row.command_windows||e.row.command_linux?(c(),_(x,{key:1,underline:!1,style:{"font-size":"14px",color:"#2972C8"},onClick:function(t){return se("windows"===e.row.platform?e.row.command_windows:e.row.command_linux)}},{default:f((function(){return t[18]||(t[18]=[m("复制安装命令 ")])})),_:2,__:[18]},1032,["onClick"])):y("",!0)])]})),_:1},16,["table-data"]),d(F,{modelValue:X.value,"onUpdate:modelValue":t[6]||(t[6]=function(e){return X.value=e}),title:K.value,direction:"rtl","show-close":!1,size:"40%"},{header:f((function(e){var t=e.close,n=e.titleId;return[p("div",q,[d(r,{link:"",onClick:t},{default:f((function(){return[d(i,null,{default:f((function(){return[d(C)]})),_:1})]})),_:2},1032,["onClick"])]),p("span",{id:n,class:"titleClass"},v(K.value),9,A)]})),footer:f((function(){return[d(r,{color:"#256EBF",type:"primary",onClick:ce},{default:f((function(){return t[19]||(t[19]=[m("确定 ")])})),_:1,__:[19]}),d(r,{style:{"margin-left":"10px","margin-right":"90px"},onClick:ue},{default:f((function(){return t[20]||(t[20]=[m("取消")])})),_:1,__:[20]})]})),default:f((function(){return[d(k,g({ref_key:"customForm",ref:H},w(Y),{formOptions:w(Y).formValues,cancel:ue,submitForm:ce,isFooter:!1}),null,16,["formOptions"])]})),_:1},8,["modelValue","title"])])])}}});t("default",a(H,[["__scopeId","data-v-763e9f26"]]))}}}))}();
