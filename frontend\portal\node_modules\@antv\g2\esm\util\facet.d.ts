import { DIRECTION } from '../constant';
import { Point } from '../dependents';
import { FacetTitle } from '../interface';
/**
 * @ignore
 * 获取 facet title 的最佳默认配置，防止
 */
export declare function getFactTitleConfig(direction: DIRECTION): FacetTitle;
/**
 * @ignore
 * 根据角度，获取 ○ 上的点
 * @param center
 * @param r
 * @param angle
 */
export declare function getAnglePoint(center: Point, r: number, angle: number): Point;
