/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",o=a.toStringTag||"@@toStringTag";function i(t,a,u,o){var i=a&&a.prototype instanceof c?a:c,d=Object.create(i.prototype);return r(d,"_invoke",function(t,r,a){var u,o,i,c=0,d=a||[],f=!1,s={p:0,n:0,v:e,a:y,f:y.bind(e,4),d:function(t,r){return u=t,o=0,i=e,s.n=r,l}};function y(t,r){for(o=t,i=r,n=0;!f&&c&&!a&&n<d.length;n++){var a,u=d[n],y=s.p,v=u[2];t>3?(a=v===r)&&(i=u[(o=u[4])?5:(o=3,3)],u[4]=u[5]=e):u[0]<=y&&((a=t<2&&y<u[1])?(o=0,s.v=r,s.n=u[1]):y<v&&(a=t<3||u[0]>r||r>v)&&(u[4]=t,u[5]=r,s.n=v,o=0))}if(a||t>1)return l;throw f=!0,r}return function(a,d,v){if(c>1)throw TypeError("Generator is already running");for(f&&1===d&&y(d,v),o=d,i=v;(n=o<2?e:i)||!f;){u||(o?o<3?(o>1&&(s.n=-1),y(o,i)):s.n=i:s.v=i);try{if(c=2,u){if(o||(a="next"),n=u[a]){if(!(n=n.call(u,i)))throw TypeError("iterator result is not an object");if(!n.done)return n;i=n.value,o<2&&(o=0)}else 1===o&&(n=u.return)&&n.call(u),o<2&&(i=TypeError("The iterator does not provide a '"+a+"' method"),o=1);u=e}else if((n=(f=s.n<0)?i:t.call(r,s))!==l)break}catch(n){u=e,o=1,i=n}finally{c=1}}return{value:n,done:f}}}(t,u,o),!0),d}var l={};function c(){}function d(){}function f(){}n=Object.getPrototypeOf;var s=[][u]?n(n([][u]())):(r(n={},u,(function(){return this})),n),y=f.prototype=c.prototype=Object.create(s);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,r(e,o,"GeneratorFunction")),e.prototype=Object.create(y),e}return d.prototype=f,r(y,"constructor",f),r(f,"constructor",d),d.displayName="GeneratorFunction",r(f,o,"GeneratorFunction"),r(y),r(y,o,"Generator"),r(y,u,(function(){return this})),r(y,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:i,m:v}})()}function r(e,t,n,a){var u=Object.defineProperty;try{u({},"",{})}catch(e){u=0}r=function(e,t,n,a){if(t)u?u(e,t,{value:n,enumerable:!a,configurable:!a,writable:!a}):e[t]=n;else{var o=function(t,n){r(e,t,(function(e){return this._invoke(t,n,e)}))};o("next",0),o("throw",1),o("return",2)}},r(e,t,n,a)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(t,r,n){return(r=function(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,r||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==e(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}function o(e,t,r,n,a,u,o){try{var i=e[u](o),l=i.value}catch(e){return void r(e)}i.done?t(l):Promise.resolve(l).then(n,a)}function i(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var u=e.apply(t,r);function i(e){o(u,n,a,i,l,"next",e)}function l(e){o(u,n,a,i,l,"throw",e)}i(void 0)}))}}System.register(["./authority-legacy.db22ecf3.js","./menus-legacy.9d7a0e36.js","./apis-legacy.ae62781d.js","./datas-legacy.2d956cc9.js","./warningBar-legacy.4145d360.js","./index-legacy.dbc04544.js","./authorityBtn-legacy.a84008d4.js","./api-legacy.16c002e6.js"],(function(e,r){"use strict";var n,u,o,l,c,d,f,s,y,v,p,h,m,b,g,w,I,_,j,k,O,C=document.createElement("style");return C.textContent='@charset "UTF-8";.authority .el-input-number{margin-left:15px}.authority .el-input-number span{display:none}.tree-content{overflow:auto;height:calc(100vh - 100px);margin-top:10px}.auth-drawer .el-drawer__body{overflow:hidden}\n',document.head.appendChild(C),{setters:[function(e){n=e.g,u=e.d,o=e.c,l=e.u,c=e.a},function(e){d=e.default},function(e){f=e.default},function(e){s=e.default},function(e){y=e.W},function(e){v=e.r,p=e.h,h=e.o,m=e.d,b=e.j,g=e.e,w=e.w,I=e.k,_=e.f,j=e.g,k=e.P,O=e.M},function(){},function(){}],execute:function(){var r={class:"authority"},C={class:"gva-table-box"},N={class:"gva-btn-list"},P={class:"dialog-footer"};e("default",Object.assign({name:"Authority"},{setup:function(e){var S=v([{authorityId:0,authorityName:"根角色"}]),V=v(!1),x=v("add"),z=v({}),T=v("新增角色"),E=v(!1),D=v(!1),A=v({}),F=v({authorityId:0,authorityName:"",parentId:0}),G=v({authorityId:[{required:!0,message:"请输入角色ID",trigger:"blur"},{validator:function(e,t,r){return/^[0-9]*[1-9][0-9]*$/.test(t)?r():r(new Error("请输入正整数"))},trigger:"blur",message:"必须为正整数"}],authorityName:[{required:!0,message:"请输入角色名",trigger:"blur"}],parentId:[{required:!0,message:"请选择父角色",trigger:"blur"}]}),U=v(1),B=v(0),q=v(999),R=v([]),M=v({}),W=function(){var e=i(t().m((function e(){var r;return t().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,n(a({page:U.value,pageSize:q.value},M.value));case 1:0===(r=e.v).code&&(R.value=r.data.list,B.value=r.data.total,U.value=r.data.page,q.value=r.data.pageSize);case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();W();var $=function(e,t){z.value[e]=t},H=v(null),J=v(null),K=v(null),L=function(e,t){var r=[H,J,K];t&&r[t].value.needConfirm&&(r[t].value.enterAndNext(),r[t].value.needConfirm=!1)},Q=v(null),X=function(){Q.value&&Q.value.resetFields(),F.value={authorityId:0,authorityName:"",parentId:0}},Y=function(){X(),E.value=!1,D.value=!1},Z=function(){if(F.value.authorityId=Number(F.value.authorityId),0===F.value.authorityId)return O({type:"error",message:"角色id不能为0"}),!1;Q.value.validate(function(){var e=i(t().m((function e(r){var n,a;return t().w((function(e){for(;;)switch(e.n){case 0:if(!r){e.n=8;break}a=x.value,e.n="add"===a?1:"edit"===a?3:"copy"===a?5:7;break;case 1:return e.n=2,c(F.value);case 2:return 0===e.v.code&&(O({type:"success",message:"添加成功!"}),W(),Y()),e.a(3,7);case 3:return e.n=4,l(F.value);case 4:return 0===e.v.code&&(O({type:"success",message:"添加成功!"}),W(),Y()),e.a(3,7);case 5:return(n={authority:{authorityId:0,authorityName:"",datauthorityId:[],parentId:0},oldAuthorityId:0}).authority.authorityId=F.value.authorityId,n.authority.authorityName=F.value.authorityName,n.authority.parentId=F.value.parentId,n.authority.dataAuthorityId=A.value.dataAuthorityId,n.oldAuthorityId=A.value.authorityId,e.n=6,o(n);case 6:0===e.v.code&&(O({type:"success",message:"复制成功！"}),W());case 7:X(),E.value=!1;case 8:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},ee=function(){S.value=[{authorityId:0,authorityName:"根角色"}],te(R.value,S.value,!1)},te=function(e,t,r){F.value.authorityId=String(F.value.authorityId),e&&e.forEach((function(e){if(e.children&&e.children.length){var n={authorityId:e.authorityId,authorityName:e.authorityName,disabled:r||e.authorityId===F.value.authorityId,children:[]};te(e.children,n.children,r||e.authorityId===F.value.authorityId),t.push(n)}else{var a={authorityId:e.authorityId,authorityName:e.authorityName,disabled:r||e.authorityId===F.value.authorityId};t.push(a)}}))},re=function(e){X(),T.value="新增角色",x.value="add",F.value.parentId=e,ee(),E.value=!0};return function(e,n){var a=p("base-button"),o=p("el-table-column"),l=p("el-table"),c=p("el-cascader"),v=p("base-form-item"),D=p("base-input"),B=p("base-form"),q=p("el-dialog"),M=p("el-tab-pane"),X=p("el-tabs"),te=p("el-drawer");return h(),m("div",r,[b(y,{title:"注：右上角头像下拉可切换角色"}),g("div",C,[g("div",N,[b(a,{size:"small",type:"primary",icon:"plus",onClick:n[0]||(n[0]=function(e){return re(0)})},{default:w((function(){return n[6]||(n[6]=[I("新增角色")])})),_:1,__:[6]})]),b(l,{data:R.value,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"authorityId",style:{width:"100%"}},{default:w((function(){return[b(o,{label:"角色ID","min-width":"180",prop:"authorityId"}),b(o,{align:"left",label:"角色名称","min-width":"180",prop:"authorityName"}),b(o,{align:"left",label:"操作",width:"460"},{default:w((function(e){return[b(a,{icon:"setting",size:"small",type:"primary",link:"",onClick:function(t){return r=e.row,V.value=!0,void(z.value=r);var r}},{default:w((function(){return n[7]||(n[7]=[I("设置权限")])})),_:2,__:[7]},1032,["onClick"]),b(a,{icon:"plus",size:"small",type:"primary",link:"",onClick:function(t){return re(e.row.authorityId)}},{default:w((function(){return n[8]||(n[8]=[I("新增子角色")])})),_:2,__:[8]},1032,["onClick"]),b(a,{icon:"copy-document",size:"small",type:"primary",link:"",onClick:function(t){return function(e){for(var t in ee(),T.value="拷贝角色",x.value="copy",F.value)F.value[t]=e[t];A.value=e,E.value=!0}(e.row)}},{default:w((function(){return n[9]||(n[9]=[I("拷贝")])})),_:2,__:[9]},1032,["onClick"]),b(a,{icon:"edit",size:"small",type:"primary",link:"",onClick:function(t){return function(e){for(var t in ee(),T.value="编辑角色",x.value="edit",F.value)F.value[t]=e[t];ee(),E.value=!0}(e.row)}},{default:w((function(){return n[10]||(n[10]=[I("编辑")])})),_:2,__:[10]},1032,["onClick"]),b(a,{icon:"delete",size:"small",type:"primary",link:"",onClick:function(r){return n=e.row,void k.confirm("此操作将永久删除该角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(i(t().m((function e(){return t().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,u({authorityId:n.authorityId});case 1:0===e.v.code&&(O({type:"success",message:"删除成功!"}),1===R.value.length&&U.value>1&&U.value--,W());case 2:return e.a(2)}}),e)})))).catch((function(){O({type:"info",message:"已取消删除"})}));var n}},{default:w((function(){return n[11]||(n[11]=[I("删除")])})),_:2,__:[11]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])]),b(q,{modelValue:E.value,"onUpdate:modelValue":n[4]||(n[4]=function(e){return E.value=e}),title:T.value},{footer:w((function(){return[g("div",P,[b(a,{size:"small",onClick:Y},{default:w((function(){return n[12]||(n[12]=[I("取 消")])})),_:1,__:[12]}),b(a,{size:"small",type:"primary",onClick:Z},{default:w((function(){return n[13]||(n[13]=[I("确 定")])})),_:1,__:[13]})])]})),default:w((function(){return[b(B,{ref_key:"authorityForm",ref:Q,model:F.value,rules:G.value,"label-width":"80px"},{default:w((function(){return[b(v,{label:"父级角色",prop:"parentId"},{default:w((function(){return[b(c,{modelValue:F.value.parentId,"onUpdate:modelValue":n[1]||(n[1]=function(e){return F.value.parentId=e}),style:{width:"100%"},disabled:"add"==x.value,options:S.value,props:{checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])]})),_:1}),b(v,{label:"角色ID",prop:"authorityId"},{default:w((function(){return[b(D,{modelValue:F.value.authorityId,"onUpdate:modelValue":n[2]||(n[2]=function(e){return F.value.authorityId=e}),disabled:"edit"==x.value,autocomplete:"off"},null,8,["modelValue","disabled"])]})),_:1}),b(v,{label:"角色姓名",prop:"authorityName"},{default:w((function(){return[b(D,{modelValue:F.value.authorityName,"onUpdate:modelValue":n[3]||(n[3]=function(e){return F.value.authorityName=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue","title"]),V.value?(h(),_(te,{key:0,modelValue:V.value,"onUpdate:modelValue":n[5]||(n[5]=function(e){return V.value=e}),"custom-class":"auth-drawer","with-header":!1,size:"40%",title:"角色配置"},{default:w((function(){return[b(X,{"before-leave":L,type:"border-card"},{default:w((function(){return[b(M,{label:"角色菜单"},{default:w((function(){return[b(d,{ref_key:"menus",ref:H,row:z.value,onChangeRow:$},null,8,["row"])]})),_:1}),b(M,{label:"角色api"},{default:w((function(){return[b(f,{ref_key:"apis",ref:J,row:z.value,onChangeRow:$},null,8,["row"])]})),_:1}),b(M,{label:"资源权限"},{default:w((function(){return[b(s,{ref_key:"datas",ref:K,authority:R.value,row:z.value,onChangeRow:$},null,8,["authority","row"])]})),_:1})]})),_:1})]})),_:1},8,["modelValue"])):j("",!0)])}}}))}}}))}();
