/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{u as e,a,b as t,a0 as o,r as l,y as s,G as n,N as r,h as u,o as c,d as i,j as d,w as f,T as v,F as m,i as p,m as h,f as g,g as b,J as k}from"./index.74d1ee23.js";import x from"./index.2ee16a6f.js";import"./menuItem.1131fe90.js";import"./asyncSubmenu.a28c0585.js";const y=Object.assign({name:"Aside"},{setup(y){const T=e(),j=a(),F=t(),M=o(),w=l({}),B=()=>{switch(F.sideMode){case"#fff":w.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":w.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};B();const q=l("");s((()=>T),(()=>{q.value=T.meta.activeName||T.name}),{deep:!0}),s((()=>F.sideMode),(()=>{B()}));const N=l(!1);(()=>{q.value=T.meta.activeName||T.name;document.body.clientWidth<1e3&&(N.value=!N.value),r.on("collapse",(e=>{N.value=e}))})(),n((()=>{r.off("collapse")}));const O=(e,a,t,o)=>{var l,s;const n={},r={};(null==(l=M.routeMap[e])?void 0:l.parameters)&&(null==(s=M.routeMap[e])||s.parameters.forEach((e=>{"query"===e.type?n[e.key]=e.value:r[e.key]=e.value}))),e!==T.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):j.push({name:e,query:n,params:r}))};return(e,a)=>{const t=u("el-menu"),o=u("el-scrollbar");return c(),i("div",{style:k({background:h(F).sideMode})},[d(o,{style:{height:"calc(100vh - 110px)"}},{default:f((()=>[d(v,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[d(t,{collapse:N.value,"collapse-transition":!1,"default-active":q.value,"background-color":w.value.background,"active-text-color":w.value.active,class:"el-menu-vertical","unique-opened":"",onSelect:O},{default:f((()=>[(c(!0),i(m,null,p(h(M).asyncRouters[0].children,(e=>(c(),i(m,null,[e.hidden?b("",!0):(c(),g(x,{key:e.name,"is-collapse":N.value,"router-info":e,theme:w.value},null,8,["is-collapse","router-info","theme"]))],64)))),256))])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})],4)}}});export{y as default};
