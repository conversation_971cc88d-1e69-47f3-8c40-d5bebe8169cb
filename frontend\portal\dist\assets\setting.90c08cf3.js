/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{_ as e,r as a,H as s,a as l,b as t,E as i,i as n,w as o,e as c,t as u,M as v,j as d,o as r,k as p}from"./index.bfaf04e1.js";const b={class:"setting-page"},m={class:"main-content"},g={class:"setting-container"},_={class:"tabs-header"},V={class:"tabs-content"},f={key:0,class:"tab-panel"},y={class:"setting-section"},k={class:"setting-item setting-platformAddress"},h={class:"setting-item"},x={class:"checkbox-group"},U={key:1,class:"tab-panel"},S={class:"setting-section setting-update"},C={class:"setting-item"},I={class:"checkbox-group"},j={class:"setting-item"},w={class:"about-section"},A={class:"version-info"},q={class:"version-item"},z={class:"version-value-group"},E={class:"version-value"},F={class:"version-item"},H={class:"version-value"},J={class:"version-item"},M={class:"version-value"},N=e({__name:"setting",setup(e){const N=a("general"),O=a(""),T=a(!1),B=a(!0),D=a(!0),G=a("daily"),K=a("2.5.0"),L=a("2025.03.21 09:00"),P=a("2025.03.21 09:00");s((()=>{R()}));const Q=async()=>{v.info("正在检查更新..."),setTimeout((()=>{v.success("当前已是最新版本")}),1500)},R=()=>{const e=localStorage.getItem("appSettings");if(e){const a=JSON.parse(e);O.value=a.platformAddress||"",T.value=a.autoStart||!1,B.value=void 0===a.autoConnect||a.autoConnect,D.value=void 0===a.autoUpdate||a.autoUpdate,G.value=a.updateFrequency||"daily"}};return(e,a)=>{const s=d("base-input"),v=d("base-checkbox"),R=d("base-option"),W=d("base-select"),X=d("base-button");return r(),l("div",b,[t("div",m,[t("div",g,[t("div",_,[t("div",{class:i(["tab-item",{active:"general"===N.value}]),onClick:a[0]||(a[0]=e=>N.value="general")}," 通用设置 ",2),t("div",{class:i(["tab-item",{active:"version"===N.value}]),onClick:a[1]||(a[1]=e=>N.value="version")}," 版本信息 ",2)]),t("div",V,["general"===N.value?(r(),l("div",f,[t("div",y,[t("div",k,[a[7]||(a[7]=t("label",{class:"setting-label"},"平台地址",-1)),n(s,{modelValue:O.value,"onUpdate:modelValue":a[2]||(a[2]=e=>O.value=e),placeholder:"输入您连接的平台服务器地址",class:"setting-input",clearable:""},null,8,["modelValue"])]),t("div",h,[a[10]||(a[10]=t("label",{class:"setting-label"},"启动选项",-1)),t("div",x,[n(v,{modelValue:T.value,"onUpdate:modelValue":a[3]||(a[3]=e=>T.value=e),class:"setting-checkbox"},{default:o((()=>a[8]||(a[8]=[p(" 开机自启动 ")]))),_:1,__:[8]},8,["modelValue"]),n(v,{modelValue:B.value,"onUpdate:modelValue":a[4]||(a[4]=e=>B.value=e),class:"setting-checkbox"},{default:o((()=>a[9]||(a[9]=[p(" 启动后自动连接 ")]))),_:1,__:[9]},8,["modelValue"])])])])])):c("",!0),"version"===N.value?(r(),l("div",U,[t("div",S,[t("div",C,[a[12]||(a[12]=t("label",{class:"setting-label"},"更新选项",-1)),t("div",I,[n(v,{modelValue:D.value,"onUpdate:modelValue":a[5]||(a[5]=e=>D.value=e),class:"setting-checkbox"},{default:o((()=>a[11]||(a[11]=[p(" 自动检查更新 ")]))),_:1,__:[11]},8,["modelValue"])])]),t("div",j,[a[13]||(a[13]=t("label",{class:"setting-label"},"更新检查频率",-1)),n(W,{modelValue:G.value,"onUpdate:modelValue":a[6]||(a[6]=e=>G.value=e),class:"setting-select",placeholder:"请选择"},{default:o((()=>[n(R,{label:"每天",value:"daily"}),n(R,{label:"每周",value:"weekly"}),n(R,{label:"每月",value:"monthly"})])),_:1},8,["modelValue"])])]),t("div",w,[a[18]||(a[18]=t("h3",{class:"about-title"},"关于安全客户端",-1)),t("div",A,[t("div",q,[a[15]||(a[15]=t("span",{class:"version-label"},"当前版本",-1)),t("div",z,[t("span",E,u(K.value),1),n(X,{text:"",type:"primary",size:"small",onClick:Q},{default:o((()=>a[14]||(a[14]=[p(" 检查更新 ")]))),_:1,__:[14]})])]),t("div",F,[a[16]||(a[16]=t("span",{class:"version-label"},"构建时间",-1)),t("span",H,u(L.value),1)]),t("div",J,[a[17]||(a[17]=t("span",{class:"version-label"},"上次更新时间",-1)),t("span",M,u(P.value),1)])]),a[19]||(a[19]=t("div",{class:"copyright"},[t("p",null,"© 2025 Security Systems Inc. 保留所有权利")],-1))])])):c("",!0)])])])])}}},[["__scopeId","data-v-c55304de"]]);export{N as default};
