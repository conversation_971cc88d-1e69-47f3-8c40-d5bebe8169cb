/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import"./iconfont.2d75af05.js";import{_ as e,o as t,a as n,b as u,F as a,h as c,E as o,t as i}from"./index.bfaf04e1.js";const r=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],s={class:"layout-aside"},m={class:"menu-wrapper"},d=["onClick"],l={class:"icon menu-item-icon","aria-hidden":"true"},h=["xlink:href"],p={class:"menu-item-title"};const g=e({name:"ClientMenu",data:()=>({currentRouteCode:"101"}),computed:{computedMenu(){return this.computedMenuFun()}},mounted(){this.$router.push({path:"/client/main",query:[]})},watch:{$route:{handler(e,t){if(logger.log("路由变化",e,t),e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun(){const e=[];return r&&r.forEach((t=>{if(t.meta&&t.meta.menu){const{name:n,icon:u,uiId:a}=t.meta.menu,c={name:n,icon:u,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:a};e.push(c)}})),e},changeMenu(e,t={},n=0){const u={...t,menuClick:!0};logger.log(e,u),this.$router.push({path:e,query:u}),this.currentRouteCode=this.cutOut(n)},routerInterceptor(e){const t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:e=>e&&e.length?e.substr(0,3):e}},[["render",function(e,r,g,f,C,M){return t(),n("div",s,[u("ul",m,[(t(!0),n(a,null,c(M.computedMenu,(e=>(t(),n("li",{key:e.code,class:o(["menu-item",M.cutOut(e.code)===C.currentRouteCode?"active-menu-item":""]),onClick:t=>M.changeMenu(e.url,e.params,e.code)},[(t(),n("svg",l,[u("use",{"xlink:href":"#"+e.icon+(M.cutOut(e.code)===C.currentRouteCode?"-active":"")},null,8,h)])),u("div",p,i(e.name),1)],10,d)))),128))])])}],["__scopeId","data-v-4220c5e1"]]);export{g as default};
