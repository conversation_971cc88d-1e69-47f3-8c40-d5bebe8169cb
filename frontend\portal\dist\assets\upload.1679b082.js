/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{_ as a,U as e,g as t,d as l,e as n}from"./common.a1b58fdb.js";import{C as o}from"./index.2ff0c44c.js";import{f as s}from"./format.5148d109.js";import{W as i}from"./warningBar.0cee9251.js";import{_ as r,r as u,j as d,V as c,W as p,o as m,a as g,b as f,i as v,w as h,k as w,t as y,l as b,P as _,M as k}from"./index.bfaf04e1.js";import"./noBody.e1ae38ec.js";import"./index-browser-esm.c2d3b5c9.js";import"./date.23f5a973.js";import"./dictionary.20c9ce22.js";import"./sysDictionary.cef56f98.js";const x=(a,e)=>{var t=new Image;t.setAttribute("crossOrigin","anonymous"),t.onload=function(){var a=document.createElement("canvas");a.width=t.width,a.height=t.height,a.getContext("2d").drawImage(t,0,0,t.width,t.height);var l=a.toDataURL("image/png"),n=document.createElement("a"),o=new MouseEvent("click");n.download=e||"photo",n.href=l,n.dispatchEvent(o)},t.src=a},C={class:"gva-table-box"},j={class:"gva-btn-list"},z=["onClick"],U={class:"gva-pagination"},B=r(Object.assign({name:"Upload"},{setup(r){const B=u("/auth"),O=u(""),S=u(""),E=u(1),V=u(0),T=u(10),A=u({}),I=u([]),M=a=>{T.value=a,L()},D=a=>{E.value=a,L()},L=async()=>{const a=await t({page:E.value,pageSize:T.value,...A.value});0===a.code&&(I.value=a.data.list,V.value=a.data.total,E.value=a.data.page,T.value=a.data.pageSize)};L();return(t,r)=>{const u=d("base-input"),P=d("base-form-item"),W=d("base-button"),F=d("base-form"),R=d("el-table-column"),q=d("el-tag"),G=d("el-table"),H=d("el-pagination"),J=c("loading");return p((m(),g("div",null,[f("div",C,[v(i,{title:"点击“文件名/备注”可以编辑文件名或者备注内容。"}),f("div",j,[v(a,{imageCommon:S.value,"onUpdate:imageCommon":r[0]||(r[0]=a=>S.value=a),class:"upload-btn",onOnSuccess:L},null,8,["imageCommon"]),v(e,{imageUrl:O.value,"onUpdate:imageUrl":r[1]||(r[1]=a=>O.value=a),"file-size":512,"max-w-h":1080,class:"upload-btn",onOnSuccess:L},null,8,["imageUrl"]),v(F,{ref:"searchForm",inline:!0,model:A.value},{default:h((()=>[v(P,{label:""},{default:h((()=>[v(u,{modelValue:A.value.keyword,"onUpdate:modelValue":r[2]||(r[2]=a=>A.value.keyword=a),class:"keyword",placeholder:"请输入文件名或备注"},null,8,["modelValue"])])),_:1}),v(P,null,{default:h((()=>[v(W,{size:"small",type:"primary",icon:"search",onClick:L},{default:h((()=>r[3]||(r[3]=[w("查询")]))),_:1,__:[3]})])),_:1})])),_:1},8,["model"])]),v(G,{data:I.value},{default:h((()=>[v(R,{align:"left",label:"预览",width:"100"},{default:h((a=>[v(o,{"pic-type":"file","pic-src":a.row.url},null,8,["pic-src"])])),_:1}),v(R,{align:"left",label:"日期",prop:"UpdatedAt",width:"180"},{default:h((a=>[f("div",null,y(b(s)(a.row.UpdatedAt)),1)])),_:1}),v(R,{align:"left",label:"文件名/备注",prop:"name",width:"180"},{default:h((a=>[f("div",{class:"name",onClick:e=>(async a=>{_.prompt("请输入文件名或者备注","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/\S/,inputErrorMessage:"不能为空",inputValue:a.name}).then((async({value:e})=>{a.name=e,0===(await n(a)).code&&(k({type:"success",message:"编辑成功!"}),L())})).catch((()=>{k({type:"info",message:"取消修改"})}))})(a.row)},y(a.row.name),9,z)])),_:1}),v(R,{align:"left",label:"链接",prop:"url","min-width":"300"}),v(R,{align:"left",label:"标签",prop:"tag",width:"100"},{default:h((a=>[v(q,{type:"jpg"===a.row.tag?"primary":"success","disable-transitions":""},{default:h((()=>[w(y(a.row.tag),1)])),_:2},1032,["type"])])),_:1}),v(R,{align:"left",label:"操作",width:"160"},{default:h((a=>[v(W,{size:"small",icon:"download",type:"primary",link:"",onClick:e=>{var t;(t=a.row).url.indexOf("http://")>-1||t.url.indexOf("https://")>-1?x(t.url,t.name):x(B.value+"/"+t.url,t.name)}},{default:h((()=>r[4]||(r[4]=[w("下载")]))),_:2,__:[4]},1032,["onClick"]),v(W,{size:"small",icon:"delete",type:"primary",link:"",onClick:e=>(async a=>{_.confirm("此操作将永久删除文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await l(a)).code&&(k({type:"success",message:"删除成功!"}),1===I.value.length&&E.value>1&&E.value--,L())})).catch((()=>{k({type:"info",message:"已取消删除"})}))})(a.row)},{default:h((()=>r[5]||(r[5]=[w("删除")]))),_:2,__:[5]},1032,["onClick"])])),_:1})])),_:1},8,["data"]),f("div",U,[v(H,{"current-page":E.value,"page-size":T.value,"page-sizes":[10,30,50,100],style:{float:"right",padding:"20px"},total:V.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:D,onSizeChange:M},null,8,["current-page","page-size","total"])])])])),[[J,t.fullscreenLoading,void 0,{fullscreen:!0,lock:!0}]])}}}),[["__scopeId","data-v-fddba491"]]);export{B as default};
