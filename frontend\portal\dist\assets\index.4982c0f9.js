/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
function e(){import("data:text/javascript,")}
/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function t(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerpolicy&&(t.referrerPolicy=e.referrerpolicy),"use-credentials"===e.crossorigin?t.credentials="include":"anonymous"===e.crossorigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n=Object.freeze({}),o=Object.freeze([]),r=()=>{},s=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),i=e=>e.startsWith("onUpdate:"),l=Object.assign,c=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},u=Object.prototype.hasOwnProperty,d=(e,t)=>u.call(e,t),p=Array.isArray,f=e=>"[object Map]"===S(e),h=e=>"[object Set]"===S(e),m=e=>"[object Date]"===S(e),g=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,b=e=>null!==e&&"object"==typeof e,_=e=>(b(e)||g(e))&&g(e.then)&&g(e.catch),w=Object.prototype.toString,S=e=>w.call(e),x=e=>S(e).slice(8,-1),k=e=>"[object Object]"===S(e),C=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,$=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=t("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),O=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},T=/-(\w)/g,I=O((e=>e.replace(T,((e,t)=>t?t.toUpperCase():"")))),A=/\B([A-Z])/g,j=O((e=>e.replace(A,"-$1").toLowerCase())),R=O((e=>e.charAt(0).toUpperCase()+e.slice(1))),P=O((e=>e?`on${R(e)}`:"")),L=(e,t)=>!Object.is(e,t),D=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},V=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},N=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let M;const U=()=>M||(M="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function F(e){if(p(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?H(o):F(o);if(r)for(const e in r)t[e]=r[e]}return t}if(v(e)||b(e))return e}const B=/;(?![^(]*\))/g,q=/:([^]+)/,z=/\/\*[^]*?\*\//g;function H(e){const t={};return e.replace(z,"").split(B).forEach((e=>{if(e){const n=e.split(q);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function W(e){let t="";if(v(e))t=e;else if(p(e))for(let n=0;n<e.length;n++){const o=W(e[n]);o&&(t+=o+" ")}else if(b(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const G=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),J=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),K=t("annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics"),Y=t("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function X(e){return!!e||""===e}function Q(e,t){if(e===t)return!0;let n=m(e),o=m(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=y(e),o=y(t),n||o)return e===t;if(n=p(e),o=p(t),n||o)return!(!n||!o)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=Q(e[o],t[o]);return n}(e,t);if(n=b(e),o=b(t),n||o){if(!n||!o)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const o=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(o&&!r||!o&&r||!Q(e[n],t[n]))return!1}}return String(e)===String(t)}function Z(e,t){return e.findIndex((e=>Q(e,t)))}const ee=e=>!(!e||!0!==e.__v_isRef),te=e=>v(e)?e:null==e?"":p(e)||b(e)&&(e.toString===w||!g(e.toString))?ee(e)?te(e.value):JSON.stringify(e,ne,2):String(e),ne=(e,t)=>ee(t)?ne(e,t.value):f(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[oe(t,o)+" =>"]=n,e)),{})}:h(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>oe(e)))}:y(t)?oe(t):!b(t)||p(t)||k(t)?t:String(t),oe=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};
/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function re(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let se,ae;class ie{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=se,!e&&se&&(this.index=(se.scopes||(se.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=se;try{return se=this,e()}finally{se=t}}else re("cannot run an inactive effect scope.")}on(){1===++this._on&&(this.prevScope=se,se=this)}off(){this._on>0&&0===--this._on&&(se=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function le(e){return new ie(e)}function ce(){return se}const ue=new WeakSet;class de{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,se&&se.active&&se.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,ue.has(this)&&(ue.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||me(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,Oe(this),ye(this);const e=ae,t=ke;ae=this,ke=!0;try{return this.fn()}finally{ae!==this&&re("Active effect was not restored correctly - this is likely a Vue internal bug."),be(this),ae=e,ke=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)Se(e);this.deps=this.depsTail=void 0,Oe(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?ue.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){_e(this)&&this.run()}get dirty(){return _e(this)}}let pe,fe,he=0;function me(e,t=!1){if(e.flags|=8,t)return e.next=fe,void(fe=e);e.next=pe,pe=e}function ge(){he++}function ve(){if(--he>0)return;if(fe){let e=fe;for(fe=void 0;e;){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;pe;){let n=pe;for(pe=void 0;n;){const o=n.next;if(n.next=void 0,n.flags&=-9,1&n.flags)try{n.trigger()}catch(t){e||(e=t)}n=o}}if(e)throw e}function ye(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function be(e){let t,n=e.depsTail,o=n;for(;o;){const e=o.prevDep;-1===o.version?(o===n&&(n=e),Se(o),xe(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=e}e.deps=t,e.depsTail=n}function _e(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(we(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function we(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===Te)return;if(e.globalVersion=Te,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!_e(e)))return;e.flags|=2;const t=e.dep,n=ae,o=ke;ae=e,ke=!0;try{ye(e);const n=e.fn(e._value);(0===t.version||L(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(r){throw t.version++,r}finally{ae=n,ke=o,be(e),e.flags&=-3}}function Se(e,t=!1){const{dep:n,prevSub:o,nextSub:r}=e;if(o&&(o.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=o,e.nextSub=void 0),n.subsHead===e&&(n.subsHead=r),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)Se(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function xe(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let ke=!0;const Ce=[];function $e(){Ce.push(ke),ke=!1}function Ee(){const e=Ce.pop();ke=void 0===e||e}function Oe(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=ae;ae=void 0;try{t()}finally{ae=e}}}let Te=0;class Ie{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ae{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.subsHead=void 0}track(e){if(!ae||!ke||ae===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==ae)t=this.activeLink=new Ie(ae,this),ae.deps?(t.prevDep=ae.depsTail,ae.depsTail.nextDep=t,ae.depsTail=t):ae.deps=ae.depsTail=t,je(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=ae.depsTail,t.nextDep=void 0,ae.depsTail.nextDep=t,ae.depsTail=t,ae.deps===t&&(ae.deps=e)}return ae.onTrack&&ae.onTrack(l({effect:ae},e)),t}trigger(e){this.version++,Te++,this.notify(e)}notify(e){ge();try{for(let t=this.subsHead;t;t=t.nextSub)!t.sub.onTrigger||8&t.sub.flags||t.sub.onTrigger(l({effect:t.sub},e));for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{ve()}}}function je(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)je(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),void 0===e.dep.subsHead&&(e.dep.subsHead=e),e.dep.subs=e}}const Re=new WeakMap,Pe=Symbol("Object iterate"),Le=Symbol("Map keys iterate"),De=Symbol("Array iterate");function Ve(e,t,n){if(ke&&ae){let o=Re.get(e);o||Re.set(e,o=new Map);let r=o.get(n);r||(o.set(n,r=new Ae),r.map=o,r.key=n),r.track({target:e,type:t,key:n})}}function Ne(e,t,n,o,r,s){const a=Re.get(e);if(!a)return void Te++;const i=a=>{a&&a.trigger({target:e,type:t,key:n,newValue:o,oldValue:r,oldTarget:s})};if(ge(),"clear"===t)a.forEach(i);else{const r=p(e),s=r&&C(n);if(r&&"length"===n){const e=Number(o);a.forEach(((t,n)=>{("length"===n||n===De||!y(n)&&n>=e)&&i(t)}))}else switch((void 0!==n||a.has(void 0))&&i(a.get(n)),s&&i(a.get(De)),t){case"add":r?s&&i(a.get("length")):(i(a.get(Pe)),f(e)&&i(a.get(Le)));break;case"delete":r||(i(a.get(Pe)),f(e)&&i(a.get(Le)));break;case"set":f(e)&&i(a.get(Pe))}}ve()}function Me(e){const t=Et(e);return t===e?t:(Ve(t,"iterate",De),Ct(e)?t:t.map(Tt))}function Ue(e){return Ve(e=Et(e),"iterate",De),e}const Fe={__proto__:null,[Symbol.iterator](){return Be(this,Symbol.iterator,Tt)},concat(...e){return Me(this).concat(...e.map((e=>p(e)?Me(e):e)))},entries(){return Be(this,"entries",(e=>(e[1]=Tt(e[1]),e)))},every(e,t){return ze(this,"every",e,t,void 0,arguments)},filter(e,t){return ze(this,"filter",e,t,(e=>e.map(Tt)),arguments)},find(e,t){return ze(this,"find",e,t,Tt,arguments)},findIndex(e,t){return ze(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ze(this,"findLast",e,t,Tt,arguments)},findLastIndex(e,t){return ze(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ze(this,"forEach",e,t,void 0,arguments)},includes(...e){return We(this,"includes",e)},indexOf(...e){return We(this,"indexOf",e)},join(e){return Me(this).join(e)},lastIndexOf(...e){return We(this,"lastIndexOf",e)},map(e,t){return ze(this,"map",e,t,void 0,arguments)},pop(){return Ge(this,"pop")},push(...e){return Ge(this,"push",e)},reduce(e,...t){return He(this,"reduce",e,t)},reduceRight(e,...t){return He(this,"reduceRight",e,t)},shift(){return Ge(this,"shift")},some(e,t){return ze(this,"some",e,t,void 0,arguments)},splice(...e){return Ge(this,"splice",e)},toReversed(){return Me(this).toReversed()},toSorted(e){return Me(this).toSorted(e)},toSpliced(...e){return Me(this).toSpliced(...e)},unshift(...e){return Ge(this,"unshift",e)},values(){return Be(this,"values",Tt)}};function Be(e,t,n){const o=Ue(e),r=o[t]();return o===e||Ct(e)||(r._next=r.next,r.next=()=>{const e=r._next();return e.value&&(e.value=n(e.value)),e}),r}const qe=Array.prototype;function ze(e,t,n,o,r,s){const a=Ue(e),i=a!==e&&!Ct(e),l=a[t];if(l!==qe[t]){const t=l.apply(e,s);return i?Tt(t):t}let c=n;a!==e&&(i?c=function(t,o){return n.call(this,Tt(t),o,e)}:n.length>2&&(c=function(t,o){return n.call(this,t,o,e)}));const u=l.call(a,c,o);return i&&r?r(u):u}function He(e,t,n,o){const r=Ue(e);let s=n;return r!==e&&(Ct(e)?n.length>3&&(s=function(t,o,r){return n.call(this,t,o,r,e)}):s=function(t,o,r){return n.call(this,t,Tt(o),r,e)}),r[t](s,...o)}function We(e,t,n){const o=Et(e);Ve(o,"iterate",De);const r=o[t](...n);return-1!==r&&!1!==r||!$t(n[0])?r:(n[0]=Et(n[0]),o[t](...n))}function Ge(e,t,n=[]){$e(),ge();const o=Et(e)[t].apply(e,n);return ve(),Ee(),o}const Je=t("__proto__,__v_isRef,__isVue"),Ke=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y));function Ye(e){y(e)||(e=String(e));const t=Et(this);return Ve(t,"has",e),t.hasOwnProperty(e)}class Xe{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if("__v_skip"===t)return e.__v_skip;const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?vt:gt:r?mt:ht).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const s=p(e);if(!o){let e;if(s&&(e=Fe[t]))return e;if("hasOwnProperty"===t)return Ye}const a=Reflect.get(e,t,At(e)?e:n);return(y(t)?Ke.has(t):Je(t))?a:(o||Ve(e,"get",t),r?a:At(a)?s&&C(t)?a:a.value:b(a)?o?_t(a):yt(a):a)}}class Qe extends Xe{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=kt(r);if(Ct(n)||kt(n)||(r=Et(r),n=Et(n)),!p(e)&&At(r)&&!At(n))return!t&&(r.value=n,!0)}const s=p(e)&&C(t)?Number(t)<e.length:d(e,t),a=Reflect.set(e,t,n,At(e)?e:o);return e===Et(o)&&(s?L(n,r)&&Ne(e,"set",t,n,r):Ne(e,"add",t,n)),a}deleteProperty(e,t){const n=d(e,t),o=e[t],r=Reflect.deleteProperty(e,t);return r&&n&&Ne(e,"delete",t,void 0,o),r}has(e,t){const n=Reflect.has(e,t);return y(t)&&Ke.has(t)||Ve(e,"has",t),n}ownKeys(e){return Ve(e,"iterate",p(e)?"length":Pe),Reflect.ownKeys(e)}}class Ze extends Xe{constructor(e=!1){super(!0,e)}set(e,t){return re(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0}deleteProperty(e,t){return re(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}}const et=new Qe,tt=new Ze,nt=new Qe(!0),ot=new Ze(!0),rt=e=>e,st=e=>Reflect.getPrototypeOf(e);function at(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";re(`${R(e)} operation ${n}failed: target is readonly.`,Et(this))}return"delete"!==e&&("clear"===e?void 0:this)}}function it(e,t){const n={get(n){const o=this.__v_raw,r=Et(o),s=Et(n);e||(L(n,s)&&Ve(r,"get",n),Ve(r,"get",s));const{has:a}=st(r),i=t?rt:e?It:Tt;return a.call(r,n)?i(o.get(n)):a.call(r,s)?i(o.get(s)):void(o!==r&&o.get(n))},get size(){const t=this.__v_raw;return!e&&Ve(Et(t),"iterate",Pe),Reflect.get(t,"size",t)},has(t){const n=this.__v_raw,o=Et(n),r=Et(t);return e||(L(t,r)&&Ve(o,"has",t),Ve(o,"has",r)),t===r?n.has(t):n.has(t)||n.has(r)},forEach(n,o){const r=this,s=r.__v_raw,a=Et(s),i=t?rt:e?It:Tt;return!e&&Ve(a,"iterate",Pe),s.forEach(((e,t)=>n.call(o,i(e),i(t),r)))}};l(n,e?{add:at("add"),set:at("set"),delete:at("delete"),clear:at("clear")}:{add(e){t||Ct(e)||kt(e)||(e=Et(e));const n=Et(this);return st(n).has.call(n,e)||(n.add(e),Ne(n,"add",e,e)),this},set(e,n){t||Ct(n)||kt(n)||(n=Et(n));const o=Et(this),{has:r,get:s}=st(o);let a=r.call(o,e);a?ft(o,r,e):(e=Et(e),a=r.call(o,e));const i=s.call(o,e);return o.set(e,n),a?L(n,i)&&Ne(o,"set",e,n,i):Ne(o,"add",e,n),this},delete(e){const t=Et(this),{has:n,get:o}=st(t);let r=n.call(t,e);r?ft(t,n,e):(e=Et(e),r=n.call(t,e));const s=o?o.call(t,e):void 0,a=t.delete(e);return r&&Ne(t,"delete",e,void 0,s),a},clear(){const e=Et(this),t=0!==e.size,n=f(e)?new Map(e):new Set(e),o=e.clear();return t&&Ne(e,"clear",void 0,void 0,n),o}});return["keys","values","entries",Symbol.iterator].forEach((o=>{n[o]=function(e,t,n){return function(...o){const r=this.__v_raw,s=Et(r),a=f(s),i="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=r[e](...o),u=n?rt:t?It:Tt;return!t&&Ve(s,"iterate",l?Le:Pe),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:i?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}(o,e,t)})),n}function lt(e,t){const n=it(e,t);return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(d(n,o)&&o in t?n:t,o,r)}const ct={get:lt(!1,!1)},ut={get:lt(!1,!0)},dt={get:lt(!0,!1)},pt={get:lt(!0,!0)};function ft(e,t,n){const o=Et(n);if(o!==n&&t.call(e,o)){const t=x(e);re(`Reactive ${t} contains both the raw and reactive versions of the same object${"Map"===t?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const ht=new WeakMap,mt=new WeakMap,gt=new WeakMap,vt=new WeakMap;function yt(e){return kt(e)?e:St(e,!1,et,ct,ht)}function bt(e){return St(e,!1,nt,ut,mt)}function _t(e){return St(e,!0,tt,dt,gt)}function wt(e){return St(e,!0,ot,pt,vt)}function St(e,t,n,o,r){if(!b(e))return re(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const s=(a=e).__v_skip||!Object.isExtensible(a)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(x(a));var a;if(0===s)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,2===s?o:n);return r.set(e,l),l}function xt(e){return kt(e)?xt(e.__v_raw):!(!e||!e.__v_isReactive)}function kt(e){return!(!e||!e.__v_isReadonly)}function Ct(e){return!(!e||!e.__v_isShallow)}function $t(e){return!!e&&!!e.__v_raw}function Et(e){const t=e&&e.__v_raw;return t?Et(t):e}function Ot(e){return!d(e,"__v_skip")&&Object.isExtensible(e)&&V(e,"__v_skip",!0),e}const Tt=e=>b(e)?yt(e):e,It=e=>b(e)?_t(e):e;function At(e){return!!e&&!0===e.__v_isRef}function jt(e){return Rt(e,!1)}function Rt(e,t){return At(e)?e:new Pt(e,t)}class Pt{constructor(e,t){this.dep=new Ae,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:Et(e),this._value=t?e:Tt(e),this.__v_isShallow=t}get value(){return this.dep.track({target:this,type:"get",key:"value"}),this._value}set value(e){const t=this._rawValue,n=this.__v_isShallow||Ct(e)||kt(e);e=n?e:Et(e),L(e,t)&&(this._rawValue=e,this._value=n?e:Tt(e),this.dep.trigger({target:this,type:"set",key:"value",newValue:e,oldValue:t}))}}function Lt(e){return At(e)?e.value:e}const Dt={get:(e,t,n)=>"__v_raw"===t?e:Lt(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return At(r)&&!At(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Vt(e){return xt(e)?e:new Proxy(e,Dt)}function Nt(e){$t(e)||re("toRefs() expects a reactive object but received a plain one.");const t=p(e)?new Array(e.length):{};for(const n in e)t[n]=Bt(e,n);return t}class Mt{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){const n=Re.get(e);return n&&n.get(t)}(Et(this._object),this._key)}}class Ut{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Ft(e,t,n){return At(e)?e:g(e)?new Ut(e):b(e)&&arguments.length>1?Bt(e,t,n):jt(e)}function Bt(e,t,n){const o=e[t];return At(o)?o:new Mt(e,t,n)}class qt{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new Ae(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Te-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags)&&ae!==this)return me(this,!0),!0}get value(){const e=this.dep.track({target:this,type:"get",key:"value"});return we(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter?this.setter(e):re("Write operation failed: computed value is readonly")}}const zt={},Ht=new WeakMap;let Wt;function Gt(e,t,o=n){const{immediate:s,deep:a,once:i,scheduler:l,augmentJob:u,call:d}=o,f=e=>{(o.onWarn||re)("Invalid watch source: ",e,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},h=e=>a?e:Ct(e)||!1===a||0===a?Jt(e,1):Jt(e);let m,v,y,b,_=!1,w=!1;if(At(e)?(v=()=>e.value,_=Ct(e)):xt(e)?(v=()=>h(e),_=!0):p(e)?(w=!0,_=e.some((e=>xt(e)||Ct(e))),v=()=>e.map((e=>At(e)?e.value:xt(e)?h(e):g(e)?d?d(e,2):e():void f(e)))):g(e)?v=t?d?()=>d(e,2):e:()=>{if(y){$e();try{y()}finally{Ee()}}const t=Wt;Wt=m;try{return d?d(e,3,[b]):e(b)}finally{Wt=t}}:(v=r,f(e)),t&&a){const e=v,t=!0===a?1/0:a;v=()=>Jt(e(),t)}const S=ce(),x=()=>{m.stop(),S&&S.active&&c(S.effects,m)};if(i&&t){const e=t;t=(...t)=>{e(...t),x()}}let k=w?new Array(e.length).fill(zt):zt;const C=e=>{if(1&m.flags&&(m.dirty||e))if(t){const e=m.run();if(a||_||(w?e.some(((e,t)=>L(e,k[t]))):L(e,k))){y&&y();const n=Wt;Wt=m;try{const n=[e,k===zt?void 0:w&&k[0]===zt?[]:k,b];k=e,d?d(t,3,n):t(...n)}finally{Wt=n}}}else m.run()};return u&&u(C),m=new de(v),m.scheduler=l?()=>l(C,!1):C,b=e=>function(e,t=!1,n=Wt){if(n){let t=Ht.get(n);t||Ht.set(n,t=[]),t.push(e)}else t||re("onWatcherCleanup() was called when there was no active watcher to associate with.")}(e,!1,m),y=m.onStop=()=>{const e=Ht.get(m);if(e){if(d)d(e,4);else for(const t of e)t();Ht.delete(m)}},m.onTrack=o.onTrack,m.onTrigger=o.onTrigger,t?s?C(!0):k=m.run():l?l(C.bind(null,!0),!0):m.run(),x.pause=m.pause.bind(m),x.resume=m.resume.bind(m),x.stop=x,x}function Jt(e,t=1/0,n){if(t<=0||!b(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,At(e))Jt(e.value,t,n);else if(p(e))for(let o=0;o<e.length;o++)Jt(e[o],t,n);else if(h(e)||f(e))e.forEach((e=>{Jt(e,t,n)}));else if(k(e)){for(const o in e)Jt(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&Jt(e[o],t,n)}return e}
/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Kt=[];function Yt(e){Kt.push(e)}function Xt(){Kt.pop()}let Qt=!1;function Zt(e,...t){if(Qt)return;Qt=!0,$e();const n=Kt.length?Kt[Kt.length-1].component:null,o=n&&n.appContext.config.warnHandler,r=function(){let e=Kt[Kt.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}();if(o)on(o,n,11,[e+t.map((e=>{var t,n;return null!=(n=null==(t=e.toString)?void 0:t.call(e))?n:JSON.stringify(e)})).join(""),n&&n.proxy,r.map((({vnode:e})=>`at <${la(n,e.type)}>`)).join("\n"),r]);else{const n=[`[Vue warn]: ${e}`,...t];r.length&&n.push("\n",...function(e){const t=[];return e.forEach(((e,n)=>{t.push(...0===n?[]:["\n"],...function({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=!!e.component&&null==e.component.parent,r=` at <${la(e.component,e.type,o)}`,s=">"+n;return e.props?[r,...en(e.props),s]:[r+s]}(e))})),t}(r)),console.warn(...n)}Ee(),Qt=!1}function en(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach((n=>{t.push(...tn(n,e[n]))})),n.length>3&&t.push(" ..."),t}function tn(e,t,n){return v(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):"number"==typeof t||"boolean"==typeof t||null==t?n?t:[`${e}=${t}`]:At(t)?(t=tn(e,Et(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):g(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Et(t),n?t:[`${e}=`,t])}const nn={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function on(e,t,n,o){try{return o?e(...o):e()}catch(r){sn(r,t,n)}}function rn(e,t,n,o){if(g(e)){const r=on(e,t,n,o);return r&&_(r)&&r.catch((e=>{sn(e,t,n)})),r}if(p(e)){const r=[];for(let s=0;s<e.length;s++)r.push(rn(e[s],t,n,o));return r}Zt("Invalid value type passed to callWithAsyncErrorHandling(): "+typeof e)}function sn(e,t,o,r=!0){const s=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||n;if(t){let n=t.parent;const r=t.proxy,s=nn[o];for(;n;){const t=n.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,s))return;n=n.parent}if(a)return $e(),on(a,null,10,[e,r,s]),void Ee()}!function(e,t,n,o=!0){{const r=nn[t];if(n&&Yt(n),Zt("Unhandled error"+(r?` during execution of ${r}`:"")),n&&Xt(),o)throw e;console.error(e)}}(e,o,s,r,i)}const an=[];let ln=-1;const cn=[];let un=null,dn=0;const pn=Promise.resolve();let fn=null;function hn(e){const t=fn||pn;return e?t.then(this?e.bind(this):e):t}function mn(e){if(!(1&e.flags)){const t=_n(e),n=an[an.length-1];!n||!(2&e.flags)&&t>=_n(n)?an.push(e):an.splice(function(e){let t=ln+1,n=an.length;for(;t<n;){const o=t+n>>>1,r=an[o],s=_n(r);s<e||s===e&&2&r.flags?t=o+1:n=o}return t}(t),0,e),e.flags|=1,gn()}}function gn(){fn||(fn=pn.then(wn))}function vn(e){p(e)?cn.push(...e):un&&-1===e.id?un.splice(dn+1,0,e):1&e.flags||(cn.push(e),e.flags|=1),gn()}function yn(e,t,n=ln+1){for(t=t||new Map;n<an.length;n++){const o=an[n];if(o&&2&o.flags){if(e&&o.id!==e.uid)continue;if(Sn(t,o))continue;an.splice(n,1),n--,4&o.flags&&(o.flags&=-2),o(),4&o.flags||(o.flags&=-2)}}}function bn(e){if(cn.length){const t=[...new Set(cn)].sort(((e,t)=>_n(e)-_n(t)));if(cn.length=0,un)return void un.push(...t);for(un=t,e=e||new Map,dn=0;dn<un.length;dn++){const t=un[dn];Sn(e,t)||(4&t.flags&&(t.flags&=-2),8&t.flags||t(),t.flags&=-2)}un=null,dn=0}}const _n=e=>null==e.id?2&e.flags?-1:1/0:e.id;function wn(e){e=e||new Map;const t=t=>Sn(e,t);try{for(ln=0;ln<an.length;ln++){const e=an[ln];if(e&&!(8&e.flags)){if(t(e))continue;4&e.flags&&(e.flags&=-2),on(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2)}}}finally{for(;ln<an.length;ln++){const e=an[ln];e&&(e.flags&=-2)}ln=-1,an.length=0,bn(e),fn=null,(an.length||cn.length)&&wn(e)}}function Sn(e,t){const n=e.get(t)||0;if(n>100){const e=t.i,n=e&&ia(e.type);return sn(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,n+1),!1}let xn=!1;const kn=new Map;U().__VUE_HMR_RUNTIME__={createRecord:Tn($n),rerender:Tn((function(e,t){const n=Cn.get(e);if(!n)return;n.initialDef.render=t,[...n.instances].forEach((e=>{t&&(e.render=t,En(e.type).render=t),e.renderCache=[],xn=!0,e.update(),xn=!1}))})),reload:Tn((function(e,t){const n=Cn.get(e);if(!n)return;t=En(t),On(n.initialDef,t);const o=[...n.instances];for(let r=0;r<o.length;r++){const e=o[r],s=En(e.type);let a=kn.get(s);a||(s!==n.initialDef&&On(s,t),kn.set(s,a=new Set)),a.add(e),e.appContext.propsCache.delete(e.type),e.appContext.emitsCache.delete(e.type),e.appContext.optionsCache.delete(e.type),e.ceReload?(a.add(e),e.ceReload(t.styles),a.delete(e)):e.parent?mn((()=>{xn=!0,e.parent.update(),xn=!1,a.delete(e)})):e.appContext.reload?e.appContext.reload():"undefined"!=typeof window?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),e.root.ce&&e!==e.root&&e.root.ce._removeChildStyle(s)}vn((()=>{kn.clear()}))}))};const Cn=new Map;function $n(e,t){return!Cn.has(e)&&(Cn.set(e,{initialDef:En(t),instances:new Set}),!0)}function En(e){return ca(e)?e.__vccOpts:e}function On(e,t){l(e,t);for(const n in e)"__file"===n||n in t||delete e[n]}function Tn(e){return(t,n)=>{try{return e(t,n)}catch(o){console.error(o),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let In,An=[],jn=!1;function Rn(e,...t){In?In.emit(e,...t):jn||An.push({event:e,args:t})}function Pn(e,t){var n,o;if(In=e,In)In.enabled=!0,An.forEach((({event:e,args:t})=>In.emit(e,...t))),An=[];else if("undefined"!=typeof window&&window.HTMLElement&&!(null==(o=null==(n=window.navigator)?void 0:n.userAgent)?void 0:o.includes("jsdom"))){(t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push((e=>{Pn(e,t)})),setTimeout((()=>{In||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,jn=!0,An=[])}),3e3)}else jn=!0,An=[]}const Ln=Nn("component:added"),Dn=Nn("component:updated"),Vn=Nn("component:removed");
/*! #__NO_SIDE_EFFECTS__ */
function Nn(e){return t=>{Rn(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const Mn=Fn("perf:start"),Un=Fn("perf:end");function Fn(e){return(t,n,o)=>{Rn(e,t.appContext.app,t.uid,t,n,o)}}let Bn=null,qn=null;function zn(e){const t=Bn;return Bn=e,qn=e&&e.type.__scopeId||null,t}function Hn(e,t=Bn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&xs(-1);const r=zn(t);let s;try{s=e(...n)}finally{zn(r),o._d&&xs(1)}return Dn(t),s};return o._n=!0,o._c=!0,o._d=!0,o}function Wn(e){E(e)&&Zt("Do not use built-in directive ids as custom directive id: "+e)}function Gn(e,t){if(null===Bn)return Zt("withDirectives can only be used inside render functions."),e;const o=ra(Bn),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[e,a,i,l=n]=t[s];e&&(g(e)&&(e={mounted:e,updated:e}),e.deep&&Jt(a),r.push({dir:e,instance:o,value:a,oldValue:void 0,arg:i,modifiers:l}))}return e}function Jn(e,t,n,o){const r=e.dirs,s=t&&t.dirs;for(let a=0;a<r.length;a++){const i=r[a];s&&(i.oldValue=s[a].value);let l=i.dir[o];l&&($e(),rn(l,n,8,[e.el,i,e,t]),Ee())}}const Kn=Symbol("_vte"),Yn=e=>e.__isTeleport,Xn=Symbol("_leaveCb"),Qn=Symbol("_enterCb");const Zn=[Function,Array],eo={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Zn,onEnter:Zn,onAfterEnter:Zn,onEnterCancelled:Zn,onBeforeLeave:Zn,onLeave:Zn,onAfterLeave:Zn,onLeaveCancelled:Zn,onBeforeAppear:Zn,onAppear:Zn,onAfterAppear:Zn,onAppearCancelled:Zn},to=e=>{const t=e.subTree;return t.component?to(t.component):t};function no(e){let t=e[0];if(e.length>1){let n=!1;for(const o of e)if(o.type!==vs){if(n){Zt("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}t=o,n=!0}}return t}const oo={name:"BaseTransition",props:eo,setup(e,{slots:t}){const n=zs(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Oo((()=>{e.isMounted=!0})),Ao((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&co(t.default(),!0);if(!r||!r.length)return;const s=no(r),a=Et(e),{mode:i}=a;if(i&&"in-out"!==i&&"out-in"!==i&&"default"!==i&&Zt(`invalid <transition> mode: ${i}`),o.isLeaving)return ao(s);const l=io(s);if(!l)return ao(s);let c=so(l,a,o,n,(e=>c=e));l.type!==vs&&lo(l,c);let u=n.subTree&&io(n.subTree);if(u&&u.type!==vs&&!Os(l,u)&&to(n).type!==vs){let e=so(u,a,o,n);if(lo(u,e),"out-in"===i&&l.type!==vs)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,8&n.job.flags||n.update(),delete e.afterLeave,u=void 0},ao(s);"in-out"===i&&l.type!==vs?e.delayLeave=(e,t,n)=>{ro(o,u)[String(u.key)]=u,e[Xn]=()=>{t(),e[Xn]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{n(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return s}}};function ro(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function so(e,t,n,o,r){const{appear:s,mode:a,persisted:i=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:f,onLeave:h,onAfterLeave:m,onLeaveCancelled:g,onBeforeAppear:v,onAppear:y,onAfterAppear:b,onAppearCancelled:_}=t,w=String(e.key),S=ro(n,e),x=(e,t)=>{e&&rn(e,o,9,t)},k=(e,t)=>{const n=t[1];x(e,t),p(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},C={mode:a,persisted:i,beforeEnter(t){let o=l;if(!n.isMounted){if(!s)return;o=v||l}t[Xn]&&t[Xn](!0);const r=S[w];r&&Os(e,r)&&r.el[Xn]&&r.el[Xn](),x(o,[t])},enter(e){let t=c,o=u,r=d;if(!n.isMounted){if(!s)return;t=y||c,o=b||u,r=_||d}let a=!1;const i=e[Qn]=t=>{a||(a=!0,x(t?r:o,[e]),C.delayedLeave&&C.delayedLeave(),e[Qn]=void 0)};t?k(t,[e,i]):i()},leave(t,o){const r=String(e.key);if(t[Qn]&&t[Qn](!0),n.isUnmounting)return o();x(f,[t]);let s=!1;const a=t[Xn]=n=>{s||(s=!0,o(),x(n?g:m,[t]),t[Xn]=void 0,S[r]===e&&delete S[r])};S[r]=e,h?k(h,[t,a]):a()},clone(e){const s=so(e,t,n,o,r);return r&&r(s),s}};return C}function ao(e){if(go(e))return(e=Rs(e)).children=null,e}function io(e){if(!go(e))return Yn(e.type)&&e.children?no(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&g(n.default))return n.default()}}function lo(e,t){6&e.shapeFlag&&e.component?(e.transition=t,lo(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function co(e,t=!1,n){let o=[],r=0;for(let s=0;s<e.length;s++){let a=e[s];const i=null==n?a.key:String(n)+String(null!=a.key?a.key:s);a.type===ms?(128&a.patchFlag&&r++,o=o.concat(co(a.children,t,i))):(t||a.type!==vs)&&o.push(null!=i?Rs(a,{key:i}):a)}if(r>1)for(let s=0;s<o.length;s++)o[s].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function uo(e,t){return g(e)?(()=>l({name:e.name},t,{setup:e}))():e}function po(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const fo=new WeakSet;function ho(e,t,o,r,s=!1){if(p(e))return void e.forEach(((e,n)=>ho(e,t&&(p(t)?t[n]:t),o,r,s)));if(mo(r)&&!s)return void(512&r.shapeFlag&&r.type.__asyncResolved&&r.component.subTree.component&&ho(e,t,o,r.component.subTree));const a=4&r.shapeFlag?ra(r.component):r.el,i=s?null:a,{i:l,r:u}=e;if(!l)return void Zt("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");const f=t&&t.r,h=l.refs===n?l.refs={}:l.refs,m=l.setupState,y=Et(m),b=m===n?()=>!1:e=>(d(y,e)&&!At(y[e])&&Zt(`Template ref "${e}" used on a non-ref value. It will not work in the production build.`),!fo.has(y[e])&&d(y,e));if(null!=f&&f!==u&&(v(f)?(h[f]=null,b(f)&&(m[f]=null)):At(f)&&(f.value=null)),g(u))on(u,l,12,[i,h]);else{const t=v(u),n=At(u);if(t||n){const r=()=>{if(e.f){const n=t?b(u)?m[u]:h[u]:u.value;s?p(n)&&c(n,a):p(n)?n.includes(a)||n.push(a):t?(h[u]=[a],b(u)&&(m[u]=h[u])):(u.value=[a],e.k&&(h[e.k]=u.value))}else t?(h[u]=i,b(u)&&(m[u]=i)):n?(u.value=i,e.k&&(h[e.k]=i)):Zt("Invalid template ref type:",u,`(${typeof u})`)};i?(r.id=-1,Br(r,o)):r()}else Zt("Invalid template ref type:",u,`(${typeof u})`)}}U().requestIdleCallback,U().cancelIdleCallback;const mo=e=>!!e.type.__asyncLoader,go=e=>e.type.__isKeepAlive,vo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=zs(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=new Map,s=new Set;let a=null;n.__v_cache=r;const i=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,p=d("div");function f(e){xo(e),u(e,n,i,!0)}function h(e){r.forEach(((t,n)=>{const o=ia(t.type);o&&!e(o)&&m(n)}))}function m(e){const t=r.get(e);!t||a&&Os(t,a)?a&&xo(a):f(t),r.delete(e),s.delete(e)}o.activate=(e,t,n,o,r)=>{const s=e.component;c(e,t,n,0,i),l(s.vnode,e,t,n,s,i,o,e.slotScopeIds,r),Br((()=>{s.isDeactivated=!1,s.a&&D(s.a);const t=e.props&&e.props.onVnodeMounted;t&&Us(t,s.parent,e)}),i),Ln(s)},o.deactivate=e=>{const t=e.component;Jr(t.m),Jr(t.a),c(e,p,null,1,i),Br((()=>{t.da&&D(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Us(n,t.parent,e),t.isDeactivated=!0}),i),Ln(t),t.__keepAliveStorageContainer=p},Xr((()=>[e.include,e.exclude]),(([e,t])=>{e&&h((t=>yo(e,t))),t&&h((e=>!yo(t,e)))}),{flush:"post",deep:!0});let g=null;const v=()=>{null!=g&&(hs(n.subTree.type)?Br((()=>{r.set(g,ko(n.subTree))}),n.subTree.suspense):r.set(g,ko(n.subTree)))};return Oo(v),Io(v),Ao((()=>{r.forEach((e=>{const{subTree:t,suspense:o}=n,r=ko(t);if(e.type!==r.type||e.key!==r.key)f(e);else{xo(r);const e=r.component.da;e&&Br(e,o)}}))})),()=>{if(g=null,!t.default)return a=null;const n=t.default(),o=n[0];if(n.length>1)return Zt("KeepAlive should contain exactly one component child."),a=null,n;if(!(Es(o)&&(4&o.shapeFlag||128&o.shapeFlag)))return a=null,o;let i=ko(o);if(i.type===vs)return a=null,i;const l=i.type,c=ia(mo(i)?i.type.__asyncResolved||{}:l),{include:u,exclude:d,max:p}=e;if(u&&(!c||!yo(u,c))||d&&c&&yo(d,c))return i.shapeFlag&=-257,a=i,o;const f=null==i.key?l:i.key,h=r.get(f);return i.el&&(i=Rs(i),128&o.shapeFlag&&(o.ssContent=i)),g=f,h?(i.el=h.el,i.component=h.component,i.transition&&lo(i,i.transition),i.shapeFlag|=512,s.delete(f),s.add(f)):(s.add(f),p&&s.size>parseInt(p,10)&&m(s.values().next().value)),i.shapeFlag|=256,a=i,hs(o.type)?o:i}}};function yo(e,t){return p(e)?e.some((e=>yo(e,t))):v(e)?e.split(",").includes(t):"[object RegExp]"===S(e)&&(e.lastIndex=0,e.test(t))}function bo(e,t){wo(e,"a",t)}function _o(e,t){wo(e,"da",t)}function wo(e,t,n=qs){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(Co(t,o,n),n){let e=n.parent;for(;e&&e.parent;)go(e.parent.vnode)&&So(o,t,n,e),e=e.parent}}function So(e,t,n,o){const r=Co(t,e,o,!0);jo((()=>{c(o[t],r)}),n)}function xo(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function ko(e){return 128&e.shapeFlag?e.ssContent:e}function Co(e,t,n=qs,o=!1){if(n){const r=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{$e();const r=Gs(n),s=rn(t,n,e,o);return r(),Ee(),s});return o?r.unshift(s):r.push(s),s}Zt(`${P(nn[e].replace(/ hook$/,""))} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}const $o=e=>(t,n=qs)=>{Zs&&"sp"!==e||Co(e,((...e)=>t(...e)),n)},Eo=$o("bm"),Oo=$o("m"),To=$o("bu"),Io=$o("u"),Ao=$o("bum"),jo=$o("um"),Ro=$o("sp"),Po=$o("rtg"),Lo=$o("rtc");function Do(e,t=qs){Co("ec",e,t)}const Vo="components";function No(e,t){return Bo(Vo,e,!0,t)||e}const Mo=Symbol.for("v-ndc");function Uo(e){return v(e)?Bo(Vo,e,!1)||e:e||Mo}function Fo(e){return Bo("directives",e)}function Bo(e,t,n=!0,o=!1){const r=Bn||qs;if(r){const s=r.type;if(e===Vo){const e=ia(s,!1);if(e&&(e===t||e===I(t)||e===R(I(t))))return s}const a=qo(r[e]||s[e],t)||qo(r.appContext[e],t);if(!a&&o)return s;if(n&&!a){const n=e===Vo?"\nIf this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.":"";Zt(`Failed to resolve ${e.slice(0,-1)}: ${t}${n}`)}return a}Zt(`resolve${R(e.slice(0,-1))} can only be used in render() or setup().`)}function qo(e,t){return e&&(e[t]||e[I(t)]||e[R(I(t))])}function zo(e,t,n,o){let r;const s=n&&n[o],a=p(e);if(a||v(e)){let n=!1,o=!1;a&&xt(e)&&(n=!Ct(e),o=kt(e),e=Ue(e)),r=new Array(e.length);for(let a=0,i=e.length;a<i;a++)r[a]=t(n?o?It(Tt(e[a])):Tt(e[a]):e[a],a,void 0,s&&s[a])}else if("number"==typeof e){Number.isInteger(e)||Zt(`The v-for range expect an integer value but got ${e}.`),r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,s&&s[n])}else if(b(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,s&&s[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];r[o]=t(e[a],a,o,s&&s[o])}}else r=[];return n&&(n[o]=r),r}function Ho(e,t,n={},o,r){if(Bn.ce||Bn.parent&&mo(Bn.parent)&&Bn.parent.ce)return"default"!==t&&(n.name=t),ws(),$s(ms,null,[js("slot",n,o&&o())],64);let s=e[t];s&&s.length>1&&(Zt("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),s=()=>[]),s&&s._c&&(s._d=!1),ws();const a=s&&Wo(s(n)),i=n.key||a&&a.key,l=$s(ms,{key:(i&&!y(i)?i:`_${t}`)+(!a&&o?"_fb":"")},a||(o?o():[]),a&&1===e._?64:-2);return!r&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),s&&s._c&&(s._d=!0),l}function Wo(e){return e.some((e=>!Es(e)||e.type!==vs&&!(e.type===ms&&!Wo(e.children))))?e:null}const Go=e=>e?Xs(e)?ra(e):Go(e.parent):null,Jo=l(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>wt(e.props),$attrs:e=>wt(e.attrs),$slots:e=>wt(e.slots),$refs:e=>wt(e.refs),$parent:e=>Go(e.parent),$root:e=>Go(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>or(e),$forceUpdate:e=>e.f||(e.f=()=>{mn(e.update)}),$nextTick:e=>e.n||(e.n=hn.bind(e.proxy)),$watch:e=>Zr.bind(e)}),Ko=e=>"_"===e||"$"===e,Yo=(e,t)=>e!==n&&!e.__isScriptSetup&&d(e,t),Xo={get({_:e},t){if("__v_skip"===t)return!0;const{ctx:o,setupState:r,data:s,props:a,accessCache:i,type:l,appContext:c}=e;if("__isVue"===t)return!0;let u;if("$"!==t[0]){const l=i[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return s[t];case 4:return o[t];case 3:return a[t]}else{if(Yo(r,t))return i[t]=1,r[t];if(s!==n&&d(s,t))return i[t]=2,s[t];if((u=e.propsOptions[0])&&d(u,t))return i[t]=3,a[t];if(o!==n&&d(o,t))return i[t]=4,o[t];Zo&&(i[t]=0)}}const p=Jo[t];let f,h;return p?("$attrs"===t?(Ve(e.attrs,"get",""),as()):"$slots"===t&&Ve(e,"get",t),p(e)):(f=l.__cssModules)&&(f=f[t])?f:o!==n&&d(o,t)?(i[t]=4,o[t]):(h=c.config.globalProperties,d(h,t)?h[t]:void(!Bn||v(t)&&0===t.indexOf("__v")||(s!==n&&Ko(t[0])&&d(s,t)?Zt(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===Bn&&Zt(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))))},set({_:e},t,o){const{data:r,setupState:s,ctx:a}=e;return Yo(s,t)?(s[t]=o,!0):s.__isScriptSetup&&d(s,t)?(Zt(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):r!==n&&d(r,t)?(r[t]=o,!0):d(e.props,t)?(Zt(`Attempting to mutate prop "${t}". Props are readonly.`),!1):"$"===t[0]&&t.slice(1)in e?(Zt(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):(t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:o}):a[t]=o,!0)},has({_:{data:e,setupState:t,accessCache:o,ctx:r,appContext:s,propsOptions:a}},i){let l;return!!o[i]||e!==n&&d(e,i)||Yo(t,i)||(l=a[0])&&d(l,i)||d(r,i)||d(Jo,i)||d(s.config.globalProperties,i)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:d(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Qo(e){return p(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}Xo.ownKeys=e=>(Zt("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e));let Zo=!0;function er(e){const t=or(e),n=e.proxy,o=e.ctx;Zo=!1,t.beforeCreate&&tr(t.beforeCreate,e,"bc");const{data:s,computed:a,methods:i,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:h,beforeUpdate:m,updated:v,activated:y,deactivated:w,beforeDestroy:S,beforeUnmount:x,destroyed:k,unmounted:C,render:$,renderTracked:E,renderTriggered:O,errorCaptured:T,serverPrefetch:I,expose:A,inheritAttrs:j,components:R,directives:P,filters:L}=t,D=function(){const e=Object.create(null);return(t,n)=>{e[n]?Zt(`${t} property "${n}" is already defined in ${e[n]}.`):e[n]=t}}();{const[t]=e.propsOptions;if(t)for(const e in t)D("Props",e)}if(u&&function(e,t,n=r){p(e)&&(e=ir(e));for(const o in e){const r=e[o];let s;s=b(r)?"default"in r?gr(r.from||o,r.default,!0):gr(r.from||o):gr(r),At(s)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e}):t[o]=s,n("Inject",o)}}(u,o,D),i)for(const r in i){const e=i[r];g(e)?(Object.defineProperty(o,r,{value:e.bind(n),configurable:!0,enumerable:!0,writable:!0}),D("Methods",r)):Zt(`Method "${r}" has type "${typeof e}" in the component definition. Did you reference the function correctly?`)}if(s){g(s)||Zt("The data option must be a function. Plain object usage is no longer supported.");const t=s.call(n,n);if(_(t)&&Zt("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),b(t)){e.data=yt(t);for(const e in t)D("Data",e),Ko(e[0])||Object.defineProperty(o,e,{configurable:!0,enumerable:!0,get:()=>t[e],set:r})}else Zt("data() should return an object.")}if(Zo=!0,a)for(const p in a){const e=a[p],t=g(e)?e.bind(n,n):g(e.get)?e.get.bind(n,n):r;t===r&&Zt(`Computed property "${p}" has no getter.`);const s=!g(e)&&g(e.set)?e.set.bind(n):()=>{Zt(`Write operation failed: computed property "${p}" is readonly.`)},i=ua({get:t,set:s});Object.defineProperty(o,p,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}),D("Computed",p)}if(l)for(const r in l)nr(l[r],o,n,r);if(c){const e=g(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{mr(t,e[t])}))}function V(e,t){p(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&tr(d,e,"c"),V(Eo,f),V(Oo,h),V(To,m),V(Io,v),V(bo,y),V(_o,w),V(Do,T),V(Lo,E),V(Po,O),V(Ao,x),V(jo,C),V(Ro,I),p(A))if(A.length){const t=e.exposed||(e.exposed={});A.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});$&&e.render===r&&(e.render=$),null!=j&&(e.inheritAttrs=j),R&&(e.components=R),P&&(e.directives=P),I&&po(e)}function tr(e,t,n){rn(p(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function nr(e,t,n,o){let r=o.includes(".")?es(n,o):()=>n[o];if(v(e)){const n=t[e];g(n)?Xr(r,n):Zt(`Invalid watch handler specified by key "${e}"`,n)}else if(g(e))Xr(r,e.bind(n));else if(b(e))if(p(e))e.forEach((e=>nr(e,t,n,o)));else{const o=g(e.handler)?e.handler.bind(n):t[e.handler];g(o)?Xr(r,o,e):Zt(`Invalid watch handler specified by key "${e.handler}"`,o)}else Zt(`Invalid watch option: "${o}"`,e)}function or(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:s,config:{optionMergeStrategies:a}}=e.appContext,i=s.get(t);let l;return i?l=i:r.length||n||o?(l={},r.length&&r.forEach((e=>rr(l,e,a,!0))),rr(l,t,a)):l=t,b(t)&&s.set(t,l),l}function rr(e,t,n,o=!1){const{mixins:r,extends:s}=t;s&&rr(e,s,n,!0),r&&r.forEach((t=>rr(e,t,n,!0)));for(const a in t)if(o&&"expose"===a)Zt('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const o=sr[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const sr={data:ar,props:ur,emits:ur,methods:cr,computed:cr,beforeCreate:lr,created:lr,beforeMount:lr,mounted:lr,beforeUpdate:lr,updated:lr,beforeDestroy:lr,beforeUnmount:lr,destroyed:lr,unmounted:lr,activated:lr,deactivated:lr,errorCaptured:lr,serverPrefetch:lr,components:cr,directives:cr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=l(Object.create(null),e);for(const o in t)n[o]=lr(e[o],t[o]);return n},provide:ar,inject:function(e,t){return cr(ir(e),ir(t))}};function ar(e,t){return t?e?function(){return l(g(e)?e.call(this,this):e,g(t)?t.call(this,this):t)}:t:e}function ir(e){if(p(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function lr(e,t){return e?[...new Set([].concat(e,t))]:t}function cr(e,t){return e?l(Object.create(null),e,t):t}function ur(e,t){return e?p(e)&&p(t)?[...new Set([...e,...t])]:l(Object.create(null),Qo(e),Qo(null!=t?t:{})):t}function dr(){return{app:null,config:{isNativeTag:s,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let pr=0;function fr(e,t){return function(n,o=null){g(n)||(n=l({},n)),null==o||b(o)||(Zt("root props passed to app.mount() must be an object."),o=null);const r=dr(),s=new WeakSet,a=[];let i=!1;const c=r.app={_uid:pr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:pa,get config(){return r.config},set config(e){Zt("app.config cannot be replaced. Modify individual options instead.")},use:(e,...t)=>(s.has(e)?Zt("Plugin has already been applied to target app."):e&&g(e.install)?(s.add(e),e.install(c,...t)):g(e)?(s.add(e),e(c,...t)):Zt('A plugin must either be a function or an object with an "install" function.'),c),mixin:e=>(r.mixins.includes(e)?Zt("Mixin has already been applied to target app"+(e.name?`: ${e.name}`:"")):r.mixins.push(e),c),component:(e,t)=>(Ys(e,r.config),t?(r.components[e]&&Zt(`Component "${e}" has already been registered in target app.`),r.components[e]=t,c):r.components[e]),directive:(e,t)=>(Wn(e),t?(r.directives[e]&&Zt(`Directive "${e}" has already been registered in target app.`),r.directives[e]=t,c):r.directives[e]),mount(s,a,l){if(!i){s.__vue_app__&&Zt("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const u=c._ceVNode||js(n,o);return u.appContext=r,!0===l?l="svg":!1===l&&(l=void 0),r.reload=()=>{const t=Rs(u);t.el=null,e(t,s,l)},a&&t?t(u,s):e(u,s,l),i=!0,c._container=s,s.__vue_app__=c,c._instance=u.component,function(e,t){Rn("app:init",e,t,{Fragment:ms,Text:gs,Comment:vs,Static:ys})}(c,pa),ra(u.component)}Zt("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`")},onUnmount(e){"function"!=typeof e&&Zt("Expected function as first argument to app.onUnmount(), but got "+typeof e),a.push(e)},unmount(){i?(rn(a,c._instance,16),e(null,c._container),c._instance=null,function(e){Rn("app:unmount",e)}(c),delete c._container.__vue_app__):Zt("Cannot unmount an app that is not mounted.")},provide:(e,t)=>(e in r.provides&&(d(r.provides,e)?Zt(`App already provides property with key "${String(e)}". It will be overwritten with the new value.`):Zt(`App already provides property with key "${String(e)}" inherited from its parent element. It will be overwritten with the new value.`)),r.provides[e]=t,c),runWithContext(e){const t=hr;hr=c;try{return e()}finally{hr=t}}};return c}}let hr=null;function mr(e,t){if(qs){let n=qs.provides;const o=qs.parent&&qs.parent.provides;o===n&&(n=qs.provides=Object.create(o)),n[e]=t}else Zt("provide() can only be used inside setup().")}function gr(e,t,n=!1){const o=qs||Bn;if(o||hr){let r=hr?hr._context.provides:o?null==o.parent||o.ce?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&g(t)?t.call(o&&o.proxy):t;Zt(`injection "${String(e)}" not found.`)}else Zt("inject() can only be used inside setup() or functional components.")}const vr={},yr=()=>Object.create(vr),br=e=>Object.getPrototypeOf(e)===vr;function _r(e,t,o,r){const[s,a]=e.propsOptions;let i,l=!1;if(t)for(let n in t){if($(n))continue;const c=t[n];let u;s&&d(s,u=I(n))?a&&a.includes(u)?(i||(i={}))[u]=c:o[u]=c:rs(e.emitsOptions,n)||n in r&&c===r[n]||(r[n]=c,l=!0)}if(a){const t=Et(o),r=i||n;for(let n=0;n<a.length;n++){const i=a[n];o[i]=wr(s,t,i,r[i],e,!d(r,i))}}return l}function wr(e,t,n,o,r,s){const a=e[n];if(null!=a){const e=d(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&!a.skipFactory&&g(e)){const{propsDefaults:s}=r;if(n in s)o=s[n];else{const a=Gs(r);o=s[n]=e.call(null,t),a()}}else o=e;r.ce&&r.ce._setProp(n,o)}a[0]&&(s&&!e?o=!1:!a[1]||""!==o&&o!==j(n)||(o=!0))}return o}const Sr=new WeakMap;function xr(e,t,r=!1){const s=r?Sr:t.propsCache,a=s.get(e);if(a)return a;const i=e.props,c={},u=[];let f=!1;if(!g(e)){const n=e=>{f=!0;const[n,o]=xr(e,t,!0);l(c,n),o&&u.push(...o)};!r&&t.mixins.length&&t.mixins.forEach(n),e.extends&&n(e.extends),e.mixins&&e.mixins.forEach(n)}if(!i&&!f)return b(e)&&s.set(e,o),o;if(p(i))for(let o=0;o<i.length;o++){v(i[o])||Zt("props must be strings when using array syntax.",i[o]);const e=I(i[o]);kr(e)&&(c[e]=n)}else if(i){b(i)||Zt("invalid props options",i);for(const e in i){const t=I(e);if(kr(t)){const n=i[e],o=c[t]=p(n)||g(n)?{type:n}:l({},n),r=o.type;let s=!1,a=!0;if(p(r))for(let e=0;e<r.length;++e){const t=r[e],n=g(t)&&t.name;if("Boolean"===n){s=!0;break}"String"===n&&(a=!1)}else s=g(r)&&"Boolean"===r.name;o[0]=s,o[1]=a,(s||d(o,"default"))&&u.push(t)}}}const h=[c,u];return b(e)&&s.set(e,h),h}function kr(e){return"$"!==e[0]&&!$(e)||(Zt(`Invalid prop name: "${e}" is a reserved property.`),!1)}function Cr(e,t,n){const o=Et(t),r=n.propsOptions[0],s=Object.keys(e).map((e=>I(e)));for(const a in r){let e=r[a];null!=e&&$r(a,o[a],e,wt(o),!s.includes(a))}}function $r(e,t,n,o,r){const{type:s,required:a,validator:i,skipCheck:l}=n;if(a&&r)Zt('Missing required prop: "'+e+'"');else if(null!=t||a){if(null!=s&&!0!==s&&!l){let n=!1;const o=p(s)?s:[s],r=[];for(let e=0;e<o.length&&!n;e++){const{valid:s,expectedType:a}=Or(t,o[e]);r.push(a||""),n=s}if(!n)return void Zt(function(e,t,n){if(0===n.length)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let o=`Invalid prop: type check failed for prop "${e}". Expected ${n.map(R).join(" | ")}`;const r=n[0],s=x(t),a=Tr(t,r),i=Tr(t,s);1===n.length&&Ir(r)&&!function(...e){return e.some((e=>"boolean"===e.toLowerCase()))}(r,s)&&(o+=` with value ${a}`);o+=`, got ${s} `,Ir(s)&&(o+=`with value ${i}.`);return o}(e,t,r))}i&&!i(t,o)&&Zt('Invalid prop: custom validator check failed for prop "'+e+'".')}}const Er=t("String,Number,Boolean,Function,Symbol,BigInt");function Or(e,t){let n;const o=function(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e)return e.constructor&&e.constructor.name||"";return""}(t);if("null"===o)n=null===e;else if(Er(o)){const r=typeof e;n=r===o.toLowerCase(),n||"object"!==r||(n=e instanceof t)}else n="Object"===o?b(e):"Array"===o?p(e):e instanceof t;return{valid:n,expectedType:o}}function Tr(e,t){return"String"===t?`"${e}"`:"Number"===t?`${Number(e)}`:`${e}`}function Ir(e){return["string","number","boolean"].some((t=>e.toLowerCase()===t))}const Ar=e=>"_"===e[0]||"$stable"===e,jr=e=>p(e)?e.map(Vs):[Vs(e)],Rr=(e,t,n)=>{if(t._n)return t;const o=Hn(((...o)=>(!qs||null===n&&Bn||n&&n.root!==qs.root||Zt(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),jr(t(...o)))),n);return o._c=!1,o},Pr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Ar(r))continue;const n=e[r];if(g(n))t[r]=Rr(r,n,o);else if(null!=n){Zt(`Non-function value encountered for slot "${r}". Prefer function slots for better performance.`);const e=jr(n);t[r]=()=>e}}},Lr=(e,t)=>{go(e.vnode)||Zt("Non-function value encountered for default slot. Prefer function slots for better performance.");const n=jr(t);e.slots.default=()=>n},Dr=(e,t,n)=>{for(const o in t)!n&&Ar(o)||(e[o]=t[o])};let Vr,Nr;function Mr(e,t){e.appContext.config.performance&&Fr()&&Nr.mark(`vue-${t}-${e.uid}`),Mn(e,t,Fr()?Nr.now():Date.now())}function Ur(e,t){if(e.appContext.config.performance&&Fr()){const n=`vue-${t}-${e.uid}`,o=n+":end";Nr.mark(o),Nr.measure(`<${la(e,e.type)}> ${t}`,n,o),Nr.clearMarks(n),Nr.clearMarks(o)}Un(e,t,Fr()?Nr.now():Date.now())}function Fr(){return void 0!==Vr||("undefined"!=typeof window&&window.performance?(Vr=!0,Nr=window.performance):Vr=!1),Vr}const Br=function(e,t){t&&t.pendingBranch?p(e)?t.effects.push(...e):t.effects.push(e):vn(e)};function qr(e){return function(e,t){!function(){const e=[];if("boolean"!=typeof __VUE_PROD_HYDRATION_MISMATCH_DETAILS__&&(e.push("__VUE_PROD_HYDRATION_MISMATCH_DETAILS__"),U().__VUE_PROD_HYDRATION_MISMATCH_DETAILS__=!1),e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.\n\nFor more details, see https://link.vuejs.org/feature-flags.`)}}();const s=U();s.__VUE__=!0,Pn(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:a,remove:i,patchProp:l,createElement:c,createText:u,createComment:f,setText:h,setElementText:m,parentNode:g,nextSibling:v,setScopeId:y=r,insertStaticContent:b}=e,w=(e,t,n,o=null,r=null,s=null,a=void 0,i=null,l=!xn&&!!t.dynamicChildren)=>{if(e===t)return;e&&!Os(e,t)&&(o=ne(e),X(e,r,s,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case gs:S(e,t,n,o);break;case vs:x(e,t,n,o);break;case ys:null==e?k(t,n,o,a):C(e,t,n,a);break;case ms:F(e,t,n,o,r,s,a,i,l);break;default:1&d?T(e,t,n,o,r,s,a,i,l):6&d?B(e,t,n,o,r,s,a,i,l):64&d||128&d?c.process(e,t,n,o,r,s,a,i,l,se):Zt("Invalid VNode type:",c,`(${typeof c})`)}null!=u&&r&&ho(u,e&&e.ref,s,t||e,!t)},S=(e,t,n,o)=>{if(null==e)a(t.el=u(t.children),n,o);else{const n=t.el=e.el;t.children!==e.children&&h(n,t.children)}},x=(e,t,n,o)=>{null==e?a(t.el=f(t.children||""),n,o):t.el=e.el},k=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},C=(e,t,n,o)=>{if(t.children!==e.children){const r=v(e.anchor);O(e),[t.el,t.anchor]=b(t.children,n,r,o)}else t.el=e.el,t.anchor=e.anchor},E=({el:e,anchor:t},n,o)=>{let r;for(;e&&e!==t;)r=v(e),a(e,n,o),e=r;a(t,n,o)},O=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),i(e),e=n;i(t)},T=(e,t,n,o,r,s,a,i,l)=>{"svg"===t.type?a="svg":"math"===t.type&&(a="mathml"),null==e?A(t,n,o,r,s,a,i,l):L(e,t,r,s,a,i,l)},A=(e,t,n,o,r,s,i,u)=>{let d,p;const{props:f,shapeFlag:h,transition:g,dirs:v}=e;if(d=e.el=c(e.type,s,f&&f.is,f),8&h?m(d,e.children):16&h&&P(e.children,d,null,o,r,zr(e,s),i,u),v&&Jn(e,null,o,"created"),R(d,e,e.scopeId,i,o),f){for(const e in f)"value"===e||$(e)||l(d,e,null,f[e],s,o);"value"in f&&l(d,"value",null,f.value,s),(p=f.onVnodeBeforeMount)&&Us(p,o,e)}V(d,"__vnode",e,!0),V(d,"__vueParentComponent",o,!0),v&&Jn(e,null,o,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(r,g);y&&g.beforeEnter(d),a(d,t,n),((p=f&&f.onVnodeMounted)||y||v)&&Br((()=>{p&&Us(p,o,e),y&&g.enter(d),v&&Jn(e,null,o,"mounted")}),r)},R=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let s=0;s<o.length;s++)y(e,o[s]);if(r){let n=r.subTree;if(n.patchFlag>0&&2048&n.patchFlag&&(n=cs(n.children)||n),t===n||hs(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=r.vnode;R(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},P=(e,t,n,o,r,s,a,i,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=i?Ns(e[c]):Vs(e[c]);w(null,l,t,n,o,r,s,a,i)}},L=(e,t,o,r,s,a,i)=>{const c=t.el=e.el;c.__vnode=t;let{patchFlag:u,dynamicChildren:d,dirs:p}=t;u|=16&e.patchFlag;const f=e.props||n,h=t.props||n;let g;if(o&&Hr(o,!1),(g=h.onVnodeBeforeUpdate)&&Us(g,o,t,e),p&&Jn(t,e,o,"beforeUpdate"),o&&Hr(o,!0),xn&&(u=0,i=!1,d=null),(f.innerHTML&&null==h.innerHTML||f.textContent&&null==h.textContent)&&m(c,""),d?(N(e.dynamicChildren,d,c,o,r,zr(t,s),a),Wr(e,t)):i||G(e,t,c,null,o,r,zr(t,s),a,!1),u>0){if(16&u)M(c,f,h,o,s);else if(2&u&&f.class!==h.class&&l(c,"class",null,h.class,s),4&u&&l(c,"style",f.style,h.style,s),8&u){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t],r=f[n],a=h[n];a===r&&"value"!==n||l(c,n,r,a,s,o)}}1&u&&e.children!==t.children&&m(c,t.children)}else i||null!=d||M(c,f,h,o,s);((g=h.onVnodeUpdated)||p)&&Br((()=>{g&&Us(g,o,t,e),p&&Jn(t,e,o,"updated")}),r)},N=(e,t,n,o,r,s,a)=>{for(let i=0;i<t.length;i++){const l=e[i],c=t[i],u=l.el&&(l.type===ms||!Os(l,c)||198&l.shapeFlag)?g(l.el):n;w(l,c,u,null,o,r,s,a,!0)}},M=(e,t,o,r,s)=>{if(t!==o){if(t!==n)for(const n in t)$(n)||n in o||l(e,n,t[n],null,s,r);for(const n in o){if($(n))continue;const a=o[n],i=t[n];a!==i&&"value"!==n&&l(e,n,i,a,s,r)}"value"in o&&l(e,"value",t.value,o.value,s)}},F=(e,t,n,o,r,s,i,l,c)=>{const d=t.el=e?e.el:u(""),p=t.anchor=e?e.anchor:u("");let{patchFlag:f,dynamicChildren:h,slotScopeIds:m}=t;(xn||2048&f)&&(f=0,c=!1,h=null),m&&(l=l?l.concat(m):m),null==e?(a(d,n,o),a(p,n,o),P(t.children||[],n,p,r,s,i,l,c)):f>0&&64&f&&h&&e.dynamicChildren?(N(e.dynamicChildren,h,n,r,s,i,l),Wr(e,t)):G(e,t,n,p,r,s,i,l,c)},B=(e,t,n,o,r,s,a,i,l)=>{t.slotScopeIds=i,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,a,l):q(t,n,o,r,s,a,l):z(e,t,l)},q=(e,t,o,s,a,i,l)=>{const c=e.component=function(e,t,o){const s=e.type,a=(t?t.appContext:e.appContext)||Fs,i={uid:Bs++,vnode:e,type:s,parent:t,appContext:a,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ie(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(a.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:xr(s,a),emitsOptions:os(s,a),emit:null,emitted:null,propsDefaults:n,inheritAttrs:s.inheritAttrs,ctx:n,data:n,props:n,attrs:n,slots:n,refs:n,setupState:n,setupContext:null,suspense:o,suspenseId:o?o.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx=function(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(Jo).forEach((n=>{Object.defineProperty(t,n,{configurable:!0,enumerable:!1,get:()=>Jo[n](e),set:r})})),t}(i),i.root=t?t.root:i,i.emit=ns.bind(null,i),e.ce&&e.ce(i);return i}(e,s,a);if(c.type.__hmrId&&function(e){const t=e.type.__hmrId;let n=Cn.get(t);n||($n(t,e.type),n=Cn.get(t)),n.instances.add(e)}(c),Yt(e),Mr(c,"mount"),go(e)&&(c.ctx.renderer=se),Mr(c,"init"),function(e,t=!1,n=!1){t&&Ws(t);const{props:o,children:s}=e.vnode,a=Xs(e);(function(e,t,n,o=!1){const r={},s=yr();e.propsDefaults=Object.create(null),_r(e,t,r,s);for(const a in e.propsOptions[0])a in r||(r[a]=void 0);Cr(t||{},r,e),n?e.props=o?r:bt(r):e.type.props?e.props=r:e.props=s,e.attrs=s})(e,o,a,t),((e,t,n)=>{const o=e.slots=yr();if(32&e.vnode.shapeFlag){const e=t._;e?(Dr(o,t,n),n&&V(o,"_",e,!0)):Pr(t,o)}else t&&Lr(e,t)})(e,s,n||t);const i=a?function(e,t){var n;const o=e.type;o.name&&Ys(o.name,e.appContext.config);if(o.components){const t=Object.keys(o.components);for(let n=0;n<t.length;n++)Ys(t[n],e.appContext.config)}if(o.directives){const e=Object.keys(o.directives);for(let t=0;t<e.length;t++)Wn(e[t])}o.compilerOptions&&ta()&&Zt('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.');e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Xo),function(e){const{ctx:t,propsOptions:[n]}=e;n&&Object.keys(n).forEach((n=>{Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>e.props[n],set:r})}))}(e);const{setup:s}=o;if(s){$e();const r=e.setupContext=s.length>1?function(e){const t=t=>{if(e.exposed&&Zt("expose() should be called only once per setup()."),null!=t){let e=typeof t;"object"===e&&(p(t)?e="array":At(t)&&(e="ref")),"object"!==e&&Zt(`expose() should be passed a plain object, received ${e}.`)}e.exposed=t||{}};{let n,o;return Object.freeze({get attrs(){return n||(n=new Proxy(e.attrs,oa))},get slots(){return o||(o=function(e){return new Proxy(e.slots,{get:(t,n)=>(Ve(e,"get","$slots"),t[n])})}(e))},get emit(){return(t,...n)=>e.emit(t,...n)},expose:t})}}(e):null,a=Gs(e),i=on(s,e,0,[wt(e.props),r]),l=_(i);if(Ee(),a(),!l&&!e.sp||mo(e)||po(e),l){if(i.then(Js,Js),t)return i.then((n=>{ea(e,n,t)})).catch((t=>{sn(t,e,0)}));if(e.asyncDep=i,!e.suspense){Zt(`Component <${null!=(n=o.name)?n:"Anonymous"}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else ea(e,i,t)}else na(e,t)}(e,t):void 0;t&&Ws(!1)}(c,!1,l),Ur(c,"init"),xn&&(e.el=null),c.asyncDep){if(a&&a.registerDep(c,H,l),!e.el){const e=c.subTree=js(vs);x(null,e,t,o)}}else H(c,e,t,o,a,i,l);Xt(),Ur(c,"mount")},z=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:s}=e,{props:a,children:i,patchFlag:l}=t,c=s.emitsOptions;if((r||i)&&xn)return!0;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!i||i&&i.$stable)||o!==a&&(o?!a||fs(o,a,c):!!a);if(1024&l)return!0;if(16&l)return o?fs(o,a,c):!!a;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==o[n]&&!rs(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return Yt(t),W(o,t,n),void Xt();o.next=t,o.update()}else t.el=e.el,o.vnode=t},H=(e,t,n,o,r,s,a)=>{const i=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:l,vnode:c}=e;{const n=Gr(e);if(n)return t&&(t.el=c.el,W(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||i()}))}let u,d=t;Yt(t||e.vnode),Hr(e,!1),t?(t.el=c.el,W(e,t,a)):t=c,n&&D(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Us(u,l,t,c),Hr(e,!0),Mr(e,"render");const p=is(e);Ur(e,"render");const f=e.subTree;e.subTree=p,Mr(e,"patch"),w(f,p,g(f.el),ne(f),e,r,s),Ur(e,"patch"),t.el=p.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,p.el),o&&Br(o,r),(u=t.props&&t.props.onVnodeUpdated)&&Br((()=>Us(u,l,t,c)),r),Dn(e),Xt()}else{let a;const{el:i,props:l}=t,{bm:c,m:u,parent:d,root:p,type:f}=e,h=mo(t);if(Hr(e,!1),c&&D(c),!h&&(a=l&&l.onVnodeBeforeMount)&&Us(a,d,t),Hr(e,!0),i&&le){const t=()=>{Mr(e,"render"),e.subTree=is(e),Ur(e,"render"),Mr(e,"hydrate"),le(i,e.subTree,e,r,null),Ur(e,"hydrate")};h&&f.__asyncHydrate?f.__asyncHydrate(i,e,t):t()}else{p.ce&&p.ce._injectChildStyle(f),Mr(e,"render");const a=e.subTree=is(e);Ur(e,"render"),Mr(e,"patch"),w(null,a,n,o,e,r,s),Ur(e,"patch"),t.el=a.el}if(u&&Br(u,r),!h&&(a=l&&l.onVnodeMounted)){const e=t;Br((()=>Us(a,d,e)),r)}(256&t.shapeFlag||d&&mo(d.vnode)&&256&d.vnode.shapeFlag)&&e.a&&Br(e.a,r),e.isMounted=!0,Ln(e),t=n=o=null}};e.scope.on();const l=e.effect=new de(i);e.scope.off();const c=e.update=l.run.bind(l),u=e.job=l.runIfDirty.bind(l);u.i=e,u.id=e.uid,l.scheduler=()=>mn(u),Hr(e,!0),l.onTrack=e.rtc?t=>D(e.rtc,t):void 0,l.onTrigger=e.rtg?t=>D(e.rtg,t):void 0,c()},W=(e,t,o)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:s,vnode:{patchFlag:a}}=e,i=Et(r),[l]=e.propsOptions;let c=!1;if(function(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}(e)||!(o||a>0)||16&a){let o;_r(e,t,r,s)&&(c=!0);for(const s in i)t&&(d(t,s)||(o=j(s))!==s&&d(t,o))||(l?!n||void 0===n[s]&&void 0===n[o]||(r[s]=wr(l,i,s,void 0,e,!0)):delete r[s]);if(s!==i)for(const e in s)t&&d(t,e)||(delete s[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(rs(e.emitsOptions,a))continue;const u=t[a];if(l)if(d(s,a))u!==s[a]&&(s[a]=u,c=!0);else{const t=I(a);r[t]=wr(l,i,t,u,e,!1)}else u!==s[a]&&(s[a]=u,c=!0)}}c&&Ne(e.attrs,"set",""),Cr(t||{},r,e)}(e,t.props,r,o),((e,t,o)=>{const{vnode:r,slots:s}=e;let a=!0,i=n;if(32&r.shapeFlag){const n=t._;n?xn?(Dr(s,t,o),Ne(e,"set","$slots")):o&&1===n?a=!1:Dr(s,t,o):(a=!t.$stable,Pr(t,s)),i=t}else t&&(Lr(e,t),i={default:1});if(a)for(const n in s)Ar(n)||null!=i[n]||delete s[n]})(e,t.children,o),$e(),yn(e),Ee()},G=(e,t,n,o,r,s,a,i,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:f}=t;if(p>0){if(128&p)return void K(c,d,n,o,r,s,a,i,l);if(256&p)return void J(c,d,n,o,r,s,a,i,l)}8&f?(16&u&&te(c,r,s),d!==c&&m(n,d)):16&u?16&f?K(c,d,n,o,r,s,a,i,l):te(c,r,s,!0):(8&u&&m(n,""),16&f&&P(d,n,o,r,s,a,i,l))},J=(e,t,n,r,s,a,i,l,c)=>{t=t||o;const u=(e=e||o).length,d=t.length,p=Math.min(u,d);let f;for(f=0;f<p;f++){const o=t[f]=c?Ns(t[f]):Vs(t[f]);w(e[f],o,n,null,s,a,i,l,c)}u>d?te(e,s,a,!0,!1,p):P(t,n,r,s,a,i,l,c,p)},K=(e,t,n,r,s,a,i,l,c)=>{let u=0;const d=t.length;let p=e.length-1,f=d-1;for(;u<=p&&u<=f;){const o=e[u],r=t[u]=c?Ns(t[u]):Vs(t[u]);if(!Os(o,r))break;w(o,r,n,null,s,a,i,l,c),u++}for(;u<=p&&u<=f;){const o=e[p],r=t[f]=c?Ns(t[f]):Vs(t[f]);if(!Os(o,r))break;w(o,r,n,null,s,a,i,l,c),p--,f--}if(u>p){if(u<=f){const e=f+1,o=e<d?t[e].el:r;for(;u<=f;)w(null,t[u]=c?Ns(t[u]):Vs(t[u]),n,o,s,a,i,l,c),u++}}else if(u>f)for(;u<=p;)X(e[u],s,a,!0),u++;else{const h=u,m=u,g=new Map;for(u=m;u<=f;u++){const e=t[u]=c?Ns(t[u]):Vs(t[u]);null!=e.key&&(g.has(e.key)&&Zt("Duplicate keys found during update:",JSON.stringify(e.key),"Make sure keys are unique."),g.set(e.key,u))}let v,y=0;const b=f-m+1;let _=!1,S=0;const x=new Array(b);for(u=0;u<b;u++)x[u]=0;for(u=h;u<=p;u++){const o=e[u];if(y>=b){X(o,s,a,!0);continue}let r;if(null!=o.key)r=g.get(o.key);else for(v=m;v<=f;v++)if(0===x[v-m]&&Os(o,t[v])){r=v;break}void 0===r?X(o,s,a,!0):(x[r-m]=u+1,r>=S?S=r:_=!0,w(o,t[r],n,null,s,a,i,l,c),y++)}const k=_?function(e){const t=e.slice(),n=[0];let o,r,s,a,i;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(s=0,a=n.length-1;s<a;)i=s+a>>1,e[n[i]]<l?s=i+1:a=i;l<e[n[s]]&&(s>0&&(t[o]=n[s-1]),n[s]=o)}}s=n.length,a=n[s-1];for(;s-- >0;)n[s]=a,a=t[a];return n}(x):o;for(v=k.length-1,u=b-1;u>=0;u--){const e=m+u,o=t[e],p=e+1<d?t[e+1].el:r;0===x[u]?w(null,o,n,p,s,a,i,l,c):_&&(v<0||u!==k[v]?Y(o,n,p,2):v--)}}},Y=(e,t,n,o,r=null)=>{const{el:s,type:l,transition:c,children:u,shapeFlag:d}=e;if(6&d)return void Y(e.component.subTree,t,n,o);if(128&d)return void e.suspense.move(t,n,o);if(64&d)return void l.move(e,t,n,se);if(l===ms){a(s,t,n);for(let e=0;e<u.length;e++)Y(u[e],t,n,o);return void a(e.anchor,t,n)}if(l===ys)return void E(e,t,n);if(2!==o&&1&d&&c)if(0===o)c.beforeEnter(s),a(s,t,n),Br((()=>c.enter(s)),r);else{const{leave:o,delayLeave:r,afterLeave:l}=c,u=()=>{e.ctx.isUnmounted?i(s):a(s,t,n)},d=()=>{o(s,(()=>{u(),l&&l()}))};r?r(s,u,d):d()}else a(s,t,n)},X=(e,t,n,o=!1,r=!1)=>{const{type:s,props:a,ref:i,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:p,cacheIndex:f}=e;if(-2===d&&(r=!1),null!=i&&($e(),ho(i,null,n,e,!0),Ee()),null!=f&&(t.renderCache[f]=void 0),256&u)return void t.ctx.deactivate(e);const h=1&u&&p,m=!mo(e);let g;if(m&&(g=a&&a.onVnodeBeforeUnmount)&&Us(g,t,e),6&u)ee(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);h&&Jn(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,se,o):c&&!c.hasOnce&&(s!==ms||d>0&&64&d)?te(c,t,n,!1,!0):(s===ms&&384&d||!r&&16&u)&&te(l,t,n),o&&Q(e)}(m&&(g=a&&a.onVnodeUnmounted)||h)&&Br((()=>{g&&Us(g,t,e),h&&Jn(e,null,t,"unmounted")}),n)},Q=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===ms)return void(e.patchFlag>0&&2048&e.patchFlag&&r&&!r.persisted?e.children.forEach((e=>{e.type===vs?i(e.el):Q(e)})):Z(n,o));if(t===ys)return void O(e);const s=()=>{i(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,a=()=>t(n,s);o?o(e.el,s,a):a()}else s()},Z=(e,t)=>{let n;for(;e!==t;)n=v(e),i(e),e=n;i(t)},ee=(e,t,n)=>{e.type.__hmrId&&function(e){Cn.get(e.type.__hmrId).instances.delete(e)}(e);const{bum:o,scope:r,job:s,subTree:a,um:i,m:l,a:c,parent:u,slots:{__:d}}=e;var f;Jr(l),Jr(c),o&&D(o),u&&p(d)&&d.forEach((e=>{u.renderCache[e]=void 0})),r.stop(),s&&(s.flags|=8,X(a,e,t,n)),i&&Br(i,t),Br((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve()),f=e,In&&"function"==typeof In.cleanupBuffer&&!In.cleanupBuffer(f)&&Vn(f)},te=(e,t,n,o=!1,r=!1,s=0)=>{for(let a=s;a<e.length;a++)X(e[a],t,n,o,r)},ne=e=>{if(6&e.shapeFlag)return ne(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=v(e.anchor||e.el),n=t&&t[Kn];return n?v(n):t};let oe=!1;const re=(e,t,n)=>{null==e?t._vnode&&X(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),t._vnode=e,oe||(oe=!0,yn(),bn(),oe=!1)},se={p:w,um:X,m:Y,r:Q,mt:q,mc:P,pc:G,pbc:N,n:ne,o:e};let ae,le;t&&([ae,le]=t(se));return{render:re,hydrate:ae,createApp:fr(re,ae)}}(e)}function zr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Hr({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Wr(e,t,n=!1){const o=e.children,r=t.children;if(p(o)&&p(r))for(let s=0;s<o.length;s++){const e=o[s];let t=r[s];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[s]=Ns(r[s]),t.el=e.el),n||-2===t.patchFlag||Wr(e,t)),t.type===gs&&(t.el=e.el),t.type!==vs||t.el||(t.el=e.el),t.el&&(t.el.__vnode=t)}}function Gr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Gr(t)}function Jr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Kr=Symbol.for("v-scx"),Yr=()=>{{const e=gr(Kr);return e||Zt("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Xr(e,t,n){return g(t)||Zt("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Qr(e,t,n)}function Qr(e,t,o=n){const{immediate:s,deep:a,flush:i,once:c}=o;t||(void 0!==s&&Zt('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),void 0!==a&&Zt('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),void 0!==c&&Zt('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const u=l({},o);u.onWarn=Zt;const d=t&&s||!t&&"post"!==i;let p;if(Zs)if("sync"===i){const e=Yr();p=e.__watcherHandles||(e.__watcherHandles=[])}else if(!d){const e=()=>{};return e.stop=r,e.resume=r,e.pause=r,e}const f=qs;u.call=(e,t,n)=>rn(e,f,t,n);let h=!1;"post"===i?u.scheduler=e=>{Br(e,f&&f.suspense)}:"sync"!==i&&(h=!0,u.scheduler=(e,t)=>{t?e():mn(e)}),u.augmentJob=e=>{t&&(e.flags|=4),h&&(e.flags|=2,f&&(e.id=f.uid,e.i=f))};const m=Gt(e,t,u);return Zs&&(p?p.push(m):d&&m()),m}function Zr(e,t,n){const o=this.proxy,r=v(e)?e.includes(".")?es(o,e):()=>o[e]:e.bind(o,o);let s;g(t)?s=t:(s=t.handler,n=t);const a=Gs(this),i=Qr(r,s.bind(o),n);return a(),i}function es(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}const ts=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${I(t)}Modifiers`]||e[`${j(t)}Modifiers`];function ns(e,t,...o){if(e.isUnmounted)return;const r=e.vnode.props||n;{const{emitsOptions:n,propsOptions:[r]}=e;if(n)if(t in n){const e=n[t];if(g(e)){e(...o)||Zt(`Invalid event arguments: event validation failed for event "${t}".`)}}else r&&P(I(t))in r||Zt(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${P(I(t))}" prop.`)}let s=o;const a=t.startsWith("update:"),i=a&&ts(r,t.slice(7));i&&(i.trim&&(s=o.map((e=>v(e)?e.trim():e))),i.number&&(s=o.map(N))),function(e,t,n){Rn("component:emit",e.appContext.app,e,t,n)}(e,t,s);{const n=t.toLowerCase();n!==t&&r[P(n)]&&Zt(`Event "${n}" is emitted in component ${la(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${j(t)}" instead of "${t}".`)}let l,c=r[l=P(t)]||r[l=P(I(t))];!c&&a&&(c=r[l=P(j(t))]),c&&rn(c,e,6,s);const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,rn(u,e,6,s)}}function os(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const s=e.emits;let a={},i=!1;if(!g(e)){const o=e=>{const n=os(e,t,!0);n&&(i=!0,l(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return s||i?(p(s)?s.forEach((e=>a[e]=null)):l(a,s),b(e)&&o.set(e,a),a):(b(e)&&o.set(e,null),null)}function rs(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),d(e,t[0].toLowerCase()+t.slice(1))||d(e,j(t))||d(e,t))}let ss=!1;function as(){ss=!0}function is(e){const{type:t,vnode:n,proxy:o,withProxy:r,propsOptions:[s],slots:l,attrs:c,emit:u,render:d,renderCache:p,props:f,data:h,setupState:m,ctx:g,inheritAttrs:v}=e,y=zn(e);let b,_;ss=!1;try{if(4&n.shapeFlag){const e=r||o,t=m.__isScriptSetup?new Proxy(e,{get:(e,t,n)=>(Zt(`Property '${String(t)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(e,t,n))}):e;b=Vs(d.call(t,e,p,wt(f),m,h,g)),_=c}else{const e=t;c===f&&as(),b=Vs(e.length>1?e(wt(f),{get attrs(){return as(),wt(c)},slots:l,emit:u}):e(wt(f),null)),_=t.props?c:us(c)}}catch(x){bs.length=0,sn(x,e,1),b=js(vs)}let w,S=b;if(b.patchFlag>0&&2048&b.patchFlag&&([S,w]=ls(b)),_&&!1!==v){const e=Object.keys(_),{shapeFlag:t}=S;if(e.length)if(7&t)s&&e.some(i)&&(_=ds(_,s)),S=Rs(S,_,!1,!0);else if(!ss&&S.type!==vs){const e=Object.keys(c),t=[],n=[];for(let o=0,r=e.length;o<r;o++){const r=e[o];a(r)?i(r)||t.push(r[2].toLowerCase()+r.slice(3)):n.push(r)}n.length&&Zt(`Extraneous non-props attributes (${n.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),t.length&&Zt(`Extraneous non-emits event listeners (${t.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}return n.dirs&&(ps(S)||Zt("Runtime directive used on component with non-element root node. The directives will not function as intended."),S=Rs(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&(ps(S)||Zt("Component inside <Transition> renders non-element root node that cannot be animated."),lo(S,n.transition)),w?w(S):b=S,zn(y),b}const ls=e=>{const t=e.children,n=e.dynamicChildren,o=cs(t,!1);if(!o)return[e,void 0];if(o.patchFlag>0&&2048&o.patchFlag)return ls(o);const r=t.indexOf(o),s=n?n.indexOf(o):-1;return[Vs(o),o=>{t[r]=o,n&&(s>-1?n[s]=o:o.patchFlag>0&&(e.dynamicChildren=[...n,o]))}]};function cs(e,t=!0){let n;for(let o=0;o<e.length;o++){const r=e[o];if(!Es(r))return;if(r.type!==vs||"v-if"===r.children){if(n)return;if(n=r,t&&n.patchFlag>0&&2048&n.patchFlag)return cs(n.children)}}return n}const us=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},ds=(e,t)=>{const n={};for(const o in e)i(o)&&o.slice(9)in t||(n[o]=e[o]);return n},ps=e=>7&e.shapeFlag||e.type===vs;function fs(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const s=o[r];if(t[s]!==e[s]&&!rs(n,s))return!0}return!1}const hs=e=>e.__isSuspense;const ms=Symbol.for("v-fgt"),gs=Symbol.for("v-txt"),vs=Symbol.for("v-cmt"),ys=Symbol.for("v-stc"),bs=[];let _s=null;function ws(e=!1){bs.push(_s=e?null:[])}let Ss=1;function xs(e,t=!1){Ss+=e,e<0&&_s&&t&&(_s.hasOnce=!0)}function ks(e){return e.dynamicChildren=Ss>0?_s||o:null,bs.pop(),_s=bs[bs.length-1]||null,Ss>0&&_s&&_s.push(e),e}function Cs(e,t,n,o,r,s){return ks(As(e,t,n,o,r,s,!0))}function $s(e,t,n,o,r){return ks(js(e,t,n,o,r,!0))}function Es(e){return!!e&&!0===e.__v_isVNode}function Os(e,t){if(6&t.shapeFlag&&e.component){const n=kn.get(t.type);if(n&&n.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const Ts=({key:e})=>null!=e?e:null,Is=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||At(e)||g(e)?{i:Bn,r:e,k:t,f:!!n}:e:null);function As(e,t=null,n=null,o=0,r=null,s=(e===ms?0:1),a=!1,i=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ts(t),ref:t&&Is(t),scopeId:qn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Bn};return i?(Ms(l,n),128&s&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),l.key!=l.key&&Zt("VNode created with invalid key (NaN). VNode type:",l.type),Ss>0&&!a&&_s&&(l.patchFlag>0||6&s)&&32!==l.patchFlag&&_s.push(l),l}const js=(...e)=>function(e,t=null,n=null,o=0,r=null,s=!1){e&&e!==Mo||(e||Zt(`Invalid vnode type when creating vnode: ${e}.`),e=vs);if(Es(e)){const o=Rs(e,t,!0);return n&&Ms(o,n),Ss>0&&!s&&_s&&(6&o.shapeFlag?_s[_s.indexOf(e)]=o:_s.push(o)),o.patchFlag=-2,o}ca(e)&&(e=e.__vccOpts);if(t){t=function(e){return e?$t(e)||br(e)?l({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=W(e)),b(n)&&($t(n)&&!p(n)&&(n=l({},n)),t.style=F(n))}const a=v(e)?1:hs(e)?128:Yn(e)?64:b(e)?4:g(e)?2:0;4&a&&$t(e)&&Zt("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.","\nComponent that was made reactive: ",e=Et(e));return As(e,t,n,o,r,a,s,!0)}(...e);function Rs(e,t,n=!1,o=!1){const{props:r,ref:s,patchFlag:i,children:l,transition:c}=e,u=t?function(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=W([t.class,o.class]));else if("style"===e)t.style=F([t.style,o.style]);else if(a(e)){const n=t[e],r=o[e];!r||n===r||p(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}(r||{},t):r,d={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Ts(u),ref:t&&t.ref?n&&s?p(s)?s.concat(Is(t)):[s,Is(t)]:Is(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:-1===i&&p(l)?l.map(Ps):l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ms?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Rs(e.ssContent),ssFallback:e.ssFallback&&Rs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&o&&lo(d,c.clone(d)),d}function Ps(e){const t=Rs(e);return p(e.children)&&(t.children=e.children.map(Ps)),t}function Ls(e=" ",t=0){return js(gs,null,e,t)}function Ds(e="",t=!1){return t?(ws(),$s(vs,null,e)):js(vs,null,e)}function Vs(e){return null==e||"boolean"==typeof e?js(vs):p(e)?js(ms,null,e.slice()):Es(e)?Ns(e):js(gs,null,String(e))}function Ns(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Rs(e)}function Ms(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(p(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),Ms(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||br(t)?3===o&&Bn&&(1===Bn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Bn}}else g(t)?(t={default:t,_ctx:Bn},n=32):(t=String(t),64&o?(n=16,t=[Ls(t)]):n=8);e.children=t,e.shapeFlag|=n}function Us(e,t,n,o=null){rn(e,t,7,[n,o])}const Fs=dr();let Bs=0;let qs=null;const zs=()=>qs||Bn;let Hs,Ws;{const e=U(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};Hs=t("__VUE_INSTANCE_SETTERS__",(e=>qs=e)),Ws=t("__VUE_SSR_SETTERS__",(e=>Zs=e))}const Gs=e=>{const t=qs;return Hs(e),e.scope.on(),()=>{e.scope.off(),Hs(t)}},Js=()=>{qs&&qs.scope.off(),Hs(null)},Ks=t("slot,component");function Ys(e,{isNativeTag:t}){(Ks(e)||t(e))&&Zt("Do not use built-in or reserved HTML elements as component id: "+e)}function Xs(e){return 4&e.vnode.shapeFlag}let Qs,Zs=!1;function ea(e,t,n){g(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:b(t)?(Es(t)&&Zt("setup() should not return VNodes directly - return a render function instead."),e.devtoolsRawSetupState=t,e.setupState=Vt(t),function(e){const{ctx:t,setupState:n}=e;Object.keys(Et(n)).forEach((e=>{if(!n.__isScriptSetup){if(Ko(e[0]))return void Zt(`setup() return property ${JSON.stringify(e)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);Object.defineProperty(t,e,{enumerable:!0,configurable:!0,get:()=>n[e],set:r})}}))}(e)):void 0!==t&&Zt("setup() should return an object. Received: "+(null===t?"null":typeof t)),na(e,n)}const ta=()=>!Qs;function na(e,t,n){const o=e.type;if(!e.render){if(!t&&Qs&&!o.render){const t=o.template||or(e).template;if(t){Mr(e,"compile");const{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:s,compilerOptions:a}=o,i=l(l({isCustomElement:n,delimiters:s},r),a);o.render=Qs(t,i),Ur(e,"compile")}}e.render=o.render||r}{const t=Gs(e);$e();try{er(e)}finally{Ee(),t()}}o.render||e.render!==r||t||(o.template?Zt('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Zt("Component is missing template or render function: ",o))}const oa={get:(e,t)=>(as(),Ve(e,"get",""),e[t]),set:()=>(Zt("setupContext.attrs is readonly."),!1),deleteProperty:()=>(Zt("setupContext.attrs is readonly."),!1)};function ra(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Vt(Ot(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Jo?Jo[n](e):void 0,has:(e,t)=>t in e||t in Jo})):e.proxy}const sa=/(?:^|[-_])(\w)/g,aa=e=>e.replace(sa,(e=>e.toUpperCase())).replace(/[-_]/g,"");function ia(e,t=!0){return g(e)?e.displayName||e.name:e.name||t&&e.__name}function la(e,t,n=!1){let o=ia(t);if(!o&&t.__file){const e=t.__file.match(/([^/\\]+)\.\w+$/);e&&(o=e[1])}if(!o&&e&&e.parent){const n=e=>{for(const n in e)if(e[n]===t)return n};o=n(e.components||e.parent.type.components)||n(e.appContext.components)}return o?aa(o):n?"App":"Anonymous"}function ca(e){return g(e)&&"__vccOpts"in e}const ua=(e,t)=>{const n=function(e,t,n=!1){let o,r;g(e)?o=e:(o=e.get,r=e.set);const s=new qt(o,r,n);return t&&!n&&(s.onTrack=t.onTrack,s.onTrigger=t.onTrigger),s}(e,t,Zs);{const e=zs();e&&e.appContext.config.warnRecursiveComputed&&(n._warnRecursive=!0)}return n};function da(e,t,n){const o=arguments.length;return 2===o?b(t)&&!p(t)?Es(t)?js(e,null,[t]):js(e,t):js(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Es(n)&&(n=[n]),js(e,t,n))}const pa="3.5.16",fa=Zt;
/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let ha;const ma="undefined"!=typeof window&&window.trustedTypes;if(ma)try{ha=ma.createPolicy("vue",{createHTML:e=>e})}catch(Yh){fa(`Error creating trusted types policy: ${Yh}`)}const ga=ha?e=>ha.createHTML(e):e=>e,va="undefined"!=typeof document?document:null,ya=va&&va.createElement("template"),ba={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?va.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?va.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?va.createElement(e,{is:n}):va.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>va.createTextNode(e),createComment:e=>va.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>va.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,s){const a=n?n.previousSibling:t.lastChild;if(r&&(r===s||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==s&&(r=r.nextSibling););else{ya.innerHTML=ga("svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e);const r=ya.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},_a="transition",wa="animation",Sa=Symbol("_vtc"),xa={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},ka=l({},eo,xa),Ca=(e=>(e.displayName="Transition",e.props=ka,e))(((e,{slots:t})=>da(oo,function(e){const t={};for(const l in e)l in xa||(t[l]=e[l]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:s=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:i=`${n}-enter-to`,appearFromClass:c=s,appearActiveClass:u=a,appearToClass:d=i,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(b(e))return[Oa(e.enter),Oa(e.leave)];{const t=Oa(e);return[t,t]}}(r),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:_,onEnterCancelled:w,onLeave:S,onLeaveCancelled:x,onBeforeAppear:k=y,onAppear:C=_,onAppearCancelled:$=w}=t,E=(e,t,n,o)=>{e._enterCancelled=o,Ia(e,t?d:i),Ia(e,t?u:a),n&&n()},O=(e,t)=>{e._isLeaving=!1,Ia(e,p),Ia(e,h),Ia(e,f),t&&t()},T=e=>(t,n)=>{const r=e?C:_,a=()=>E(t,e,n);$a(r,[t,a]),Aa((()=>{Ia(t,e?c:s),Ta(t,e?d:i),Ea(r)||Ra(t,o,g,a)}))};return l(t,{onBeforeEnter(e){$a(y,[e]),Ta(e,s),Ta(e,a)},onBeforeAppear(e){$a(k,[e]),Ta(e,c),Ta(e,u)},onEnter:T(!1),onAppear:T(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>O(e,t);Ta(e,p),e._enterCancelled?(Ta(e,f),Da()):(Da(),Ta(e,f)),Aa((()=>{e._isLeaving&&(Ia(e,p),Ta(e,h),Ea(S)||Ra(e,o,v,n))})),$a(S,[e,n])},onEnterCancelled(e){E(e,!1,void 0,!0),$a(w,[e])},onAppearCancelled(e){E(e,!0,void 0,!0),$a($,[e])},onLeaveCancelled(e){O(e),$a(x,[e])}})}(e),t))),$a=(e,t=[])=>{p(e)?e.forEach((e=>e(...t))):e&&e(...t)},Ea=e=>!!e&&(p(e)?e.some((e=>e.length>1)):e.length>1);function Oa(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return function(e,t){void 0!==e&&("number"!=typeof e?Zt(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Zt(`${t} is NaN - the duration expression might be incorrect.`))}(t,"<transition> explicit duration"),t}function Ta(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Sa]||(e[Sa]=new Set)).add(t)}function Ia(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Sa];n&&(n.delete(t),n.size||(e[Sa]=void 0))}function Aa(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let ja=0;function Ra(e,t,n,o){const r=e._endId=++ja,s=()=>{r===e._endId&&o()};if(null!=n)return setTimeout(s,n);const{type:a,timeout:i,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o(`${_a}Delay`),s=o(`${_a}Duration`),a=Pa(r,s),i=o(`${wa}Delay`),l=o(`${wa}Duration`),c=Pa(i,l);let u=null,d=0,p=0;t===_a?a>0&&(u=_a,d=a,p=s.length):t===wa?c>0&&(u=wa,d=c,p=l.length):(d=Math.max(a,c),u=d>0?a>c?_a:wa:null,p=u?u===_a?s.length:l.length:0);const f=u===_a&&/\b(transform|all)(,|$)/.test(o(`${_a}Property`).toString());return{type:u,timeout:d,propCount:p,hasTransform:f}}(e,t);if(!a)return o();const c=a+"end";let u=0;const d=()=>{e.removeEventListener(c,p),s()},p=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),i+1),e.addEventListener(c,p)}function Pa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>La(t)+La(e[n]))))}function La(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function Da(){return document.body.offsetHeight}const Va=Symbol("_vod"),Na=Symbol("_vsh"),Ma={beforeMount(e,{value:t},{transition:n}){e[Va]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ua(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ua(e,!0),o.enter(e)):o.leave(e,(()=>{Ua(e,!1)})):Ua(e,t))},beforeUnmount(e,{value:t}){Ua(e,t)}};function Ua(e,t){e.style.display=t?e[Va]:"none",e[Na]=!t}Ma.name="show";const Fa=Symbol("CSS_VAR_TEXT");function Ba(e){const t=zs();if(!t)return void fa("useCssVars is called without current active component instance.");const n=t.ut=(n=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach((e=>za(e,n)))};t.getCssVars=()=>e(t.proxy);const o=()=>{const o=e(t.proxy);t.ce?za(t.ce,o):qa(t.subTree,o),n(o)};To((()=>{vn(o)})),Oo((()=>{Xr(o,r,{flush:"post"});const e=new MutationObserver(o);e.observe(t.subTree.el.parentNode,{childList:!0}),jo((()=>e.disconnect()))}))}function qa(e,t){if(128&e.shapeFlag){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push((()=>{qa(n.activeBranch,t)}))}for(;e.component;)e=e.component.subTree;if(1&e.shapeFlag&&e.el)za(e.el,t);else if(e.type===ms)e.children.forEach((e=>qa(e,t)));else if(e.type===ys){let{el:n,anchor:o}=e;for(;n&&(za(n,t),n!==o);)n=n.nextSibling}}function za(e,t){if(1===e.nodeType){const n=e.style;let o="";for(const e in t)n.setProperty(`--${e}`,t[e]),o+=`--${e}: ${t[e]};`;n[Fa]=o}}const Ha=/(^|;)\s*display\s*:/;const Wa=/[^\\];\s*$/,Ga=/\s*!important$/;function Ja(e,t,n){if(p(n))n.forEach((n=>Ja(e,t,n)));else if(null==n&&(n=""),Wa.test(n)&&fa(`Unexpected semicolon at the end of '${t}' style value: '${n}'`),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Ya[t];if(n)return n;let o=I(t);if("filter"!==o&&o in e)return Ya[t]=o;o=R(o);for(let r=0;r<Ka.length;r++){const n=Ka[r]+o;if(n in e)return Ya[t]=n}return t}(e,t);Ga.test(n)?e.setProperty(j(o),n.replace(Ga,""),"important"):e[o]=n}}const Ka=["Webkit","Moz","ms"],Ya={};const Xa="http://www.w3.org/1999/xlink";function Qa(e,t,n,o,r,s=Y(t)){o&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(Xa,t.slice(6,t.length)):e.setAttributeNS(Xa,t,n):null==n||s&&!X(n)?e.removeAttribute(t):e.setAttribute(t,s?"":y(n)?String(n):n)}function Za(e,t,n,o,r){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?ga(n):n));const s=e.tagName;if("value"===t&&"PROGRESS"!==s&&!s.includes("-")){const o="OPTION"===s?e.getAttribute("value")||"":e.value,r=null==n?"checkbox"===e.type?"on":"":String(n);return o===r&&"_value"in e||(e.value=r),null==n&&e.removeAttribute(t),void(e._value=n)}let a=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=X(n):null==n&&"string"===o?(n="",a=!0):"number"===o&&(n=0,a=!0)}try{e[t]=n}catch(Yh){a||fa(`Failed setting prop "${t}" on <${s.toLowerCase()}>: value ${n} is invalid.`,Yh)}a&&e.removeAttribute(r||t)}function ei(e,t,n,o){e.addEventListener(t,n,o)}const ti=Symbol("_vei");function ni(e,t,n,o,r=null){const s=e[ti]||(e[ti]={}),a=s[t];if(o&&a)a.value=ii(o,t);else{const[n,i]=function(e){let t;if(oi.test(e)){let n;for(t={};n=e.match(oi);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):j(e.slice(2));return[n,t]}(t);if(o){const a=s[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();rn(function(e,t){if(p(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=ai(),n}(ii(o,t),r);ei(e,n,a,i)}else a&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,a,i),s[t]=void 0)}}const oi=/(?:Once|Passive|Capture)$/;let ri=0;const si=Promise.resolve(),ai=()=>ri||(si.then((()=>ri=0)),ri=Date.now());function ii(e,t){return g(e)||p(e)?e:(fa(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?\nExpected function or array of functions, received type ${typeof e}.`),r)}const li=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const ci=e=>{const t=e.props["onUpdate:modelValue"]||!1;return p(t)?e=>D(t,e):t},ui=Symbol("_assign"),di={deep:!0,created(e,t,n){e[ui]=ci(n),ei(e,"change",(()=>{const t=e._modelValue,n=gi(e),o=e.checked,r=e[ui];if(p(t)){const e=Z(t,n),s=-1!==e;if(o&&!s)r(t.concat(n));else if(!o&&s){const n=[...t];n.splice(e,1),r(n)}}else if(h(t)){const e=new Set(t);o?e.add(n):e.delete(n),r(e)}else r(vi(e,o))}))},mounted:pi,beforeUpdate(e,t,n){e[ui]=ci(n),pi(e,t,n)}};function pi(e,{value:t,oldValue:n},o){let r;if(e._modelValue=t,p(t))r=Z(t,o.props.value)>-1;else if(h(t))r=t.has(o.props.value);else{if(t===n)return;r=Q(t,vi(e,!0))}e.checked!==r&&(e.checked=r)}const fi={created(e,{value:t},n){e.checked=Q(t,n.props.value),e[ui]=ci(n),ei(e,"change",(()=>{e[ui](gi(e))}))},beforeUpdate(e,{value:t,oldValue:n},o){e[ui]=ci(o),t!==n&&(e.checked=Q(t,o.props.value))}},hi={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const r=h(t);ei(e,"change",(()=>{const t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?N(gi(e)):gi(e)));e[ui](e.multiple?r?new Set(t):t:t[0]),e._assigning=!0,hn((()=>{e._assigning=!1}))})),e[ui]=ci(o)},mounted(e,{value:t}){mi(e,t)},beforeUpdate(e,t,n){e[ui]=ci(n)},updated(e,{value:t}){e._assigning||mi(e,t)}};function mi(e,t){const n=e.multiple,o=p(t);if(!n||o||h(t)){for(let r=0,s=e.options.length;r<s;r++){const s=e.options[r],a=gi(s);if(n)if(o){const e=typeof a;s.selected="string"===e||"number"===e?t.some((e=>String(e)===String(a))):Z(t,a)>-1}else s.selected=t.has(a);else if(Q(gi(s),t))return void(e.selectedIndex!==r&&(e.selectedIndex=r))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}else fa(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`)}function gi(e){return"_value"in e?e._value:e.value}function vi(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const yi=["ctrl","shift","alt","meta"],bi={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>yi.some((n=>e[`${n}Key`]&&!t.includes(n)))},_i=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=bi[t[e]];if(o&&o(n,t))return}return e(n,...o)})},wi={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Si=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=n=>{if(!("key"in n))return;const o=j(n.key);return t.some((e=>e===o||wi[e]===o))?e(n):void 0})},xi=l({patchProp:(e,t,n,o,r,s)=>{const l="svg"===r;"class"===t?function(e,t,n){const o=e[Sa];o&&(t=(t?[t,...o]:[...o]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,l):"style"===t?function(e,t,n){const o=e.style,r=v(n);let s=!1;if(n&&!r){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Ja(o,t,"")}else for(const e in t)null==n[e]&&Ja(o,e,"");for(const e in n)"display"===e&&(s=!0),Ja(o,e,n[e])}else if(r){if(t!==n){const e=o[Fa];e&&(n+=";"+e),o.cssText=n,s=Ha.test(n)}}else t&&e.removeAttribute("style");Va in e&&(e[Va]=s?o.display:"",e[Na]&&(o.display="none"))}(e,n,o):a(t)?i(t)||ni(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&li(t)&&g(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"autocorrect"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(li(t)&&v(n))return!1;return t in e}(e,t,o,l))?(Za(e,t,o),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||Qa(e,t,o,l,0,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&v(o)?("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),Qa(e,t,o,l)):Za(e,I(t),o,0,t)}},ba);let ki;const Ci=(...e)=>{const t=(ki||(ki=qr(xi))).createApp(...e);!function(e){Object.defineProperty(e.config,"isNativeTag",{value:e=>G(e)||J(e)||K(e),writable:!1})}(t),function(e){if(ta()){const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get:()=>t,set(){fa("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const n=e.config.compilerOptions,o='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get:()=>(fa(o),n),set(){fa(o)}})}}(t);const{mount:n}=t;return t.mount=e=>{const o=function(e){if(v(e)){const t=document.querySelector(e);return t||fa(`Failed to mount app: mount target selector "${e}" returned null.`),t}window.ShadowRoot&&e instanceof window.ShadowRoot&&"closed"===e.mode&&fa('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs');return e}
/**
* vue v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/(e);if(!o)return;const r=t._component;g(r)||r.render||r.template||(r.template=o.innerHTML),1===o.nodeType&&(o.textContent="");const s=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),s},t};!function(){if("undefined"==typeof window)return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},o={style:"color:#f5222d"},r={style:"color:#eb2f96"},s={__vue_custom_formatter:!0,header(t){if(!b(t))return null;if(t.__isVue)return["div",e,"VueInstance"];if(At(t)){$e();const n=t.value;return Ee(),["div",{},["span",e,f(t)],"<",c(n),">"]}return xt(t)?["div",{},["span",e,Ct(t)?"ShallowReactive":"Reactive"],"<",c(t),">"+(kt(t)?" (readonly)":"")]:kt(t)?["div",{},["span",e,Ct(t)?"ShallowReadonly":"Readonly"],"<",c(t),">"]:null},hasBody:e=>e&&e.__isVue,body(e){if(e&&e.__isVue)return["div",{},...a(e.$)]}};function a(e){const t=[];e.type.props&&e.props&&t.push(i("props",Et(e.props))),e.setupState!==n&&t.push(i("setup",e.setupState)),e.data!==n&&t.push(i("data",Et(e.data)));const o=u(e,"computed");o&&t.push(i("computed",o));const s=u(e,"inject");return s&&t.push(i("injected",s)),t.push(["div",{},["span",{style:r.style+";opacity:0.66"},"$ (internal): "],["object",{object:e}]]),t}function i(e,t){return t=l({},t),Object.keys(t).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},e],["div",{style:"padding-left:1.25em"},...Object.keys(t).map((e=>["div",{},["span",r,e+": "],c(t[e],!1)]))]]:["span",{}]}function c(e,n=!0){return"number"==typeof e?["span",t,e]:"string"==typeof e?["span",o,JSON.stringify(e)]:"boolean"==typeof e?["span",r,e]:b(e)?["object",{object:n?Et(e):e}]:["span",o,String(e)]}function u(e,t){const n=e.type;if(g(n))return;const o={};for(const r in e.ctx)d(n,r,t)&&(o[r]=e.ctx[r]);return o}function d(e,t,n){const o=e[n];return!!(p(o)&&o.includes(t)||b(o)&&t in o)||!(!e.extends||!d(e.extends,t,n))||!(!e.mixins||!e.mixins.some((e=>d(e,t,n))))||void 0}function f(e){return Ct(e)?"ShallowRef":e.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(s):window.devtoolsFormatters=[s]}();const $i=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},Ei=["disabled","type"],Oi={key:0,class:"loading"},Ti=$i({__name:"Button",props:{type:{type:String,default:"default",validator:e=>["default","primary","success","warning","danger"].includes(e)},size:{type:String,default:"default",validator:e=>["small","default","large"].includes(e)},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},nativeType:{type:String,default:"button",validator:e=>["button","submit","reset"].includes(e)}},emits:["click"],setup(e,{emit:t}){const n=e,o=t,r=ua((()=>{const e=["btn"];return"default"!==n.type?e.push(`btn-${n.type}`):e.push("btn-default"),"default"!==n.size&&e.push(`btn-${n.size}`),n.loading&&e.push("btn-loading"),e.join(" ")})),s=e=>{n.disabled||n.loading||o("click",e)};return(t,n)=>(ws(),Cs("button",{class:W(r.value),disabled:e.disabled,type:e.nativeType,onClick:s},[e.loading?(ws(),Cs("span",Oi)):Ds("v-if",!0),Ho(t.$slots,"default",{},void 0,!0)],10,Ei))}},[["__scopeId","data-v-7966f793"],["__file","D:/asec-platform/frontend/portal/src/components/base/Button.vue"]]),Ii={class:"input-wrapper"},Ai=["type","value","placeholder","disabled","readonly","maxlength"],ji=$i({__name:"Input",props:{modelValue:{type:[String,Number],default:""},type:{type:String,default:"text"},placeholder:{type:String,default:""},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:void 0},size:{type:String,default:"default",validator:e=>["small","default","large"].includes(e)}},emits:["update:modelValue","input","change","focus","blur"],setup(e,{expose:t,emit:n}){const o=e,r=n,s=jt(null),a=jt(!1),i=ua((()=>{const e=["base-input"];return"default"!==o.size&&e.push(`base-input--${o.size}`),a.value&&e.push("base-input--focused"),o.disabled&&e.push("base-input--disabled"),e.join(" ")})),l=e=>{const t=e.target.value;r("update:modelValue",t),r("input",t,e)},c=e=>{r("change",e.target.value,e)},u=e=>{a.value=!0,r("focus",e)},d=e=>{a.value=!1,r("blur",e)};return t({focus:()=>{var e;return null==(e=s.value)?void 0:e.focus()},blur:()=>{var e;return null==(e=s.value)?void 0:e.blur()}}),(t,n)=>(ws(),Cs("div",Ii,[As("input",{ref_key:"inputRef",ref:s,class:W(i.value),type:e.type,value:e.modelValue,placeholder:e.placeholder,disabled:e.disabled,readonly:e.readonly,maxlength:e.maxlength,onInput:l,onChange:c,onFocus:u,onBlur:d},null,42,Ai)]))}},[["__scopeId","data-v-93e6570a"],["__file","D:/asec-platform/frontend/portal/src/components/base/Input.vue"]]),Ri=$i({__name:"Form",props:{model:{type:Object,default:()=>({})},rules:{type:Object,default:()=>({})},labelPosition:{type:String,default:"right",validator:e=>["left","right","top"].includes(e)},labelWidth:{type:String,default:"100px"},inline:{type:Boolean,default:!1}},emits:["submit","validate"],setup(e,{expose:t,emit:n}){const o=e,r=n,s=jt([]),a=ua((()=>{const e=["base-form"];return o.inline&&e.push("base-form--inline"),e.push(`base-form--label-${o.labelPosition}`),e.join(" ")})),i=e=>{r("submit",e)};return t({validate:e=>new Promise(((t,n)=>{let o=!0,r=0;const a=[];if(0===s.value.length)return e&&e(!0),void t(!0);s.value.forEach((i=>{i.validate("",(i=>{r++,i&&(o=!1,a.push(i)),r===s.value.length&&(e&&e(o,a),o?t(!0):n(a))}))}))})),validateField:(e,t)=>{const n=Array.isArray(e)?e:[e],o=s.value.filter((e=>n.includes(e.prop)));if(0===o.length)return void(t&&t());let r=!0,a=0;o.forEach((e=>{e.validate("",(e=>{a++,e&&(r=!1),a===o.length&&t&&t(r)}))}))},resetFields:()=>{s.value.forEach((e=>{e.resetField()}))},clearValidate:e=>{if(e){const t=Array.isArray(e)?e:[e];s.value.forEach((e=>{t.includes(e.prop)&&e.clearValidate()}))}else s.value.forEach((e=>{e.clearValidate()}))}}),mr("baseForm",{model:o.model,rules:o.rules,labelPosition:o.labelPosition,labelWidth:o.labelWidth,addFormItem:e=>{s.value.push(e)},removeFormItem:e=>{const t=s.value.indexOf(e);t>-1&&s.value.splice(t,1)}}),(e,t)=>(ws(),Cs("form",{class:W(a.value),onSubmit:_i(i,["prevent"])},[Ho(e.$slots,"default",{},void 0,!0)],34))}},[["__scopeId","data-v-90721ac8"],["__file","D:/asec-platform/frontend/portal/src/components/base/Form.vue"]]),Pi={class:"base-form-item__content"},Li={key:0,class:"base-form-item__error"},Di=$i({__name:"FormItem",props:{label:{type:String,default:""},prop:{type:String,default:""},rules:{type:[Object,Array],default:()=>[]},required:{type:Boolean,default:!1},labelWidth:{type:String,default:""}},setup(e,{expose:t}){const n=e,o=gr("baseForm",{}),r=jt(""),s=jt(null),a=ua((()=>{const e=["base-form-item"];return r.value&&e.push("base-form-item--error"),(n.required||c.value)&&e.push("base-form-item--required"),e.join(" ")})),i=ua((()=>{const e=["base-form-item__label"];return(n.required||c.value)&&e.push("base-form-item__label--required"),e.join(" ")})),l=ua((()=>{const e=n.labelWidth||o.labelWidth;return e&&"top"!==o.labelPosition?{width:e,minWidth:e}:{}})),c=ua((()=>u().some((e=>e.required)))),u=()=>{var e;const t=(null==(e=o.rules)?void 0:e[n.prop])||[],r=n.rules||[];return[].concat(t,r)},d=(e,t)=>{if(!n.prop||!o.model)return t&&t(),!0;const s=o.model[n.prop],a=u();if(0===a.length)return t&&t(),!0;for(const o of a)if(!e||!o.trigger||o.trigger===e){if(o.required&&(null==s||""===s)){const e=o.message||`${n.label}是必填项`;return r.value=e,t&&t(e),!1}if(null!=s&&""!==s){if(o.min&&String(s).length<o.min){const e=o.message||`${n.label}长度不能少于${o.min}个字符`;return r.value=e,t&&t(e),!1}if(o.max&&String(s).length>o.max){const e=o.message||`${n.label}长度不能超过${o.max}个字符`;return r.value=e,t&&t(e),!1}if(o.pattern&&!o.pattern.test(String(s))){const e=o.message||`${n.label}格式不正确`;return r.value=e,t&&t(e),!1}if(o.validator&&"function"==typeof o.validator)try{if(!1===o.validator(o,s,(e=>{e?(r.value=e.message||e,t&&t(e.message||e)):(r.value="",t&&t())}))){const e=o.message||`${n.label}验证失败`;return r.value=e,t&&t(e),!1}}catch(i){const e=o.message||i.message||`${n.label}验证失败`;return r.value=e,t&&t(e),!1}}}return r.value="",t&&t(),!0},p=()=>{n.prop&&o.model&&void 0!==s.value&&(o.model[n.prop]=s.value),r.value=""},f=()=>{r.value=""};return n.prop&&o.model&&Xr((()=>o.model[n.prop]),(()=>{r.value&&d("change")})),Oo((()=>{n.prop&&o.model&&(s.value=o.model[n.prop]),o.addFormItem&&o.addFormItem({prop:n.prop,validate:d,resetField:p,clearValidate:f})})),jo((()=>{o.removeFormItem&&o.removeFormItem({prop:n.prop,validate:d,resetField:p,clearValidate:f})})),t({validate:d,resetField:p,clearValidate:f,prop:n.prop}),(t,n)=>(ws(),Cs("div",{class:W(a.value)},[e.label?(ws(),Cs("label",{key:0,class:W(i.value),style:F(l.value)},te(e.label),7)):Ds("v-if",!0),As("div",Pi,[Ho(t.$slots,"default",{},void 0,!0),r.value?(ws(),Cs("div",Li,te(r.value),1)):Ds("v-if",!0)])],2))}},[["__scopeId","data-v-59663274"],["__file","D:/asec-platform/frontend/portal/src/components/base/FormItem.vue"]]),Vi={class:"container"},Ni=$i({__name:"Container",setup:e=>(e,t)=>(ws(),Cs("div",Vi,[Ho(e.$slots,"default",{},void 0,!0)]))},[["__scopeId","data-v-3d73176e"],["__file","D:/asec-platform/frontend/portal/src/components/base/Container.vue"]]),Mi=$i({__name:"Aside",props:{width:{type:String,default:"220px"},collapsed:{type:Boolean,default:!1},collapsedWidth:{type:String,default:"54px"}},setup(e){const t=e,n=ua((()=>{const e=["aside"];return t.collapsed&&e.push("collapsed"),e.join(" ")})),o=ua((()=>({width:t.collapsed?t.collapsedWidth:t.width})));return(e,t)=>(ws(),Cs("aside",{class:W(n.value),style:F(o.value)},[Ho(e.$slots,"default",{},void 0,!0)],6))}},[["__scopeId","data-v-59e6df51"],["__file","D:/asec-platform/frontend/portal/src/components/base/Aside.vue"]]),Ui={class:"main"},Fi=$i({__name:"Main",setup:e=>(e,t)=>(ws(),Cs("main",Ui,[Ho(e.$slots,"default",{},void 0,!0)]))},[["__scopeId","data-v-fb1ed7e4"],["__file","D:/asec-platform/frontend/portal/src/components/base/Main.vue"]]),Bi=$i({__name:"Row",props:{gutter:{type:Number,default:0},justify:{type:String,default:"start",validator:e=>["start","end","center","space-around","space-between"].includes(e)},align:{type:String,default:"top",validator:e=>["top","middle","bottom"].includes(e)}},setup(e){const t=e,n=ua((()=>{const e=["row"];return"start"!==t.justify&&e.push(`row-justify-${t.justify}`),"top"!==t.align&&e.push(`row-align-${t.align}`),e.join(" ")})),o=ua((()=>{const e={};return t.gutter>0&&(e.marginLeft=`-${t.gutter/2}px`,e.marginRight=`-${t.gutter/2}px`),e}));return provide("row",{gutter:t.gutter}),(e,t)=>(ws(),Cs("div",{class:W(n.value),style:F(o.value)},[Ho(e.$slots,"default",{},void 0,!0)],6))}},[["__scopeId","data-v-335417f0"],["__file","D:/asec-platform/frontend/portal/src/components/base/Row.vue"]]),qi=$i({__name:"Col",props:{span:{type:Number,default:24},offset:{type:Number,default:0},push:{type:Number,default:0},pull:{type:Number,default:0},xs:{type:[Number,Object],default:void 0},sm:{type:[Number,Object],default:void 0},md:{type:[Number,Object],default:void 0},lg:{type:[Number,Object],default:void 0},xl:{type:[Number,Object],default:void 0}},setup(e){const t=e,n=gr("row",{gutter:0}),o=ua((()=>{const e=["col"];24!==t.span&&e.push(`col-${t.span}`),t.offset>0&&e.push(`col-offset-${t.offset}`),t.push>0&&e.push(`col-push-${t.push}`),t.pull>0&&e.push(`col-pull-${t.pull}`);return["xs","sm","md","lg","xl"].forEach((n=>{const o=t[n];void 0!==o&&("number"==typeof o?e.push(`col-${n}-${o}`):"object"==typeof o&&(void 0!==o.span&&e.push(`col-${n}-${o.span}`),void 0!==o.offset&&e.push(`col-${n}-offset-${o.offset}`),void 0!==o.push&&e.push(`col-${n}-push-${o.push}`),void 0!==o.pull&&e.push(`col-${n}-pull-${o.pull}`)))})),e.join(" ")})),r=ua((()=>{const e={};return n.gutter>0&&(e.paddingLeft=n.gutter/2+"px",e.paddingRight=n.gutter/2+"px"),e}));return(e,t)=>(ws(),Cs("div",{class:W(o.value),style:F(r.value)},[Ho(e.$slots,"default",{},void 0,!0)],6))}},[["__scopeId","data-v-cb3274b7"],["__file","D:/asec-platform/frontend/portal/src/components/base/Col.vue"]]),zi=$i({__name:"Divider",props:{direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},contentPosition:{type:String,default:"center",validator:e=>["left","center","right"].includes(e)}},setup(e){const t=e,n=ua((()=>{const e=["divider"];return"vertical"===t.direction?e.push("divider-vertical"):e.push("divider-horizontal"),e.join(" ")})),o=ua((()=>{const e=["divider-content"];return"horizontal"===t.direction&&e.push(`divider-content-${t.contentPosition}`),e.join(" ")}));return(e,t)=>(ws(),Cs("div",{class:W(n.value)},[e.$slots.default?(ws(),Cs("span",{key:0,class:W(o.value)},[Ho(e.$slots,"default",{},void 0,!0)],2)):Ds("v-if",!0)],2))}},[["__scopeId","data-v-fd2bdd89"],["__file","D:/asec-platform/frontend/portal/src/components/base/Divider.vue"]]),Hi=["src","alt"],Wi={key:1,class:"avatar-icon","aria-hidden":"true"},Gi=["xlink:href"],Ji={key:2,class:"avatar-text"},Ki=$i({__name:"Avatar",props:{size:{type:[Number,String],default:40,validator:e=>"string"==typeof e?["small","default","large"].includes(e):"number"==typeof e&&e>0},shape:{type:String,default:"circle",validator:e=>["circle","square"].includes(e)},src:{type:String,default:""},alt:{type:String,default:""},icon:{type:String,default:""},text:{type:String,default:""}},emits:["error"],setup(e,{emit:t}){const n=e,o=t,r=jt(!1),s=ua((()=>{const e=["avatar"];return"string"==typeof n.size&&e.push(`avatar-${n.size}`),"square"===n.shape&&e.push("avatar-square"),e.join(" ")})),a=ua((()=>{const e={};return"number"==typeof n.size&&(e.width=`${n.size}px`,e.height=`${n.size}px`,e.lineHeight=`${n.size}px`,e.fontSize=`${Math.floor(.35*n.size)}px`),e})),i=e=>{r.value=!0,o("error",e)};return(t,n)=>(ws(),Cs("div",{class:W(s.value),style:F(a.value)},[e.src?(ws(),Cs("img",{key:0,src:e.src,alt:e.alt,onError:i},null,40,Hi)):e.icon?(ws(),Cs("svg",Wi,[As("use",{"xlink:href":`#${e.icon}`},null,8,Gi)])):(ws(),Cs("span",Ji,[Ho(t.$slots,"default",{},(()=>[Ls(te(e.text),1)]),!0)]))],6))}},[["__scopeId","data-v-865e621e"],["__file","D:/asec-platform/frontend/portal/src/components/base/Avatar.vue"]]),Yi=["onClick"],Xi=$i({__name:"Carousel",props:{height:{type:String,default:"300px"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,default:"bottom",validator:e=>["bottom","top","none"].includes(e)},arrow:{type:String,default:"hover",validator:e=>["always","hover","never"].includes(e)}},emits:["change"],setup(e,{expose:t,emit:n}){const o=e,r=n,s=jt(0),a=jt(0);let i=null;const l=ua((()=>({transform:`translateX(-${100*s.value}%)`}))),c=ua((()=>{const e=["carousel-indicators"];return e.push(`carousel-indicators-${o.indicatorPosition}`),e.join(" ")})),u=e=>{e!==s.value&&(s.value=e,r("change",e))},d=()=>{const e=(s.value+1)%a.value;u(e)},p=()=>{const e=(s.value-1+a.value)%a.value;u(e)};return mr("carousel",{addItem:()=>{a.value++},removeItem:()=>{a.value--}}),Oo((()=>{o.autoplay&&a.value>1&&(i=setInterval(d,o.interval))})),jo((()=>{i&&(clearInterval(i),i=null)})),t({next:d,prev:p,setCurrentIndex:u}),(t,n)=>(ws(),Cs("div",{class:"carousel",style:F({height:e.height})},[As("div",{class:"carousel-container",style:F(l.value)},[Ho(t.$slots,"default",{},void 0,!0)],4),"none"!==e.indicatorPosition?(ws(),Cs("div",{key:0,class:W(c.value)},[(ws(!0),Cs(ms,null,zo(a.value,((e,t)=>(ws(),Cs("button",{key:t,class:W(["carousel-indicator",{active:t===s.value}]),onClick:e=>u(t)},null,10,Yi)))),128))],2)):Ds("v-if",!0),"never"!==e.arrow?(ws(),Cs("button",{key:1,class:"carousel-arrow carousel-arrow-left",onClick:p}," ‹ ")):Ds("v-if",!0),"never"!==e.arrow?(ws(),Cs("button",{key:2,class:"carousel-arrow carousel-arrow-right",onClick:d}," › ")):Ds("v-if",!0)],4))}},[["__scopeId","data-v-0c63f958"],["__file","D:/asec-platform/frontend/portal/src/components/base/Carousel.vue"]]),Qi={class:"carousel-item"},Zi=$i({__name:"CarouselItem",setup(e){const t=gr("carousel",null);return Oo((()=>{null==t||t.addItem()})),jo((()=>{null==t||t.removeItem()})),(e,t)=>(ws(),Cs("div",Qi,[Ho(e.$slots,"default",{},void 0,!0)]))}},[["__scopeId","data-v-18d93493"],["__file","D:/asec-platform/frontend/portal/src/components/base/CarouselItem.vue"]]),el={key:0,class:"base-card__header"};const tl=$i({name:"BaseCard",props:{shadow:{type:String,default:"always",validator:e=>["always","hover","never"].includes(e)},bodyStyle:{type:Object,default:()=>({})}}},[["render",function(e,t,n,o,r,s){return ws(),Cs("div",{class:W(["base-card",{"base-card--shadow":n.shadow}])},[e.$slots.header?(ws(),Cs("div",el,[Ho(e.$slots,"header",{},void 0,!0)])):Ds("v-if",!0),As("div",{class:"base-card__body",style:F(n.bodyStyle)},[Ho(e.$slots,"default",{},void 0,!0)],4)],2)}],["__scopeId","data-v-ae218b1b"],["__file","D:/asec-platform/frontend/portal/src/components/base/Card.vue"]]),nl={class:"base-timeline"};const ol=$i({name:"BaseTimeline"},[["render",function(e,t,n,o,r,s){return ws(),Cs("div",nl,[Ho(e.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-43112243"],["__file","D:/asec-platform/frontend/portal/src/components/base/Timeline.vue"]]),rl={name:"BaseTimelineItem",props:{timestamp:{type:String,default:""},hideTimestamp:{type:Boolean,default:!1},placement:{type:String,default:"bottom",validator:e=>["top","bottom"].includes(e)},type:{type:String,default:"",validator:e=>["primary","success","warning","danger","info",""].includes(e)},color:{type:String,default:""},size:{type:String,default:"normal",validator:e=>["normal","large"].includes(e)},icon:{type:String,default:""}},computed:{nodeClass(){const e=[`base-timeline-item__node--${this.size}`];return this.type&&e.push(`base-timeline-item__node--${this.type}`),e},nodeStyle(){const e={};return this.color&&(e.backgroundColor=this.color,e.borderColor=this.color),e},timestampClass(){return[`base-timeline-item__timestamp--${this.placement}`]}}},sl={class:"base-timeline-item"},al={class:"base-timeline-item__wrapper"},il={class:"base-timeline-item__content"};const ll=$i(rl,[["render",function(e,t,n,o,r,s){return ws(),Cs("div",sl,[t[1]||(t[1]=As("div",{class:"base-timeline-item__tail"},null,-1)),As("div",{class:W(["base-timeline-item__node",s.nodeClass]),style:F(s.nodeStyle)},[Ho(e.$slots,"dot",{},(()=>[t[0]||(t[0]=As("div",{class:"base-timeline-item__node-normal"},null,-1))]),!0)],6),As("div",al,[n.timestamp?(ws(),Cs("div",{key:0,class:W(["base-timeline-item__timestamp",s.timestampClass])},te(n.timestamp),3)):Ds("v-if",!0),As("div",il,[Ho(e.$slots,"default",{},void 0,!0)])])])}],["__scopeId","data-v-105a9016"],["__file","D:/asec-platform/frontend/portal/src/components/base/TimelineItem.vue"]]),cl={name:"BaseSelect",props:{modelValue:{type:[String,Number,Boolean],default:""},placeholder:{type:String,default:"请选择"},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],data:()=>({visible:!1,selectedLabel:""}),mounted(){this.updateSelectedLabel(),document.addEventListener("click",this.handleDocumentClick)},beforeUnmount(){document.removeEventListener("click",this.handleDocumentClick)},watch:{modelValue(){this.updateSelectedLabel()}},methods:{toggleDropdown(){this.disabled||(this.visible=!this.visible)},handleDocumentClick(e){this.$el.contains(e.target)||(this.visible=!1)},handleOptionClick(e,t){this.$emit("update:modelValue",e),this.$emit("change",e),this.selectedLabel=t,this.visible=!1},updateSelectedLabel(){this.$nextTick((()=>{var e;const t=null==(e=this.$el)?void 0:e.querySelectorAll(".base-option");t&&t.forEach((e=>{var t,n;(null==(t=e.__vue__)?void 0:t.value)===this.modelValue&&(this.selectedLabel=(null==(n=e.__vue__)?void 0:n.label)||e.textContent)}))}))}},provide(){return{select:this}}},ul={key:0,class:"base-select__selected"},dl={key:1,class:"base-select__placeholder"},pl={class:"base-select__dropdown"},fl={class:"base-select__options"};const hl=$i(cl,[["render",function(e,t,n,o,r,s){return ws(),Cs("div",{class:W(["base-select",{"is-disabled":n.disabled}])},[As("div",{class:W(["base-select__input",{"is-focus":r.visible}]),onClick:t[0]||(t[0]=(...e)=>s.toggleDropdown&&s.toggleDropdown(...e))},[r.selectedLabel?(ws(),Cs("span",ul,te(r.selectedLabel),1)):(ws(),Cs("span",dl,te(n.placeholder),1)),As("i",{class:W(["base-select__arrow",{"is-reverse":r.visible}])},"▼",2)],2),Gn(As("div",pl,[As("div",fl,[Ho(e.$slots,"default",{},void 0,!0)])],512),[[Ma,r.visible]])],2)}],["__scopeId","data-v-93976a64"],["__file","D:/asec-platform/frontend/portal/src/components/base/Select.vue"]]);const ml=$i({name:"BaseOption",props:{value:{type:[String,Number,Boolean],required:!0},label:{type:[String,Number],default:""},disabled:{type:Boolean,default:!1}},inject:["select"],computed:{isSelected(){return this.select.modelValue===this.value}},methods:{handleClick(){this.disabled||this.select.handleOptionClick(this.value,this.label||this.$el.textContent)}}},[["render",function(e,t,n,o,r,s){return ws(),Cs("div",{class:W(["base-option",{"is-selected":s.isSelected,"is-disabled":n.disabled}]),onClick:t[0]||(t[0]=(...e)=>s.handleClick&&s.handleClick(...e))},[Ho(e.$slots,"default",{},(()=>[Ls(te(n.label),1)]),!0)],2)}],["__scopeId","data-v-f707b401"],["__file","D:/asec-platform/frontend/portal/src/components/base/Option.vue"]]),gl={name:"BaseCheckbox",props:{modelValue:{type:[Boolean,String,Number,Array],default:!1},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],computed:{model:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}},isChecked(){return Array.isArray(this.modelValue)?this.modelValue.includes(this.label):!0===this.modelValue}},methods:{handleChange(e){this.$emit("change",e.target.checked)}}},vl={class:"base-checkbox__input"},yl=["disabled","value"],bl={key:0,class:"base-checkbox__label"};const _l=$i(gl,[["render",function(e,t,n,o,r,s){return ws(),Cs("label",{class:W(["base-checkbox",{"is-disabled":n.disabled,"is-checked":s.isChecked}])},[As("span",vl,[t[2]||(t[2]=As("span",{class:"base-checkbox__inner"},null,-1)),Gn(As("input",{type:"checkbox",class:"base-checkbox__original",disabled:n.disabled,value:n.label,"onUpdate:modelValue":t[0]||(t[0]=e=>s.model=e),onChange:t[1]||(t[1]=(...e)=>s.handleChange&&s.handleChange(...e))},null,40,yl),[[di,s.model]])]),e.$slots.default||n.label?(ws(),Cs("span",bl,[Ho(e.$slots,"default",{},(()=>[Ls(te(n.label),1)]),!0)])):Ds("v-if",!0)],2)}],["__scopeId","data-v-19854599"],["__file","D:/asec-platform/frontend/portal/src/components/base/Checkbox.vue"]]),wl={name:"BaseRadio",props:{modelValue:{type:[String,Number,Boolean],default:""},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],computed:{model:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}},isChecked(){return this.modelValue===this.label}},methods:{handleChange(e){this.$emit("change",e.target.value)}}},Sl={class:"base-radio__input"},xl=["disabled","value"],kl={key:0,class:"base-radio__label"};const Cl=$i(wl,[["render",function(e,t,n,o,r,s){return ws(),Cs("label",{class:W(["base-radio",{"is-disabled":n.disabled,"is-checked":s.isChecked}])},[As("span",Sl,[t[2]||(t[2]=As("span",{class:"base-radio__inner"},null,-1)),Gn(As("input",{type:"radio",class:"base-radio__original",disabled:n.disabled,value:n.label,"onUpdate:modelValue":t[0]||(t[0]=e=>s.model=e),onChange:t[1]||(t[1]=(...e)=>s.handleChange&&s.handleChange(...e))},null,40,xl),[[fi,s.model]])]),e.$slots.default||n.label?(ws(),Cs("span",kl,[Ho(e.$slots,"default",{},(()=>[Ls(te(n.label),1)]),!0)])):Ds("v-if",!0)],2)}],["__scopeId","data-v-755550cb"],["__file","D:/asec-platform/frontend/portal/src/components/base/Radio.vue"]]),$l={name:"BaseRadioGroup",props:{modelValue:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)},textColor:{type:String,default:""},fill:{type:String,default:""}},emits:["update:modelValue","change"],watch:{modelValue(e){this.$emit("change",e)}},provide(){return{radioGroup:this}}},El={class:"base-radio-group",role:"radiogroup"};const Ol=$i($l,[["render",function(e,t,n,o,r,s){return ws(),Cs("div",El,[Ho(e.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-9458390a"],["__file","D:/asec-platform/frontend/portal/src/components/base/RadioGroup.vue"]]),Tl={key:0,viewBox:"0 0 1024 1024",width:"1em",height:"1em",fill:"currentColor"},Il=["d"];const Al=$i({name:"BaseIcon",props:{name:{type:String,default:""},size:{type:[String,Number],default:"16px"},color:{type:String,default:"currentColor"}},computed:{iconClass(){return{[`base-icon--${this.name}`]:this.name}},iconStyle(){return{fontSize:"number"==typeof this.size?`${this.size}px`:this.size,color:this.color}},iconPath(){return{search:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116 65.6-158.4C296 211.3 352.2 188 412 188s116 23.3 158.4 65.6C612.7 296 636 352.2 636 412s-23.3 116-65.6 158.4z",plus:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z",warning:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z",document:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494z",loading:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C637 83.6 579.4 72 520 72s-117 11.6-171.3 34.6a440.45 440.45 0 0 0-139.9 94.3 437.71 437.71 0 0 0-94.3 139.9C91.6 395 80 452.6 80 512s11.6 117 34.6 171.3a440.45 440.45 0 0 0 94.3 139.9 437.71 437.71 0 0 0 139.9 94.3C475 940.4 532.6 952 592 952c19.9 0 36 16.1 36 36s-16.1 36-36 36c-59.4 0-117-11.6-171.3-34.6a512.69 512.69 0 0 1-139.9-94.3c-40.8-35.4-73.4-76.3-94.3-139.9C163.6 709 152 651.4 152 592s11.6-117 34.6-171.3a512.69 512.69 0 0 1 94.3-139.9c35.4-40.8 76.3-73.4 139.9-94.3C467 163.6 524.6 152 584 152c19.9 0 36 16.1 36 36s-16.1 36-36 36z"}[this.name]||""}}},[["render",function(e,t,n,o,r,s){return ws(),Cs("i",{class:W(["base-icon",s.iconClass]),style:F(s.iconStyle)},[n.name?(ws(),Cs("svg",Tl,[As("path",{d:s.iconPath},null,8,Il)])):Ho(e.$slots,"default",{key:1},void 0,!0)],6)}],["__scopeId","data-v-1278d3c6"],["__file","D:/asec-platform/frontend/portal/src/components/base/Icon.vue"]]),jl={template:'\n    <div class="loading-overlay" v-if="visible">\n      <div class="loading-content">\n        <div class="loading"></div>\n        <div v-if="text" class="loading-text">{{ text }}</div>\n      </div>\n    </div>\n  ',data:()=>({visible:!1,text:""}),methods:{show(e={}){this.visible=!0,this.text=e.text||""},hide(){this.visible=!1,this.text=""}}};const Rl=new class{constructor(){this.instance=null,this.container=null}service(e={}){if(this.instance&&this.close(),this.container=document.createElement("div"),this.container.className="loading-service-container",!1!==e.fullscreen)document.body.appendChild(this.container);else if(e.target){const t="string"==typeof e.target?document.querySelector(e.target):e.target;t?(t.appendChild(this.container),t.style.position="relative"):document.body.appendChild(this.container)}else document.body.appendChild(this.container);this.instance=Ci(jl);return this.instance.mount(this.container).show(e),{close:()=>this.close()}}close(){this.instance&&(this.instance.unmount(),this.instance=null),this.container&&this.container.parentNode&&(this.container.parentNode.removeChild(this.container),this.container=null)}},Pl={service:e=>Rl.service(e)},Ll={name:"BaseMessage",props:{message:{type:String,default:""},type:{type:String,default:"info",validator:e=>["success","warning","info","error"].includes(e)},showClose:{type:Boolean,default:!1},duration:{type:Number,default:3e3}},data:()=>({visible:!0}),mounted(){this.duration>0&&setTimeout((()=>{this.close()}),this.duration)},methods:{close(){this.visible=!1,setTimeout((()=>{this.$el.remove()}),300)}},render(){return this.visible?da("div",{class:["base-message",`base-message--${this.type}`,{"base-message--closable":this.showClose}],style:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",zIndex:9999,padding:"12px 16px",borderRadius:"4px",color:"#fff",fontSize:"14px",boxShadow:"0 2px 12px 0 rgba(0, 0, 0, 0.1)",transition:"all 0.3s",backgroundColor:this.getBackgroundColor()}},[da("span",this.message),this.showClose&&da("span",{style:{marginLeft:"8px",cursor:"pointer",fontSize:"16px"},onClick:this.close},"×")]):null},methods:{getBackgroundColor(){const e={success:"#67c23a",warning:"#e6a23c",error:"#f56c6c",info:"#909399"};return e[this.type]||e.info}}},Dl=e=>{"string"==typeof e&&(e={message:e});const t=document.createElement("div");document.body.appendChild(t);const n=Ci(Ll,e);return n.mount(t),{close:()=>{n.unmount(),document.body.removeChild(t)}}};Dl.success=e=>Dl({message:e,type:"success"}),Dl.warning=e=>Dl({message:e,type:"warning"}),Dl.error=e=>Dl({message:e,type:"error"}),Dl.info=e=>Dl({message:e,type:"info"});const Vl={name:"BaseMessageBox",props:{title:{type:String,default:"提示"},message:{type:String,default:""},type:{type:String,default:"info",validator:e=>["success","warning","info","error"].includes(e)},showCancelButton:{type:Boolean,default:!1},confirmButtonText:{type:String,default:"确定"},cancelButtonText:{type:String,default:"取消"}},data:()=>({visible:!0}),methods:{handleConfirm(){this.$emit("confirm"),this.close()},handleCancel(){this.$emit("cancel"),this.close()},close(){this.visible=!1,setTimeout((()=>{this.$el.remove()}),300)}},render(){return this.visible?da("div",{class:"base-message-box-overlay",style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:9999,display:"flex",alignItems:"center",justifyContent:"center"}},[da("div",{class:"base-message-box",style:{backgroundColor:"#fff",borderRadius:"4px",boxShadow:"0 2px 12px 0 rgba(0, 0, 0, 0.1)",minWidth:"300px",maxWidth:"500px",padding:"20px"}},[da("div",{style:{fontSize:"16px",fontWeight:"bold",marginBottom:"10px",color:"#303133"}},this.title),da("div",{style:{fontSize:"14px",color:"#606266",marginBottom:"20px",lineHeight:"1.5"}},this.message),da("div",{style:{textAlign:"right"}},[this.showCancelButton&&da("button",{style:{padding:"8px 16px",marginRight:"10px",border:"1px solid #dcdfe6",borderRadius:"4px",backgroundColor:"#fff",color:"#606266",cursor:"pointer"},onClick:this.handleCancel},this.cancelButtonText),da("button",{style:{padding:"8px 16px",border:"none",borderRadius:"4px",backgroundColor:"#409eff",color:"#fff",cursor:"pointer"},onClick:this.handleConfirm},this.confirmButtonText)])])]):null}},Nl=e=>new Promise(((t,n)=>{const o=document.createElement("div");document.body.appendChild(o);const r=Ci(Vl,{...e,onConfirm:()=>{r.unmount(),document.body.removeChild(o),t("confirm")},onCancel:()=>{r.unmount(),document.body.removeChild(o),n("cancel")}});r.mount(o)}));Nl.confirm=(e,t="确认",n={})=>Nl({message:e,title:t,showCancelButton:!0,...n}),Nl.alert=(e,t="提示",n={})=>Nl({message:e,title:t,showCancelButton:!1,...n});const Ml={"base-button":Ti,"base-input":ji,"base-form":Ri,"base-form-item":Di,"base-container":Ni,"base-aside":Mi,"base-main":Fi,"base-row":Bi,"base-col":qi,"base-divider":zi,"base-avatar":Ki,"base-carousel":Xi,"base-carousel-item":Zi,"base-card":tl,"base-timeline":ol,"base-timeline-item":ll,"base-select":hl,"base-option":ml,"base-checkbox":_l,"base-radio":Cl,"base-radio-group":Ol,"base-icon":Al},Ul={install(e){Object.keys(Ml).forEach((t=>{e.component(t,Ml[t])})),e.config.globalProperties.$loading=Pl,e.config.globalProperties.$message=Dl,e.config.globalProperties.$messageBox=Nl}},Fl={appName:"ASec安全平台",appLogo:"/src/assets/ASD.png",introduction:"ASec",showViteLogo:!1},Bl={install:e=>{(e=>{e.config.globalProperties.$GIN_VUE_ADMIN=Fl})(e)}},ql={},zl=function(e,t,n){if(!t||0===t.length)return e();const o=document.getElementsByTagName("link");return Promise.all(t.map((e=>{if(e=function(e,t){return new URL(e,t).href}(e,n),e in ql)return;ql[e]=!0;const t=e.endsWith(".css"),r=t?'[rel="stylesheet"]':"";if(!!n)for(let n=o.length-1;n>=0;n--){const r=o[n];if(r.href===e&&(!t||"stylesheet"===r.rel))return}else if(document.querySelector(`link[href="${e}"]${r}`))return;const s=document.createElement("link");return s.rel=t?"stylesheet":"modulepreload",t||(s.as="script",s.crossOrigin=""),s.href=e,document.head.appendChild(s),t?new Promise(((t,n)=>{s.addEventListener("load",t),s.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e()))};function Hl(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:{}}const Wl="function"==typeof Proxy;let Gl,Jl;function Kl(){return void 0!==Gl||("undefined"!=typeof window&&window.performance?(Gl=!0,Jl=window.performance):"undefined"!=typeof globalThis&&(null===(e=globalThis.perf_hooks)||void 0===e?void 0:e.performance)?(Gl=!0,Jl=globalThis.perf_hooks.performance):Gl=!1),Gl?Jl.now():Date.now();var e}class Yl{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const s in e.settings){const t=e.settings[s];n[s]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let r=Object.assign({},n);try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(r,t)}catch(Yh){}this.fallbacks={getSettings:()=>r,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(Yh){}r=e},now:()=>Kl()},t&&t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function Xl(e,t){const n=e,o=Hl(),r=Hl().__VUE_DEVTOOLS_GLOBAL_HOOK__,s=Wl&&n.enableEarlyProxy;if(!r||!o.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&s){const e=s?new Yl(n,r):null;(o.__VUE_DEVTOOLS_PLUGINS__=o.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:n,setupFn:t,proxy:e}),e&&t(e.proxiedTarget)}else r.emit("devtools-plugin:setup",e,t)}
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ql="undefined"!=typeof document;function Zl(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const ec=Object.assign;function tc(e,t){const n={};for(const o in t){const r=t[o];n[o]=oc(r)?r.map(e):e(r)}return n}const nc=()=>{},oc=Array.isArray;function rc(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const sc=/#/g,ac=/&/g,ic=/\//g,lc=/=/g,cc=/\?/g,uc=/\+/g,dc=/%5B/g,pc=/%5D/g,fc=/%5E/g,hc=/%60/g,mc=/%7B/g,gc=/%7C/g,vc=/%7D/g,yc=/%20/g;function bc(e){return encodeURI(""+e).replace(gc,"|").replace(dc,"[").replace(pc,"]")}function _c(e){return bc(e).replace(uc,"%2B").replace(yc,"+").replace(sc,"%23").replace(ac,"%26").replace(hc,"`").replace(mc,"{").replace(vc,"}").replace(fc,"^")}function wc(e){return null==e?"":function(e){return bc(e).replace(sc,"%23").replace(cc,"%3F")}(e).replace(ic,"%2F")}function Sc(e){try{return decodeURIComponent(""+e)}catch(t){rc(`Error decoding "${e}". Using original value`)}return""+e}const xc=/\/$/;function kc(e,t,n="/"){let o,r={},s="",a="";const i=t.indexOf("#");let l=t.indexOf("?");return i<l&&i>=0&&(l=-1),l>-1&&(o=t.slice(0,l),s=t.slice(l+1,i>-1?i:t.length),r=e(s)),i>-1&&(o=o||t.slice(0,i),a=t.slice(i,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!t.startsWith("/"))return rc(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let s,a,i=n.length-1;for(s=0;s<o.length;s++)if(a=o[s],"."!==a){if(".."!==a)break;i>1&&i--}return n.slice(0,i).join("/")+"/"+o.slice(s).join("/")}(null!=o?o:t,n),{fullPath:o+(s&&"?")+s+a,path:o,query:r,hash:Sc(a)}}function Cc(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function $c(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Ec(t.matched[o],n.matched[r])&&Oc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Ec(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Oc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Tc(e[n],t[n]))return!1;return!0}function Tc(e,t){return oc(e)?Ic(e,t):oc(t)?Ic(t,e):e===t}function Ic(e,t){return oc(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}const Ac={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var jc,Rc,Pc,Lc;function Dc(e){if(!e)if(Ql){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(xc,"")}(Rc=jc||(jc={})).pop="pop",Rc.push="push",(Lc=Pc||(Pc={})).back="back",Lc.forward="forward",Lc.unknown="";const Vc=/^[^#]+#/;function Nc(e,t){return e.replace(Vc,"#")+t}const Mc=()=>({left:window.scrollX,top:window.scrollY});function Uc(e){let t;if("el"in e){const o=e.el,r="string"==typeof o&&o.startsWith("#");if(!("string"!=typeof e.el||r&&document.getElementById(e.el.slice(1))))try{const t=document.querySelector(e.el);if(r&&t)return void rc(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`)}catch(n){return void rc(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`)}const s="string"==typeof o?r?document.getElementById(o.slice(1)):document.querySelector(o):o;if(!s)return void rc(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function Fc(e,t){return(history.state?history.state.position-t:-1)+e}const Bc=new Map;function qc(e,t){const{pathname:n,search:o,hash:r}=t,s=e.indexOf("#");if(s>-1){let t=r.includes(e.slice(s))?e.slice(s).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Cc(n,"")}return Cc(n,e)+o+r}function zc(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?Mc():null}}function Hc(e){const{history:t,location:n}=window,o={value:qc(e,n)},r={value:t.state};function s(o,s,a){const i=e.indexOf("#"),l=i>-1?(n.host&&document.querySelector("base")?e:e.slice(i))+o:location.protocol+"//"+location.host+e+o;try{t[a?"replaceState":"pushState"](s,"",l),r.value=s}catch(c){rc("Error with push/replace State",c),n[a?"replace":"assign"](l)}}return r.value||s(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const a=ec({},r.value,t.state,{forward:e,scroll:Mc()});t.state||rc("history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\n\nhistory.replaceState(history.state, '', url)\n\nYou can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state"),s(a.current,a,!0),s(e,ec({},zc(o.value,e,null),{position:a.position+1},n),!1),o.value=e},replace:function(e,n){s(e,ec({},t.state,zc(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function Wc(e){const t=Hc(e=Dc(e)),n=function(e,t,n,o){let r=[],s=[],a=null;const i=({state:s})=>{const i=qc(e,location),l=n.value,c=t.value;let u=0;if(s){if(n.value=i,t.value=s,a&&a===l)return void(a=null);u=c?s.position-c.position:0}else o(i);r.forEach((e=>{e(n.value,l,{delta:u,type:jc.pop,direction:u?u>0?Pc.forward:Pc.back:Pc.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(ec({},e.state,{scroll:Mc()}),"")}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){a=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return s.push(t),t},destroy:function(){for(const e of s)e();s=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=ec({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Nc.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Gc(e){return"string"==typeof e||e&&"object"==typeof e}function Jc(e){return"string"==typeof e||"symbol"==typeof e}const Kc=Symbol("navigation failure");var Yc,Xc;(Xc=Yc||(Yc={}))[Xc.aborted=4]="aborted",Xc[Xc.cancelled=8]="cancelled",Xc[Xc.duplicated=16]="duplicated";const Qc={1:({location:e,currentLocation:t})=>`No match for\n ${JSON.stringify(e)}${t?"\nwhile being at\n"+JSON.stringify(t):""}`,2:({from:e,to:t})=>`Redirected from "${e.fullPath}" to "${function(e){if("string"==typeof e)return e;if(null!=e.path)return e.path;const t={};for(const n of tu)n in e&&(t[n]=e[n]);return JSON.stringify(t,null,2)}(t)}" via a navigation guard.`,4:({from:e,to:t})=>`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`,8:({from:e,to:t})=>`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`,16:({from:e,to:t})=>`Avoided redundant navigation to current location: "${e.fullPath}".`};function Zc(e,t){return ec(new Error(Qc[e](t)),{type:e,[Kc]:!0},t)}function eu(e,t){return e instanceof Error&&Kc in e&&(null==t||!!(e.type&t))}const tu=["params","query","hash"];const nu="[^/]+?",ou={sensitive:!1,strict:!1,start:!0,end:!0},ru=/[.+*?^${}()[\]/\\]/g;function su(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function au(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=su(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(iu(o))return 1;if(iu(r))return-1}return r.length-o.length}function iu(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const lu={type:0,value:""},cu=/[a-zA-Z0-9_]/;function uu(e,t,n){const o=function(e,t){const n=ec({},ou,t),o=[];let r=n.start?"^":"";const s=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let a=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(ru,"\\$&"),a+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;s.push({name:e,repeatable:n,optional:c});const d=u||nu;if(d!==nu){a+=10;try{new RegExp(`(${d})`)}catch(i){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+i.message)}}let p=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(p=c&&l.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),r+=p,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const a=new RegExp(r,n.sensitive?"":"i");return{re:a,score:o,keys:s,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=s[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:s,repeatable:a,optional:i}=e,l=s in t?t[s]:"";if(oc(l)&&!a)throw new Error(`Provided param "${s}" is an array but it is not repeatable (* or + modifiers)`);const c=oc(l)?l.join("/"):l;if(!c){if(!i)throw new Error(`Missing required param "${s}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[lu]];if(!e.startsWith("/"))throw new Error(`Route paths should start with a "/": "${e}" should be "/${e}".`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let s;function a(){s&&r.push(s),s=[]}let i,l=0,c="",u="";function d(){c&&(0===n?s.push({type:0,value:c}):1===n||2===n||3===n?(s.length>1&&("*"===i||"+"===i)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:c,regexp:u,repeatable:"*"===i||"+"===i,optional:"*"===i||"?"===i})):t("Invalid state to consume buffer"),c="")}function p(){c+=i}for(;l<e.length;)if(i=e[l++],"\\"!==i||2===n)switch(n){case 0:"/"===i?(c&&d(),a()):":"===i?(d(),n=1):p();break;case 4:p(),n=o;break;case 1:"("===i?n=2:cu.test(i)?p():(d(),n=0,"*"!==i&&"?"!==i&&"+"!==i&&l--);break;case 2:")"===i?"\\"==u[u.length-1]?u=u.slice(0,-1)+i:n=3:u+=i;break;case 3:d(),n=0,"*"!==i&&"?"!==i&&"+"!==i&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),a(),r}(e.path),n);{const t=new Set;for(const n of o.keys)t.has(n.name)&&rc(`Found duplicated params with name "${n.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),t.add(n.name)}const r=ec(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function du(e,t){const n=[],o=new Map;function r(e,n,o){const i=!o,l=fu(e);!function(e,t){t&&t.record.name&&!e.name&&!e.path&&rc(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}(l,n),l.aliasOf=o&&o.record;const c=vu(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(fu(ec({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l})))}let d,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if("*"===t.path)throw new Error('Catch all routes ("*") must now be defined using a param with a custom regexp.\nSee more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.');if(d=uu(t,n,c),n&&"/"===u[0]&&wu(d,n),o?(o.alias.push(d),bu(o,d)):(p=p||d,p!==d&&p.alias.push(d),i&&e.name&&!mu(d)&&(_u(e,n),s(e.name))),Su(d)&&a(d),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d}return p?()=>{s(p)}:nc}function s(e){if(Jc(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(s),t.alias.forEach(s))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(s),e.alias.forEach(s))}}function a(e){const t=function(e,t){let n=0,o=t.length;for(;n!==o;){const r=n+o>>1;au(e,t[r])<0?o=r:n=r+1}const r=function(e){let t=e;for(;t=t.parent;)if(Su(t)&&0===au(e,t))return t;return}(e);r&&(o=t.lastIndexOf(r,o-1),o<0&&rc(`Finding ancestor route "${r.record.path}" failed for "${e.record.path}"`));return o}(e,n);n.splice(t,0,e),e.record.name&&!mu(e)&&o.set(e.record.name,e)}return t=vu({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,s,a,i={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw Zc(1,{location:e});{const t=Object.keys(e.params||{}).filter((e=>!r.keys.find((t=>t.name===e))));t.length&&rc(`Discarded invalid param(s) "${t.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}a=r.record.name,i=ec(pu(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&pu(e.params,r.keys.map((e=>e.name)))),s=r.stringify(i)}else if(null!=e.path)s=e.path,s.startsWith("/")||rc(`The Matcher cannot resolve relative paths but received "${s}". Unless you directly called \`matcher.resolve("${s}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),r=n.find((e=>e.re.test(s))),r&&(i=r.parse(s),a=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw Zc(1,{location:e,currentLocation:t});a=r.record.name,i=ec({},t.params,e.params),s=r.stringify(i)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:s,params:i,matched:l,meta:gu(l)}},removeRoute:s,clearRoutes:function(){n.length=0,o.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function pu(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function fu(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:hu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function hu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function mu(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function gu(e){return e.reduce(((e,t)=>ec(e,t.meta)),{})}function vu(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function yu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function bu(e,t){for(const n of e.keys)if(!n.optional&&!t.keys.find(yu.bind(null,n)))return rc(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${n.name}"`);for(const n of t.keys)if(!n.optional&&!e.keys.find(yu.bind(null,n)))return rc(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${n.name}"`)}function _u(e,t){for(let n=t;n;n=n.parent)if(n.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===n?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function wu(e,t){for(const n of t.keys)if(!e.keys.find(yu.bind(null,n)))return rc(`Absolute path "${e.record.path}" must have the exact same param named "${n.name}" as its parent "${t.record.path}".`)}function Su({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function xu(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(uc," "),r=e.indexOf("="),s=Sc(r<0?e:e.slice(0,r)),a=r<0?null:Sc(e.slice(r+1));if(s in t){let e=t[s];oc(e)||(e=t[s]=[e]),e.push(a)}else t[s]=a}return t}function ku(e){let t="";for(let n in e){const o=e[n];if(n=_c(n).replace(lc,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(oc(o)?o.map((e=>e&&_c(e))):[o&&_c(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Cu(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=oc(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const $u=Symbol("router view location matched"),Eu=Symbol("router view depth"),Ou=Symbol("router"),Tu=Symbol("route location"),Iu=Symbol("router view location");function Au(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function ju(e,t,n,o,r,s=e=>e()){const a=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((i,l)=>{const c=e=>{!1===e?l(Zc(4,{from:n,to:t})):e instanceof Error?l(e):Gc(e)?l(Zc(2,{from:t,to:e})):(a&&o.enterCallbacks[r]===a&&"function"==typeof e&&a.push(e),i())},u=s((()=>e.call(o&&o.instances[r],t,n,function(e,t,n){let o=0;return function(){1===o++&&rc(`The "next" callback was called more than once in one navigation guard when going from "${n.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,1===o&&e.apply(null,arguments)}}(c,t,n))));let d=Promise.resolve(u);if(e.length<3&&(d=d.then(c)),e.length>2){const t=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:\n${e.toString()}\n. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if("object"==typeof u&&"then"in u)d=d.then((e=>c._called?e:(rc(t),Promise.reject(new Error("Invalid navigation guard")))));else if(void 0!==u&&!c._called)return rc(t),void l(new Error("Invalid navigation guard"))}d.catch((e=>l(e)))}))}function Ru(e,t,n,o,r=e=>e()){const s=[];for(const a of e){a.components||a.children.length||rc(`Record with path "${a.path}" is either missing a "component(s)" or "children" property.`);for(const e in a.components){let i=a.components[e];if(!i||"object"!=typeof i&&"function"!=typeof i)throw rc(`Component "${e}" in record with path "${a.path}" is not a valid component. Received "${String(i)}".`),new Error("Invalid route component");if("then"in i){rc(`Component "${e}" in record with path "${a.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const t=i;i=()=>t}else i.__asyncLoader&&!i.__warnedDefineAsync&&(i.__warnedDefineAsync=!0,rc(`Component "${e}" in record with path "${a.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`));if("beforeRouteEnter"===t||a.instances[e])if(Zl(i)){const l=(i.__vccOpts||i)[t];l&&s.push(ju(l,n,o,a,e,r))}else{let l=i();"catch"in l||(rc(`Component "${e}" in record with path "${a.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),l=Promise.resolve(l)),s.push((()=>l.then((s=>{if(!s)throw new Error(`Couldn't resolve component "${e}" at "${a.path}"`);const i=(l=s).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&Zl(l.default)?s.default:s;var l;a.mods[e]=s,a.components[e]=i;const c=(i.__vccOpts||i)[t];return c&&ju(c,n,o,a,e,r)()}))))}}}return s}function Pu(e){const t=gr(Ou),n=gr(Tu);let o=!1,r=null;const s=ua((()=>{const n=Lt(e.to);return o&&n===r||(Gc(n)||(o?rc('Invalid value for prop "to" in useLink()\n- to:',n,"\n- previous to:",r,"\n- props:",e):rc('Invalid value for prop "to" in useLink()\n- to:',n,"\n- props:",e)),r=n,o=!0),t.resolve(n)})),a=ua((()=>{const{matched:e}=s.value,{length:t}=e,o=e[t-1],r=n.matched;if(!o||!r.length)return-1;const a=r.findIndex(Ec.bind(null,o));if(a>-1)return a;const i=Du(e[t-2]);return t>1&&Du(o)===i&&r[r.length-1].path!==i?r.findIndex(Ec.bind(null,e[t-2])):a})),i=ua((()=>a.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!oc(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,s.value.params))),l=ua((()=>a.value>-1&&a.value===n.matched.length-1&&Oc(n.params,s.value.params)));if(Ql){const t=zs();if(t){const n={route:s.value,isActive:i.value,isExactActive:l.value,error:null};t.__vrl_devtools=t.__vrl_devtools||[],t.__vrl_devtools.push(n),Qr((()=>{n.route=s.value,n.isActive=i.value,n.isExactActive=l.value,n.error=Gc(Lt(e.to))?null:'Invalid "to" value'}),null,{flush:"post"})}}return{route:s,href:ua((()=>s.value.href)),isActive:i,isExactActive:l,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[Lt(e.replace)?"replace":"push"](Lt(e.to)).catch(nc);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition((()=>n)),n}return Promise.resolve()}}}const Lu=uo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Pu,setup(e,{slots:t}){const n=yt(Pu(e)),{options:o}=gr(Ou),r=ua((()=>({[Vu(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Vu(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&(1===(s=t.default(n)).length?s[0]:s);var s;return e.custom?o:da("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function Du(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Vu=(e,t,n)=>null!=e?e:null!=t?t:n;function Nu(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Mu=uo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){!function(){const e=zs(),t=e.parent&&e.parent.type.name,n=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&("KeepAlive"===t||t.includes("Transition"))&&"object"==typeof n&&"RouterView"===n.name){const e="KeepAlive"===t?"keep-alive":"transition";rc(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\nUse slot props instead:\n\n<router-view v-slot="{ Component }">\n  <${e}>\n    <component :is="Component" />\n  </${e}>\n</router-view>`)}}();const o=gr(Iu),r=ua((()=>e.route||o.value)),s=gr(Eu,0),a=ua((()=>{let e=Lt(s);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),i=ua((()=>r.value.matched[a.value]));mr(Eu,ua((()=>a.value+1))),mr($u,i),mr(Iu,r);const l=jt();return Xr((()=>[l.value,i.value,e.name]),(([e,t,n],[o,r,s])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Ec(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,s=e.name,c=i.value,u=c&&c.components[s];if(!u)return Nu(n.default,{Component:u,route:o});const d=c.props[s],p=d?!0===d?o.params:"function"==typeof d?d(o):d:null,f=da(u,ec({},p,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(c.instances[s]=null)},ref:l}));if(Ql&&f.ref){const e={depth:a.value,name:c.name,path:c.path,meta:c.meta};(oc(f.ref)?f.ref.map((e=>e.i)):[f.ref.i]).forEach((t=>{t.__vrv_devtools=e}))}return Nu(n.default,{Component:f,route:o})||f}}});function Uu(e,t){const n=ec({},e,{matched:e.matched.map((e=>function(e,t){const n={};for(const o in e)t.includes(o)||(n[o]=e[o]);return n}(e,["instances","children","aliasOf"])))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:n}}}function Fu(e){return{_custom:{display:e}}}let Bu=0;function qu(e,t,n){if(t.__hasDevtools)return;t.__hasDevtools=!0;const o=Bu++;Xl({id:"org.vuejs.router"+(o?"."+o:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},(r=>{"function"!=typeof r.now&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),r.on.inspectComponent(((e,n)=>{e.instanceData&&e.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Uu(t.currentRoute.value,"Current Route")})})),r.on.visitComponentTree((({treeNode:e,componentInstance:t})=>{if(t.__vrv_devtools){const n=t.__vrv_devtools;e.tags.push({label:(n.name?`${n.name.toString()}: `:"")+n.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:Hu})}oc(t.__vrl_devtools)&&(t.__devtoolsApi=r,t.__vrl_devtools.forEach((t=>{let n=t.route.path,o=Ku,r="",s=0;t.error?(n=t.error,o=Xu,s=Qu):t.isExactActive?(o=Gu,r="This is exactly active"):t.isActive&&(o=Wu,r="This link is active"),e.tags.push({label:n,textColor:s,tooltip:r,backgroundColor:o})})))})),Xr(t.currentRoute,(()=>{l(),r.notifyComponentUpdate(),r.sendInspectorTree(i),r.sendInspectorState(i)}));const s="router:navigations:"+o;r.addTimelineLayer({id:s,label:`Router${o?" "+o:""} Navigations`,color:4237508}),t.onError(((e,t)=>{r.addTimelineEvent({layerId:s,event:{title:"Error during Navigation",subtitle:t.fullPath,logType:"error",time:r.now(),data:{error:e},groupId:t.meta.__navigationId}})}));let a=0;t.beforeEach(((e,t)=>{const n={guard:Fu("beforeEach"),from:Uu(t,"Current Location during this navigation"),to:Uu(e,"Target location")};Object.defineProperty(e.meta,"__navigationId",{value:a++}),r.addTimelineEvent({layerId:s,event:{time:r.now(),title:"Start of navigation",subtitle:e.fullPath,data:n,groupId:e.meta.__navigationId}})})),t.afterEach(((e,t,n)=>{const o={guard:Fu("afterEach")};n?(o.failure={_custom:{type:Error,readOnly:!0,display:n?n.message:"",tooltip:"Navigation Failure",value:n}},o.status=Fu("❌")):o.status=Fu("✅"),o.from=Uu(t,"Current Location during this navigation"),o.to=Uu(e,"Target location"),r.addTimelineEvent({layerId:s,event:{title:"End of navigation",subtitle:e.fullPath,time:r.now(),data:o,logType:n?"warning":"default",groupId:e.meta.__navigationId}})}));const i="router-inspector:"+o;function l(){if(!c)return;const e=c;let o=n.getRoutes().filter((e=>!e.parent||!e.parent.record.components));o.forEach(od),e.filter&&(o=o.filter((t=>rd(t,e.filter.toLowerCase())))),o.forEach((e=>nd(e,t.currentRoute.value))),e.rootNodes=o.map(Zu)}let c;r.addInspector({id:i,label:"Routes"+(o?" "+o:""),icon:"book",treeFilterPlaceholder:"Search routes"}),r.on.getInspectorTree((t=>{c=t,t.app===e&&t.inspectorId===i&&l()})),r.on.getInspectorState((t=>{if(t.app===e&&t.inspectorId===i){const e=n.getRoutes().find((e=>e.record.__vd_id===t.nodeId));e&&(t.state={options:zu(e)})}})),r.sendInspectorTree(i),r.sendInspectorState(i)}))}function zu(e){const{record:t}=e,n=[{editable:!1,key:"path",value:t.path}];return null!=t.name&&n.push({editable:!1,key:"name",value:t.name}),n.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&n.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map((e=>`${e.name}${function(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}(e)}`)).join(" "),tooltip:"Param keys",value:e.keys}}}),null!=t.redirect&&n.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&n.push({editable:!1,key:"aliases",value:e.alias.map((e=>e.record.path))}),Object.keys(e.record.meta).length&&n.push({editable:!1,key:"meta",value:e.record.meta}),n.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map((e=>e.join(", "))).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),n}const Hu=15485081,Wu=2450411,Gu=8702998,Ju=2282478,Ku=16486972,Yu=6710886,Xu=16704226,Qu=12131356;function Zu(e){const t=[],{record:n}=e;null!=n.name&&t.push({label:String(n.name),textColor:0,backgroundColor:Ju}),n.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Ku}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:Hu}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Gu}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:Wu}),n.redirect&&t.push({label:"string"==typeof n.redirect?`redirect: ${n.redirect}`:"redirects",textColor:16777215,backgroundColor:Yu});let o=n.__vd_id;return null==o&&(o=String(ed++),n.__vd_id=o),{id:o,label:n.path,tags:t,children:e.children.map(Zu)}}let ed=0;const td=/^\/(.*)\/([a-z]*)$/;function nd(e,t){const n=t.matched.length&&Ec(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=n,n||(e.__vd_active=t.matched.some((t=>Ec(t,e.record)))),e.children.forEach((e=>nd(e,t)))}function od(e){e.__vd_match=!1,e.children.forEach(od)}function rd(e,t){const n=String(e.re).match(td);if(e.__vd_match=!1,!n||n.length<3)return!1;if(new RegExp(n[1].replace(/\$$/,""),n[2]).test(t))return e.children.forEach((e=>rd(e,t))),("/"!==e.record.path||"/"===t)&&(e.__vd_match=e.re.test(t),!0);const o=e.record.path.toLowerCase(),r=Sc(o);return!(t.startsWith("/")||!r.includes(t)&&!o.includes(t))||(!(!r.startsWith(t)&&!o.startsWith(t))||(!(!e.record.name||!String(e.record.name).includes(t))||e.children.some((e=>rd(e,t)))))}function sd(){return gr(Ou)}function ad(e){return gr(Tu)}const id=[{path:"/",redirect:"/login"},{path:"/status",name:"Status",component:()=>zl((()=>import("./status.044752a5.js")),["./status.044752a5.js","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./iconfont.2d75af05.js","./status.d881a304.css"],import.meta.url)},{path:"/verify",name:"verify",component:()=>zl((()=>import("./verify.e3d1558d.js")),[],import.meta.url)},{path:"/login",name:"Login",component:()=>zl((()=>import("./index.4332345e.js")),["./index.4332345e.js","./localLogin.6a42ce4f.js","./localLogin.f639b4eb.css","./wechat.23adad4b.js","./wechat.3b1b375f.css","./feishu.e6ba81d5.js","./dingtalk.3aa78e6e.js","./oauth2.bbc060fc.js","./oauth2.03d0b5c4.css","./iconfont.2d75af05.js","./sms.829ddc94.js","./sms.844b2c56.css","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./index.25be4b34.css"],import.meta.url)},{path:"/client",name:"Client",component:()=>zl((()=>import("./index.642fceb8.js")),["./index.642fceb8.js","./header.0b412779.js","./ASD.492c8837.js","./header.dcb4d233.css","./menu.f88a8c29.js","./iconfont.2d75af05.js","./menu.8bb454ca.css","./index.6b45d132.css"],import.meta.url),children:[{path:"/client/login",name:"ClientNewLogin",component:()=>zl((()=>import("./login.1b4b1a21.js")),["./login.1b4b1a21.js","./index.4332345e.js","./localLogin.6a42ce4f.js","./localLogin.f639b4eb.css","./wechat.23adad4b.js","./wechat.3b1b375f.css","./feishu.e6ba81d5.js","./dingtalk.3aa78e6e.js","./oauth2.bbc060fc.js","./oauth2.03d0b5c4.css","./iconfont.2d75af05.js","./sms.829ddc94.js","./sms.844b2c56.css","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./index.25be4b34.css"],import.meta.url)},{path:"/client/main",name:"ClientMain",component:()=>zl((()=>import("./main.b48f7b5e.js")),["./main.b48f7b5e.js","./index.058e1ded.js","./index.d3d3644e.css","./main.a77c3312.css"],import.meta.url)},{path:"/client/setting",name:"ClientSetting",component:()=>zl((()=>import("./setting.549678dd.js")),["./setting.549678dd.js","./setting.b14ec24d.css"],import.meta.url)}]},{path:"/clientLogin",name:"ClientLogin",component:()=>zl((()=>import("./clientLogin.e84e67be.js")),[],import.meta.url)},{path:"/downloadWin",name:"downloadWin",component:()=>zl((()=>import("./downloadWin.91b8ed53.js")),["./downloadWin.91b8ed53.js","./ASD.492c8837.js","./iconfont.2d75af05.js","./browser.dd769507.js","./downloadWin.91f39202.css"],import.meta.url)},{path:"/wx_oauth_callback",name:"WxOAuthCallback",component:()=>zl((()=>import("./wx_oauth_callback.23f2a03d.js")),["./wx_oauth_callback.23f2a03d.js","./iconfont.2d75af05.js"],import.meta.url)},{path:"/oauth2_result",name:"OAuth2Result",component:()=>zl((()=>import("./oauth2_result.b7727725.js")),["./oauth2_result.b7727725.js","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./oauth2_result.08376432.css"],import.meta.url)},{path:"/oauth2_premises",name:"OAuth2Premises",component:()=>zl((()=>import("./oauth2_premises.02b195a8.js")),["./oauth2_premises.02b195a8.js","./iconfont.2d75af05.js","./oauth2_premises.987b2776.css"],import.meta.url)}],ld=function(e){const t=du(e.routes,e),n=e.parseQuery||xu,o=e.stringifyQuery||ku,r=e.history;if(!r)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const s=Au(),a=Au(),i=Au(),l=Rt(Ac,!0);let c=Ac;Ql&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=tc.bind(null,(e=>""+e)),d=tc.bind(null,wc),p=tc.bind(null,Sc);function f(e,s){if(s=ec({},s||l.value),"string"==typeof e){const o=kc(n,e,s.path),a=t.resolve({path:o.path},s),i=r.createHref(o.fullPath);return i.startsWith("//")?rc(`Location "${e}" resolved to "${i}". A resolved location cannot start with multiple slashes.`):a.matched.length||rc(`No match found for location with path "${e}"`),ec(o,a,{params:p(a.params),hash:Sc(o.hash),redirectedFrom:void 0,href:i})}if(!Gc(e))return rc("router.resolve() was passed an invalid location. This will fail in production.\n- Location:",e),f({});let a;if(null!=e.path)"params"in e&&!("name"in e)&&Object.keys(e.params).length&&rc(`Path "${e.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),a=ec({},e,{path:kc(n,e.path,s.path).path});else{const t=ec({},e.params);for(const e in t)null==t[e]&&delete t[e];a=ec({},e,{params:d(t)}),s.params=d(s.params)}const i=t.resolve(a,s),c=e.hash||"";c&&!c.startsWith("#")&&rc(`A \`hash\` should always start with the character "#". Replace "${c}" with "#${c}".`),i.params=u(p(i.params));const h=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,ec({},e,{hash:(m=c,bc(m).replace(mc,"{").replace(vc,"}").replace(fc,"^")),path:i.path}));var m;const g=r.createHref(h);return g.startsWith("//")?rc(`Location "${e}" resolved to "${g}". A resolved location cannot start with multiple slashes.`):i.matched.length||rc(`No match found for location with path "${null!=e.path?e.path:e}"`),ec({fullPath:h,hash:c,query:o===ku?Cu(e.query):e.query||{}},i,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?kc(n,e,l.value.path):ec({},e)}function m(e,t){if(c!==e)return Zc(8,{from:t,to:e})}function g(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;if("string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),null==o.path&&!("name"in o))throw rc(`Invalid redirect found:\n${JSON.stringify(o,null,2)}\n when navigating to "${e.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return ec({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=f(e),r=l.value,s=e.state,a=e.force,i=!0===e.replace,u=v(n);if(u)return y(ec(h(u),{state:"object"==typeof u?ec({},s,u.state):s,force:a,replace:i}),t||n);const d=n;let p;return d.redirectedFrom=t,!a&&$c(o,r,n)&&(p=Zc(16,{to:d,from:r}),A(r,r,!0,!1)),(p?Promise.resolve(p):w(d,r)).catch((e=>eu(e)?eu(e,2)?e:I(e):T(e,d,r))).then((e=>{if(e){if(eu(e,2))return $c(o,f(e.to),d)&&t&&(t._count=t._count?t._count+1:1)>30?(rc(`Detected a possibly infinite redirection in a navigation guard when going from "${r.fullPath}" to "${d.fullPath}". Aborting to avoid a Stack Overflow.\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):y(ec({replace:i},h(e.to),{state:"object"==typeof e.to?ec({},s,e.to.state):s,force:a}),t||d)}else e=x(d,r,!0,i,s);return S(d,r,e),e}))}function b(e,t){const n=m(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=P.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,i]=function(e,t){const n=[],o=[],r=[],s=Math.max(t.matched.length,e.matched.length);for(let a=0;a<s;a++){const s=t.matched[a];s&&(e.matched.find((e=>Ec(e,s)))?o.push(s):n.push(s));const i=e.matched[a];i&&(t.matched.find((e=>Ec(e,i)))||r.push(i))}return[n,o,r]}(e,t);n=Ru(o.reverse(),"beforeRouteLeave",e,t);for(const s of o)s.leaveGuards.forEach((o=>{n.push(ju(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),D(n).then((()=>{n=[];for(const o of s.list())n.push(ju(o,e,t));return n.push(l),D(n)})).then((()=>{n=Ru(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(ju(o,e,t))}));return n.push(l),D(n)})).then((()=>{n=[];for(const o of i)if(o.beforeEnter)if(oc(o.beforeEnter))for(const r of o.beforeEnter)n.push(ju(r,e,t));else n.push(ju(o.beforeEnter,e,t));return n.push(l),D(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ru(i,"beforeRouteEnter",e,t,_),n.push(l),D(n)))).then((()=>{n=[];for(const o of a.list())n.push(ju(o,e,t));return n.push(l),D(n)})).catch((e=>eu(e,8)?e:Promise.reject(e)))}function S(e,t,n){i.list().forEach((o=>_((()=>o(e,t,n)))))}function x(e,t,n,o,s){const a=m(e,t);if(a)return a;const i=t===Ac,c=Ql?history.state:{};n&&(o||i?r.replace(e.fullPath,ec({scroll:i&&c&&c.scroll},s)):r.push(e.fullPath,s)),l.value=e,A(e,t,n,i),I()}let k;function C(){k||(k=r.listen(((e,t,n)=>{if(!L.listening)return;const o=f(e),s=v(o);if(s)return void y(ec(s,{replace:!0,force:!0}),o).catch(nc);c=o;const a=l.value;var i,u;Ql&&(i=Fc(a.fullPath,n.delta),u=Mc(),Bc.set(i,u)),w(o,a).catch((e=>eu(e,12)?e:eu(e,2)?(y(ec(h(e.to),{force:!0}),o).then((e=>{eu(e,20)&&!n.delta&&n.type===jc.pop&&r.go(-1,!1)})).catch(nc),Promise.reject()):(n.delta&&r.go(-n.delta,!1),T(e,o,a)))).then((e=>{(e=e||x(o,a,!1))&&(n.delta&&!eu(e,8)?r.go(-n.delta,!1):n.type===jc.pop&&eu(e,20)&&r.go(-1,!1)),S(o,a,e)})).catch(nc)})))}let $,E=Au(),O=Au();function T(e,t,n){I(e);const o=O.list();return o.length?o.forEach((o=>o(e,t,n))):(rc("uncaught error during route navigation:"),console.error(e)),Promise.reject(e)}function I(e){return $||($=!e,C(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function A(t,n,o,r){const{scrollBehavior:s}=e;if(!Ql||!s)return Promise.resolve();const a=!o&&function(e){const t=Bc.get(e);return Bc.delete(e),t}(Fc(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return hn().then((()=>s(t,n,a))).then((e=>e&&Uc(e))).catch((e=>T(e,t,n)))}const j=e=>r.go(e);let R;const P=new Set,L={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return Jc(e)?(o=t.getRecordMatcher(e),o||rc(`Parent route "${String(e)}" not found when adding child route`,n),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n?t.removeRoute(n):rc(`Cannot remove non-existent route "${String(e)}"`)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:f,options:e,push:g,replace:function(e){return g(ec(h(e),{replace:!0}))},go:j,back:()=>j(-1),forward:()=>j(1),beforeEach:s.add,beforeResolve:a.add,afterEach:i.add,onError:O.add,isReady:function(){return $&&l.value!==Ac?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){const n=this;e.component("RouterLink",Lu),e.component("RouterView",Mu),e.config.globalProperties.$router=n,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Lt(l)}),Ql&&!R&&l.value===Ac&&(R=!0,g(r.location).catch((e=>{rc("Unexpected error when starting the router:",e)})));const o={};for(const t in Ac)Object.defineProperty(o,t,{get:()=>l.value[t],enumerable:!0});e.provide(Ou,n),e.provide(Tu,bt(o)),e.provide(Iu,l);const s=e.unmount;P.add(e),e.unmount=function(){P.delete(e),P.size<1&&(c=Ac,k&&k(),k=null,l.value=Ac,R=!1,$=!1),s()},Ql&&qu(e,n,t)}};function D(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return L}({history:((cd=location.host?cd||location.pathname+location.search:"").includes("#")||(cd+="#"),cd.endsWith("#/")||cd.endsWith("#")||rc(`A hash base must end with a "#":\n"${cd}" should be "${cd.replace(/#.*$/,"#")}".`),Wc(cd)),routes:id});var cd;ld.beforeEach((async(e,t,n)=>{const o=window.location.href,r=window.location.origin;if(logger.log("Router beforeEach Current URL:",o,"origin:",r),!o.startsWith(r+"/#/")){console.log("Hash is not at the correct position");const e=o.indexOf("#");let t;if(-1===e)t=`${r}/#${o.substring(r.length)}`;else{let n=o.substring(r.length,e);const s=o.substring(e);n=n.replace(/^\/\?/,"&"),console.log("beforeHash:",n),console.log("afterHash:",s),t=`${r}/${s}${n}`}return console.log("Final new URL:",t),void window.location.replace(t)}logger.log("Proceeding with normal navigation"),n()}));var ud="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function dd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var pd={exports:{}},fd={exports:{}},hd=function(e,t){return function(){for(var n=new Array(arguments.length),o=0;o<n.length;o++)n[o]=arguments[o];return e.apply(t,n)}},md=hd,gd=Object.prototype.toString;function vd(e){return"[object Array]"===gd.call(e)}function yd(e){return void 0===e}function bd(e){return null!==e&&"object"==typeof e}function _d(e){return"[object Function]"===gd.call(e)}function wd(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),vd(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.call(null,e[r],r,e)}var Sd={isArray:vd,isArrayBuffer:function(e){return"[object ArrayBuffer]"===gd.call(e)},isBuffer:function(e){return null!==e&&!yd(e)&&null!==e.constructor&&!yd(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:bd,isUndefined:yd,isDate:function(e){return"[object Date]"===gd.call(e)},isFile:function(e){return"[object File]"===gd.call(e)},isBlob:function(e){return"[object Blob]"===gd.call(e)},isFunction:_d,isStream:function(e){return bd(e)&&_d(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:wd,merge:function e(){var t={};function n(n,o){"object"==typeof t[o]&&"object"==typeof n?t[o]=e(t[o],n):t[o]=n}for(var o=0,r=arguments.length;o<r;o++)wd(arguments[o],n);return t},deepMerge:function e(){var t={};function n(n,o){"object"==typeof t[o]&&"object"==typeof n?t[o]=e(t[o],n):t[o]="object"==typeof n?e({},n):n}for(var o=0,r=arguments.length;o<r;o++)wd(arguments[o],n);return t},extend:function(e,t,n){return wd(t,(function(t,o){e[o]=n&&"function"==typeof t?md(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}},xd=Sd;function kd(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var Cd=function(e,t,n){if(!t)return e;var o;if(n)o=n(t);else if(xd.isURLSearchParams(t))o=t.toString();else{var r=[];xd.forEach(t,(function(e,t){null!=e&&(xd.isArray(e)?t+="[]":e=[e],xd.forEach(e,(function(e){xd.isDate(e)?e=e.toISOString():xd.isObject(e)&&(e=JSON.stringify(e)),r.push(kd(t)+"="+kd(e))})))})),o=r.join("&")}if(o){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e},$d=Sd;function Ed(){this.handlers=[]}Ed.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Ed.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Ed.prototype.forEach=function(e){$d.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var Od,Td,Id=Ed,Ad=Sd;function jd(){return Td?Od:(Td=1,Od=function(e){return!(!e||!e.__CANCEL__)})}var Rd,Pd,Ld,Dd,Vd,Nd,Md,Ud,Fd,Bd,qd,zd,Hd,Wd,Gd,Jd,Kd,Yd,Xd,Qd,Zd=Sd;function ep(){if(Dd)return Ld;Dd=1;var e=Pd?Rd:(Pd=1,Rd=function(e,t,n,o,r){return e.config=t,n&&(e.code=n),e.request=o,e.response=r,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e});return Ld=function(t,n,o,r,s){var a=new Error(t);return e(a,n,o,r,s)}}function tp(){if(zd)return qd;zd=1;var e=Ud?Md:(Ud=1,Md=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}),t=Bd?Fd:(Bd=1,Fd=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e});return qd=function(n,o){return n&&!e(o)?t(n,o):o}}function np(){if(Qd)return Xd;Qd=1;var e=Sd,t=function(){if(Nd)return Vd;Nd=1;var e=ep();return Vd=function(t,n,o){var r=o.config.validateStatus;!r||r(o.status)?t(o):n(e("Request failed with status code "+o.status,o.config,null,o.request,o))}}(),n=Cd,o=tp(),r=function(){if(Wd)return Hd;Wd=1;var e=Sd,t=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];return Hd=function(n){var o,r,s,a={};return n?(e.forEach(n.split("\n"),(function(n){if(s=n.indexOf(":"),o=e.trim(n.substr(0,s)).toLowerCase(),r=e.trim(n.substr(s+1)),o){if(a[o]&&t.indexOf(o)>=0)return;a[o]="set-cookie"===o?(a[o]?a[o]:[]).concat([r]):a[o]?a[o]+", "+r:r}})),a):a}}(),s=function(){if(Jd)return Gd;Jd=1;var e=Sd;return Gd=e.isStandardBrowserEnv()?function(){var t,n=/(msie|trident)/i.test(navigator.userAgent),o=document.createElement("a");function r(e){var t=e;return n&&(o.setAttribute("href",t),t=o.href),o.setAttribute("href",t),{href:o.href,protocol:o.protocol?o.protocol.replace(/:$/,""):"",host:o.host,search:o.search?o.search.replace(/^\?/,""):"",hash:o.hash?o.hash.replace(/^#/,""):"",hostname:o.hostname,port:o.port,pathname:"/"===o.pathname.charAt(0)?o.pathname:"/"+o.pathname}}return t=r(window.location.href),function(n){var o=e.isString(n)?r(n):n;return o.protocol===t.protocol&&o.host===t.host}}():function(){return!0}}(),a=ep();return Xd=function(i){return new Promise((function(l,c){var u=i.data,d=i.headers;e.isFormData(u)&&delete d["Content-Type"];var p=new XMLHttpRequest;if(i.auth){var f=i.auth.username||"",h=i.auth.password||"";d.Authorization="Basic "+btoa(f+":"+h)}var m=o(i.baseURL,i.url);if(p.open(i.method.toUpperCase(),n(m,i.params,i.paramsSerializer),!0),p.timeout=i.timeout,p.onreadystatechange=function(){if(p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var e="getAllResponseHeaders"in p?r(p.getAllResponseHeaders()):null,n={data:i.responseType&&"text"!==i.responseType?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:e,config:i,request:p};t(l,c,n),p=null}},p.onabort=function(){p&&(c(a("Request aborted",i,"ECONNABORTED",p)),p=null)},p.onerror=function(){c(a("Network Error",i,null,p)),p=null},p.ontimeout=function(){var e="timeout of "+i.timeout+"ms exceeded";i.timeoutErrorMessage&&(e=i.timeoutErrorMessage),c(a(e,i,"ECONNABORTED",p)),p=null},e.isStandardBrowserEnv()){var g=function(){if(Yd)return Kd;Yd=1;var e=Sd;return Kd=e.isStandardBrowserEnv()?{write:function(t,n,o,r,s,a){var i=[];i.push(t+"="+encodeURIComponent(n)),e.isNumber(o)&&i.push("expires="+new Date(o).toGMTString()),e.isString(r)&&i.push("path="+r),e.isString(s)&&i.push("domain="+s),!0===a&&i.push("secure"),document.cookie=i.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}}(),v=(i.withCredentials||s(m))&&i.xsrfCookieName?g.read(i.xsrfCookieName):void 0;v&&(d[i.xsrfHeaderName]=v)}if("setRequestHeader"in p&&e.forEach(d,(function(e,t){void 0===u&&"content-type"===t.toLowerCase()?delete d[t]:p.setRequestHeader(t,e)})),e.isUndefined(i.withCredentials)||(p.withCredentials=!!i.withCredentials),i.responseType)try{p.responseType=i.responseType}catch(Yh){if("json"!==i.responseType)throw Yh}"function"==typeof i.onDownloadProgress&&p.addEventListener("progress",i.onDownloadProgress),"function"==typeof i.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",i.onUploadProgress),i.cancelToken&&i.cancelToken.promise.then((function(e){p&&(p.abort(),c(e),p=null)})),void 0===u&&(u=null),p.send(u)}))}}var op=Sd,rp=function(e,t){Zd.forEach(e,(function(n,o){o!==t&&o.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[o])}))},sp={"Content-Type":"application/x-www-form-urlencoded"};function ap(e,t){!op.isUndefined(e)&&op.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var ip,lp={adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(ip=np()),ip),transformRequest:[function(e,t){return rp(t,"Accept"),rp(t,"Content-Type"),op.isFormData(e)||op.isArrayBuffer(e)||op.isBuffer(e)||op.isStream(e)||op.isFile(e)||op.isBlob(e)?e:op.isArrayBufferView(e)?e.buffer:op.isURLSearchParams(e)?(ap(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):op.isObject(e)?(ap(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(Yh){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};lp.headers={common:{Accept:"application/json, text/plain, */*"}},op.forEach(["delete","get","head"],(function(e){lp.headers[e]={}})),op.forEach(["post","put","patch"],(function(e){lp.headers[e]=op.merge(sp)}));var cp=lp,up=Sd,dp=function(e,t,n){return Ad.forEach(n,(function(n){e=n(e,t)})),e},pp=jd(),fp=cp;function hp(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var mp,gp,vp,yp,bp,_p,wp=Sd,Sp=function(e,t){t=t||{};var n={},o=["url","method","params","data"],r=["headers","auth","proxy"],s=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];wp.forEach(o,(function(e){void 0!==t[e]&&(n[e]=t[e])})),wp.forEach(r,(function(o){wp.isObject(t[o])?n[o]=wp.deepMerge(e[o],t[o]):void 0!==t[o]?n[o]=t[o]:wp.isObject(e[o])?n[o]=wp.deepMerge(e[o]):void 0!==e[o]&&(n[o]=e[o])})),wp.forEach(s,(function(o){void 0!==t[o]?n[o]=t[o]:void 0!==e[o]&&(n[o]=e[o])}));var a=o.concat(r).concat(s),i=Object.keys(t).filter((function(e){return-1===a.indexOf(e)}));return wp.forEach(i,(function(o){void 0!==t[o]?n[o]=t[o]:void 0!==e[o]&&(n[o]=e[o])})),n},xp=Sd,kp=Cd,Cp=Id,$p=function(e){return hp(e),e.headers=e.headers||{},e.data=dp(e.data,e.headers,e.transformRequest),e.headers=up.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),up.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||fp.adapter)(e).then((function(t){return hp(e),t.data=dp(t.data,t.headers,e.transformResponse),t}),(function(t){return pp(t)||(hp(e),t&&t.response&&(t.response.data=dp(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},Ep=Sp;function Op(e){this.defaults=e,this.interceptors={request:new Cp,response:new Cp}}function Tp(){if(gp)return mp;function e(e){this.message=e}return gp=1,e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,mp=e}Op.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=Ep(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[$p,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},Op.prototype.getUri=function(e){return e=Ep(this.defaults,e),kp(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},xp.forEach(["delete","get","head","options"],(function(e){Op.prototype[e]=function(t,n){return this.request(xp.merge(n||{},{method:e,url:t}))}})),xp.forEach(["post","put","patch"],(function(e){Op.prototype[e]=function(t,n,o){return this.request(xp.merge(o||{},{method:e,url:t,data:n}))}}));var Ip=Sd,Ap=hd,jp=Op,Rp=Sp;function Pp(e){var t=new jp(e),n=Ap(jp.prototype.request,t);return Ip.extend(n,jp.prototype,t),Ip.extend(n,t),n}var Lp=Pp(cp);Lp.Axios=jp,Lp.create=function(e){return Pp(Rp(Lp.defaults,e))},Lp.Cancel=Tp(),Lp.CancelToken=function(){if(yp)return vp;yp=1;var e=Tp();function t(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var n;this.promise=new Promise((function(e){n=e}));var o=this;t((function(t){o.reason||(o.reason=new e(t),n(o.reason))}))}return t.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},t.source=function(){var e;return{token:new t((function(t){e=t})),cancel:e}},vp=t}(),Lp.isCancel=jd(),Lp.all=function(e){return Promise.all(e)},Lp.spread=_p?bp:(_p=1,bp=function(e){return function(t){return e.apply(null,t)}}),fd.exports=Lp,fd.exports.default=Lp;const Dp=dd(pd.exports=fd.exports);const Vp={all:Np=Np||new Map,on:function(e,t){var n=Np.get(e);n?n.push(t):Np.set(e,[t])},off:function(e,t){var n=Np.get(e);n&&(t?n.splice(n.indexOf(t)>>>0,1):Np.set(e,[]))},emit:function(e,t){var n=Np.get(e);n&&n.slice().map((function(e){e(t)})),(n=Np.get("*"))&&n.slice().map((function(n){n(e,t)}))}};var Np;document.location.protocol,document.location.host;let Mp="";Mp="https://*************:";const Up=Dp.create({baseURL:"https://*************:",timeout:99999});let Fp,Bp=0;const qp=()=>{Bp--,Bp<=0&&(clearTimeout(Fp),Vp.emit("closeLoading"))};Up.interceptors.request.use((e=>{const t=Ah();return e.donNotShowLoading||(Bp++,Fp&&clearTimeout(Fp),Fp=setTimeout((()=>{Bp>0&&Vp.emit("showLoading")}),400)),"console"===e.url.match(/(\w+\/){0}\w+/)[0]&&(e.baseURL="https://*************"),e.headers={"Content-Type":"application/json",...e.headers},t.token.accessToken&&(e.url.includes("refresh_token")?e.headers.Authorization=`${t.token.tokenType} ${t.token.refreshToken}`:e.headers.Authorization=`${t.token.tokenType} ${t.token.accessToken}`),e}),(e=>(qp(),Dl({showClose:!0,message:e,type:"error"}),e))),Up.interceptors.response.use((e=>{const t=Ah();return qp(),e.headers["new-token"]&&t.setToken(e.headers["new-token"]),logger.log("请求：",{request_url:e.config.url,response:e}),200===e.status||204===e.status||201===e.status||"true"===e.headers.success?e:(Dl({showClose:!0,message:e.data.msg||decodeURI(e.headers.msg),type:"error"}),e.data.data&&e.data.data.reload&&(t.token="",localStorage.clear(),ld.push({name:"Login",replace:!0})),e.data.msg?e.data:e)}),(e=>{const t=Ah();if(qp(),e.response){switch(e.response.status){case 500:Nl.confirm(`\n        <p>检测到接口错误${e}</p>\n        <p>错误码<span style="color:red"> 500 </span>：此类错误内容常见于后台panic，请先查看后台日志，如果影响您正常使用可强制登出清理缓存</p>\n        `,"接口报错",{dangerouslyUseHTMLString:!0,distinguishCancelAndClose:!0,confirmButtonText:"清理缓存",cancelButtonText:"取消"}).then((()=>{Ah().token="",localStorage.clear(),ld.push({name:"Login",replace:!0})}));break;case 404:Dl({showClose:!0,message:e.response.data.error,type:"error"});break;case 401:t.authFailureLoginOut();const n=window.localStorage.getItem("refresh_times")||0;window.localStorage.setItem("refresh_times",Number(n)+1);break;default:console.log(e.response),Dl({showClose:!0,message:e.response.data.errorMessage||e.response.data.error,type:"error"})}return e}Nl.confirm(`\n        <p>检测到请求错误</p>\n        <p>${e}</p>\n        `,"请求报错",{dangerouslyUseHTMLString:!0,distinguishCancelAndClose:!0,confirmButtonText:"稍后重试",cancelButtonText:"取消"})}));const zp=new XMLHttpRequest;zp.open("GET",document.location,!1),zp.send(null);const Hp=zp.getResponseHeader("X-Corp-ID")||"default";function Wp(e,t,n){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,n),n):(e[t]=n,n)}function Gp(e,t){Array.isArray(e)?e.splice(t,1):delete e[t]}
/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Jp;const Kp=e=>Jp=e,Yp=Symbol("pinia");function Xp(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var Qp,Zp;(Zp=Qp||(Qp={})).direct="direct",Zp.patchObject="patch object",Zp.patchFunction="patch function";const ef="undefined"!=typeof window,tf=(()=>"object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:"object"==typeof globalThis?globalThis:{HTMLElement:null})();function nf(e,t,n){const o=new XMLHttpRequest;o.open("GET",e),o.responseType="blob",o.onload=function(){lf(o.response,t,n)},o.onerror=function(){console.error("could not download file")},o.send()}function of(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(Yh){}return t.status>=200&&t.status<=299}function rf(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(Yh){const n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(n)}}const sf="object"==typeof navigator?navigator:{userAgent:""},af=(()=>/Macintosh/.test(sf.userAgent)&&/AppleWebKit/.test(sf.userAgent)&&!/Safari/.test(sf.userAgent))(),lf=ef?"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype&&!af?function(e,t="download",n){const o=document.createElement("a");o.download=t,o.rel="noopener","string"==typeof e?(o.href=e,o.origin!==location.origin?of(o.href)?nf(e,t,n):(o.target="_blank",rf(o)):rf(o)):(o.href=URL.createObjectURL(e),setTimeout((function(){URL.revokeObjectURL(o.href)}),4e4),setTimeout((function(){rf(o)}),0))}:"msSaveOrOpenBlob"in sf?function(e,t="download",n){if("string"==typeof e)if(of(e))nf(e,t,n);else{const t=document.createElement("a");t.href=e,t.target="_blank",setTimeout((function(){rf(t)}))}else navigator.msSaveOrOpenBlob(function(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}(e,n),t)}:function(e,t,n,o){(o=o||open("","_blank"))&&(o.document.title=o.document.body.innerText="downloading...");if("string"==typeof e)return nf(e,t,n);const r="application/octet-stream"===e.type,s=/constructor/i.test(String(tf.HTMLElement))||"safari"in tf,a=/CriOS\/[\d]+/.test(navigator.userAgent);if((a||r&&s||af)&&"undefined"!=typeof FileReader){const t=new FileReader;t.onloadend=function(){let e=t.result;if("string"!=typeof e)throw o=null,new Error("Wrong reader.result type");e=a?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=e:location.assign(e),o=null},t.readAsDataURL(e)}else{const t=URL.createObjectURL(e);o?o.location.assign(t):location.href=t,o=null,setTimeout((function(){URL.revokeObjectURL(t)}),4e4)}}:()=>{};function cf(e,t){const n="🍍 "+e;"function"==typeof __VUE_DEVTOOLS_TOAST__?__VUE_DEVTOOLS_TOAST__(n,t):"error"===t?console.error(n):"warn"===t?console.warn(n):console.log(n)}function uf(e){return"_a"in e&&"install"in e}function df(){if(!("clipboard"in navigator))return cf("Your browser doesn't support the Clipboard API","error"),!0}function pf(e){return!!(e instanceof Error&&e.message.toLowerCase().includes("document is not focused"))&&(cf('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0)}let ff;async function hf(e){try{const t=(ff||(ff=document.createElement("input"),ff.type="file",ff.accept=".json"),function(){return new Promise(((e,t)=>{ff.onchange=async()=>{const t=ff.files;if(!t)return e(null);const n=t.item(0);return e(n?{text:await n.text(),file:n}:null)},ff.oncancel=()=>e(null),ff.onerror=t,ff.click()}))}),n=await t();if(!n)return;const{text:o,file:r}=n;mf(e,JSON.parse(o)),cf(`Global state imported from "${r.name}".`)}catch(t){cf("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function mf(e,t){for(const n in t){const o=e.state.value[n];o?Object.assign(o,t[n]):e.state.value[n]=t[n]}}function gf(e){return{_custom:{display:e}}}const vf="🍍 Pinia (root)",yf="_root";function bf(e){return uf(e)?{id:yf,label:vf}:{id:e.$id,label:e.$id}}function _f(e){return e?Array.isArray(e)?e.reduce(((e,t)=>(e.keys.push(t.key),e.operations.push(t.type),e.oldValue[t.key]=t.oldValue,e.newValue[t.key]=t.newValue,e)),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:gf(e.type),key:gf(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function wf(e){switch(e){case Qp.direct:return"mutation";case Qp.patchFunction:case Qp.patchObject:return"$patch";default:return"unknown"}}let Sf=!0;const xf=[],kf="pinia:mutations",Cf="pinia",{assign:$f}=Object,Ef=e=>"🍍 "+e;function Of(e,t){Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:xf,app:e},(n=>{"function"!=typeof n.now&&cf("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.addTimelineLayer({id:kf,label:"Pinia 🍍",color:15064968}),n.addInspector({id:Cf,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{!async function(e){if(!df())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),cf("Global state copied to clipboard.")}catch(t){if(pf(t))return;cf("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await async function(e){if(!df())try{mf(e,JSON.parse(await navigator.clipboard.readText())),cf("Global state pasted from clipboard.")}catch(t){if(pf(t))return;cf("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}(t),n.sendInspectorTree(Cf),n.sendInspectorState(Cf)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{!async function(e){try{lf(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){cf("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await hf(t),n.sendInspectorTree(Cf),n.sendInspectorState(Cf)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:e=>{const n=t._s.get(e);n?"function"!=typeof n.$reset?cf(`Cannot reset "${e}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),cf(`Store "${e}" reset.`)):cf(`Cannot reset "${e}" store because it wasn't found.`,"warn")}}]}),n.on.inspectComponent(((e,t)=>{const n=e.componentInstance&&e.componentInstance.proxy;if(n&&n._pStores){const t=e.componentInstance.proxy._pStores;Object.values(t).forEach((t=>{e.instanceData.state.push({type:Ef(t.$id),key:"state",editable:!0,value:t._isOptionsAPI?{_custom:{value:Et(t.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>t.$reset()}]}}:Object.keys(t.$state).reduce(((e,n)=>(e[n]=t.$state[n],e)),{})}),t._getters&&t._getters.length&&e.instanceData.state.push({type:Ef(t.$id),key:"getters",editable:!1,value:t._getters.reduce(((e,n)=>{try{e[n]=t[n]}catch(o){e[n]=o}return e}),{})})}))}})),n.on.getInspectorTree((n=>{if(n.app===e&&n.inspectorId===Cf){let e=[t];e=e.concat(Array.from(t._s.values())),n.rootNodes=(n.filter?e.filter((e=>"$id"in e?e.$id.toLowerCase().includes(n.filter.toLowerCase()):vf.toLowerCase().includes(n.filter.toLowerCase()))):e).map(bf)}})),globalThis.$pinia=t,n.on.getInspectorState((n=>{if(n.app===e&&n.inspectorId===Cf){const e=n.nodeId===yf?t:t._s.get(n.nodeId);if(!e)return;e&&(n.nodeId!==yf&&(globalThis.$store=Et(e)),n.state=function(e){if(uf(e)){const t=Array.from(e._s.keys()),n=e._s;return{state:t.map((t=>({editable:!0,key:t,value:e.state.value[t]}))),getters:t.filter((e=>n.get(e)._getters)).map((e=>{const t=n.get(e);return{editable:!1,key:e,value:t._getters.reduce(((e,n)=>(e[n]=t[n],e)),{})}}))}}const t={state:Object.keys(e.$state).map((t=>({editable:!0,key:t,value:e.$state[t]})))};return e._getters&&e._getters.length&&(t.getters=e._getters.map((t=>({editable:!1,key:t,value:e[t]})))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map((t=>({editable:!0,key:t,value:e[t]})))),t}(e))}})),n.on.editInspectorState(((n,o)=>{if(n.app===e&&n.inspectorId===Cf){const e=n.nodeId===yf?t:t._s.get(n.nodeId);if(!e)return cf(`store "${n.nodeId}" not found`,"error");const{path:o}=n;uf(e)?o.unshift("state"):1===o.length&&e._customProperties.has(o[0])&&!(o[0]in e.$state)||o.unshift("$state"),Sf=!1,n.set(e,o,n.state.value),Sf=!0}})),n.on.editComponentState((e=>{if(e.type.startsWith("🍍")){const n=e.type.replace(/^🍍\s*/,""),o=t._s.get(n);if(!o)return cf(`store "${n}" not found`,"error");const{path:r}=e;if("state"!==r[0])return cf(`Invalid path for store "${n}":\n${r}\nOnly state can be modified.`);r[0]="$state",Sf=!1,e.set(o,r,e.state.value),Sf=!0}}))}))}let Tf,If=0;function Af(e,t,n){const o=t.reduce(((t,n)=>(t[n]=Et(e)[n],t)),{});for(const r in o)e[r]=function(){const t=If,s=n?new Proxy(e,{get:(...e)=>(Tf=t,Reflect.get(...e)),set:(...e)=>(Tf=t,Reflect.set(...e))}):e;Tf=t;const a=o[r].apply(s,arguments);return Tf=void 0,a}}function jf({app:e,store:t,options:n}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!n.state,!t._p._testing){Af(t,Object.keys(n.actions),t._isOptionsAPI);const e=t._hotUpdate;Et(t)._hotUpdate=function(n){e.apply(this,arguments),Af(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}!function(e,t){xf.includes(Ef(t.$id))||xf.push(Ef(t.$id)),Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:xf,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},(e=>{const n="function"==typeof e.now?e.now.bind(e):Date.now;t.$onAction((({after:o,onError:r,name:s,args:a})=>{const i=If++;e.addTimelineEvent({layerId:kf,event:{time:n(),title:"🛫 "+s,subtitle:"start",data:{store:gf(t.$id),action:gf(s),args:a},groupId:i}}),o((o=>{Tf=void 0,e.addTimelineEvent({layerId:kf,event:{time:n(),title:"🛬 "+s,subtitle:"end",data:{store:gf(t.$id),action:gf(s),args:a,result:o},groupId:i}})})),r((o=>{Tf=void 0,e.addTimelineEvent({layerId:kf,event:{time:n(),logType:"error",title:"💥 "+s,subtitle:"end",data:{store:gf(t.$id),action:gf(s),args:a,error:o},groupId:i}})}))}),!0),t._customProperties.forEach((o=>{Xr((()=>Lt(t[o])),((t,r)=>{e.notifyComponentUpdate(),e.sendInspectorState(Cf),Sf&&e.addTimelineEvent({layerId:kf,event:{time:n(),title:"Change",subtitle:o,data:{newValue:t,oldValue:r},groupId:Tf}})}),{deep:!0})})),t.$subscribe((({events:o,type:r},s)=>{if(e.notifyComponentUpdate(),e.sendInspectorState(Cf),!Sf)return;const a={time:n(),title:wf(r),data:$f({store:gf(t.$id)},_f(o)),groupId:Tf};r===Qp.patchFunction?a.subtitle="⤵️":r===Qp.patchObject?a.subtitle="🧩":o&&!Array.isArray(o)&&(a.subtitle=o.type),o&&(a.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:o}}),e.addTimelineEvent({layerId:kf,event:a})}),{detached:!0,flush:"sync"});const o=t._hotUpdate;t._hotUpdate=Ot((r=>{o(r),e.addTimelineEvent({layerId:kf,event:{time:n(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:gf(t.$id),info:gf("HMR update")}}}),e.notifyComponentUpdate(),e.sendInspectorTree(Cf),e.sendInspectorState(Cf)}));const{$dispose:r}=t;t.$dispose=()=>{r(),e.notifyComponentUpdate(),e.sendInspectorTree(Cf),e.sendInspectorState(Cf),e.getSettings().logStoreChanges&&cf(`Disposed "${t.$id}" store 🗑`)},e.notifyComponentUpdate(),e.sendInspectorTree(Cf),e.sendInspectorState(Cf),e.getSettings().logStoreChanges&&cf(`"${t.$id}" store installed 🆕`)}))}(e,t)}}function Rf(e,t){for(const n in t){const o=t[n];if(!(n in e))continue;const r=e[n];Xp(r)&&Xp(o)&&!At(o)&&!xt(o)?e[n]=Rf(r,o):e[n]=o}return e}const Pf=()=>{};function Lf(e,t,n,o=Pf){e.push(t);const r=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),o())};return!n&&ce()&&function(e,t=!1){se?se.cleanups.push(e):t||re("onScopeDispose() is called when there is no active effect scope to be associated with.")}(r),r}function Df(e,...t){e.slice().forEach((e=>{e(...t)}))}const Vf=e=>e(),Nf=Symbol(),Mf=Symbol();function Uf(e,t){e instanceof Map&&t instanceof Map?t.forEach(((t,n)=>e.set(n,t))):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],r=e[n];Xp(r)&&Xp(o)&&e.hasOwnProperty(n)&&!At(o)&&!xt(o)?e[n]=Uf(r,o):e[n]=o}return e}const Ff=Symbol("pinia:skipHydration");const{assign:Bf}=Object;function qf(e){return!(!At(e)||!e.effect)}function zf(e,t,n,o){const{state:r,actions:s,getters:a}=t,i=n.state.value[e];let l;return l=Hf(e,(function(){i||o||(n.state.value[e]=r?r():{});const t=Nt(o?jt(r?r():{}).value:n.state.value[e]);return Bf(t,s,Object.keys(a||{}).reduce(((o,r)=>(r in t&&console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with "${r}" in store "${e}".`),o[r]=Ot(ua((()=>{Kp(n);const t=n._s.get(e);return a[r].call(t,t)}))),o)),{}))}),t,n,o,!0),l}function Hf(e,t,n={},o,r,s){let a;const i=Bf({actions:{}},n);if(!o._e.active)throw new Error("Pinia destroyed");const l={deep:!0};let c,u;l.onTrigger=e=>{c?d=e:0!=c||S._hotUpdating||(Array.isArray(d)?d.push(e):console.error("🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug."))};let d,p=[],f=[];const h=o.state.value[e];s||h||r||(o.state.value[e]={});const m=jt({});let g;function v(t){let n;c=u=!1,d=[],"function"==typeof t?(t(o.state.value[e]),n={type:Qp.patchFunction,storeId:e,events:d}):(Uf(o.state.value[e],t),n={type:Qp.patchObject,payload:t,storeId:e,events:d});const r=g=Symbol();hn().then((()=>{g===r&&(c=!0)})),u=!0,Df(p,n,o.state.value[e])}const y=s?function(){const{state:e}=n,t=e?e():{};this.$patch((e=>{Bf(e,t)}))}:()=>{throw new Error(`🍍: Store "${e}" is built using the setup syntax and does not implement $reset().`)};const b=(t,n="")=>{if(Nf in t)return t[Mf]=n,t;const r=function(){Kp(o);const n=Array.from(arguments),s=[],a=[];let i;Df(f,{args:n,name:r[Mf],store:S,after:function(e){s.push(e)},onError:function(e){a.push(e)}});try{i=t.apply(this&&this.$id===e?this:S,n)}catch(l){throw Df(a,l),l}return i instanceof Promise?i.then((e=>(Df(s,e),e))).catch((e=>(Df(a,e),Promise.reject(e)))):(Df(s,i),i)};return r[Nf]=!0,r[Mf]=n,r},_=Ot({actions:{},getters:{},state:[],hotState:m}),w={_p:o,$id:e,$onAction:Lf.bind(null,f),$patch:v,$reset:y,$subscribe(t,n={}){const r=Lf(p,t,n.detached,(()=>s())),s=a.run((()=>Xr((()=>o.state.value[e]),(o=>{("sync"===n.flush?u:c)&&t({storeId:e,type:Qp.direct,events:d},o)}),Bf({},l,n))));return r},$dispose:function(){a.stop(),p=[],f=[],o._s.delete(e)}},S=yt(Bf({_hmrPayload:_,_customProperties:Ot(new Set)},w));o._s.set(e,S);const x=(o._a&&o._a.runWithContext||Vf)((()=>o._e.run((()=>(a=le()).run((()=>t({action:b})))))));for(const C in x){const t=x[C];if(At(t)&&!qf(t)||xt(t))r?Wp(m.value,C,Ft(x,C)):s||(!h||Xp(k=t)&&k.hasOwnProperty(Ff)||(At(t)?t.value=h[C]:Uf(t,h[C])),o.state.value[e][C]=t),_.state.push(C);else if("function"==typeof t){const e=r?t:b(t,C);x[C]=e,_.actions[C]=t,i.actions[C]=t}else if(qf(t)&&(_.getters[C]=s?n.getters[C]:t,ef)){(x._getters||(x._getters=Ot([]))).push(C)}}var k;if(Bf(S,x),Bf(Et(S),x),Object.defineProperty(S,"$state",{get:()=>r?m.value:o.state.value[e],set:e=>{if(r)throw new Error("cannot set hotState");v((t=>{Bf(t,e)}))}}),S._hotUpdate=Ot((t=>{S._hotUpdating=!0,t._hmrPayload.state.forEach((e=>{if(e in S.$state){const n=t.$state[e],o=S.$state[e];"object"==typeof n&&Xp(n)&&Xp(o)?Rf(n,o):t.$state[e]=o}Wp(S,e,Ft(t.$state,e))})),Object.keys(S.$state).forEach((e=>{e in t.$state||Gp(S,e)})),c=!1,u=!1,o.state.value[e]=Ft(t._hmrPayload,"hotState"),u=!0,hn().then((()=>{c=!0}));for(const e in t._hmrPayload.actions){const n=t[e];Wp(S,e,b(n,e))}for(const e in t._hmrPayload.getters){const n=t._hmrPayload.getters[e],r=s?ua((()=>(Kp(o),n.call(S,S)))):n;Wp(S,e,r)}Object.keys(S._hmrPayload.getters).forEach((e=>{e in t._hmrPayload.getters||Gp(S,e)})),Object.keys(S._hmrPayload.actions).forEach((e=>{e in t._hmrPayload.actions||Gp(S,e)})),S._hmrPayload=t._hmrPayload,S._getters=t._getters,S._hotUpdating=!1})),ef){const e={writable:!0,configurable:!0,enumerable:!1};["_p","_hmrPayload","_getters","_customProperties"].forEach((t=>{Object.defineProperty(S,t,Bf({value:S[t]},e))}))}return o._p.forEach((e=>{if(ef){const t=a.run((()=>e({store:S,app:o._a,pinia:o,options:i})));Object.keys(t||{}).forEach((e=>S._customProperties.add(e))),Bf(S,t)}else Bf(S,a.run((()=>e({store:S,app:o._a,pinia:o,options:i}))))})),S.$state&&"object"==typeof S.$state&&"function"==typeof S.$state.constructor&&!S.$state.constructor.toString().includes("[native code]")&&console.warn(`[🍍]: The "state" must be a plain object. It cannot be\n\tstate: () => new MyClass()\nFound in store "${S.$id}".`),h&&s&&n.hydrate&&n.hydrate(S.$state,h),c=!0,u=!0,S}
/*! #__NO_SIDE_EFFECTS__ */function Wf(e,t,n){let o,r;const s="function"==typeof t;if("string"==typeof e)o=e,r=s?n:t;else if(r=e,o=e.id,"string"!=typeof o)throw new Error('[🍍]: "defineStore()" must be passed a store id as its first argument.');function a(e,n){if((e=e||(!!(qs||Bn||hr)?gr(Yp,null):null))&&Kp(e),!Jp)throw new Error('[🍍]: "getActivePinia()" was called but there was no active Pinia. Are you trying to use a store before calling "app.use(pinia)"?\nSee https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\nThis will fail in production.');(e=Jp)._s.has(o)||(s?Hf(o,t,r,e):zf(o,r,e),a._pinia=e);const i=e._s.get(o);if(n){const a="__hot:"+o,i=s?Hf(a,t,r,e,!0):zf(a,Bf({},r),e,!0);n._hotUpdate(i),delete e.state.value[a],e._s.delete(a)}if(ef){const e=zs();if(e&&e.proxy&&!n){const t=e.proxy;("_pStores"in t?t._pStores:t._pStores={})[o]=i}}return i}return a.$id=o,a}const Gf=Object.assign({"../view/app/index.vue":()=>zl((()=>import("./index.058e1ded.js")),["./index.058e1ded.js","./index.d3d3644e.css"],import.meta.url),"../view/client/download.vue":()=>zl((()=>import("./download.5fbec220.js")),["./download.5fbec220.js","./iconfont.2d75af05.js","./browser.dd769507.js","./download.3d540d75.css"],import.meta.url),"../view/client/header.vue":()=>zl((()=>import("./header.0b412779.js")),["./header.0b412779.js","./ASD.492c8837.js","./header.dcb4d233.css"],import.meta.url),"../view/client/index.vue":()=>zl((()=>import("./index.642fceb8.js")),["./index.642fceb8.js","./header.0b412779.js","./ASD.492c8837.js","./header.dcb4d233.css","./menu.f88a8c29.js","./iconfont.2d75af05.js","./menu.8bb454ca.css","./index.6b45d132.css"],import.meta.url),"../view/client/login.vue":()=>zl((()=>import("./login.1b4b1a21.js")),["./login.1b4b1a21.js","./index.4332345e.js","./localLogin.6a42ce4f.js","./localLogin.f639b4eb.css","./wechat.23adad4b.js","./wechat.3b1b375f.css","./feishu.e6ba81d5.js","./dingtalk.3aa78e6e.js","./oauth2.bbc060fc.js","./oauth2.03d0b5c4.css","./iconfont.2d75af05.js","./sms.829ddc94.js","./sms.844b2c56.css","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./index.25be4b34.css"],import.meta.url),"../view/client/main.vue":()=>zl((()=>import("./main.b48f7b5e.js")),["./main.b48f7b5e.js","./index.058e1ded.js","./index.d3d3644e.css","./main.a77c3312.css"],import.meta.url),"../view/client/menu.vue":()=>zl((()=>import("./menu.f88a8c29.js")),["./menu.f88a8c29.js","./iconfont.2d75af05.js","./menu.8bb454ca.css"],import.meta.url),"../view/client/setting.vue":()=>zl((()=>import("./setting.549678dd.js")),["./setting.549678dd.js","./setting.b14ec24d.css"],import.meta.url),"../view/error/index.vue":()=>zl((()=>import("./index.718a178d.js")),["./index.718a178d.js","./index.e1fc439c.css"],import.meta.url),"../view/error/reload.vue":()=>zl((()=>import("./reload.584237e9.js")),[],import.meta.url),"../view/layout/aside/asideComponent/asyncSubmenu.vue":()=>zl((()=>import("./asyncSubmenu.dd863e39.js")),["./asyncSubmenu.dd863e39.js","./asyncSubmenu.d213bcdc.css"],import.meta.url),"../view/layout/aside/asideComponent/index.vue":()=>zl((()=>import("./index.981b0fdd.js")),["./index.981b0fdd.js","./menuItem.208f05b7.js","./menuItem.e3deabe4.css","./asyncSubmenu.dd863e39.js","./asyncSubmenu.d213bcdc.css"],import.meta.url),"../view/layout/aside/asideComponent/menuItem.vue":()=>zl((()=>import("./menuItem.208f05b7.js")),["./menuItem.208f05b7.js","./menuItem.e3deabe4.css"],import.meta.url),"../view/layout/aside/historyComponent/history.vue":()=>zl((()=>import("./history.02dc9e44.js")),["./history.02dc9e44.js","./index-browser-esm.c2d3b5c9.js","./history.a6ae9cc3.css"],import.meta.url),"../view/layout/aside/index.vue":()=>zl((()=>import("./index.e4cdb417.js")),["./index.e4cdb417.js","./index.981b0fdd.js","./menuItem.208f05b7.js","./menuItem.e3deabe4.css","./asyncSubmenu.dd863e39.js","./asyncSubmenu.d213bcdc.css","./index.c6b67cfa.css"],import.meta.url),"../view/layout/bottomInfo/bottomInfo.vue":()=>zl((()=>import("./bottomInfo.c0e6dc33.js")),["./bottomInfo.c0e6dc33.js","./bottomInfo.844a8d22.css"],import.meta.url),"../view/layout/index.vue":()=>zl((()=>import("./index.703213c3.js")),["./index.703213c3.js","./ASD.492c8837.js","./index.e4cdb417.js","./index.981b0fdd.js","./menuItem.208f05b7.js","./menuItem.e3deabe4.css","./asyncSubmenu.dd863e39.js","./asyncSubmenu.d213bcdc.css","./index.c6b67cfa.css","./index-browser-esm.c2d3b5c9.js","./index.015d8c1c.css"],import.meta.url),"../view/layout/screenfull/index.vue":()=>zl((()=>import("./index.9d4c6242.js")),["./index.9d4c6242.js","./index.90f247f5.css"],import.meta.url),"../view/layout/search/search.vue":()=>zl((()=>import("./search.fd1bb891.js")),["./search.fd1bb891.js","./index.9d4c6242.js","./index.90f247f5.css","./search.451aca04.css"],import.meta.url),"../view/layout/setting/index.vue":()=>zl((()=>import("./index.67cdacf0.js")),["./index.67cdacf0.js","./index.0d77c3f6.css"],import.meta.url),"../view/login/clientLogin.vue":()=>zl((()=>import("./clientLogin.e84e67be.js")),[],import.meta.url),"../view/login/dingtalk/dingtalk.vue":()=>zl((()=>import("./dingtalk.3aa78e6e.js")),[],import.meta.url),"../view/login/downloadWin.vue":()=>zl((()=>import("./downloadWin.91b8ed53.js")),["./downloadWin.91b8ed53.js","./ASD.492c8837.js","./iconfont.2d75af05.js","./browser.dd769507.js","./downloadWin.91f39202.css"],import.meta.url),"../view/login/feishu/feishu.vue":()=>zl((()=>import("./feishu.e6ba81d5.js")),[],import.meta.url),"../view/login/index.vue":()=>zl((()=>import("./index.4332345e.js")),["./index.4332345e.js","./localLogin.6a42ce4f.js","./localLogin.f639b4eb.css","./wechat.23adad4b.js","./wechat.3b1b375f.css","./feishu.e6ba81d5.js","./dingtalk.3aa78e6e.js","./oauth2.bbc060fc.js","./oauth2.03d0b5c4.css","./iconfont.2d75af05.js","./sms.829ddc94.js","./sms.844b2c56.css","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./index.25be4b34.css"],import.meta.url),"../view/login/localLogin/localLogin.vue":()=>zl((()=>import("./localLogin.6a42ce4f.js")),["./localLogin.6a42ce4f.js","./localLogin.f639b4eb.css"],import.meta.url),"../view/login/oauth2/oauth2.vue":()=>zl((()=>import("./oauth2.bbc060fc.js")),["./oauth2.bbc060fc.js","./oauth2.03d0b5c4.css"],import.meta.url),"../view/login/oauth2/oauth2_premises.vue":()=>zl((()=>import("./oauth2_premises.02b195a8.js")),["./oauth2_premises.02b195a8.js","./iconfont.2d75af05.js","./oauth2_premises.987b2776.css"],import.meta.url),"../view/login/oauth2/oauth2_result.vue":()=>zl((()=>import("./oauth2_result.b7727725.js")),["./oauth2_result.b7727725.js","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./oauth2_result.08376432.css"],import.meta.url),"../view/login/secondaryAuth/secondaryAuth.vue":()=>zl((()=>import("./secondaryAuth.95bf0c1b.js")),["./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css"],import.meta.url),"../view/login/secondaryAuth/verifyCode.vue":()=>zl((()=>import("./verifyCode.0c196ff0.js")),["./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css"],import.meta.url),"../view/login/sms/sms.vue":()=>zl((()=>import("./sms.829ddc94.js")),["./sms.829ddc94.js","./sms.844b2c56.css"],import.meta.url),"../view/login/verify.vue":()=>zl((()=>import("./verify.e3d1558d.js")),[],import.meta.url),"../view/login/wx/status.vue":()=>zl((()=>import("./status.044752a5.js")),["./status.044752a5.js","./secondaryAuth.95bf0c1b.js","./verifyCode.0c196ff0.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css","./iconfont.2d75af05.js","./status.d881a304.css"],import.meta.url),"../view/login/wx/wechat.vue":()=>zl((()=>import("./wechat.23adad4b.js")),["./wechat.23adad4b.js","./wechat.3b1b375f.css"],import.meta.url),"../view/login/wx/wx_oauth_callback.vue":()=>zl((()=>import("./wx_oauth_callback.23f2a03d.js")),["./wx_oauth_callback.23f2a03d.js","./iconfont.2d75af05.js"],import.meta.url),"../view/resource/appverify.vue":()=>zl((()=>import("./appverify.b1bb5fd6.js")),["./appverify.b1bb5fd6.js","./iconfont.2d75af05.js","./appverify.1430be1b.css"],import.meta.url),"../view/routerHolder.vue":()=>zl((()=>import("./routerHolder.5ac55142.js")),[],import.meta.url)}),Jf=Object.assign({}),Kf=e=>{e.forEach((e=>{e.component?"view"===e.component.split("/")[0]?e.component=Yf(Gf,e.component):"plugin"===e.component.split("/")[0]&&(e.component=Yf(Jf,e.component)):delete e.component,e.children&&Kf(e.children)}))};function Yf(e,t){return e[Object.keys(e).filter((e=>e.replace("../","")===t))[0]]}const Xf=[],Qf=[],Zf=[],eh={},th=(e,t)=>{e&&e.forEach((e=>{e.children&&!e.children.every((e=>e.hidden))||"404"===e.name||e.hidden||Xf.push({label:e.meta.title,value:e.name}),e.meta.btns=e.btns,e.meta.hidden=e.hidden,!0===e.meta.defaultMenu?Qf.push({...e,path:`/${e.path}`}):(t[e.name]=e,e.children&&e.children.length>0&&th(e.children,t))}))},nh=e=>{e&&e.forEach((e=>{(e.children&&e.children.some((e=>e.meta.keepAlive))||e.meta.keepAlive)&&e.component&&e.component().then((t=>{Zf.push(t.default.name),eh[e.name]=t.default.name})),e.children&&e.children.length>0&&nh(e.children)}))},oh=Wf("router",(()=>{const e=jt([]);Vp.on("setKeepAlive",(t=>{const n=[];t.forEach((e=>{eh[e.name]&&n.push(eh[e.name])})),e.value=Array.from(new Set(n))}));const t=jt([]),n=jt(Xf),o={};return{asyncRouters:t,routerList:n,keepAliveRouters:e,SetAsyncRouter:async()=>{const e=[{path:"/layout",name:"layout",component:"view/layout/index.vue",meta:{title:"底层layout"},children:[]},{path:"/appverify",name:"appverify",component:"view/resource/appverify.vue",meta:{title:"appverify"},children:[]}],r=(await new Promise((function(e,t){e({code:0,data:{menus:[{ID:9,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"clientLogin",name:"clientLogin",hidden:!0,component:"view/login/clientLogin.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"客户端登陆",topTitle:"客户端登陆",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"9",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"dashboard",name:"dashboard",hidden:!1,component:"view/app/index.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"应用门户",topTitle:"",icon:"icon-zuhu-yingyongliebiao",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"download",name:"download",hidden:!1,component:"view/client/download.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"客户端下载",topTitle:"客户端下载",icon:"icon-zuhu-kehuduanxiazai",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:8,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"person",name:"person",hidden:!0,component:"view/person/person.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"个人信息",topTitle:"个人信息",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"8",children:null,parameters:[],btns:null}]},msg:"获取成功"})}))).data.menus;return r&&r.push({path:"404",name:"404",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/index.vue"},{path:"reload",name:"Reload",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/reload.vue"}),th(r,o),e[0].children=r,0!==Qf.length&&e.push(...Qf),e.push({path:"/:catchAll(.*)",redirect:"/layout/404"}),Kf(e),nh(r),t.value=e,n.value=Xf,logger.log({asyncRouters:t.value}),logger.log({routerList:n.value}),!0},routeMap:o}}));var rh={},sh=Object.prototype.hasOwnProperty;function ah(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(Yh){return null}}function ih(e){try{return encodeURIComponent(e)}catch(Yh){return null}}rh.stringify=function(e,t){t=t||"";var n,o,r=[];for(o in"string"!=typeof t&&(t="?"),e)if(sh.call(e,o)){if((n=e[o])||null!=n&&!isNaN(n)||(n=""),o=ih(o),n=ih(n),null===o||null===n)continue;r.push(o+"="+n)}return r.length?t+r.join("&"):""},rh.parse=function(e){for(var t,n=/([^=?#&]+)=?([^&]*)/g,o={};t=n.exec(e);){var r=ah(t[1]),s=ah(t[2]);null===r||null===s||r in o||(o[r]=s)}return o};var lh=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e},ch=rh,uh=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,dh=/[\n\r\t]/g,ph=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,fh=/:\d+$/,hh=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,mh=/^[a-zA-Z]:/;function gh(e){return(e||"").toString().replace(uh,"")}var vh=[["#","hash"],["?","query"],function(e,t){return _h(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],yh={hash:1,query:1};function bh(e){var t,n=("undefined"!=typeof window?window:void 0!==ud?ud:"undefined"!=typeof self?self:{}).location||{},o={},r=typeof(e=e||n);if("blob:"===e.protocol)o=new Sh(unescape(e.pathname),{});else if("string"===r)for(t in o=new Sh(e,{}),yh)delete o[t];else if("object"===r){for(t in e)t in yh||(o[t]=e[t]);void 0===o.slashes&&(o.slashes=ph.test(e.href))}return o}function _h(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function wh(e,t){e=(e=gh(e)).replace(dh,""),t=t||{};var n,o=hh.exec(e),r=o[1]?o[1].toLowerCase():"",s=!!o[2],a=!!o[3],i=0;return s?a?(n=o[2]+o[3]+o[4],i=o[2].length+o[3].length):(n=o[2]+o[4],i=o[2].length):a?(n=o[3]+o[4],i=o[3].length):n=o[4],"file:"===r?i>=2&&(n=n.slice(2)):_h(r)?n=o[4]:r?s&&(n=n.slice(2)):i>=2&&_h(t.protocol)&&(n=o[4]),{protocol:r,slashes:s||_h(r),slashesCount:i,rest:n}}function Sh(e,t,n){if(e=(e=gh(e)).replace(dh,""),!(this instanceof Sh))return new Sh(e,t,n);var o,r,s,a,i,l,c=vh.slice(),u=typeof t,d=this,p=0;for("object"!==u&&"string"!==u&&(n=t,t=null),n&&"function"!=typeof n&&(n=ch.parse),o=!(r=wh(e||"",t=bh(t))).protocol&&!r.slashes,d.slashes=r.slashes||o&&t.slashes,d.protocol=r.protocol||t.protocol||"",e=r.rest,("file:"===r.protocol&&(2!==r.slashesCount||mh.test(e))||!r.slashes&&(r.protocol||r.slashesCount<2||!_h(d.protocol)))&&(c[3]=[/(.*)/,"pathname"]);p<c.length;p++)"function"!=typeof(a=c[p])?(s=a[0],l=a[1],s!=s?d[l]=e:"string"==typeof s?~(i="@"===s?e.lastIndexOf(s):e.indexOf(s))&&("number"==typeof a[2]?(d[l]=e.slice(0,i),e=e.slice(i+a[2])):(d[l]=e.slice(i),e=e.slice(0,i))):(i=s.exec(e))&&(d[l]=i[1],e=e.slice(0,i.index)),d[l]=d[l]||o&&a[3]&&t[l]||"",a[4]&&(d[l]=d[l].toLowerCase())):e=a(e,d);n&&(d.query=n(d.query)),o&&t.slashes&&"/"!==d.pathname.charAt(0)&&(""!==d.pathname||""!==t.pathname)&&(d.pathname=function(e,t){if(""===e)return t;for(var n=(t||"/").split("/").slice(0,-1).concat(e.split("/")),o=n.length,r=n[o-1],s=!1,a=0;o--;)"."===n[o]?n.splice(o,1):".."===n[o]?(n.splice(o,1),a++):a&&(0===o&&(s=!0),n.splice(o,1),a--);return s&&n.unshift(""),"."!==r&&".."!==r||n.push(""),n.join("/")}(d.pathname,t.pathname)),"/"!==d.pathname.charAt(0)&&_h(d.protocol)&&(d.pathname="/"+d.pathname),lh(d.port,d.protocol)||(d.host=d.hostname,d.port=""),d.username=d.password="",d.auth&&(~(i=d.auth.indexOf(":"))?(d.username=d.auth.slice(0,i),d.username=encodeURIComponent(decodeURIComponent(d.username)),d.password=d.auth.slice(i+1),d.password=encodeURIComponent(decodeURIComponent(d.password))):d.username=encodeURIComponent(decodeURIComponent(d.auth)),d.auth=d.password?d.username+":"+d.password:d.username),d.origin="file:"!==d.protocol&&_h(d.protocol)&&d.host?d.protocol+"//"+d.host:"null",d.href=d.toString()}Sh.prototype={set:function(e,t,n){var o=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(n||ch.parse)(t)),o[e]=t;break;case"port":o[e]=t,lh(t,o.protocol)?t&&(o.host=o.hostname+":"+t):(o.host=o.hostname,o[e]="");break;case"hostname":o[e]=t,o.port&&(t+=":"+o.port),o.host=t;break;case"host":o[e]=t,fh.test(t)?(t=t.split(":"),o.port=t.pop(),o.hostname=t.join(":")):(o.hostname=t,o.port="");break;case"protocol":o.protocol=t.toLowerCase(),o.slashes=!n;break;case"pathname":case"hash":if(t){var r="pathname"===e?"/":"#";o[e]=t.charAt(0)!==r?r+t:t}else o[e]=t;break;case"username":case"password":o[e]=encodeURIComponent(t);break;case"auth":var s=t.indexOf(":");~s?(o.username=t.slice(0,s),o.username=encodeURIComponent(decodeURIComponent(o.username)),o.password=t.slice(s+1),o.password=encodeURIComponent(decodeURIComponent(o.password))):o.username=encodeURIComponent(decodeURIComponent(t))}for(var a=0;a<vh.length;a++){var i=vh[a];i[4]&&(o[i[1]]=o[i[1]].toLowerCase())}return o.auth=o.password?o.username+":"+o.password:o.username,o.origin="file:"!==o.protocol&&_h(o.protocol)&&o.host?o.protocol+"//"+o.host:"null",o.href=o.toString(),o},toString:function(e){e&&"function"==typeof e||(e=ch.stringify);var t,n=this,o=n.host,r=n.protocol;r&&":"!==r.charAt(r.length-1)&&(r+=":");var s=r+(n.protocol&&n.slashes||_h(n.protocol)?"//":"");return n.username?(s+=n.username,n.password&&(s+=":"+n.password),s+="@"):n.password?(s+=":"+n.password,s+="@"):"file:"!==n.protocol&&_h(n.protocol)&&!o&&"/"!==n.pathname&&(s+="@"),(":"===o[o.length-1]||fh.test(n.hostname)&&!n.port)&&(o+=":"),s+=o+n.pathname,(t="object"==typeof n.query?e(n.query):n.query)&&(s+="?"!==t.charAt(0)?"?"+t:t),n.hash&&(s+=n.hash),s}},Sh.extractProtocol=wh,Sh.location=bh,Sh.trimLeft=gh,Sh.qs=ch;var xh=Sh;const kh=e=>Up({url:"/auth/login/v1/cache",method:"post",data:e}),Ch=()=>Up({url:"/auth/authz/v1/user/refresh_token",method:"get",donNotShowLoading:!0});let $h=!1;function Eh(e,t){setInterval((()=>{$h||($h=!0,Ch().then((n=>{console.log("---refreshToken--"),200===n.status?-1===n.data.code?(console.log("刷新token失败，退出至登录"),e()):(console.log("刷新token成功，保存token"),t(n.data)):(console.log("刷新token失败，退出至登录"),e())})).catch((()=>{console.log("---refreshToken err--"),e()})).finally((()=>{$h=!1})))}),6e5)}const Oh=e=>Up({url:"/auth/login/v1/send_sms",method:"post",data:e}),Th=e=>Up({url:"/auth/login/v1/sms_verify",method:"post",data:e}),Ih=e=>Up({url:"/auth/login/v1/sms_key",method:"post",data:e}),Ah=Wf("user",(()=>{const e=jt(null),t=jt({id:"",name:"",groupId:"",groupName:"",corpId:"",sourceId:"",phone:"",email:"",avatar:"",roles:[],sideMode:"dark",activeColor:"#4D70FF",baseColor:"#fff"}),n=jt(window.localStorage.getItem("token")||""),o=jt(window.localStorage.getItem("loginType")||"");try{n.value=n.value?JSON.parse(n.value):""}catch(Yh){console.log("---清理localStorage中的token---"),window.localStorage.removeItem("token"),n.value=""}const r=e=>{n.value=e},s=e=>{o.value=e},a=async e=>{const n=await Up({url:"/auth/user/v1/login_user",method:"get"});var o;return 200===n.status&&(o=n.data.userInfo,t.value=o),n},i=async()=>{Eh();const e=await Up({url:"/auth/user/v1/logout",method:"post",data:""});console.log("登出res",e),200===e.status?-1===e.data.code?Dl({showClose:!0,message:e.data.msg,type:"error"}):e.data.redirectUrl?(console.log("检测到OAuth2登出URL，正在重定向:",e.data.redirectUrl),l(),window.location.href=e.data.redirectUrl):(ld.push({name:"Login",replace:!0}),l()):Dl({showClose:!0,message:"服务异常，请联系管理员！",type:"error"})},l=async()=>{sessionStorage.clear(),window.localStorage.removeItem("userInfo"),window.localStorage.removeItem("token"),n.value=""};return Xr((()=>n.value),(()=>{window.localStorage.setItem("token",JSON.stringify(n.value))})),Xr((()=>o.value),(()=>{window.localStorage.setItem("loginType",o.value)})),{userInfo:t,token:n,loginType:o,NeedInit:()=>{n.value="",window.localStorage.removeItem("token"),ld.push({name:"Init",replace:!0})},ResetUserInfo:(e={})=>{t.value={...t.value,...e}},GetUserInfo:a,LoginIn:async(t,n,o)=>{var l,c,u,d,p,f,h,m,g,v,y,b,_,w,S;e.value=Pl.service({fullscreen:!0,text:"登录中，请稍候..."});try{let x="";switch(n){case"qiyewx":case"qiyewx_oauth":case"feishu":case"dingtalk":case"oauth2":case"cas":case"msad":case"ldap":x=await(S=t,Up({url:"/auth/login/v1/user/third",method:"post",data:S})),s(o);break;case"accessory":x=await Th(t);break;default:x=await(e=>Up({url:"/auth/login/v1/user",method:"post",data:JSON.stringify(e)}))(t),s(o)}const k=x.data.msg;if(200===x.status){if(-1===x.data.code||1===(null==(c=null==(l=x.data)?void 0:l.data)?void 0:c.status))return Dl({showClose:!0,message:k,type:"error"}),e.value.close(),{code:-1};{if(x.data.data){if(x.data.data.secondary)return e.value.close(),{isSecondary:!0,secondary:x.data.data.secondary,uniqKey:x.data.data.uniqKey,contactType:x.data.data.contactType,hasContactInfo:x.data.data.hasContactInfo,secondaryType:x.data.secondaryType,userName:x.data.data.userName,user_id:x.data.data.userID};r(x.data.data)}await a(),Eh(i,r);const t=oh();await t.SetAsyncRouter();t.asyncRouters.forEach((e=>{ld.addRoute(e)}));const o=window.location.href.replace(/#/g,"&"),s=xh(o,!0);let l={},c=null,S=null;try{const e=localStorage.getItem("client_params");if(e){const t=JSON.parse(e);c=t.type,S=t.wp}}catch(Yh){console.warn("LoginIn: 获取localStorage参数失败:",Yh)}const k=window.location.search;new URLSearchParams(k).get("type");if((null==(u=s.query)?void 0:u.redirect)||(null==(d=s.query)?void 0:d.redirect_url)){let t="";return(null==(p=s.query)?void 0:p.redirect)?t=(null==(f=s.query)?void 0:f.redirect.indexOf("?"))>-1?null==(m=s.query)?void 0:m.redirect.substring((null==(h=s.query)?void 0:h.redirect.indexOf("?"))+1):"":(null==(g=s.query)?void 0:g.redirect_url)&&(t=(null==(v=s.query)?void 0:v.redirect_url.indexOf("?"))>-1?null==(b=s.query)?void 0:b.redirect_url.substring((null==(y=s.query)?void 0:y.redirect_url.indexOf("?"))+1):""),t.split("&").forEach((function(e){const t=e.split("=");l[t[0]]=t[1]})),c&&(l.type=c),S&&(l.wp=S),e.value.close(),window.localStorage.setItem("refresh_times",0),"qiyewx_oauth"===n||(window.location.href=(null==(_=s.query)?void 0:_.redirect)||(null==(w=s.query)?void 0:w.redirect_url)),!0}return l={type:c||s.query.type},(S||s.query.wp)&&(l.wp=S||s.query.wp),s.query.wp&&(l.wp=s.query.wp),await ld.push({name:"dashboard",query:l}),e.value.close(),!0}}Dl({showClose:!0,message:k,type:"error"}),e.value.close()}catch(Yh){Dl({showClose:!0,message:"服务异常，请联系管理员！",type:"error"}),e.value.close()}},LoginOut:i,authFailureLoginOut:async()=>{Eh(),l(),ld.push({name:"Login",replace:!0}),window.location.reload()},changeSideMode:async e=>{const n=await(e=>Up({url:"/user/setSelfInfo",method:"put",data:e}))({sideMode:e});0===n.code&&(t.value.sideMode=e,Dl({type:"success",message:"设置成功"}))},mode:"dark",sideMode:"#273444",setToken:r,baseColor:"#fff",activeColor:"#4D70FF",loadingInstance:e,ClearStorage:l,GetOrganize:async e=>{const t=await(e=>Up({url:`/auth/admin/realms/${Hp}/groups`,method:"get",params:e}))(e);return 0===t.code?"":t},GetOrganizeDetails:async e=>{const t=await(n=e,Up({url:`/auth/admin/realms/${Hp}/groups/${n}`,method:"get"}));var n;return 0===t.code?"":t},UpdateOrganize:async e=>{const t=await(e=>{const t=e.id;return delete e.id,Up({url:`/auth/admin/realms/${Hp}/groups/${t}`,method:"put",data:e})})(e);return 0===t.code?"":t},CreateOrganize:async e=>{const t=await(n=e,delete n.id,Up({url:`/auth/admin/realms/${Hp}/groups`,method:"post",data:n}));var n;return 0===t.code?"":t},DelOrganize:async e=>{const t=await(e=>Up({url:`/auth/admin/realms/${Hp}/groups/${e}`,method:"delete"}))(e);return 0===t.code?"":t},AddSubgroup:async e=>{const t=await(e=>{const t=e.id;return delete e.id,Up({url:`/auth/admin/realms/${Hp}/groups/${t}/children`,method:"post",data:e})})(e);return 0===t.code?"":t},CreateUser:async e=>{delete e.id;const t=await(e=>Up({url:`/auth/admin/realms/${Hp}/users`,method:"post",data:e}))(e);return 0===t.code?"":t},GetUserList:async e=>{const t=await(n=e,Up({url:`/auth/admin/realms/${Hp}/users`,method:"get",params:n}));var n;return 0===t.code?"":t},GetUserListCount:async e=>{const t=await(n=e,Up({url:`/auth/admin/realms/${Hp}/users/count`,method:"get",params:n}));var n;return 0===t.code?"":t},UpdateUser:async e=>{const t=await(e=>{const t=e.id;return delete e.id,Up({url:`/auth/admin/realms/${Hp}/users/${t}`,method:"put",data:e})})(e);return 0===t.code?"":t},DeleteUser:async e=>{const t=await(e=>Up({url:`/auth/admin/realms/${Hp}/users/${e}`,method:"delete"}))(e);return 0===t.code?"":t},GetRoles:async e=>{const t=await(e=>Up({url:`/auth/admin/realms/${Hp}/roles`,method:"get",data:e}))(e);return 0===t.code?"":t},GetGroupMembers:async(e,t)=>{const n=await((e,t)=>Up({url:`/auth/admin/realms/${Hp}/groups/${e}/members`,method:"get",params:t}))(e,t);return 0===n.code?"":n},GetOrganizeCount:async e=>{const t=await(e=>Up({url:`/auth/admin/realms/${Hp}/groups/count`,method:"get",params:e}))(e);return 0===t.code?"":t},GetUserOrigin:async()=>{const e=await Up({url:"/console/v1/user/director_types",method:"get",params:t});var t;return 0===e.code?"":e},GetUserGroups:async e=>{const t=await(e=>Up({url:`/auth/admin/realms/${Hp}/users/${e}/groups`,method:"get"}))(e);return 0===t.code?"":t},GetUserRole:async e=>{const t=await getUserRole(e);return 0===t.code?"":t},handleOAuth2Login:async(t,n,o)=>{try{e.value=Pl.service({fullscreen:!0,text:"处理登录中..."});const s=await((e,t,n)=>Up({url:`/auth/login/v1/callback/${e}`,method:"get",params:{code:t,state:n}}))(t,n,o);if(200===s.status&&s.data){const t=s.data;if(t.needSecondary)return e.value.close(),{isSecondary:!0,uniqKey:t.uniqKey};if(t.token)return r({accessToken:t.token,refreshToken:t.refresh_token,expireIn:t.expires_in,tokenType:t.token_type||"Bearer"}),await a(),e.value.close(),!0}return e.value.close(),!1}catch(s){return console.error("OAuth2登录处理失败:",s),e.value.close(),Dl({showClose:!0,message:s.message||"登录失败，请重试",type:"error"}),!1}}}})),jh=Wf("app",{state:()=>({isClient:!1,clientType:"windows"}),actions:{setIsClient(){let e=/QtWebEngine/.test(navigator.userAgent);e||urlHashParams&&urlHashParams.get("asec_client")&&(e=!0),this.isClient=e}}}),Rh=(e,t)=>{const n=/\$\{(.+?)\}/,o=e.match(/\$\{(.+?)\}/g);return o&&o.forEach((o=>{const r=o.match(n)[1],s=t.params[r]||t.query[r];e=e.replace(o,s)})),e};function Ph(e,t){if(e){return`${Rh(e,t)} - ${Fl.appName}`}return`${Fl.appName}`}var Lh={exports:{}};
/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */Lh.exports=function(){var e,t,n={version:"0.2.0"},o=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function r(e,t,n){return e<t?t:e>n?n:e}function s(e){return 100*(-1+e)}function a(e,t,n){var r;return(r="translate3d"===o.positionUsing?{transform:"translate3d("+s(e)+"%,0,0)"}:"translate"===o.positionUsing?{transform:"translate("+s(e)+"%,0)"}:{"margin-left":s(e)+"%"}).transition="all "+t+"ms "+n,r}n.configure=function(e){var t,n;for(t in e)void 0!==(n=e[t])&&e.hasOwnProperty(t)&&(o[t]=n);return this},n.status=null,n.set=function(e){var t=n.isStarted();e=r(e,o.minimum,1),n.status=1===e?null:e;var s=n.render(!t),c=s.querySelector(o.barSelector),u=o.speed,d=o.easing;return s.offsetWidth,i((function(t){""===o.positionUsing&&(o.positionUsing=n.getPositioningCSS()),l(c,a(e,u,d)),1===e?(l(s,{transition:"none",opacity:1}),s.offsetWidth,setTimeout((function(){l(s,{transition:"all "+u+"ms linear",opacity:0}),setTimeout((function(){n.remove(),t()}),u)}),u)):setTimeout(t,u)})),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var e=function(){setTimeout((function(){n.status&&(n.trickle(),e())}),o.trickleSpeed)};return o.trickle&&e(),this},n.done=function(e){return e||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(e){var t=n.status;return t?("number"!=typeof e&&(e=(1-t)*r(Math.random()*t,.1,.95)),t=r(t+e,0,.994),n.set(t)):n.start()},n.trickle=function(){return n.inc(Math.random()*o.trickleRate)},e=0,t=0,n.promise=function(o){return o&&"resolved"!==o.state()?(0===t&&n.start(),e++,t++,o.always((function(){0===--t?(e=0,n.done()):n.set((e-t)/e)})),this):this},n.render=function(e){if(n.isRendered())return document.getElementById("nprogress");u(document.documentElement,"nprogress-busy");var t=document.createElement("div");t.id="nprogress",t.innerHTML=o.template;var r,a=t.querySelector(o.barSelector),i=e?"-100":s(n.status||0),c=document.querySelector(o.parent);return l(a,{transition:"all 0 linear",transform:"translate3d("+i+"%,0,0)"}),o.showSpinner||(r=t.querySelector(o.spinnerSelector))&&f(r),c!=document.body&&u(c,"nprogress-custom-parent"),c.appendChild(t),t},n.remove=function(){d(document.documentElement,"nprogress-busy"),d(document.querySelector(o.parent),"nprogress-custom-parent");var e=document.getElementById("nprogress");e&&f(e)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return t+"Perspective"in e?"translate3d":t+"Transform"in e?"translate":"margin"};var i=function(){var e=[];function t(){var n=e.shift();n&&n(t)}return function(n){e.push(n),1==e.length&&t()}}(),l=function(){var e=["Webkit","O","Moz","ms"],t={};function n(e){return e.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,(function(e,t){return t.toUpperCase()}))}function o(t){var n=document.body.style;if(t in n)return t;for(var o,r=e.length,s=t.charAt(0).toUpperCase()+t.slice(1);r--;)if((o=e[r]+s)in n)return o;return t}function r(e){return e=n(e),t[e]||(t[e]=o(e))}function s(e,t,n){t=r(t),e.style[t]=n}return function(e,t){var n,o,r=arguments;if(2==r.length)for(n in t)void 0!==(o=t[n])&&t.hasOwnProperty(n)&&s(e,n,o);else s(e,r[1],r[2])}}();function c(e,t){return("string"==typeof e?e:p(e)).indexOf(" "+t+" ")>=0}function u(e,t){var n=p(e),o=n+t;c(n,t)||(e.className=o.substring(1))}function d(e,t){var n,o=p(e);c(e,t)&&(n=o.replace(" "+t+" "," "),e.className=n.substring(1,n.length-1))}function p(e){return(" "+(e.className||"")+" ").replace(/\s+/gi," ")}function f(e){e&&e.parentNode&&e.parentNode.removeChild(e)}return n}();const Dh=Lh.exports;let Vh=0;const Nh=["Login","Init","ClientLogin","Status","downloadWin","WxOAuthCallback","OAuth2Result","OAuth2Premises"],Mh=async e=>{logger.log("----getRouter---");const t=oh();await t.SetAsyncRouter(),await e.GetUserInfo();t.asyncRouters.forEach((e=>{ld.addRoute(e)}))};async function Uh(e){if(e.matched.some((e=>e.meta.keepAlive))&&e.matched&&e.matched.length>2)for(let t=1;t<e.matched.length;t++){const n=e.matched[t-1];"layout"===n.name&&(e.matched.splice(t,1),await Uh(e)),"function"==typeof n.components.default&&(await n.components.default(),await Uh(e))}}const Fh=e=>(logger.log("socket连接开始"),new Promise(((t,n)=>{const o={action:2,msg:"",platform:document.location.hostname},r=jt({}),s=jt("ws://127.0.0.1:50001"),a=navigator.platform;0!==a.indexOf("Mac")&&"MacIntel"!==a||(s.value="wss://127.0.0.1:50001");const i=e=>{r.value.send(e)},l=()=>{logger.log("socket断开链接"),r.value.close()};logger.log(`asecagent://?web=${JSON.stringify(o)}`),(async()=>{let n;r.value=new WebSocket(s.value);r.value.onopen=()=>{logger.log("socket连接成功"),n=setTimeout((()=>{console.log("WebSocket连接超时"),l(),t()}),2e3),i(JSON.stringify(o))},r.value.onmessage=async o=>{var r,s;if(logger.log("-------e--------"),logger.log(JSON.parse(o.data)),clearTimeout(n),null==o?void 0:o.data)try{const n=JSON.parse(o.data);if(!n.msg.token)return void t();const a={accessToken:n.msg.token,expireIn:3600,refreshToken:n.msg.refreshToken,refreshExpireIn:604800,tokenType:"Bearer"};await e.setToken(a);const i=await Ch();200===i.status&&(((null==(r=null==i?void 0:i.data)?void 0:r.code)||-1!==(null==(s=null==i?void 0:i.data)?void 0:s.code))&&(await e.setToken(i.data),await e.GetUserInfo(),t()),t()),t()}catch(a){await l(),t()}await l(),t()},r.value.onerror=()=>{console.log("socket连接错误"),clearTimeout(n),t()}})()})));ld.beforeEach((async(e,t)=>{Dh.start();if(jh().isClient)return(e=>["/client","/client/login","/client/setting"].includes(e.path)?(logger.log("客户端直接返回"),!0):(logger.log("客户端查询登录状态:",e.path),!0))(e);const n=Ah();e.meta.matched=[...e.matched],await Uh(e);let o=n.token;document.title=Ph(e.meta.title,e),"WxOAuthCallback"==e.name||"verify"==e.name?document.title="":document.title=Ph(e.meta.title,e),logger.log("路由参数：",{whiteList:Nh,to:e,from:t});const r=window.localStorage.getItem("refresh_times")||0;return(!o||'""'===o)&&Number(r)<5&&"Login"!==e.name&&(await Fh(n),o=n.token),Nh.includes(e.name)?o&&!["downloadWin","Login","WxOAuthCallback","OAuth2Callback"].includes(e.name)?(!Vh&&Nh.indexOf(t.name)<0&&(Vh++,await Mh(n),logger.log("getRouter")),n.userInfo?(logger.log("dashboard"),{name:"dashboard"}):(Eh(),await n.ClearStorage(),logger.log("强制退出账号"),{name:"Login",query:{redirect:document.location.hash}})):(logger.log("直接返回"),!0):(logger.log("不在白名单中:",o),o?!Vh&&Nh.indexOf(t.name)<0?(Vh++,await Mh(n),logger.log("初始化动态路由:",n.token),n.token?(logger.log("返回to"),{...e,replace:!1}):(logger.log("返回login"),{name:"Login",query:{redirect:e.href}})):e.matched.length?(Eh(n.LoginOut,n.setToken),logger.log("返回refresh"),!0):(console.log("404:",e.matched),{path:"/layout/404"}):(logger.log("不在白名单中并且未登录的时候"),{name:"Login",query:{redirect:document.location.hash}}))})),ld.afterEach((()=>{Dh.done()})),ld.onError((()=>{Dh.remove()}));const Bh={install:e=>{const t=Ah();e.directive("auth",{mounted:function(e,n){const o=t.userInfo;let r="";switch(Object.prototype.toString.call(n.value)){case"[object Array]":r="Array";break;case"[object String]":r="String";break;case"[object Number]":r="Number";break;default:r=""}if(""===r)return void e.parentNode.removeChild(e);let s=n.value.toString().split(",").some((e=>Number(e)===o.id));n.modifiers.not&&(s=!s),s||e.parentNode.removeChild(e)}})}},qh=function(){const e=le(!0),t=e.run((()=>jt({})));let n=[],o=[];const r=Ot({install(e){Kp(r),r._a=e,e.provide(Yp,r),e.config.globalProperties.$pinia=r,ef&&Of(e,r),o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return ef&&"undefined"!=typeof Proxy&&r.use(jf),r}(),zh={id:"app"};const Hh=$i({name:"App",created(){const e=gr("$keycloak");logger.log("App created: ",e)}},[["render",function(e,t,n,o,r,s){const a=No("router-view");return ws(),Cs("div",zh,[js(a)])}],["__file","D:/asec-platform/frontend/portal/src/App.vue"]]);logger.log(navigator.userAgent),logger.log(document.location.href),Dh.configure({showSpinner:!1,ease:"ease",speed:500}),Dh.start();if(/msie|trident/i.test(navigator.userAgent)){alert("\n    对不起，您正在使用的浏览器版本过低。\n    本网站不支持IE浏览器，请使用现代浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。\n  ")}const Wh=Ci(Hh);Wh.config.productionTip=!1,document.location.protocol,document.location.host;const Gh=new XMLHttpRequest;Gh.open("GET",document.location,!1),Gh.send(null),Gh.getResponseHeader("X-Corp-ID");let Jh="";Jh="https://*************:/auth",logger.log("url:https://*************:/auth"),Wh.use(Bl).use(qh).use(Bh).use(ld).use(Ul).mount("#app");const Kh=jh();Kh.setIsClient(),logger.log("是否是客户端:",Kh.isClient,"客户端类型:",Kh.clientType);export{Si as A,kh as B,W as C,jo as D,Oo as E,ms as F,_i as G,F as H,Ba as I,Vp as J,Fo as K,Pl as L,Dl as M,Gn as N,Ho as O,Rh as P,Ma as Q,oh as R,hn as S,Ca as T,hi as U,vo as V,Ih as W,Th as X,$i as _,e as __vite_legacy_guard,sd as a,Ah as b,ua as c,Cs as d,As as e,Ds as f,$s as g,No as h,zo as i,js as j,Ls as k,gr as l,Lt as m,Oh as n,ws as o,mr as p,Dp as q,jt as r,Up as s,te as t,ad as u,Xr as v,Hn as w,Uo as x,ud as y,yt as z};
