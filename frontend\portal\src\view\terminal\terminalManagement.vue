<template>
  <div class="terminal">
    <div
        style="background-color: #FFFFFF;padding: 17px 20px 60px 20px;border-radius: 4px;min-height: calc(100vh - 145px);"
    >
      <div class="header">
        <base-button color="#2972C8" plain class="iconfont icon-shuaxin" @click="getTableData">刷新</base-button>
        <base-input
            v-model="searchUser"
            class="w-50 m-2 organize-search"
            placeholder="输入mac或用户名"
            :suffix-icon="Search"
            style="width: 15%;float: right"
            @change="getTableData"
        />
      </div>
      <CustomTable
          :table-data="tableData"
          v-bind="tableConfig"
      >
        <template #app_name="scope">
          <div style="height: 40px">
            <span style="font-size: 13px;float: left;margin-right: 7px">
              <svg class="icon" aria-hidden="true">
                <use v-bind:xlink:href="platIcon(scope.row.app_plat)"></use>
              </svg>
            </span>
            <span
                style="font-size: 12px;color: #252631;text-overflow: ellipsis;overflow: hidden;width: 130px;display: inline-block"
            >{{ scope.row.app_name }}</span>
          </div>
        </template>
        <template #mac_info="scope">
          <div style="height: 40px">
          <span
              style="font-size: 12px;color: #252631;text-overflow: ellipsis;overflow: hidden;width: 130px;display: inline-block"
          >{{ scope.row.mac_info.join('&nbsp;&nbsp;') }}</span>
          </div>
        </template>
        <template #app_ips="scope">
          <div style="height: 40px">
          <span
              style="font-size: 12px;color: #252631;text-overflow: ellipsis;overflow: hidden;width: 120px;display: inline-block"
          >{{ scope.row.app_ips.join('&nbsp;&nbsp;') }}</span>
          </div>
        </template>
        <template #online="scope">
          <div style="height: 40px">
            <base-avatar :size="8"
                       :style="[
                           {background:scope.row.online?'#6DD230':'#252631'},
                           {opacity:!scope.row.online?'0.3':'1'},
                           {'margin-right':'6px'}
                           ]"
            ></base-avatar>
            <span style="color: #252631;font-size: 12px">{{ scope.row.online ? '在线' : '离线' }}</span>
            <!--            <el-icon v-if="scope.row.online">-->
            <!--              <SuccessFilled style="color: #52c41a"/>-->
            <!--            </el-icon>-->
            <!--            <el-icon v-else>-->
            <!--              <Remove/>-->
            <!--            </el-icon>-->
          </div>
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TerminalManagement',
}
</script>
<script setup>
import { computed, provide, ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  RefreshRight,
  Search,
} from '@element-plus/icons-vue'
import '@/assets/ali/iconfont'
import { getTerminalList } from '@/api/terminal'
import CustomTable from '@/components/customTable.vue'

const searchUser = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(100)
const total = ref(0)

const propList = [
  {
    prop: 'app_name',
    label: '终端名',
    slotName: 'app_name',
    isCenter: '',
  },
  {
    prop: 'os_info',
    label: '终端类型',
    slotName: 'os_info',
  },
  {
    prop: 'mac_info',
    label: 'MAC地址',
    slotName: 'mac_info',
  },
  {
    prop: 'app_ips',
    label: 'IP地址',
    slotName: 'app_ips',
  },
  {
    prop: 'login_user',
    label: '登陆用户',
    slotName: 'login_user',
  },
  {
    prop: 'update_time',
    label: '最后上线时间',
    slotName: 'update_time',
  },
  {
    prop: 'online',
    label: '在线状态',
    slotName: 'online',
  },
]

const tableConfig = {
  propList,
  isSelectColumn: false,
  isOperationColumn: false,
}

// 查询
const tableData = ref([])
const getTableData = async() => {
  const query = {
    limit: pageSize.value,
    offset: (currentPage.value - 1) * pageSize.value,
    search: searchUser.value,
  }
  const terminalList = await getTerminalList(query)
  console.log('terminalList')
  console.log(terminalList)
  console.log('end')
  if (terminalList.data.code === 0) {
    tableData.value = terminalList.data.data.rows
    total.value = terminalList.data.data.total_rows
  }
}

getTableData()

const platIcon = (app_plat) => {
  switch (app_plat) {
    case 'windows':
      return '#icon-windows'
      break
    case 'linux':
      return '#icon-linux-5'
      break
    case 'mac':
      return '#icon-mac'
      break
    case 'android':
      return ''
      break
    case 'ios':
      return '#icon-mac'
      break
    default :
      return ''
      break
  }
}

const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  console.log(val)
  multipleSelection.value = val
}

const handleDelete = (index, row) => {
  console.log(index)
  console.log(row)
  ElMessageBox.confirm(
      '<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个终端吗？</strong><br><strong>删除后关联的用户将强制下线，请谨慎操作。</strong>',
      '删除终端',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
  )
      .then(() => {
        if (row.empno === '0001') {
          ElMessage({
            type: 'success',
            message: '删除成功',
          })
        } else {
          ElMessage({
            type: 'error',
            message: '删除失败',
          })
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
}
provide('currentPage', currentPage)
provide('pageSize', pageSize)
provide('total', total)
provide('getTableData', getTableData)
</script>
<style lang="scss" scoped>
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

</style>
