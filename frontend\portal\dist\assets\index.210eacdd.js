/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import e from"./fieldDialog.74e2194f.js";import a from"./previewCodeDialg.0f5b9026.js";import{a as l,b as t,t as i,c as o}from"./stringFun.2b3a18f6.js";import{p as d,c as u,g as s,a as r,b as n,d as m,e as p}from"./autoCode.a94c47f9.js";import{u as c}from"./dictionary.aa4d2fe8.js";import{_ as v,u as f,a as b,B as g,r as y,y as _,h as N,o as k,d as h,j as w,e as V,w as x,k as C,F as T,i as U,f as z,t as S,g as j,M as F,aj as q}from"./index.74d1ee23.js";import{W as D}from"./warningBar.4338ec87.js";import"./sysDictionary.2a470765.js";const L={class:"gva-search-box"},J={style:{fontSize:"16px",paddingLeft:"20px"}},A={class:"gva-search-box"},O={class:"gva-table-box"},B={class:"gva-btn-list"},M={style:{"text-align":"right","margin-top":"8px"}},E={class:"gva-btn-list justify-content-flex-end auto-btn-list"},P={class:"dialog-footer"},$={class:"previewCodeTool"},I={class:"dialog-footer",style:{"padding-top":"14px","padding-right":"14px"}},R=v(Object.assign({name:"AutoCode"},{setup(v){const R={fieldName:"",fieldDesc:"",fieldType:"",dataType:"",fieldJson:"",columnName:"",dataTypeLong:"",comment:"",require:!1,errorText:"",clearable:!0,fieldSearchType:"",dictType:""},X=f(),W=b(),G=g([]),H=y({}),K=y({dbName:"",tableName:""}),Q=y([]),Y=y([]),Z=y(""),ee=y({}),ae=y({structName:"",tableName:"",packageName:"",package:"",abbreviation:"",description:"",autoCreateApiToSql:!0,autoMoveFile:!0,fields:[]}),le=y({structName:[{required:!0,message:"请输入结构体名称",trigger:"blur"}],abbreviation:[{required:!0,message:"请输入结构体简称",trigger:"blur"}],description:[{required:!0,message:"请输入结构体描述",trigger:"blur"}],packageName:[{required:!0,message:"文件名称：sysXxxxXxxx",trigger:"blur"}],package:[{required:!0,message:"请选择package",trigger:"blur"}]}),te=y({}),ie=y({}),oe=y(!1),de=y(!1),ue=y(null),se=()=>{ue.value.selectText()},re=()=>{ue.value.copy()},ne=e=>{oe.value=!0,e?(Z.value="edit",ie.value=JSON.parse(JSON.stringify(e)),te.value=e):(Z.value="add",te.value=JSON.parse(JSON.stringify(R)))},me=q(),pe=()=>{me.refs.fieldDialogNode.fieldDialogFrom.validate((e=>{if(!e)return!1;te.value.fieldName=t(te.value.fieldName),"add"===Z.value&&ae.value.fields.push(te.value),oe.value=!1}))},ce=()=>{"edit"===Z.value&&(te.value=ie.value),oe.value=!1},ve=y(null),fe=async e=>ae.value.fields.length<=0?(F({type:"error",message:"请填写至少一个field"}),!1):ae.value.fields.some((e=>e.fieldName===ae.value.structName))?(F({type:"error",message:"存在与结构体同名的字段"}),!1):void ve.value.validate((async a=>{var o;if(!a)return!1;for(const e in ae.value)"string"==typeof ae.value[e]&&(ae.value[e]=ae.value[e].trim());if(ae.value.structName=t(ae.value.structName),ae.value.tableName=ae.value.tableName.replace(" ",""),ae.value.tableName||(ae.value.tableName=i(l(ae.value.structName))),ae.value.structName===ae.value.abbreviation)return F({type:"error",message:"structName和struct简称不能相同"}),!1;if(ae.value.humpPackageName=i(ae.value.packageName),e){const e=await d(ae.value);H.value=e.data.autoCode,de.value=!0}else{const e=await u(ae.value);if("false"===(null==(o=e.headers)?void 0:o.success))return;if(ae.value.autoMoveFile)return void F({type:"success",message:"自动化代码创建成功，自动移动成功"});F({type:"success",message:"自动化代码创建成功，正在下载"});const a=new Blob([e]),l="ginvueadmin.zip";if("download"in document.createElement("a")){const e=window.URL.createObjectURL(a),t=document.createElement("a");t.style.display="none",t.href=e,t.setAttribute("download",l),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)}else window.navigator.msSaveBlob(a,l)}})),be=async()=>{const e=await s({dbName:K.value.dbName});0===e.code&&(Y.value=e.data.tables),K.value.tableName=""},ge=async()=>{const e=["id","created_at","updated_at","deleted_at"],a=await r(K.value);if(0===a.code){const l=o(K.value.tableName);ae.value.structName=t(l),ae.value.tableName=K.value.tableName,ae.value.packageName=l,ae.value.abbreviation=l,ae.value.description=l+"表",ae.value.autoCreateApiToSql=!0,ae.value.autoMoveFile=!0,ae.value.fields=[],a.data.columns&&a.data.columns.forEach((a=>{if(!e.some((e=>e===a.columnName))){const e=o(a.columnName);ae.value.fields.push({fieldName:t(e),fieldDesc:a.columnComment||e+"字段",fieldType:ee.value[a.dataType],dataType:a.dataType,fieldJson:e,dataTypeLong:a.dataTypeLong&&a.dataTypeLong.split(",")[0],columnName:a.columnName,comment:a.columnComment,require:!1,errorText:"",clearable:!0,fieldSearchType:"",dictType:""})}}))}},ye=async()=>{["string","int","bool","float64","time.Time"].forEach((async e=>{const a=await(async e=>{const a=c();return await a.getDictionary(e),a.dictionaryMap[e]})(e);a&&a.forEach((a=>{ee.value[a.label]=e}))}))},_e=y([]),Ne=async()=>{const e=await n();0===e.code&&(_e.value=e.data.pkgs)},ke=()=>{W.push({name:"autoPkg"})},he=()=>{(async()=>{const e=await m();0===e.code&&(Q.value=e.data.dbs)})(),ye(),Ne();const e=X.params.id;e&&(async e=>{const a=await p({id:Number(e)});0===a.code&&(ae.value=JSON.parse(a.data.meta))})(e)};return he(),_((()=>X.params.id),(e=>{"autoCodeEdit"===X.name&&he()})),(t,i)=>{const o=N("pointer"),d=N("el-icon"),u=N("base-option"),s=N("base-select"),r=N("base-form-item"),n=N("base-button"),m=N("base-form"),p=N("el-collapse-item"),c=N("el-collapse"),v=N("base-input"),f=N("refresh"),b=N("document-add"),g=N("el-tooltip"),y=N("base-checkbox"),_=N("el-table-column"),F=N("el-popover"),q=N("el-table"),R=N("el-dialog");return k(),h("div",null,[w(D,{href:"https://www.bilibili.com/video/BV1kv4y1g7nT?p=3",title:"此功能为开发环境使用，不建议发布到生产，具体使用效果请看视频https://www.bilibili.com/video/BV1kv4y1g7nT?p=3"}),V("div",L,[w(c,{modelValue:G,"onUpdate:modelValue":i[2]||(i[2]=e=>G=e),style:{"margin-bottom":"12px"}},{default:x((()=>[w(p,{name:"1"},{title:x((()=>[V("div",J,[i[18]||(i[18]=C(" 点这里从现有数据库创建代码 ")),w(d,{class:"header-icon"},{default:x((()=>[w(o)])),_:1})])])),default:x((()=>[w(m,{ref:"getTableForm",style:{"margin-top":"24px"},inline:!0,model:K.value,"label-width":"120px"},{default:x((()=>[w(r,{label:"数据库名",prop:"structName"},{default:x((()=>[w(s,{modelValue:K.value.dbName,"onUpdate:modelValue":i[0]||(i[0]=e=>K.value.dbName=e),filterable:"",placeholder:"请选择数据库",onChange:be},{default:x((()=>[(k(!0),h(T,null,U(Q.value,(e=>(k(),z(u,{key:e.database,label:e.database,value:e.database},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),w(r,{label:"表名",prop:"structName"},{default:x((()=>[w(s,{modelValue:K.value.tableName,"onUpdate:modelValue":i[1]||(i[1]=e=>K.value.tableName=e),disabled:!K.value.dbName,filterable:"",placeholder:"请选择表"},{default:x((()=>[(k(!0),h(T,null,U(Y.value,(e=>(k(),z(u,{key:e.tableName,label:e.tableName,value:e.tableName},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),w(r,null,{default:x((()=>[w(n,{size:"small",type:"primary",onClick:ge},{default:x((()=>i[19]||(i[19]=[C("使用此表创建")]))),_:1,__:[19]})])),_:1})])),_:1},8,["model"])])),_:1})])),_:1},8,["modelValue"])]),V("div",A,[w(m,{ref_key:"autoCodeForm",ref:ve,rules:le.value,model:ae.value,size:"small","label-width":"120px",inline:!0},{default:x((()=>[w(r,{label:"Struct名称",prop:"structName"},{default:x((()=>[w(v,{modelValue:ae.value.structName,"onUpdate:modelValue":i[3]||(i[3]=e=>ae.value.structName=e),placeholder:"首字母自动转换大写"},null,8,["modelValue"])])),_:1}),w(r,{label:"TableName",prop:"tableName"},{default:x((()=>[w(v,{modelValue:ae.value.tableName,"onUpdate:modelValue":i[4]||(i[4]=e=>ae.value.tableName=e),placeholder:"指定表名（非必填）"},null,8,["modelValue"])])),_:1}),w(r,{label:"Struct简称",prop:"abbreviation"},{default:x((()=>[w(v,{modelValue:ae.value.abbreviation,"onUpdate:modelValue":i[5]||(i[5]=e=>ae.value.abbreviation=e),placeholder:"简称会作为入参对象名和路由group"},null,8,["modelValue"])])),_:1}),w(r,{label:"Struct中文名称",prop:"description"},{default:x((()=>[w(v,{modelValue:ae.value.description,"onUpdate:modelValue":i[6]||(i[6]=e=>ae.value.description=e),placeholder:"中文描述作为自动api描述"},null,8,["modelValue"])])),_:1}),w(r,{label:"文件名称",prop:"packageName"},{default:x((()=>[w(v,{modelValue:ae.value.packageName,"onUpdate:modelValue":i[7]||(i[7]=e=>ae.value.packageName=e),placeholder:"生成文件的默认名称(建议为驼峰格式,首字母小写,如sysXxxXxxx)",onBlur:i[8]||(i[8]=e=>{var a,t;(a=ae.value)[t="packageName"]=l(a[t])})},null,8,["modelValue"])])),_:1}),w(r,{label:"Package（包）",prop:"package"},{default:x((()=>[w(s,{modelValue:ae.value.package,"onUpdate:modelValue":i[9]||(i[9]=e=>ae.value.package=e),style:{width:"194px"}},{default:x((()=>[(k(!0),h(T,null,U(_e.value,(e=>(k(),z(u,{key:e.ID,value:e.packageName,label:e.packageName},null,8,["value","label"])))),128))])),_:1},8,["modelValue"]),w(d,{class:"auto-icon",onClick:Ne},{default:x((()=>[w(f)])),_:1}),w(d,{class:"auto-icon",onClick:ke},{default:x((()=>[w(b)])),_:1})])),_:1}),w(r,null,{label:x((()=>[w(g,{content:"注：把自动生成的API注册进数据库",placement:"bottom",effect:"light"},{default:x((()=>i[20]||(i[20]=[V("div",null," 自动创建API ",-1)]))),_:1,__:[20]})])),default:x((()=>[w(y,{modelValue:ae.value.autoCreateApiToSql,"onUpdate:modelValue":i[10]||(i[10]=e=>ae.value.autoCreateApiToSql=e)},null,8,["modelValue"])])),_:1}),w(r,null,{label:x((()=>[w(g,{content:"注：自动迁移生成的文件到yaml配置的对应位置",placement:"bottom",effect:"light"},{default:x((()=>i[21]||(i[21]=[V("div",null," 自动移动文件 ",-1)]))),_:1,__:[21]})])),default:x((()=>[w(y,{modelValue:ae.value.autoMoveFile,"onUpdate:modelValue":i[11]||(i[11]=e=>ae.value.autoMoveFile=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["rules","model"])]),V("div",O,[V("div",B,[w(n,{size:"small",type:"primary",onClick:i[12]||(i[12]=e=>ne())},{default:x((()=>i[22]||(i[22]=[C("新增Field")]))),_:1,__:[22]})]),w(q,{data:ae.value.fields},{default:x((()=>[w(_,{align:"left",type:"index",label:"序列",width:"60"}),w(_,{align:"left",prop:"fieldName",label:"Field名"}),w(_,{align:"left",prop:"fieldDesc",label:"中文名"}),w(_,{align:"left",prop:"require",label:"是否必填"},{default:x((({row:e})=>[C(S(e.require?"是":"否"),1)])),_:1}),w(_,{align:"left",prop:"fieldJson","min-width":"120px",label:"FieldJson"}),w(_,{align:"left",prop:"fieldType",label:"Field数据类型",width:"130"}),w(_,{align:"left",prop:"dataTypeLong",label:"数据库字段长度",width:"130"}),w(_,{align:"left",prop:"columnName",label:"数据库字段",width:"130"}),w(_,{align:"left",prop:"comment",label:"数据库字段描述",width:"130"}),w(_,{align:"left",prop:"fieldSearchType",label:"搜索条件",width:"130"}),w(_,{align:"left",prop:"dictType",label:"字典",width:"130"}),w(_,{align:"left",label:"操作",width:"300",fixed:"right"},{default:x((e=>[w(n,{size:"small",type:"primary",link:"",icon:"edit",onClick:a=>ne(e.row)},{default:x((()=>i[23]||(i[23]=[C("编辑")]))),_:2,__:[23]},1032,["onClick"]),w(n,{size:"small",type:"primary",link:"",disabled:0===e.$index,onClick:a=>(e=>{if(0===e)return;const a=ae.value.fields[e-1];ae.value.fields.splice(e-1,1),ae.value.fields.splice(e,0,a)})(e.$index)},{default:x((()=>i[24]||(i[24]=[C("上移")]))),_:2,__:[24]},1032,["disabled","onClick"]),w(n,{size:"small",type:"primary",link:"",disabled:e.$index+1===ae.value.fields.length,onClick:a=>(e=>{if(e===ae.value.fields.length-1)return;const a=ae.value.fields[e+1];ae.value.fields.splice(e+1,1),ae.value.fields.splice(e,0,a)})(e.$index)},{default:x((()=>i[25]||(i[25]=[C("下移")]))),_:2,__:[25]},1032,["disabled","onClick"]),w(F,{modelValue:e.row.visible,"onUpdate:modelValue":a=>e.row.visible=a,placement:"top"},{reference:x((()=>[w(n,{size:"small",type:"primary",link:"",icon:"delete",onClick:a=>e.row.visible=!0},{default:x((()=>i[28]||(i[28]=[C("删除")]))),_:2,__:[28]},1032,["onClick"])])),default:x((()=>[i[29]||(i[29]=V("p",null,"确定删除吗？",-1)),V("div",M,[w(n,{size:"small",type:"primary",link:"",onClick:a=>e.row.visible=!1},{default:x((()=>i[26]||(i[26]=[C("取消")]))),_:2,__:[26]},1032,["onClick"]),w(n,{type:"primary",size:"small",onClick:a=>{return l=e.$index,void ae.value.fields.splice(l,1);var l}},{default:x((()=>i[27]||(i[27]=[C("确定")]))),_:2,__:[27]},1032,["onClick"])])])),_:2,__:[29]},1032,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"]),V("div",E,[w(n,{size:"small",type:"primary",onClick:i[13]||(i[13]=e=>fe(!0))},{default:x((()=>i[30]||(i[30]=[C("预览代码")]))),_:1,__:[30]}),w(n,{size:"small",type:"primary",onClick:i[14]||(i[14]=e=>fe(!1))},{default:x((()=>i[31]||(i[31]=[C("生成代码")]))),_:1,__:[31]})])]),w(R,{modelValue:oe.value,"onUpdate:modelValue":i[15]||(i[15]=e=>oe.value=e),width:"70%",title:"组件内容"},{footer:x((()=>[V("div",P,[w(n,{size:"small",onClick:ce},{default:x((()=>i[32]||(i[32]=[C("取 消")]))),_:1,__:[32]}),w(n,{size:"small",type:"primary",onClick:pe},{default:x((()=>i[33]||(i[33]=[C("确 定")]))),_:1,__:[33]})])])),default:x((()=>[oe.value?(k(),z(e,{key:0,ref:"fieldDialogNode","dialog-middle":te.value},null,8,["dialog-middle"])):j("",!0)])),_:1},8,["modelValue"]),w(R,{modelValue:de.value,"onUpdate:modelValue":i[17]||(i[17]=e=>de.value=e)},{header:x((()=>[V("div",$,[i[36]||(i[36]=V("p",null,"操作栏：",-1)),w(n,{size:"small",type:"primary",onClick:se},{default:x((()=>i[34]||(i[34]=[C("全选")]))),_:1,__:[34]}),w(n,{size:"small",type:"primary",onClick:re},{default:x((()=>i[35]||(i[35]=[C("复制")]))),_:1,__:[35]})])])),footer:x((()=>[V("div",I,[w(n,{size:"small",type:"primary",onClick:i[16]||(i[16]=e=>de.value=!1)},{default:x((()=>i[37]||(i[37]=[C("确 定")]))),_:1,__:[37]})])])),default:x((()=>[de.value?(k(),z(a,{key:0,ref_key:"previewNode",ref:ue,"preview-code":H.value},null,8,["preview-code"])):j("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-85da76d4"]]);export{R as default};
