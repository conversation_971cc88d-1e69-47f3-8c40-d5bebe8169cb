/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
System.register(["./index-legacy.11b10372.js","./menuItem-legacy.558bc3d2.js","./asyncSubmenu-legacy.e9753de0.js"],(function(e,n){"use strict";var t,r,o,u,i,f,l,c,a,s,d,h,m;return{setters:[function(e){t=e._,r=e.c,o=e.h,u=e.o,i=e.g,f=e.w,l=e.d,c=e.F,a=e.i,s=e.f,d=e.x},function(e){h=e.default},function(e){m=e.default}],execute:function(){var n=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:function(){return null}},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup:function(e){var n=e,t=r((function(){return n.routerInfo.children&&n.routerInfo.children.filter((function(e){return!e.hidden})).length?m:h}));return function(n,r){var h=o("AsideComponent");return e.routerInfo.hidden?s("v-if",!0):(u(),i(d(t.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:f((function(){return[e.routerInfo.children&&e.routerInfo.children.length?(u(!0),l(c,{key:0},a(e.routerInfo.children,(function(n){return u(),i(h,{key:n.name,"is-collapse":!1,"router-info":n,theme:e.theme},null,8,["router-info","theme"])})),128)):s("v-if",!0)]})),_:1},8,["is-collapse","theme","router-info"]))}}});e("default",t(n,[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/index.vue"]]))}}}));
