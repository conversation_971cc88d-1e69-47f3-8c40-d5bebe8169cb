/*! 
 Build based on gin-vue-admin 
 Time : 1749553755000 */
import{_ as e,r as a,E as s,h as l,o as t,d as i,e as n,C as o,j as c,w as u,g as v,t as d,M as r,k as p}from"./index.1aedb960.js";const b={class:"setting-page"},m={class:"main-content"},g={class:"setting-container"},_={class:"tabs-header"},V={class:"tabs-content"},y={key:0,class:"tab-panel"},h={class:"setting-section"},k={class:"setting-item setting-platformAddress"},f={class:"setting-item"},x={class:"checkbox-group"},U={key:1,class:"tab-panel"},C={class:"setting-section setting-update"},S={class:"setting-item"},I={class:"checkbox-group"},j={class:"setting-item"},w={class:"about-section"},A={class:"version-info"},q={class:"version-item"},z={class:"version-value-group"},E={class:"version-value"},F={class:"version-item"},J={class:"version-value"},M={class:"version-item"},N={class:"version-value"},O=e({__name:"setting",setup(e){const O=a("general"),T=a(""),B=a(!1),D=a(!0),G=a(!0),H=a("daily"),K=a("2.5.0"),L=a("2025.03.21 09:00"),P=a("2025.03.21 09:00");s((()=>{R()}));const Q=async()=>{r.info("正在检查更新..."),setTimeout((()=>{r.success("当前已是最新版本")}),1500)},R=()=>{const e=localStorage.getItem("appSettings");if(e){const a=JSON.parse(e);T.value=a.platformAddress||"",B.value=a.autoStart||!1,D.value=void 0===a.autoConnect||a.autoConnect,G.value=void 0===a.autoUpdate||a.autoUpdate,H.value=a.updateFrequency||"daily"}};return(e,a)=>{const s=l("base-input"),r=l("base-checkbox"),R=l("base-option"),W=l("base-select"),X=l("base-button");return t(),i("div",b,[n("div",m,[n("div",g,[n("div",_,[n("div",{class:o(["tab-item",{active:"general"===O.value}]),onClick:a[0]||(a[0]=e=>O.value="general")}," 通用设置 ",2),n("div",{class:o(["tab-item",{active:"version"===O.value}]),onClick:a[1]||(a[1]=e=>O.value="version")}," 版本信息 ",2)]),n("div",V,["general"===O.value?(t(),i("div",y,[n("div",h,[n("div",k,[a[7]||(a[7]=n("label",{class:"setting-label"},"平台地址",-1)),c(s,{modelValue:T.value,"onUpdate:modelValue":a[2]||(a[2]=e=>T.value=e),placeholder:"输入您连接的平台服务器地址",class:"setting-input",clearable:""},null,8,["modelValue"])]),n("div",f,[a[10]||(a[10]=n("label",{class:"setting-label"},"启动选项",-1)),n("div",x,[c(r,{modelValue:B.value,"onUpdate:modelValue":a[3]||(a[3]=e=>B.value=e),class:"setting-checkbox"},{default:u((()=>a[8]||(a[8]=[p(" 开机自启动 ")]))),_:1,__:[8]},8,["modelValue"]),c(r,{modelValue:D.value,"onUpdate:modelValue":a[4]||(a[4]=e=>D.value=e),class:"setting-checkbox"},{default:u((()=>a[9]||(a[9]=[p(" 启动后自动连接 ")]))),_:1,__:[9]},8,["modelValue"])])])])])):v("",!0),"version"===O.value?(t(),i("div",U,[n("div",C,[n("div",S,[a[12]||(a[12]=n("label",{class:"setting-label"},"更新选项",-1)),n("div",I,[c(r,{modelValue:G.value,"onUpdate:modelValue":a[5]||(a[5]=e=>G.value=e),class:"setting-checkbox"},{default:u((()=>a[11]||(a[11]=[p(" 自动检查更新 ")]))),_:1,__:[11]},8,["modelValue"])])]),n("div",j,[a[13]||(a[13]=n("label",{class:"setting-label"},"更新检查频率",-1)),c(W,{modelValue:H.value,"onUpdate:modelValue":a[6]||(a[6]=e=>H.value=e),class:"setting-select",placeholder:"请选择"},{default:u((()=>[c(R,{label:"每天",value:"daily"}),c(R,{label:"每周",value:"weekly"}),c(R,{label:"每月",value:"monthly"})])),_:1},8,["modelValue"])])]),n("div",w,[a[18]||(a[18]=n("h3",{class:"about-title"},"关于安全客户端",-1)),n("div",A,[n("div",q,[a[15]||(a[15]=n("span",{class:"version-label"},"当前版本",-1)),n("div",z,[n("span",E,d(K.value),1),c(X,{text:"",type:"primary",size:"small",onClick:Q},{default:u((()=>a[14]||(a[14]=[p(" 检查更新 ")]))),_:1,__:[14]})])]),n("div",F,[a[16]||(a[16]=n("span",{class:"version-label"},"构建时间",-1)),n("span",J,d(L.value),1)]),n("div",M,[a[17]||(a[17]=n("span",{class:"version-label"},"上次更新时间",-1)),n("span",N,d(P.value),1)])]),a[19]||(a[19]=n("div",{class:"copyright"},[n("p",null,"© 2025 Security Systems Inc. 保留所有权利")],-1))])])):v("",!0)])])])])}}},[["__scopeId","data-v-c55304de"]]);export{O as default};
