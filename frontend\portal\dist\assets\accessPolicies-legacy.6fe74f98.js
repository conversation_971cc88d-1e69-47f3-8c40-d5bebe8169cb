/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,r){if(e){if("string"==typeof e)return t(e,r);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?t(e,r):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",u=n.toStringTag||"@@toStringTag";function l(r,n,o,u){var l=n&&n.prototype instanceof s?n:s,c=Object.create(l.prototype);return a(c,"_invoke",function(r,a,n){var o,u,l,s=0,c=n||[],p=!1,f={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,r){return o=t,u=0,l=e,f.n=r,i}};function d(r,a){for(u=r,l=a,t=0;!p&&s&&!n&&t<c.length;t++){var n,o=c[t],d=f.p,g=o[2];r>3?(n=g===a)&&(l=o[(u=o[4])?5:(u=3,3)],o[4]=o[5]=e):o[0]<=d&&((n=r<2&&d<o[1])?(u=0,f.v=a,f.n=o[1]):d<g&&(n=r<3||o[0]>a||a>g)&&(o[4]=r,o[5]=a,f.n=g,u=0))}if(n||r>1)return i;throw p=!0,a}return function(n,c,g){if(s>1)throw TypeError("Generator is already running");for(p&&1===c&&d(c,g),u=c,l=g;(t=u<2?e:l)||!p;){o||(u?u<3?(u>1&&(f.n=-1),d(u,l)):f.n=l:f.v=l);try{if(s=2,o){if(u||(n="next"),t=o[n]){if(!(t=t.call(o,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,u<2&&(u=0)}else 1===u&&(t=o.return)&&t.call(o),u<2&&(l=TypeError("The iterator does not provide a '"+n+"' method"),u=1);o=e}else if((t=(p=f.n<0)?l:r.call(a,f))!==i)break}catch(t){o=e,u=1,l=t}finally{s=1}}return{value:t,done:p}}}(r,o,u),!0),c}var i={};function s(){}function c(){}function p(){}t=Object.getPrototypeOf;var f=[][o]?t(t([][o]())):(a(t={},o,(function(){return this})),t),d=p.prototype=s.prototype=Object.create(f);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,a(e,u,"GeneratorFunction")),e.prototype=Object.create(d),e}return c.prototype=p,a(d,"constructor",p),a(p,"constructor",c),c.displayName="GeneratorFunction",a(p,u,"GeneratorFunction"),a(d),a(d,u,"Generator"),a(d,o,(function(){return this})),a(d,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:l,m:g}})()}function a(e,t,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}a=function(e,t,r,n){if(t)o?o(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r;else{var u=function(t,r){a(e,t,(function(e){return this._invoke(t,r,e)}))};u("next",0),u("throw",1),u("return",2)}},a(e,t,r,n)}function n(e,t,r,a,n,o,u){try{var l=e[o](u),i=l.value}catch(e){return void r(e)}l.done?t(i):Promise.resolve(i).then(a,n)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var u=e.apply(t,r);function l(e){n(u,a,o,l,i,"next",e)}function i(e){n(u,a,o,l,i,"throw",e)}l(void 0)}))}}System.register(["./index-legacy.dbc04544.js","./customTable-legacy.8e1d28b3.js","./customFrom-legacy.fd23a917.js","./resource-legacy.d4de6f0a.js"],(function(t,a){"use strict";var n,u,l,i,s,c,p,f,d,g,m,_,y,v,h,b,k,C,w,S,O,V,x,N,T=document.createElement("style");return T.textContent=".titleClass[data-v-40cb3c28]{font-size:18px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#252631;margin-left:15px}.el-drawer__header{margin-bottom:5px!important;border-bottom:1px solid #EBEBEB;padding-top:10px!important;padding-bottom:10px!important}.el-table__body tr.current-row>td{background-color:#cee9fd!important}.el-table--enable-row-hover .el-table__body tr:hover>td{background-color:#cee9fd}.table-row-style .el-table__cell .cell .cell{color:#256ebf!important;font-size:12px}\n",document.head.appendChild(T),{setters:[function(e){n=e.x,u=e._,l=e.b,i=e.r,s=e.B,c=e.p,p=e.h,f=e.o,d=e.d,g=e.e,m=e.j,_=e.w,y=e.k,v=e.F,h=e.i,b=e.t,k=e.f,C=e.O,w=e.M,S=e.P},function(e){O=e._},function(e){V=e._},function(e){x=e.a,N=e.b}],execute:function(){var a=function(e){return n({url:"/console/v1/access_strategy/strategy",method:"post",data:e})},T=function(e){return n({url:"/console/v1/access_strategy/strategy",method:"put",data:e})},J={class:"policies"},F={style:{"background-color":"#FFFFFF",padding:"22px 20px 60px 20px"}},G={class:"header"},j={style:{"text-align":"center"}},P={style:{"text-align":"center"}},D={style:{"text-align":"center"}},E={style:{color:"#252631"}},A={style:{"text-align":"center"}},z={style:{width:"15px","max-width":"20px",float:"left"}},B=["id"],L=Object.assign({name:"AccessPolicies"},{setup:function(t){var u=l(),L=i(),I=i(""),R=i(),q=s({}),U=i([]),M=function(){var e=Object.keys($.formValues),t={};e.forEach((function(e){t[e]=""})),Object.assign($.formValues,t)},H=function(){U.value.length=0,ve.value.length=0,he.value.length=0,be.value.length=0,ke.value.length=0,M(),K.value=!1},$={formItems:[],formValues:s({})},K=i(!1),Q=i(""),W=i(0),X=i(1),Y=i(50),Z={propList:[{prop:"strategy_name",label:"名称",slotName:"strategy_name"},{prop:"strategy_detail",label:"描述",slotName:"strategy_detail"},{prop:"user_in_strategy",label:"用户",slotName:"user_in_strategy"},{prop:"app_in_strategy",label:"资源",slotName:"app_in_strategy"},{prop:"start_time",label:"生效时间",slotName:"start_time"},{prop:"strategy_status",label:"状态",slotName:"strategy_status"}],isSelectColumn:!0,isOperationColumn:!0},ee=i([{text:"组织",value:"group"},{text:"用户",value:"user"}]),te=i([]),re=i(""),ae=i(0),ne=i(1),oe=i(15),ue=i(),le=i(),ie=function(){var e=o(r().m((function e(){var t,a,n;return r().w((function(e){for(;;)switch(e.n){case 0:return t={briefRepresentation:!1,first:(ne.value-1)*oe.value,max:oe.value,search:ue.value},e.n=1,u.GetOrganize(t);case 1:return a=e.v,e.n=2,u.GetOrganizeCount(t);case 2:n=e.v,console.log(n),a.data&&(te.value=a.data,ae.value=n.data.count);case 3:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),se=function(){var e=o(r().m((function e(){var t,a,n;return r().w((function(e){for(;;)switch(e.n){case 0:return console.log("getTableData"),t={briefRepresentation:!1,first:(ne.value-1)*oe.value,max:oe.value,search:ue.value},e.n=1,u.GetUserListCount(t);case 1:return a=e.v,e.n=2,u.GetUserList(t);case 2:(n=e.v).data&&(te.value=JSON.parse(JSON.stringify(n.data).replace(/user_id/g,"id")),te.value=JSON.parse(JSON.stringify(n.data).replace(/username/g,"name")),console.log("userTotal"),console.log(a),ae.value=a.data);case 3:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),ce=function(){var e=o(r().m((function e(){var t;return r().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,x("corp_id");case 1:200===(t=e.v).status&&0===t.data.code&&(console.log("getGroupList"),te.value=JSON.parse(JSON.stringify(t.data.data).replace(/group_id/g,"id")),te.value=JSON.parse(JSON.stringify(t.data.data).replace(/group_name/g,"name")));case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),pe=function(){var e=o(r().m((function e(){var t,a;return r().w((function(e){for(;;)switch(e.n){case 0:return console.log("getAppList"),t={offset:(ne.value-1)*oe.value,limit:oe.value,search:ue.value},e.n=1,N(t);case 1:a=e.v,console.log("getAppList"),console.log(a),0===a.data.code&&(te.value=JSON.parse(JSON.stringify(a.data.data.rows).replace(/app_id/g,"id")),te.value=JSON.parse(JSON.stringify(a.data.data.rows).replace(/app_name/g,"name")),ae.value=a.data.data.total_rows);case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),fe=function(){var e=o(r().m((function e(){return r().w((function(e){for(;;)switch(e.n){case 0:if(console.log("getTreeData"),console.log(re.value),console.log(le.value),"group"!==re.value||"user"!==le.value){e.n=1;break}return e.n=1,ie();case 1:if("user"!==re.value||"user"!==le.value){e.n=2;break}return e.n=2,se();case 2:if("group"!==re.value||"resource"!==le.value){e.n=3;break}return e.n=3,ce();case 3:if("user"!==re.value||"resource"!==le.value){e.n=4;break}return e.n=4,pe();case 4:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),de=i([]),ge=function(){var e=o(r().m((function e(){var t,a;return r().w((function(e){for(;;)switch(e.n){case 0:return console.log("getTableData"),t={offset:(X.value-1)*Y.value,limit:Y.value,search:I.value},e.n=1,n({url:"/console/v1/access_strategy/strategy_list",method:"post",data:t});case 1:a=e.v,console.log("res"),console.log(a),0===a.data.code&&(de.value=a.data.data.strategy_list_data,W.value=a.data.data.total_num);case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();ge(),s({name:"",description:"",member:"",reason:["我因为所属组织结构原因需要访问该资源","我因为岗位职责原因需要访问该资源","我因为个人特殊原因需要访问该资源"]});var me=i("add"),_e=i("新增访问策略");i(!1),i([]);var ye=function(){var e=o(r().m((function e(t){return r().w((function(e){for(;;)switch(e.n){case 0:return console.log($.formValues),console.log($.formValues.appGroupCheck),e.n=1,R.value.ruleFormRef.validate(function(){var e=o(r().m((function e(t,n){var o,u;return r().w((function(e){for(;;)switch(e.n){case 0:if(t){e.n=1;break}return w({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),e.a(2,"");case 1:if(o="",u={app_group_ids:$.formValues.appGroupCheck.map((function(e){return e.id||e.app_group_id})),app_ids:$.formValues.appCheck.map((function(e){return e.id||e.app_id})),enable_log:$.formValues.enable_log?1:2,end_time:$.formValues.time?$.formValues.time[1]:"",start_time:$.formValues.time?$.formValues.time[0]:"",strategy_detail:$.formValues.description,strategy_name:$.formValues.name,strategy_status:Number($.formValues.statu),user_group_ids:$.formValues.groupCheck.map((function(e){return e.id||e.user_group_id})),user_ids:$.formValues.userCheck.map((function(e){return e.id||e.user_id}))},"add"!==me.value){e.n=3;break}return e.n=2,a(u);case 2:o=e.v;case 3:if("edit"!==me.value){e.n=5;break}return u.id=$.formValues.id,console.log("data"),console.log(u),e.n=4,T(u);case 4:o=e.v;case 5:if(console.log("res"),console.log(o),0!==o.data.code){e.n=6;break}return M(),e.n=6,ge();case 6:U.value.length=0,ve.value.length=0,he.value.length=0,be.value.length=0,ke.value.length=0,K.value=!1;case 7:return e.a(2)}}),e)})));return function(t,r){return e.apply(this,arguments)}}());case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),ve=i([]),he=i([]),be=i([]),ke=i([]),Ce=function(){var e=o(r().m((function e(t){var a,o,u;return r().w((function(e){for(;;)switch(e.n){case 0:return console.log("updateState"),a=L.value.getSelectionRows(),o=a.map((function(e){return e.id})),e.n=1,n({url:"/console/v1/access_strategy/strategy_enable",method:"post",data:{id:o,status:t}});case 1:return u=e.v,e.n=2,ge();case 2:console.log(u);case 3:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}();return c("getTableData",ge),c("currentPage",X),c("pageSize",Y),c("total",W),c("handleEdit",(function(t,r){console.log(t,r),Q.value="编辑访问策略",_e.value="编辑访问策略",me.value="edit",$.formItems=[{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"statu",label:"状态：",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"2"}]},{field:"description",label:"描述：",type:"input",placeholder:"访问策略描述"},{field:"user",label:"用户：",type:"dialog",treeType:"group",title:"选择用户",dataType:"user",groupCheck:"groupCheck",userCheck:"userCheck",placeholder:"请选择用户",rules:[{required:!0,message:"用户不能为空",trigger:"blur"}]},{field:"resource",label:"资源：",type:"dialog",treeType:"group",appGroupCheck:"appGroupCheck",appCheck:"appCheck",title:"选择资源",dataType:"resource",placeholder:"请选择资源",rules:[{required:!0,message:"资源不能为空",trigger:"blur"}]},{field:"time",label:"时间：",type:"timepicker",placeholder:"时间选择",dateType:"dateType"},{field:"enable_log",label:"日志：",type:"checkbox"}],$.formValues.id=r.id,$.formValues.name=r.strategy_name,$.formValues.statu=r.strategy_status.toString(),$.formValues.description=r.strategy_detail,$.formValues.groupCheck=r.user_group_in_strategy||[],$.formValues.userCheck=r.user_in_strategy||[],$.formValues.appGroupCheck=r.app_group_in_strategy||[],$.formValues.appCheck=r.app_in_strategy||[],$.formValues.dateType=r.start_time?"1":"0",he.value=[],r.user_in_strategy&&(he.value=JSON.parse(JSON.stringify(r.user_in_strategy).replace(/user_id/g,"id")),he.value=JSON.parse(JSON.stringify(he.value).replace(/user_name/g,"name"))),ve.value=[],r.user_group_in_strategy&&(ve.value=JSON.parse(JSON.stringify(r.user_group_in_strategy).replace(/user_group_id/g,"id")),ve.value=JSON.parse(JSON.stringify(ve.value).replace(/user_group_name/g,"name"))),be.value=[],r.app_group_in_strategy&&(be.value=JSON.parse(JSON.stringify(r.app_group_in_strategy).replace(/app_group_id/g,"id")),be.value=JSON.parse(JSON.stringify(be.value).replace(/app_group_name/g,"name"))),ke.value=[],r.app_in_strategy&&(ke.value=JSON.parse(JSON.stringify(r.app_in_strategy).replace(/app_id/g,"id")),ke.value=JSON.parse(JSON.stringify(ke.value).replace(/app_name/g,"name"))),$.formValues.user=[].concat(e(he.value),e(ve.value)),$.formValues.resource=[].concat(e(ke.value),e(be.value)),$.formValues.time=[r.start_time,r.end_time],$.formValues.enable_log=1===r.enable_log,K.value=!0})),c("handleDelete",(function(e,t){console.log(e),console.log(t),S.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个策略吗？</strong><br><strong>删除后关联的用户将无法访问此策略关联的应用，请谨慎操作。</strong>',"删除策略",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(o(r().m((function e(){return r().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,r={id:[t.id]},n({url:"/console/v1/access_strategy/strategy",method:"delete",data:r});case 1:if(0!==e.v.data.code){e.n=3;break}return console.log(111),w({type:"success",message:"删除成功"}),e.n=2,ge();case 2:e.n=4;break;case 3:w({type:"error",message:"删除失败"});case 4:return e.a(2)}var r}),e)})))).catch((function(){w({type:"info",message:"取消删除"})}))})),c("treeData",te),c("treeLabel",ee),c("treeType",re),c("treeTotal",ae),c("treeCurrentPage",ne),c("treePageSize",oe),c("treeSearch",ue),c("defaultProps",{id:"id",label:"name",children:"subGroups",isLeaf:"leaf"}),c("dataType",le),c("getTreeData",fe),c("groupCheck",ve),c("userCheck",he),c("appGroupCheck",be),c("appCheck",ke),c("checkedData",U),function(e,t){var r=p("base-button"),a=p("base-input"),n=p("SuccessFilled"),o=p("el-icon"),u=p("CircleCloseFilled"),l=p("Close"),i=p("el-drawer");return f(),d("div",J,[g("div",F,[g("div",G,[m(r,{color:"#2972C8",plain:"",icon:e.Plus,onClick:t[0]||(t[0]=function(e){return console.log(1),$.formItems=[{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"statu",label:"状态：",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"2"}]},{field:"description",label:"描述：",type:"input",placeholder:"访问策略描述"},{field:"user",label:"用户：",type:"dialog",treeType:"group",title:"选择用户",dataType:"user",groupCheck:"groupCheck",userCheck:"userCheck",placeholder:"请选择用户",rules:[{required:!0,message:"用户不能为空",trigger:"blur"}]},{field:"resource",label:"资源：",type:"dialog",treeType:"group",appGroupCheck:"appGroupCheck",appCheck:"appCheck",title:"选择资源",dataType:"resource",placeholder:"请选择资源",rules:[{required:!0,message:"资源不能为空",trigger:"blur"}]},{field:"time",label:"时间：",type:"timepicker",placeholder:"时间选择",dateType:"dateType"},{field:"enable_log",label:"日志：",type:"checkbox"}],$.formValues.statu="1",$.formValues.dateType="0",Q.value="新增访问策略",_e.value="新增访问策略",me.value="add",void(K.value=!0)})},{default:_((function(){return t[5]||(t[5]=[y("新增")])})),_:1,__:[5]},8,["icon"]),m(r,{color:"#2972C8",plain:"",icon:e.RefreshRight,onClick:ge},{default:_((function(){return t[6]||(t[6]=[y("刷新")])})),_:1,__:[6]},8,["icon"]),m(r,{color:"#2972C8",plain:"",icon:e.CircleCheck,onClick:t[1]||(t[1]=function(e){return Ce(1)})},{default:_((function(){return t[7]||(t[7]=[y("启用")])})),_:1,__:[7]},8,["icon"]),m(r,{color:"#2972C8",plain:"",icon:e.CircleClose,onClick:t[2]||(t[2]=function(e){return Ce(2)})},{default:_((function(){return t[8]||(t[8]=[y("禁用")])})),_:1,__:[8]},8,["icon"]),m(a,{modelValue:I.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return I.value=e}),class:"w-50 m-2 organize-search",placeholder:"Search","suffix-icon":e.Search,style:{width:"15%",float:"right"},onChange:ge},null,8,["modelValue","suffix-icon"])]),m(O,C({ref_key:"tableRef",ref:L,"table-data":de.value},Z),{user_in_strategy:_((function(e){return[g("div",j,[(f(!0),d(v,null,h(e.row.user_in_strategy,(function(e){return f(),d("span",{style:{color:"#252631"},key:e.user_id},b(e.user_name)+" ",1)})),128)),(f(!0),d(v,null,h(e.row.user_group_in_strategy,(function(e){return f(),d("span",{style:{color:"#252631"},key:e.user_group_id},b(e.user_group_name)+" ",1)})),128))])]})),app_in_strategy:_((function(e){return[g("div",P,[(f(!0),d(v,null,h(e.row.app_in_strategy,(function(e){return f(),d("span",{style:{color:"#252631"},key:e.app_id},b(e.app_name)+" ",1)})),128)),(f(!0),d(v,null,h(e.row.app_group_in_strategy,(function(e){return f(),d("span",{style:{color:"#252631"},key:e.app_group_id},b(e.app_group_name)+" ",1)})),128))])]})),start_time:_((function(e){return[g("div",D,[g("span",E,b(e.row.start_time)+" - "+b(e.row.end_time),1)])]})),strategy_status:_((function(e){return[g("div",A,[1===e.row.strategy_status?(f(),k(o,{key:0},{default:_((function(){return[m(n,{style:{color:"#52c41a"}})]})),_:1})):(f(),k(o,{key:1},{default:_((function(){return[m(u,{style:{color:"#BDBDC1"}})]})),_:1}))])]})),_:1},16,["table-data"]),m(i,{modelValue:K.value,"onUpdate:modelValue":t[4]||(t[4]=function(e){return K.value=e}),title:"drawerTitle",direction:"rtl","show-close":!1,size:"40%","before-close":H},{header:_((function(e){var t=e.close,a=e.titleId;return[g("div",z,[m(r,{link:"",onClick:t},{default:_((function(){return[m(o,{size:"20px"},{default:_((function(){return[m(l)]})),_:1})]})),_:2},1032,["onClick"])]),g("span",{id:a,class:"titleClass"},b(Q.value),9,B)]})),footer:_((function(){return[m(r,{color:"#256EBF",type:"primary",onClick:ye},{default:_((function(){return t[9]||(t[9]=[y("确定 ")])})),_:1,__:[9]}),m(r,{style:{"margin-left":"10px","margin-right":"90px"},onClick:H},{default:_((function(){return t[10]||(t[10]=[y("取消")])})),_:1,__:[10]})]})),default:_((function(){return[m(V,C({ref_key:"customForm",ref:R},$,{formOptions:q,cancel:H,submitForm:ye,getTreeData:fe,isFooter:!1}),null,16,["formOptions"])]})),_:1},8,["modelValue"])])])}}});t("default",u(L,[["__scopeId","data-v-40cb3c28"]]))}}}))}();
