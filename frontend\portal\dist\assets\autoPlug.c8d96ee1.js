/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{b as e}from"./stringFun.2b3a18f6.js";import{_ as l,B as a,h as o,o as u,d as n,e as s,j as t,w as d,f as p,F as r,i,g as m,k as c,M as b,P as V}from"./index.74d1ee23.js";import{k as f}from"./autoCode.a94c47f9.js";const y={class:"gva-table-box"},k=l({__name:"autoPlug",setup(l){const k=a({plugName:"",routerGroup:"",hasGlobal:!0,hasRequest:!0,hasResponse:!0,global:[{key:"",type:"",desc:""}],request:[{key:"",type:"",desc:""}],response:[{key:"",type:"",desc:""}]}),h=()=>{k.plugName=e(k.plugName)},g=async()=>{if(!k.plugName||!k.routerGroup)return void b.error("插件名称和插件路由组为必填项");if(k.hasGlobal){if(k.global.some((e=>{if(!e.key||!e.type)return!0})))return void b.error("全局属性的key和type为必填项")}if(k.hasRequest){if(k.request.some((e=>{if(!e.key||!e.type)return!0})))return void b.error("请求属性的key和type为必填项")}if(k.hasResponse){if(k.response.some((e=>{if(!e.key||!e.type)return!0})))return void b.error("响应属性的key和type为必填项")}0===(await f(k)).code&&V("创建成功，插件已自动写入后端plugin目录下，请按照自己的逻辑进行创造")},v=e=>{e.push({key:"",value:""})},U=(e,l)=>{1!==e.length?e.splice(l,1):b.warning("至少有一个全局属性")};return(e,l)=>{const a=o("base-input"),b=o("base-form-item"),V=o("base-checkbox"),f=o("base-option"),_=o("base-select"),R=o("base-button"),q=o("base-form");return u(),n("div",null,[s("div",y,[t(q,{"label-width":"140px",class:"plug-form"},{default:d((()=>[t(b,{label:"插件名"},{default:d((()=>[t(a,{modelValue:k.plugName,"onUpdate:modelValue":l[0]||(l[0]=e=>k.plugName=e),placeholder:"必填（英文大写字母开头）",onBlur:h},null,8,["modelValue"])])),_:1}),t(b,{label:"路由组"},{default:d((()=>[t(a,{modelValue:k.routerGroup,"onUpdate:modelValue":l[1]||(l[1]=e=>k.routerGroup=e),placeholder:"将会作为插件路由组使用"},null,8,["modelValue"])])),_:1}),t(b,{label:"使用全局属性"},{default:d((()=>[t(V,{modelValue:k.hasGlobal,"onUpdate:modelValue":l[2]||(l[2]=e=>k.hasGlobal=e)},null,8,["modelValue"])])),_:1}),k.hasGlobal?(u(),p(b,{key:0,label:"全局属性"},{default:d((()=>[(u(!0),n(r,null,i(k.global,((o,p)=>(u(),n("div",{key:p,class:"plug-row"},[s("span",null,[t(a,{modelValue:o.key,"onUpdate:modelValue":e=>o.key=e,placeholder:"key 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(_,{modelValue:o.type,"onUpdate:modelValue":e=>o.type=e,placeholder:"type 必填"},{default:d((()=>[t(f,{label:"string",value:"string"}),t(f,{label:"int",value:"int"}),t(f,{label:"float32",value:"float32"}),t(f,{label:"float64",value:"float64"}),t(f,{label:"bool",value:"bool"})])),_:2},1032,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(a,{modelValue:o.desc,"onUpdate:modelValue":e=>o.desc=e,placeholder:"备注 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(R,{icon:e.Plus,circle:"",onClick:l[3]||(l[3]=e=>v(k.global))},null,8,["icon"])]),s("span",null,[t(R,{icon:e.Minus,circle:"",onClick:e=>U(k.global,p)},null,8,["icon","onClick"])])])))),128))])),_:1})):m("",!0),t(b,{label:"使用Request"},{default:d((()=>[t(V,{modelValue:k.hasRequest,"onUpdate:modelValue":l[4]||(l[4]=e=>k.hasRequest=e)},null,8,["modelValue"])])),_:1}),k.hasRequest?(u(),p(b,{key:1,label:"Request"},{default:d((()=>[(u(!0),n(r,null,i(k.request,((o,p)=>(u(),n("div",{key:p,class:"plug-row"},[s("span",null,[t(a,{modelValue:o.key,"onUpdate:modelValue":e=>o.key=e,placeholder:"key 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(_,{modelValue:o.type,"onUpdate:modelValue":e=>o.type=e,placeholder:"type 必填"},{default:d((()=>[t(f,{label:"string",value:"string"}),t(f,{label:"int",value:"int"}),t(f,{label:"float32",value:"float32"}),t(f,{label:"float64",value:"float64"}),t(f,{label:"bool",value:"bool"})])),_:2},1032,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(a,{modelValue:o.desc,"onUpdate:modelValue":e=>o.desc=e,placeholder:"备注 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(R,{icon:e.Plus,circle:"",onClick:l[5]||(l[5]=e=>v(k.request))},null,8,["icon"])]),s("span",null,[t(R,{icon:e.Minus,circle:"",onClick:e=>U(k.request,p)},null,8,["icon","onClick"])])])))),128))])),_:1})):m("",!0),t(b,{label:"使用Response"},{default:d((()=>[t(V,{modelValue:k.hasResponse,"onUpdate:modelValue":l[6]||(l[6]=e=>k.hasResponse=e)},null,8,["modelValue"])])),_:1}),k.hasResponse?(u(),p(b,{key:2,label:"Response"},{default:d((()=>[(u(!0),n(r,null,i(k.response,((o,p)=>(u(),n("div",{key:p,class:"plug-row"},[s("span",null,[t(a,{modelValue:o.key,"onUpdate:modelValue":e=>o.key=e,placeholder:"key 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(_,{modelValue:o.type,"onUpdate:modelValue":e=>o.type=e,placeholder:"type 必填"},{default:d((()=>[t(f,{label:"string",value:"string"}),t(f,{label:"int",value:"int"}),t(f,{label:"float32",value:"float32"}),t(f,{label:"float64",value:"float64"}),t(f,{label:"bool",value:"bool"})])),_:2},1032,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(a,{modelValue:o.desc,"onUpdate:modelValue":e=>o.desc=e,placeholder:"备注 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[t(R,{icon:e.Plus,circle:"",onClick:l[7]||(l[7]=e=>v(k.response))},null,8,["icon"])]),s("span",null,[t(R,{icon:e.Minus,circle:"",onClick:e=>U(k.response,p)},null,8,["icon","onClick"])])])))),128))])),_:1})):m("",!0),t(b,null,{default:d((()=>[t(R,{type:"primary",onClick:g},{default:d((()=>l[8]||(l[8]=[c("创建")]))),_:1,__:[8]})])),_:1})])),_:1})])])}}},[["__scopeId","data-v-634ba231"]]);export{k as default};
