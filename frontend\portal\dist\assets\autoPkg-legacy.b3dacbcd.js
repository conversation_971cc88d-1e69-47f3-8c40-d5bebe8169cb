/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",u=o.toStringTag||"@@toStringTag";function l(e,o,a,u){var l=o&&o.prototype instanceof c?o:c,f=Object.create(l.prototype);return t(f,"_invoke",function(e,t,o){var a,u,l,c=0,f=o||[],s=!1,d={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(e,t){return a=e,u=0,l=n,d.n=t,i}};function p(e,t){for(u=e,l=t,r=0;!s&&c&&!o&&r<f.length;r++){var o,a=f[r],p=d.p,v=a[2];e>3?(o=v===t)&&(l=a[(u=a[4])?5:(u=3,3)],a[4]=a[5]=n):a[0]<=p&&((o=e<2&&p<a[1])?(u=0,d.v=t,d.n=a[1]):p<v&&(o=e<3||a[0]>t||t>v)&&(a[4]=e,a[5]=t,d.n=v,u=0))}if(o||e>1)return i;throw s=!0,t}return function(o,f,v){if(c>1)throw TypeError("Generator is already running");for(s&&1===f&&p(f,v),u=f,l=v;(r=u<2?n:l)||!s;){a||(u?u<3?(u>1&&(d.n=-1),p(u,l)):d.n=l:d.v=l);try{if(c=2,a){if(u||(o="next"),r=a[o]){if(!(r=r.call(a,l)))throw TypeError("iterator result is not an object");if(!r.done)return r;l=r.value,u<2&&(u=0)}else 1===u&&(r=a.return)&&r.call(a),u<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),u=1);a=n}else if((r=(s=d.n<0)?l:e.call(t,d))!==i)break}catch(r){a=n,u=1,l=r}finally{c=1}}return{value:r,done:s}}}(e,a,u),!0),f}var i={};function c(){}function f(){}function s(){}r=Object.getPrototypeOf;var d=[][a]?r(r([][a]())):(t(r={},a,(function(){return this})),r),p=s.prototype=c.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,t(e,u,"GeneratorFunction")),e.prototype=Object.create(p),e}return f.prototype=s,t(p,"constructor",s),t(s,"constructor",f),f.displayName="GeneratorFunction",t(s,u,"GeneratorFunction"),t(p),t(p,u,"Generator"),t(p,a,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:v}})()}function t(e,n,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,r,o){if(n)a?a(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var u=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};u("next",0),u("throw",1),u("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,a,u){try{var l=e[a](u),i=l.value}catch(e){return void n(e)}l.done?t(i):Promise.resolve(i).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var u=e.apply(t,r);function l(e){n(u,o,a,l,i,"next",e)}function i(e){n(u,o,a,l,i,"throw",e)}l(void 0)}))}}System.register(["./autoCode-legacy.8a6c710a.js","./warningBar-legacy.4145d360.js","./index-legacy.dbc04544.js"],(function(t,n){"use strict";var o,a,u,l,i,c,f,s,d,p,v,m,b,g,y,h=document.createElement("style");return h.textContent='@charset "UTF-8";.button-box[data-v-4d2dfb69]{padding:10px 20px}.button-box .el-button[data-v-4d2dfb69]{float:right}.warning[data-v-4d2dfb69]{color:#dc143c}\n',document.head.appendChild(h),{setters:[function(e){o=e.i,a=e.b,u=e.j},function(e){l=e.W},function(e){i=e._,c=e.r,f=e.h,s=e.o,d=e.d,p=e.j,v=e.e,m=e.w,b=e.k,g=e.M,y=e.P}],execute:function(){var n={class:"gva-table-box"},h={class:"gva-btn-list"},w={class:"dialog-footer"},_=Object.assign({name:"AutoPkg"},{setup:function(t){var i=c({packageName:"",label:"",desc:""}),_=c({packageName:[{required:!0,message:"请输入包名",trigger:"blur"},{validator:function(e,t,n){/^\d+$/.test(t[0])?n(new Error("不能够以数字开头")):n()},trigger:"blur"}]}),k=c(!1),j=function(){k.value=!1,i.value={packageName:"",label:"",desc:""}},x=c(null),V=function(){var t=r(e().m((function t(){return e().w((function(t){for(;;)switch(t.n){case 0:x.value.validate(function(){var t=r(e().m((function t(n){return e().w((function(e){for(;;)switch(e.n){case 0:if(!n){e.n=2;break}return e.n=1,o(i.value);case 1:0===e.v.code&&g({type:"success",message:"添加成功",showClose:!0}),T(),j();case 2:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}());case 1:return t.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),O=c([]),T=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,a();case 1:0===(n=e.v).code&&(O.value=n.data.pkgs);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),P=function(){var t=r(e().m((function t(n){return e().w((function(t){for(;;)switch(t.n){case 0:y.confirm("此操作仅删除数据库中的pkg存储，后端相应目录结构请自行删除与数据库保持一致！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(r(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,u(n);case 1:0===e.v.code&&(g({type:"success",message:"删除成功!"}),T());case 2:return e.a(2)}}),t)}))));case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}();return T(),function(e,t){var r=f("base-button"),o=f("el-table-column"),a=f("el-table"),u=f("base-input"),c=f("base-form-item"),g=f("base-form"),y=f("el-dialog");return s(),d("div",null,[p(l,{href:"https://www.bilibili.com/video/BV1kv4y1g7nT?p=3",title:"此功能为开发环境使用，不建议发布到生产，具体使用效果请看视频https://www.bilibili.com/video/BV1kv4y1g7nT?p=3"}),v("div",n,[v("div",h,[p(r,{size:"small",type:"primary",icon:"plus",onClick:t[0]||(t[0]=function(e){k.value=!0})},{default:m((function(){return t[5]||(t[5]=[b("新增")])})),_:1,__:[5]})]),p(a,{data:O.value},{default:m((function(){return[p(o,{align:"left",label:"id",width:"60",prop:"ID"}),p(o,{align:"left",label:"包名",width:"150",prop:"packageName"}),p(o,{align:"left",label:"展示名",width:"150",prop:"label"}),p(o,{align:"left",label:"描述","min-width":"150",prop:"desc"}),p(o,{align:"left",label:"操作",width:"200"},{default:m((function(e){return[p(r,{icon:"delete",size:"small",type:"primary",link:"",onClick:function(t){return P(e.row)}},{default:m((function(){return t[6]||(t[6]=[b("删除")])})),_:2,__:[6]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])]),p(y,{modelValue:k.value,"onUpdate:modelValue":t[4]||(t[4]=function(e){return k.value=e}),"before-close":j,title:"创建Package"},{footer:m((function(){return[v("div",w,[p(r,{size:"small",onClick:j},{default:m((function(){return t[7]||(t[7]=[b("取 消")])})),_:1,__:[7]}),p(r,{size:"small",type:"primary",onClick:V},{default:m((function(){return t[8]||(t[8]=[b("确 定")])})),_:1,__:[8]})])]})),default:m((function(){return[p(l,{title:"新增Pkg用于自动化代码使用"}),p(g,{ref_key:"pkgForm",ref:x,model:i.value,rules:_.value,"label-width":"80px"},{default:m((function(){return[p(c,{label:"包名",prop:"packageName"},{default:m((function(){return[p(u,{modelValue:i.value.packageName,"onUpdate:modelValue":t[1]||(t[1]=function(e){return i.value.packageName=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),p(c,{label:"展示名",prop:"label"},{default:m((function(){return[p(u,{modelValue:i.value.label,"onUpdate:modelValue":t[2]||(t[2]=function(e){return i.value.label=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),p(c,{label:"描述",prop:"desc"},{default:m((function(){return[p(u,{modelValue:i.value.desc,"onUpdate:modelValue":t[3]||(t[3]=function(e){return i.value.desc=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"])])}}});t("default",i(_,[["__scopeId","data-v-4d2dfb69"]]))}}}))}();
