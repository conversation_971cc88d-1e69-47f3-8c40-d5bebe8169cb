/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
!function(){function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,o,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function l(n,r,i,a){var l=r&&r.prototype instanceof u?r:u,c=Object.create(l.prototype);return e(c,"_invoke",function(n,e,r){var i,a,l,u=0,c=r||[],s=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(n,e){return i=n,a=0,l=t,f.n=e,d}};function p(n,e){for(a=n,l=e,o=0;!s&&u&&!r&&o<c.length;o++){var r,i=c[o],p=f.p,w=i[2];n>3?(r=w===e)&&(l=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=p&&((r=n<2&&p<i[1])?(a=0,f.v=e,f.n=i[1]):p<w&&(r=n<3||i[0]>e||e>w)&&(i[4]=n,i[5]=e,f.n=w,a=0))}if(r||n>1)return d;throw s=!0,e}return function(r,c,w){if(u>1)throw TypeError("Generator is already running");for(s&&1===c&&p(c,w),a=c,l=w;(o=a<2?t:l)||!s;){i||(a?a<3?(a>1&&(f.n=-1),p(a,l)):f.n=l:f.v=l);try{if(u=2,i){if(a||(r="next"),o=i[r]){if(!(o=o.call(i,l)))throw TypeError("iterator result is not an object");if(!o.done)return o;l=o.value,a<2&&(a=0)}else 1===a&&(o=i.return)&&o.call(i),a<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),a=1);i=t}else if((o=(s=f.n<0)?l:n.call(e,f))!==d)break}catch(o){i=t,a=1,l=o}finally{u=1}}return{value:o,done:s}}}(n,i,a),!0),c}var d={};function u(){}function c(){}function s(){}o=Object.getPrototypeOf;var f=[][i]?o(o([][i]())):(e(o={},i,(function(){return this})),o),p=s.prototype=u.prototype=Object.create(f);function w(n){return Object.setPrototypeOf?Object.setPrototypeOf(n,s):(n.__proto__=s,e(n,a,"GeneratorFunction")),n.prototype=Object.create(p),n}return c.prototype=s,e(p,"constructor",s),e(s,"constructor",c),c.displayName="GeneratorFunction",e(s,a,"GeneratorFunction"),e(p),e(p,a,"Generator"),e(p,i,(function(){return this})),e(p,"toString",(function(){return"[object Generator]"})),(n=function(){return{w:l,m:w}})()}function e(n,t,o,r){var i=Object.defineProperty;try{i({},"",{})}catch(n){i=0}e=function(n,t,o,r){if(t)i?i(n,t,{value:o,enumerable:!r,configurable:!r,writable:!r}):n[t]=o;else{var a=function(t,o){e(n,t,(function(n){return this._invoke(t,o,n)}))};a("next",0),a("throw",1),a("return",2)}},e(n,t,o,r)}function t(n,e,t,o,r,i,a){try{var l=n[i](a),d=l.value}catch(n){return void t(n)}l.done?e(d):Promise.resolve(d).then(o,r)}System.register(["./browser-legacy.d2f84391.js","./index-legacy.04f34b53.js"],(function(e,o){"use strict";var r,i,a,l,d,u,c,s,f,p,w,v,h,g,m,y,b=document.createElement("style");return b.textContent='@charset "UTF-8";.icon[data-v-2db15365]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.client[data-v-2db15365]{height:100vh;text-align:center;background:#FFFFFF;max-height:calc(100vh - 68px)}.client .el-main[data-v-2db15365]{height:100%}.client .el-main div div:hover .window-show[data-v-2db15365]{display:none}.client .el-main div div:hover .window-hidden[data-v-2db15365]{display:block!important}.client .el-main div div:hover .window-hidden span[data-v-2db15365]{margin-top:42px!important}\n',document.head.appendChild(b),{setters:[function(n){r=n.g,i=n.b},function(n){a=n._,l=n.r,d=n.h,u=n.K,c=n.o,s=n.d,f=n.e,p=n.j,w=n.w,v=n.k,h=n.f,g=n.g,m=n.N,y=n.M}],execute:function(){var o={class:"client"},b={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}},x={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},_={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px","margin-left":"39%",display:"none"}},k={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},F={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-left":"39%","margin-top":"60px",display:"none"}},j={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},O={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},S={__name:"download",setup:function(e){var a=l(""),S=l(""),E=l(!1),C=l(!1),L=l(!1),M=l(!1),R=l({windows:0,darwin:0}),T=function(n){return 100===n?"完成":"".concat(n,"%")},U=function(n,e){return new Promise((function(t,o){var r=new XMLHttpRequest;r.open("GET",n,!0),r.responseType="blob",r.onprogress=function(n){if(n.lengthComputable){var t=n.loaded/n.total*100;R.value[e]=Math.round(t)}},r.onload=function(){200===r.status?t(r.response):o(new Error("下载失败"))},r.onerror=function(){o(new Error("网络错误"))},r.send()}))},z=function(n,e){if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(n,e);else{var t=document.createElement("a"),o=document.querySelector("body");t.href=window.URL.createObjectURL(n),t.download=e,t.style.display="none",o.appendChild(t),t.click(),o.removeChild(t),window.URL.revokeObjectURL(t.href)}G()},G=function(){E.value=!1,C.value=!1,L.value=!1,M.value=!1,Object.keys(R.value).forEach((function(n){R.value[n]=0}))},I=l(!1),P=function(){var e,o=(e=n().m((function e(t){var o,l,d,u,c,s,f,p,w,v,h,g,m,b,x,_,k,F,j;return n().w((function(n){for(;;)switch(n.n){case 0:if("android"!==t&&"ios"!==t||!I.value){n.n=1;break}return n.a(2);case 1:return I.value=!0,(o={windows:E,darwin:C,ios:L,android:M}[t]).value=!0,n.p=2,n.n=3,r({platform:t});case 3:if(0!==(l=n.v).data.code){n.n=10;break}if("ios"!==t){n.n=5;break}return n.n=4,i.toDataURL(l.data.data.download_url);case 4:d=n.v,u=document.getElementById("ioscanvas"),S.value=d,u&&(c=u.getContext("2d"),(s=new Image).onload=function(){u.width=s.width,u.height=s.height,c.drawImage(s,0,0)},s.src=d),n.n=9;break;case 5:if("android"!==t){n.n=7;break}return f=window.location.port,p=new URL(l.data.data.download_url),f?p.toString().includes("asec-deploy")?w=l.data.data.download_url:(p.port=f,w=p.toString()):(p.port="",w=p.toString()),n.n=6,i.toDataURL(w);case 6:v=n.v,h=document.getElementById("canvas"),a.value=v,h&&(g=h.getContext("2d"),(m=new Image).onload=function(){h.width=m.width,h.height=m.height,g.drawImage(m,0,0)},m.src=v),n.n=9;break;case 7:return b=window.location.port,x=new URL(l.data.data.download_url),b?(x.toString().includes("asec-deploy")?_=l.data.data.download_url:(x.port=b,_=x.toString()),k=l.data.data.latest_filename.replace(/@(\d+)/,"@".concat(b))):(x.port="",_=x.toString(),k=l.data.data.latest_filename),n.n=8,U(_,t);case 8:F=n.v,z(F,k);case 9:n.n=11;break;case 10:throw new Error(l.data.msg);case 11:n.n=13;break;case 12:n.p=12,j=n.v,y({type:"error",message:j.message||"下载失败，请联系管理员"});case 13:return n.p=13,o.value=!1,n.f(13);case 14:return n.a(2)}}),e,null,[[2,12,13,14]])})),function(){var n=this,o=arguments;return new Promise((function(r,i){var a=e.apply(n,o);function l(n){t(a,r,i,l,d,"next",n)}function d(n){t(a,r,i,l,d,"throw",n)}l(void 0)}))});return function(n){return o.apply(this,arguments)}}();return function(n,e){var t=d("el-link"),r=d("el-progress"),i=d("base-main"),a=u("loading");return c(),s("div",null,[f("div",o,[p(i,null,{default:w((function(){return[f("div",b,[f("div",{style:{float:"left","margin-right":"5%",width:"209px",height:"209px",background:"#F1F8FF"},onClick:e[0]||(e[0]=function(n){return P("windows")})},[(c(),s("svg",x,e[6]||(e[6]=[f("use",{"xlink:href":"#icon-windows"},null,-1)]))),(c(),s("svg",_,e[7]||(e[7]=[f("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[10]||(e[10]=f("br",null,null,-1)),p(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:w((function(){return e[8]||(e[8]=[v("Windows客户端")])})),_:1,__:[8]}),p(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:w((function(){return e[9]||(e[9]=[v("点击下载Windows客户端")])})),_:1,__:[9]}),E.value?(c(),h(r,{key:0,percentage:R.value.windows,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):g("",!0)]),f("div",{style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onClick:e[1]||(e[1]=function(n){return P("darwin")})},[(c(),s("svg",k,e[11]||(e[11]=[f("use",{"xlink:href":"#icon-mac"},null,-1)]))),(c(),s("svg",F,e[12]||(e[12]=[f("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[15]||(e[15]=f("br",null,null,-1)),p(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:w((function(){return e[13]||(e[13]=[v("Mac客户端")])})),_:1,__:[13]}),p(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:w((function(){return e[14]||(e[14]=[v("点击下载Mac客户端")])})),_:1,__:[14]}),C.value?(c(),h(r,{key:0,percentage:R.value.darwin,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):g("",!0)]),m((c(),s("div",{"element-loading-text":"下载码生成中...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onMousemove:e[2]||(e[2]=function(n){return P("ios")}),onMouseleave:e[3]||(e[3]=function(n){return I.value=!1})},[(c(),s("svg",j,e[16]||(e[16]=[f("use",{"xlink:href":"#icon-ios"},null,-1)]))),e[18]||(e[18]=f("br",null,null,-1)),p(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:w((function(){return e[17]||(e[17]=[v("iOS客户端")])})),_:1,__:[17]}),e[19]||(e[19]=f("div",{id:"ios",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[f("canvas",{id:"ioscanvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[a,L.value]]),m((c(),s("div",{"element-loading-text":"下载码生成中...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF"},onMousemove:e[4]||(e[4]=function(n){return P("android")}),onMouseleave:e[5]||(e[5]=function(n){return I.value=!1})},[(c(),s("svg",O,e[20]||(e[20]=[f("use",{"xlink:href":"#icon-android"},null,-1)]))),e[22]||(e[22]=f("br",null,null,-1)),p(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:w((function(){return e[21]||(e[21]=[v("Android客户端")])})),_:1,__:[21]}),e[23]||(e[23]=f("div",{id:"android",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[f("canvas",{id:"canvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[a,M.value]])])]})),_:1})])])}}};e("default",a(S,[["__scopeId","data-v-2db15365"]]))}}}))}();
