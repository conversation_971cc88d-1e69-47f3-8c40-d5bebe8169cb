/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js","./iconfont-legacy.37c53566.js"],(function(e,n){"use strict";var t,l,o,i,a,c,s,r,u,d=document.createElement("style");return d.textContent='@charset "UTF-8";.policy-tree div{font-size:12px}.custom-tree-type{font-size:6px;margin-left:10px;background:#0d84ff;color:#fff}.icon{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden;font-size:16px}\n',document.head.appendChild(d),{setters:[function(e){t=e.l,l=e.h,o=e.o,i=e.f,a=e.w,c=e.e,s=e.t,r=e.d,u=e.g},function(){}],execute:function(){var n={class:"custom-tree-node"},d=["onClick"],p={key:0},f={key:1},m={key:2};e("default",Object.assign({name:"PolicyTree"},{emits:["append"],setup:function(e,g){g.emit;var h={label:"name",children:"zones",isLeaf:"leaf",isRoot:"root"},y=0,v=t("checkedGroupId"),x=null,k=null,C=function(e,n){return 0===e.level?n([{id:1,name:"杭州分公司"},{id:2,name:"深圳分公司"},{id:3,name:"长沙总部"},{id:4,name:"本地用户"}]):n([])};return function(e,t){var g=l("el-tree");return o(),i(g,{icon:e.CirclePlusFilled,style:{"font-size":"12px",height:"95%",overflow:"auto","max-height":"calc(100vh - 235px)"},props:h,load:C,lazy:"","expand-on-click-node":!1,"node-key":"id",class:"policy-tree"},{default:a((function(e){var l=e.node,i=e.data;return[c("span",n,[c("span",{onClick:function(e){return function(e){if(console.log("handleNodeClick"),console.log(v.value),console.log(e),y++,x&&y>=2&&(k=e.id,y=0,k===x))return alert("双击"),console.log("处理双击逻辑"),k=null,void(x=null);x=e.id,v.value=e.id,setTimeout((function(){x=null,y=0}),300)}(i)}},[t[3]||(t[3]=c("span",{style:{"margin-right":"3px"}},[c("svg",{class:"icon svg-icon","aria-hidden":"true"},[c("use",{"xlink:href":"#el-icon-wenjianjia"})])],-1)),c("span",null,s(l.label),1),1===i.id?(o(),r("span",p,t[0]||(t[0]=[c("span",{class:"custom-tree-type"},"ldap",-1)]))):u("",!0),2===i.id?(o(),r("span",f,t[1]||(t[1]=[c("span",{class:"custom-tree-type"},"企业微信",-1)]))):u("",!0),i.id>2?(o(),r("span",m,t[2]||(t[2]=[c("span",{class:"custom-tree-type"},"local",-1)]))):u("",!0)],8,d)])]})),_:1},8,["icon"])}}}))}}}));
