/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{B as e,_ as a,b as o,r as t,p as l,h as s,o as r,d as i,j as n,w as u,e as d,k as c,O as p,t as m,m as f,f as g,g as b,M as y,P as v}from"./index.74d1ee23.js";import"./iconfont.2d75af05.js";import{J as h}from"./index-browser-esm.c2d3b5c9.js";import{d as V}from"./dayjs.min.a0c675c8.js";import{D as w}from"./directoryTree.447276d1.js";import{_ as x}from"./customTable.09ee0d92.js";import{_ as I}from"./customFrom.6bcc3d4d.js";const q=(e,a,o)=>(console.log("groupFormItemOptions"),console.log(e.value),console.log(a.value),[{field:"source",label:"用户来源：",type:"select",placeholder:"请选择",options:e,optionsLabe:"name",optionsValue:"type",optionsKey:"type",rules:[{required:!0,message:"用户来源不能为空",trigger:"blur"}]},{field:"group",label:"父级组织：",type:"treeSelect",placeholder:"请选择",options:a,optionsLabe:"name",optionsValue:"type",optionsKey:"type",isHidden:o,rules:[{required:!0,message:"父级组织不能为空",trigger:"blur"}]},{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"description",label:"描述：",type:"input",placeholder:"组织描述"},{field:"uniqueIdentification",label:"唯一标识：",type:"input",placeholder:"请输入唯一标识",rules:[{required:!0,message:"唯一标识不能为空",trigger:"blur"}]}]),z=(e,a)=>(console.log("userFormItemOptions"),console.log(e.value),console.log(a.value),[{field:"name",label:"用户名：",type:"input",placeholder:"请输入用户名",rules:[{required:!0,message:"用户名不能为空",trigger:"blur"}]},{field:"statu",label:"状态：",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"0"}]},{field:"description",label:"描述：",type:"input",placeholder:"组织描述"},{field:"password",label:"密码：",type:"password",placeholder:"请输入密码",rules:[{required:!0,message:"密码不能为空",trigger:"blur"}]},{field:"confirmPassword",label:"确认密码：",type:"password",placeholder:"请输入密码",rules:[{required:!0,message:"密码不能为空",trigger:"blur"}]},{field:"source",label:"所属组织：",type:"select",placeholder:"请选择",options:e,optionsLabe:"name",optionsValue:"name",optionsKey:"id",rules:[{required:!0,message:"所属组织不能为空",trigger:"blur"}]},{field:"roles",label:"关联角色：",type:"select",placeholder:"请选择",options:a,optionsLabe:"name",optionsValue:"name",optionsKey:"id",rules:[{required:!0,message:"角色不能为空",trigger:"blur"}]},{field:"uniqueIdentification",label:"身份标识：",type:"input",placeholder:"请输入唯一标识",rules:[{required:!0,message:"身份标识不能为空",trigger:"blur"}]},{field:"email",label:"电子邮箱：",type:"input",placeholder:"请输入邮箱"},{field:"phone",label:"手机号码：",type:"input",placeholder:"请输入手机号码"},{field:"expiration",label:"过期时间：",type:"datepicker",placeholder:"请输入手机号码",dateType:"dateType"}]);e({source:"select"});const O={class:"common-layout organize"},_={style:{height:"35px"}},U={class:"header"},T={style:{"text-align":"center"}},k={style:{"font-size":"12px"}},D={style:{"text-align":"center"}},G={style:{"font-size":"12px"}},j={style:{"text-align":"center"}},R={style:{"font-size":"12px"}},Y={style:{"text-align":"center"}},$=a(Object.assign({name:"Organize"},{setup(a){const $=o(),C=t(""),L=t([]),N=t(0),S=t(1),F=t(50),M=t(!1),B=t(""),H=t(1),P=t("新增用户目录"),K=t("root"),A=t("add"),E=t(!1),X={label:"name",children:"zones",isLeaf:"leaf",isRoot:"root"},J=e({id:"",source:"",name:"",description:"",uniqueIdentification:"",statu:"1",expirationRadio:"0",expiration:""});let Q={formItems:[],formValues:e({})};const W=t([]),Z=async()=>{const e=await $.GetOrganize("");e.data&&(W.value=e.data)},ee=t([]),ae=async()=>{console.log("getTableData"),console.log(B.value);const e={briefRepresentation:!1,first:(S.value-1)*F.value,max:F.value,search:C.value};let a={data:0},o={};B.value?(o=await ce(),a.data=o.data.length):(a=await $.GetUserListCount(e),o=await $.GetUserList(e)),o.data&&(L.value=o.data,N.value=a.data)},oe=t([]);(async()=>{await Z(),await ae(),await(async()=>{const e=await $.GetRoles("");e.data&&(oe.value=e.data)})(),await(async()=>{const e=await $.GetUserOrigin();200===e.status&&0===e.data.code&&(ee.value=e.data.data)})()})();const te={propList:[{prop:"username",label:"名称",slotName:"username"},{prop:"uniqueIdentification",label:"工号",slotName:"uniqueIdentification"},{prop:"organize",label:"所属组织",slotName:"organize"},{prop:"phone",label:"手机号码",slotName:"phone"},{prop:"expiration",label:"过期时间",slotName:"expiration"},{prop:"enabled",label:"状态",slotName:"enabled"}],isSelectColumn:!1,isOperationColumn:!0},le=e({source:"select"});l("cascaderKey",H),l("treeRef",B),l("operation",A),l("title",P),l("type",K),l("dialogVisible",E),l("formLabelAlign",J),l("currentPage",S),l("pageSize",F),l("total",N),l("getTableData",ae);const se=()=>{re(),E.value=!1},re=()=>{const e=Object.keys(Q.formValues);let a={};e.forEach((e=>{a[e]=""})),Object.assign(Q.formValues,a)},ie=t(),ne=async e=>{console.log("organize"),console.log(Q.formValues),await e.validate((async(e,a)=>{if(!e)return y({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),"";console.log(J.expiration),console.log(V(J.expiration).format("YYYY-MM-DD HH:mm:ss"));let o="",t="";switch("addUser"===A.value||"updateUser"===A.value?t={id:Q.formValues.id,username:Q.formValues.name,enabled:Number(Q.formValues.statu)?1:0,totp:!1,emailVerified:!1,email:Q.formValues.email,groups:[Q.formValues.source],realmRoles:[Q.formValues.roles],attributes:{defaultRouter:"dashboard",nickName:Q.formValues.name,uniqueIdentification:[Q.formValues.uniqueIdentification],description:[Q.formValues.description||""],phone:[Q.formValues.phone],expiration:[Number(Q.formValues.dateType)?V(Q.formValues.expiration).format("YYYY-MM-DD HH:mm:ss"):"永久"]},credentials:[{type:"password",temporary:!1,value:Q.formValues.password}]}:o={id:Q.formValues.id,name:Q.formValues.name,attributes:{uniqueIdentification:[Q.formValues.uniqueIdentification],description:[Q.formValues.description||""],source:[Q.formValues.source]}},A.value){case"add":await $.CreateOrganize(o);break;case"update":await $.UpdateOrganize(o);break;case"addSub":await $.AddSubgroup(o);break;case"addUser":await $.CreateUser(t);break;case"updateUser":await $.UpdateUser(t)}E.value=!1,await ae(),await Z(),re(),++H.value,console.log(e),console.log(a)}))};l("open",(e=>{v.confirm("删除目录以后将无法恢复，确认删除目录？","删除用户目录",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{console.log(e);const a=await $.DelOrganize(e);console.log(a),204===a.status?(y({type:"success",message:"删除成功"}),++H.value,await Z()):(y({type:"error",message:"删除失败"}),ue(e)),re()})).catch((()=>{re(),y({type:"info",message:"取消删除"})}))}));const ue=e=>{const a=7===e?"删除当前用户目录需先手动清除当前用户目录所有数据再重试，包括角色、用户":"当前用户目录已被认证策略引用（XXX,YYY）关联，请先前往该认证策略修改后再删除";v.alert(a,"删除用户目录",{confirmButtonText:"确认"})},de=e=>{console.log("setFormValues"),console.log(e),Q.formValues.id=e.data.id,Q.formValues.name=e.data.username||e.data.name,Q.formValues.source=e.data.group||h("$..attributes.source[0]",e.data)[0],Q.formValues.description=h("$..description[0]",e.data)[0],Q.formValues.uniqueIdentification=h("$..uniqueIdentification[0]",e.data)[0],Q.formValues.password="123456",Q.formValues.confirmPassword="123456",Q.formValues.group=e.groupId,Q.formValues.groupId=e.groupId,Q.formValues.email=e.data.email,Q.formValues.phone=h("$..phone[0]",e.data)[0],Q.formValues.statu=e.enabled?"0":"1",h("$..expiration[0]",e.data)[0]&&"永久"===h("$..expiration[0]",e.data)[0]?Q.formValues.dateType="0":(Q.formValues.dateType="1",Q.formValues.expiration=h("$..expiration[0]",e.data)[0])};l("handleEdit",(async(e,a)=>{console.log("handleEdit"),console.log(a),P.value="编辑用户",K.value="user",M.value=!0,A.value="updateUser";const o=await $.GetUserInfo(a.id);console.log(o),Q.formItems=z(W,oe),de(o),E.value=!0})),l("handleDelete",((e,a)=>{console.log("handleDelete"),console.log(a),v.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个用户吗？</strong><br><strong>删除后用户将无法登录和访问应用，请谨慎操作。</strong>',"删除用户",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=await $.DeleteUser(a.id);console.log(e),204===e.status?(y({type:"success",message:"删除成功"}),await ae()):y({type:"error",message:"删除失败"}),re()})).catch((()=>{re(),y({type:"info",message:"取消删除"})}))}));const ce=async()=>{const e=B.value,a={briefRepresentation:!1,first:(S.value-1)*F.value,max:F.value,search:C.value};return await $.GetGroupMembers(e,a)},pe=async e=>{console.log("append"),console.log(e),A.value="addSub",P.value="新增子目录",K.value="sub";const a=await $.GetOrganizeDetails(e.id);console.log("organize"),console.log(a),Q.formValues.id=e.id,Q.formValues.source=h("$...source[0]",a.data)[0],Q.formValues.group=a.data.name,Q.formItems=q(ee,W,!0),E.value=!0},me=async(e,a)=>{console.log("edit"),console.log(a),console.log(e);const o=await $.GetOrganizeDetails(a.id);P.value="修改子目录",K.value="root",A.value="update";let t=!0;e.level>1&&(o.groupId=e.parent.data.id,t=!0),de(o),Q.formItems=q(ee,W,t),E.value=!0};let fe="";const ge=async(e,a)=>0===e.level?(fe=await $.GetOrganize(""),a(fe.data)):e.data.subGroups.length>0?a(e.data.subGroups):a([]);return(e,a)=>{const o=s("base-button"),t=s("base-aside"),l=s("base-input"),y=s("SuccessFilled"),v=s("el-icon"),V=s("Remove"),$=s("base-main"),N=s("base-container"),S=s("el-dialog");return r(),i("div",O,[n(N,{style:{height:"100%"}},{default:u((()=>[n(t,{width:"200px",style:{"min-height":"calc(100vh - 200px)"}},{default:u((()=>[d("div",_,[a[4]||(a[4]=d("span",{class:"menu-label"},"用户目录",-1)),n(o,{class:"organize-but",icon:e.FolderAdd,onClick:a[0]||(a[0]=e=>(console.log("addOrganize"),P.value="新增目录",K.value="root",A.value="add",Q.formItems=q(ee,W,!0),void(E.value=!0)))},null,8,["icon"])]),n(w,{loadOperate:true,edit:me,append:pe,loadNode:ge,treeProps:X})])),_:1}),n($,null,{default:u((()=>[d("div",U,[n(o,{icon:e.Plus,onClick:a[1]||(a[1]=e=>{return a="user",console.log("add"),P.value="新增用户",K.value=a,A.value="addUser",M.value=!1,Q.formItems=z(W,oe),console.log(Q.formItems),Q.formValues.statu="1",Q.formValues.dateType="0",void(E.value=!0);var a})},{default:u((()=>a[5]||(a[5]=[c("新增")]))),_:1,__:[5]},8,["icon"]),n(o,{icon:e.RefreshRight,onClick:ae},{default:u((()=>a[6]||(a[6]=[c("刷新")]))),_:1,__:[6]},8,["icon"]),n(l,{modelValue:C.value,"onUpdate:modelValue":a[2]||(a[2]=e=>C.value=e),class:"w-50 m-2 organize-search",placeholder:"(名称)","suffix-icon":e.Search,onChange:ae},null,8,["modelValue","suffix-icon"])]),n(x,p({"table-data":L.value},te),{uniqueIdentification:u((e=>[d("div",T,[d("span",k,m(f(h)("$...uniqueIdentification[0]",e.row)[0]),1)])])),phone:u((e=>[d("div",D,[d("span",G,m(f(h)("$...phone[0]",e.row)[0]),1)])])),expiration:u((e=>[d("div",j,[d("span",R,m(f(h)("$..expiration[0]",e.row)[0]),1)])])),enabled:u((e=>[d("div",Y,[e.row.enabled?(r(),g(v,{key:0,style:{color:"#52c41a"}},{default:u((()=>[n(y)])),_:1})):(r(),g(v,{key:1},{default:u((()=>[n(V)])),_:1}))])])),_:1},16,["table-data"])])),_:1})])),_:1}),E.value?(r(),g(S,{key:0,modelValue:E.value,"onUpdate:modelValue":a[3]||(a[3]=e=>E.value=e),title:P.value,width:"30%","custom-class":"custom-dialog"},{default:u((()=>[n(I,p({ref_key:"customForm",ref:ie},f(Q),{formOptions:le,cancel:se,submitForm:ne}),null,16,["formOptions"])])),_:1},8,["modelValue","title"])):b("",!0)])}}}),[["__scopeId","data-v-808ab330"]]);export{$ as default};
