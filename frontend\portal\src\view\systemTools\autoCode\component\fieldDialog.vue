<template>
  <div>
    <warning-bar title="id , created_at , updated_at , deleted_at 会自动生成请勿重复创建。搜索时如果条件为LIKE只支持字符串" />
    <base-form
      ref="fieldDialogFrom"
      :model="middleDate"
      label-width="120px"
      label-position="right"
      :rules="rules"
      class="grid-form"
    >
      <base-form-item label="Field名称" prop="fieldName">
        <base-input v-model="middleDate.fieldName" autocomplete="off" style="width:80%" />
        <base-button size="small" style="width:18%;margin-left:2%" @click="autoFill">
          <span style="font-size: 12px">自动填充</span>
        </base-button>
      </base-form-item>
      <base-form-item label="Field中文名" prop="fieldDesc">
        <base-input v-model="middleDate.fieldDesc" autocomplete="off" />
      </base-form-item>
      <base-form-item label="FieldJSON" prop="fieldJson">
        <base-input v-model="middleDate.fieldJson" autocomplete="off" />
      </base-form-item>
      <base-form-item label="数据库字段名" prop="columnName">
        <base-input v-model="middleDate.columnName" autocomplete="off" />
      </base-form-item>
      <base-form-item label="数据库字段描述" prop="comment">
        <base-input v-model="middleDate.comment" autocomplete="off" />
      </base-form-item>
      <base-form-item label="Field数据类型" prop="fieldType">
        <base-select
          v-model="middleDate.fieldType"
          style="width:100%"
          placeholder="请选择field数据类型"
          clearable
          @change="clearOther"
        >
          <base-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </base-select>
      </base-form-item>
      <base-form-item :label="middleDate.fieldType === 'enum' ? '枚举值' : '类型长度'" prop="dataTypeLong">
        <base-input v-model="middleDate.dataTypeLong" :placeholder="middleDate.fieldType === 'enum'?`例:'北京','天津'`:'数据库类型长度'" />
      </base-form-item>
      <base-form-item label="Field查询条件" prop="fieldSearchType">
        <base-select
          v-model="middleDate.fieldSearchType"
          style="width:100%"
          placeholder="请选择Field查询条件"
          clearable
        >
          <base-option
            v-for="item in typeSearchOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            :disabled="
              (middleDate.fieldType!=='string'&&item.value==='LIKE')||
                ((middleDate.fieldType!=='int'&&middleDate.fieldType!=='time.Time'&&middleDate.fieldType!=='float64')&&(item.value==='BETWEEN' || item.value==='NOT BETWEEN'))
            "
          />
        </base-select>
      </base-form-item>
      <base-form-item label="关联字典" prop="dictType">
        <base-select
          v-model="middleDate.dictType"
          style="width:100%"
          :disabled="middleDate.fieldType!=='int'"
          placeholder="请选择字典"
          clearable
        >
          <base-option
            v-for="item in dictOptions"
            :key="item.type"
            :label="`${item.type}(${item.name})`"
            :value="item.type"
          />
        </base-select>
      </base-form-item>
      <base-form-item label="是否必填">
        <el-switch v-model="middleDate.require" />
      </base-form-item>
      <base-form-item label="是否可清空">
        <el-switch v-model="middleDate.clearable" />
      </base-form-item>
      <base-form-item label="校验失败文案">
        <base-input v-model="middleDate.errorText" />
      </base-form-item>

    </base-form>
  </div>
</template>

<script setup>
import { toLowerCase, toSQLLine } from '@/utils/stringFun'
import { getSysDictionaryList } from '@/api/sysDictionary'
import WarningBar from '@/components/warningBar/warningBar.vue'
import { ref } from 'vue'

const props = defineProps({
  dialogMiddle: {
    type: Object,
    default: function() {
      return {}
    }
  }
})

const middleDate = ref({})
const dictOptions = ref([])
const typeSearchOptions = ref([
  {
    label: '=',
    value: '='
  },
  {
    label: '<>',
    value: '<>'
  },
  {
    label: '>',
    value: '>'
  },
  {
    label: '<',
    value: '<'
  },
  {
    label: 'LIKE',
    value: 'LIKE'
  },
  {
    label: 'BETWEEN',
    value: 'BETWEEN'
  },
  {
    label: 'NOT BETWEEN',
    value: 'NOT BETWEEN'
  }
])
const typeOptions = ref([
  {
    label: '字符串',
    value: 'string'
  },
  {
    label: '整型',
    value: 'int'
  },
  {
    label: '布尔值',
    value: 'bool'
  },
  {
    label: '浮点型',
    value: 'float64'
  },
  {
    label: '时间',
    value: 'time.Time'
  },
  {
    label: '枚举',
    value: 'enum'
  }
])
const rules = ref({
  fieldName: [
    { required: true, message: '请输入field英文名', trigger: 'blur' }
  ],
  fieldDesc: [
    { required: true, message: '请输入field中文名', trigger: 'blur' }
  ],
  fieldJson: [
    { required: true, message: '请输入field格式化json', trigger: 'blur' }
  ],
  columnName: [
    { required: true, message: '请输入数据库字段', trigger: 'blur' }
  ],
  fieldType: [
    { required: true, message: '请选择field数据类型', trigger: 'blur' }
  ]
})

const init = async() => {
  middleDate.value = props.dialogMiddle
  const dictRes = await getSysDictionaryList({
    page: 1,
    pageSize: 999999
  })

  dictOptions.value = dictRes.data.list
}
init()

const autoFill = () => {
  middleDate.value.fieldJson = toLowerCase(middleDate.value.fieldName)
  middleDate.value.columnName = toSQLLine(middleDate.value.fieldJson)
}

const clearOther = () => {
  middleDate.value.fieldSearchType = ''
  middleDate.value.dictType = ''
}

const fieldDialogFrom = ref(null)
defineExpose({ fieldDialogFrom })
</script>

<script>

export default {
  name: 'FieldDialog'
}
</script>
<style scoped>
.grid-form{
  display: grid;
  grid-template-columns: 1fr 1fr;
}
.click-text{
  color: #0d84ff;
  font-size: 13px;
  cursor: pointer;
  user-select: none;
}
</style>
