/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{x as o}from"./index.74d1ee23.js";const a=a=>o({url:"/console/v1/application/group",method:"get",params:{corp_id:a}}),t=a=>o({url:"/console/v1/application/group",method:"post",data:a}),p=a=>o({url:"/console/v1/application/group",method:"put",data:a}),e=a=>o({url:"/console/v1/application/group",method:"delete",params:a}),l=a=>o({url:"/console/v1/application",method:"post",data:a}),s=a=>o({url:"/console/v1/application",method:"put",data:a}),i=a=>o({url:"/console/v1/application",method:"delete",params:a}),c=a=>o({url:"/console/v1/application/list",method:"post",data:a}),r=a=>o({url:"/console/v1/application",method:"get",params:a}),n=()=>o({url:"/console/v1/application/getuserapp",method:"get"});export{a,c as b,r as c,l as d,p as e,t as f,n as g,e as h,i,s as u};
