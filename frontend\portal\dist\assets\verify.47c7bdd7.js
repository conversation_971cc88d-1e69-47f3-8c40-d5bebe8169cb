/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{u as t,q as e,a as o,o as n}from"./index.bfaf04e1.js";const r=Object.assign({name:"Verify"},{setup(r){const a=location.href.split("?")[1],c=new URLSearchParams(a),i=Object.fromEntries(c.entries()),s=t(),l=document.location.protocol+"//"+document.location.host,u=new URLSearchParams;"client"===i.type&&(u.set("type","client"),i.wp&&u.set("wp",i.wp));const p={method:"GET",url:`${l}/auth/user/v1/redirect_verify?redirect_url=${i.redirect_url}`,headers:{Accept:"application/json, text/plain, */*",Authorization:`${s.token.tokenType} ${s.token.accessToken}`}};return e.request(p).then((function(t){if(200===t.status){let e=t.data.url;if(u.toString()){const t=e.includes("?")?"&":"?";e+=t+u.toString()}window.location.href=e}})).catch((function(t){console.error(t)})),(t,e)=>(n(),o("div"))}});export{r as default};
