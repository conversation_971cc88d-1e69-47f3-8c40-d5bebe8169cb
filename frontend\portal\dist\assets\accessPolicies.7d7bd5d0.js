/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{x as e,_ as a,b as t,r as l,B as r,p as s,h as o,o as u,d as i,e as p,j as n,w as c,k as g,F as d,i as _,t as m,f as y,O as f,M as v,P as h}from"./index.74d1ee23.js";import{_ as b}from"./customTable.09ee0d92.js";import{_ as C}from"./customFrom.6bcc3d4d.js";import{a as k,b as w}from"./resource.29b70792.js";const S={class:"policies"},V={style:{"background-color":"#FFFFFF",padding:"22px 20px 60px 20px"}},O={class:"header"},N={style:{"text-align":"center"}},T={style:{"text-align":"center"}},J={style:{"text-align":"center"}},x={style:{color:"#252631"}},F={style:{"text-align":"center"}},G={style:{width:"15px","max-width":"20px",float:"left"}},D=["id"],L=a(Object.assign({name:"AccessPolicies"},{setup(a){const L=t(),j=l(),z=l(""),P=l(),R=r({}),q=l([]),B=()=>{const e=Object.keys(U.formValues);let a={};e.forEach((e=>{a[e]=""})),Object.assign(U.formValues,a)},I=()=>{q.value.length=0,pe.value.length=0,ne.value.length=0,ce.value.length=0,ge.value.length=0,B(),A.value=!1},U={formItems:[],formValues:r({})},A=l(!1),E=l(""),M=l(0),H=l(1),K=l(50),Q={propList:[{prop:"strategy_name",label:"名称",slotName:"strategy_name"},{prop:"strategy_detail",label:"描述",slotName:"strategy_detail"},{prop:"user_in_strategy",label:"用户",slotName:"user_in_strategy"},{prop:"app_in_strategy",label:"资源",slotName:"app_in_strategy"},{prop:"start_time",label:"生效时间",slotName:"start_time"},{prop:"strategy_status",label:"状态",slotName:"strategy_status"}],isSelectColumn:!0,isOperationColumn:!0};const W=l([{text:"组织",value:"group"},{text:"用户",value:"user"}]),X=l([]),Y=l(""),Z=l(0),$=l(1),ee=l(15),ae=l(),te=l(),le=async()=>{console.log("getTreeData"),console.log(Y.value),console.log(te.value),"group"===Y.value&&"user"===te.value&&await(async()=>{const e={briefRepresentation:!1,first:($.value-1)*ee.value,max:ee.value,search:ae.value},a=await L.GetOrganize(e),t=await L.GetOrganizeCount(e);console.log(t),a.data&&(X.value=a.data,Z.value=t.data.count)})(),"user"===Y.value&&"user"===te.value&&await(async()=>{console.log("getTableData");const e={briefRepresentation:!1,first:($.value-1)*ee.value,max:ee.value,search:ae.value},a=await L.GetUserListCount(e),t=await L.GetUserList(e);t.data&&(X.value=JSON.parse(JSON.stringify(t.data).replace(/user_id/g,"id")),X.value=JSON.parse(JSON.stringify(t.data).replace(/username/g,"name")),console.log("userTotal"),console.log(a),Z.value=a.data)})(),"group"===Y.value&&"resource"===te.value&&await(async()=>{const e=await k("corp_id");200===e.status&&0===e.data.code&&(console.log("getGroupList"),X.value=JSON.parse(JSON.stringify(e.data.data).replace(/group_id/g,"id")),X.value=JSON.parse(JSON.stringify(e.data.data).replace(/group_name/g,"name")))})(),"user"===Y.value&&"resource"===te.value&&await(async()=>{console.log("getAppList");const e={offset:($.value-1)*ee.value,limit:ee.value,search:ae.value},a=await w(e);console.log("getAppList"),console.log(a),0===a.data.code&&(X.value=JSON.parse(JSON.stringify(a.data.data.rows).replace(/app_id/g,"id")),X.value=JSON.parse(JSON.stringify(a.data.data.rows).replace(/app_name/g,"name")),Z.value=a.data.data.total_rows)})()},re=l([]),se=async()=>{console.log("getTableData");const a={offset:(H.value-1)*K.value,limit:K.value,search:z.value},t=await(l=a,e({url:"/console/v1/access_strategy/strategy_list",method:"post",data:l}));var l;console.log("res"),console.log(t),0===t.data.code&&(re.value=t.data.data.strategy_list_data,M.value=t.data.data.total_num)};se(),r({name:"",description:"",member:"",reason:["我因为所属组织结构原因需要访问该资源","我因为岗位职责原因需要访问该资源","我因为个人特殊原因需要访问该资源"]});const oe=l("add"),ue=l("新增访问策略");l(!1),l([]);const ie=async a=>{console.log(U.formValues),console.log(U.formValues.appGroupCheck),await P.value.ruleFormRef.validate((async(a,t)=>{if(!a)return v({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),"";let l="";const r={app_group_ids:U.formValues.appGroupCheck.map((e=>e.id||e.app_group_id)),app_ids:U.formValues.appCheck.map((e=>e.id||e.app_id)),enable_log:U.formValues.enable_log?1:2,end_time:U.formValues.time?U.formValues.time[1]:"",start_time:U.formValues.time?U.formValues.time[0]:"",strategy_detail:U.formValues.description,strategy_name:U.formValues.name,strategy_status:Number(U.formValues.statu),user_group_ids:U.formValues.groupCheck.map((e=>e.id||e.user_group_id)),user_ids:U.formValues.userCheck.map((e=>e.id||e.user_id))};"add"===oe.value&&(l=await(a=>e({url:"/console/v1/access_strategy/strategy",method:"post",data:a}))(r)),"edit"===oe.value&&(r.id=U.formValues.id,console.log("data"),console.log(r),l=await(a=>e({url:"/console/v1/access_strategy/strategy",method:"put",data:a}))(r)),console.log("res"),console.log(l),0===l.data.code&&(B(),await se()),q.value.length=0,pe.value.length=0,ne.value.length=0,ce.value.length=0,ge.value.length=0,A.value=!1}))},pe=l([]),ne=l([]),ce=l([]),ge=l([]),de=async a=>{console.log("updateState");const t=j.value.getSelectionRows().map((e=>e.id)),l=await(r={id:t,status:a},e({url:"/console/v1/access_strategy/strategy_enable",method:"post",data:r}));var r;await se(),console.log(l)};return s("getTableData",se),s("currentPage",H),s("pageSize",K),s("total",M),s("handleEdit",((e,a)=>{console.log(e,a),E.value="编辑访问策略",ue.value="编辑访问策略",oe.value="edit",U.formItems=[{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"statu",label:"状态：",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"2"}]},{field:"description",label:"描述：",type:"input",placeholder:"访问策略描述"},{field:"user",label:"用户：",type:"dialog",treeType:"group",title:"选择用户",dataType:"user",groupCheck:"groupCheck",userCheck:"userCheck",placeholder:"请选择用户",rules:[{required:!0,message:"用户不能为空",trigger:"blur"}]},{field:"resource",label:"资源：",type:"dialog",treeType:"group",appGroupCheck:"appGroupCheck",appCheck:"appCheck",title:"选择资源",dataType:"resource",placeholder:"请选择资源",rules:[{required:!0,message:"资源不能为空",trigger:"blur"}]},{field:"time",label:"时间：",type:"timepicker",placeholder:"时间选择",dateType:"dateType"},{field:"enable_log",label:"日志：",type:"checkbox"}],U.formValues.id=a.id,U.formValues.name=a.strategy_name,U.formValues.statu=a.strategy_status.toString(),U.formValues.description=a.strategy_detail,U.formValues.groupCheck=a.user_group_in_strategy||[],U.formValues.userCheck=a.user_in_strategy||[],U.formValues.appGroupCheck=a.app_group_in_strategy||[],U.formValues.appCheck=a.app_in_strategy||[],U.formValues.dateType=a.start_time?"1":"0",ne.value=[],a.user_in_strategy&&(ne.value=JSON.parse(JSON.stringify(a.user_in_strategy).replace(/user_id/g,"id")),ne.value=JSON.parse(JSON.stringify(ne.value).replace(/user_name/g,"name"))),pe.value=[],a.user_group_in_strategy&&(pe.value=JSON.parse(JSON.stringify(a.user_group_in_strategy).replace(/user_group_id/g,"id")),pe.value=JSON.parse(JSON.stringify(pe.value).replace(/user_group_name/g,"name"))),ce.value=[],a.app_group_in_strategy&&(ce.value=JSON.parse(JSON.stringify(a.app_group_in_strategy).replace(/app_group_id/g,"id")),ce.value=JSON.parse(JSON.stringify(ce.value).replace(/app_group_name/g,"name"))),ge.value=[],a.app_in_strategy&&(ge.value=JSON.parse(JSON.stringify(a.app_in_strategy).replace(/app_id/g,"id")),ge.value=JSON.parse(JSON.stringify(ge.value).replace(/app_name/g,"name"))),U.formValues.user=[...ne.value,...pe.value],U.formValues.resource=[...ge.value,...ce.value],U.formValues.time=[a.start_time,a.end_time],U.formValues.enable_log=1===a.enable_log,A.value=!0})),s("handleDelete",((a,t)=>{console.log(a),console.log(t),h.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个策略吗？</strong><br><strong>删除后关联的用户将无法访问此策略关联的应用，请谨慎操作。</strong>',"删除策略",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{var a;0===(await(a={id:[t.id]},e({url:"/console/v1/access_strategy/strategy",method:"delete",data:a}))).data.code?(console.log(111),v({type:"success",message:"删除成功"}),await se()):v({type:"error",message:"删除失败"})})).catch((()=>{v({type:"info",message:"取消删除"})}))})),s("treeData",X),s("treeLabel",W),s("treeType",Y),s("treeTotal",Z),s("treeCurrentPage",$),s("treePageSize",ee),s("treeSearch",ae),s("defaultProps",{id:"id",label:"name",children:"subGroups",isLeaf:"leaf"}),s("dataType",te),s("getTreeData",le),s("groupCheck",pe),s("userCheck",ne),s("appGroupCheck",ce),s("appCheck",ge),s("checkedData",q),(e,a)=>{const t=o("base-button"),l=o("base-input"),r=o("SuccessFilled"),s=o("el-icon"),v=o("CircleCloseFilled"),h=o("Close"),k=o("el-drawer");return u(),i("div",S,[p("div",V,[p("div",O,[n(t,{color:"#2972C8",plain:"",icon:e.Plus,onClick:a[0]||(a[0]=e=>(console.log(1),U.formItems=[{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"statu",label:"状态：",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"2"}]},{field:"description",label:"描述：",type:"input",placeholder:"访问策略描述"},{field:"user",label:"用户：",type:"dialog",treeType:"group",title:"选择用户",dataType:"user",groupCheck:"groupCheck",userCheck:"userCheck",placeholder:"请选择用户",rules:[{required:!0,message:"用户不能为空",trigger:"blur"}]},{field:"resource",label:"资源：",type:"dialog",treeType:"group",appGroupCheck:"appGroupCheck",appCheck:"appCheck",title:"选择资源",dataType:"resource",placeholder:"请选择资源",rules:[{required:!0,message:"资源不能为空",trigger:"blur"}]},{field:"time",label:"时间：",type:"timepicker",placeholder:"时间选择",dateType:"dateType"},{field:"enable_log",label:"日志：",type:"checkbox"}],U.formValues.statu="1",U.formValues.dateType="0",E.value="新增访问策略",ue.value="新增访问策略",oe.value="add",void(A.value=!0)))},{default:c((()=>a[5]||(a[5]=[g("新增")]))),_:1,__:[5]},8,["icon"]),n(t,{color:"#2972C8",plain:"",icon:e.RefreshRight,onClick:se},{default:c((()=>a[6]||(a[6]=[g("刷新")]))),_:1,__:[6]},8,["icon"]),n(t,{color:"#2972C8",plain:"",icon:e.CircleCheck,onClick:a[1]||(a[1]=e=>de(1))},{default:c((()=>a[7]||(a[7]=[g("启用")]))),_:1,__:[7]},8,["icon"]),n(t,{color:"#2972C8",plain:"",icon:e.CircleClose,onClick:a[2]||(a[2]=e=>de(2))},{default:c((()=>a[8]||(a[8]=[g("禁用")]))),_:1,__:[8]},8,["icon"]),n(l,{modelValue:z.value,"onUpdate:modelValue":a[3]||(a[3]=e=>z.value=e),class:"w-50 m-2 organize-search",placeholder:"Search","suffix-icon":e.Search,style:{width:"15%",float:"right"},onChange:se},null,8,["modelValue","suffix-icon"])]),n(b,f({ref_key:"tableRef",ref:j,"table-data":re.value},Q),{user_in_strategy:c((e=>[p("div",N,[(u(!0),i(d,null,_(e.row.user_in_strategy,(e=>(u(),i("span",{style:{color:"#252631"},key:e.user_id},m(e.user_name)+" ",1)))),128)),(u(!0),i(d,null,_(e.row.user_group_in_strategy,(e=>(u(),i("span",{style:{color:"#252631"},key:e.user_group_id},m(e.user_group_name)+" ",1)))),128))])])),app_in_strategy:c((e=>[p("div",T,[(u(!0),i(d,null,_(e.row.app_in_strategy,(e=>(u(),i("span",{style:{color:"#252631"},key:e.app_id},m(e.app_name)+" ",1)))),128)),(u(!0),i(d,null,_(e.row.app_group_in_strategy,(e=>(u(),i("span",{style:{color:"#252631"},key:e.app_group_id},m(e.app_group_name)+" ",1)))),128))])])),start_time:c((e=>[p("div",J,[p("span",x,m(e.row.start_time)+" - "+m(e.row.end_time),1)])])),strategy_status:c((e=>[p("div",F,[1===e.row.strategy_status?(u(),y(s,{key:0},{default:c((()=>[n(r,{style:{color:"#52c41a"}})])),_:1})):(u(),y(s,{key:1},{default:c((()=>[n(v,{style:{color:"#BDBDC1"}})])),_:1}))])])),_:1},16,["table-data"]),n(k,{modelValue:A.value,"onUpdate:modelValue":a[4]||(a[4]=e=>A.value=e),title:"drawerTitle",direction:"rtl","show-close":!1,size:"40%","before-close":I},{header:c((({close:e,titleId:a})=>[p("div",G,[n(t,{link:"",onClick:e},{default:c((()=>[n(s,{size:"20px"},{default:c((()=>[n(h)])),_:1})])),_:2},1032,["onClick"])]),p("span",{id:a,class:"titleClass"},m(E.value),9,D)])),footer:c((()=>[n(t,{color:"#256EBF",type:"primary",onClick:ie},{default:c((()=>a[9]||(a[9]=[g("确定 ")]))),_:1,__:[9]}),n(t,{style:{"margin-left":"10px","margin-right":"90px"},onClick:I},{default:c((()=>a[10]||(a[10]=[g("取消")]))),_:1,__:[10]})])),default:c((()=>[n(C,f({ref_key:"customForm",ref:P},U,{formOptions:R,cancel:I,submitForm:ie,getTreeData:le,isFooter:!1}),null,16,["formOptions"])])),_:1},8,["modelValue"])])])}}}),[["__scopeId","data-v-40cb3c28"]]);export{L as default};
