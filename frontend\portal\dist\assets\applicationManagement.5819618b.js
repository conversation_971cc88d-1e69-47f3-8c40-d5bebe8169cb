/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{_ as e}from"./customFrom.6bcc3d4d.js";import{_ as a,r as s,p as o,B as l,h as t,o as p,d as r,j as i,w as u,e as n,k as d,O as c,F as m,i as g,t as _,f,g as b,M as v,P as y}from"./index.74d1ee23.js";import{_ as h}from"./customTable.09ee0d92.js";import{D as V}from"./directoryTree.447276d1.js";import{b as w,a as x,c as k,u as j,d as A,e as S,f as N,h as q,i as C}from"./resource.29b70792.js";import{g as I}from"./agents.575e30de.js";import"./iconfont.2d75af05.js";const T=(e,a)=>(console.log("options"),console.log(e),console.log("appFormItemOptions"),console.log(a),[{field:"app_name",label:"应用名称:",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"app_desc",label:"描述:",type:"input",placeholder:"组织描述"},{field:"app_status",label:"状态:",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"0"}]},{field:"group_id",label:"所属分组:",type:"select",placeholder:"所属分组",options:e,optionsLabe:"group_name",optionsValue:"id",optionsKey:"id",rules:[{required:!0,message:"所属分组不能为空",trigger:"blur"}]},{field:"app_sites",label:"服务器地址:",type:"table",options:[{label:"tcp",value:"tcp"},{label:"udp",value:"udp"},{label:"icmp",value:"icmp"}],rules:[{required:!0,message:"服务器地址不能为空",trigger:"blur"}],subRules:{protocol:[{required:!0,message:"协议不能为空",trigger:"blur"}],server_addr:[{required:!0,message:"服务器地址不能为空",trigger:"blur"}],port:[{required:!0,message:"端口不能为空",trigger:"blur"}]}},{field:"sdp",label:"所属SDP:",type:"select",placeholder:"所属SDP",options:a,optionsLabe:"app_name",optionsValue:"appliance_id",optionsKey:"appliance_id",rules:[{required:!0,message:"SDP不能为空",trigger:"blur"}]},{field:"web_entry",label:"Web入口:",type:"input"},{field:"app_icon",label:"应用图标:",type:"imgRadio",localIcon:"/src/assets/noBody.png"}]),D={class:"application"},F={style:{height:"35px"}},L={style:{height:"95%",display:"block",width:"200px","overflow-y":"scroll"}},R={class:"header"},B={style:{"text-align":"center"}},O={style:{"text-align":"center"}},P={style:{"text-align":"center"}},z=a(Object.assign({name:"ApplicationManagement"},{setup(a){const z=s(""),E=s(!1),K=s("group"),M=s("新增应用"),U=s(""),G=s(!1),H=s([]),W={label:"group_name",children:"zones",isLeaf:"leaf",isRoot:"root"},J={propList:[{prop:"app_name",label:"应用名称",slotName:"app_name"},{prop:"app_desc",label:"描述",slotName:"app_desc"},{prop:"group_name",label:"所属分组",slotName:"group_name"},{prop:"app_sites",label:"服务器地址",slotName:"app_sites"},{prop:"app_status",label:"状态",slotName:"app_status"}],isSelectColumn:!0,isOperationColumn:!0},Q=s([]),X=s(1),Y=s(100),Z=s(0);o("treeRef",z),o("currentPage",X),o("pageSize",Y),o("total",Z);const $=async()=>{const e={offset:(X.value-1)*Y.value,limit:Y.value,search:U.value};z.value&&(e.group_id=Number(z.value));const a=await w(e);console.log("getAppList"),console.log(a),0===a.data.code&&(Q.value=a.data.data.rows,Z.value=a.data.data.total_rows)};$(),o("getTableData",$);(async()=>{const e=await x("corp_id");200===e.status&&0===e.data.code&&(H.value=e.data.data)})();const ee=s([]),ae=async(e,a)=>{if(0===e.level){const e=await x("corp_id");if(200===e.status&&0===e.data.code)return ee.value=e.data.data,a(e.data.data)}return a([])},se={formItems:[],formValues:l({})},oe=l({}),le=async()=>{const e={limit:Y.value,offset:(X.value-1)*Y.value},a=await I(e);return console.log("getAgentsList"),console.log(a),0===a.data.code?a.data.data.rows:[]},te=s(""),pe=async e=>{if("group"===e)M.value="新增应用分组",te.value="group",se.formItems=[{field:"name",label:"应用组织名称：",type:"input",placeholder:"请输入应用组织名称",rules:[{required:!0,message:"应用组织名称不能为空",trigger:"blur"}]},{field:"description",label:"应用组织描述：",type:"input",placeholder:"应用组织描述"}];else{te.value="app",M.value="新增应用",K.value="app";const e=s(await le());se.formItems=T(ee,e),se.formValues.app_sites=[{protocol:"",address:"",port:""}],se.formValues.app_status="1",se.formValues.app_icon={type:"1"}}E.value=!0},re=()=>{ie(),E.value=!1},ie=()=>{const e=Object.keys(se.formValues),a={};e.forEach((e=>{a[e]=""})),Object.assign(se.formValues,a)},ue=s(1);o("cascaderKey",ue);const ne=async e=>{console.log("submitForm"),console.log(se.formValues),await e.validate((async(e,a)=>{if(!e)return v({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),"";let o="",l="";if("app"===te.value||"updateApp"===te.value){se.formValues.app_sites[0].port=Number(se.formValues.app_sites[0].port);let e=[];const a=s(await le());for(const s in a.value)se.formValues.sdp.includes(a.value[s].appliance_id)&&e.push({app_type:a.value[s].display_app_type,connector_id:2===a.value[s].display_app_type?a.value[s].appliance_id:"",se_id:2!==a.value[s].display_app_type?a.value[s].appliance_id:""});l={app_name:se.formValues.app_name,app_desc:se.formValues.app_desc,app_status:Number(se.formValues.app_status),group_id:Number(se.formValues.group_id),corp_id:1,app_sites:se.formValues.app_sites,se_app:e,web_entry:se.formValues.web_entry}}else o={group_name:se.formValues.name};let t="";switch(te.value){case"group":t=await N(o),console.log(t);break;case"updateGroup":o.id=se.formValues.id,t=await S(o);break;case"app":console.log("createApp"),t=await A(l),console.log(t);break;case"updateApp":l.id=se.formValues.id,console.log("updateApp"),console.log(se.formValues.id),console.log(l),t=await j(l),console.log(t)}t.data.code<0?v({showClose:!0,message:t.data.msg,type:"error"}):v({type:"success",message:"添加应用成功"}),ie(),await $(),++ue.value,E.value=!1}))},de=()=>{se.formValues.app_sites.push({protocol:"",serverAddress:"",port:""})},ce=e=>{se.formValues.app_sites.splice(e,1)},me=async(e,a)=>{console.log("edit"),console.log(a),console.log(a.id),M.value="修改分组",te.value="updateGroup",se.formItems=[{field:"name",label:"应用组织名称：",type:"input",placeholder:"请输入应用组织名称",rules:[{required:!0,message:"应用组织名称不能为空",trigger:"blur"}]},{field:"description",label:"应用组织描述：",type:"input",placeholder:"应用组织描述"}],se.formValues.id=a.id,se.formValues.name=a.group_name,E.value=!0};return o("open",(e=>{y.confirm("删除分组以后将无法恢复，确认删除分组？","删除应用分组",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{console.log(e);const a=await q({id:Number(e)});console.log(a),0===a.data.code?(v({type:"success",message:"删除成功"}),++ue.value):v({type:"error",message:"删除失败"})})).catch((()=>{v({type:"info",message:"取消删除"})}))})),o("handleEdit",(async(e,a)=>{console.log("handleEdit"),console.log(e,a),M.value="编辑应用",te.value="updateApp";const o=s(await le()),l=await k({id:a.id});var t;console.log("application"),console.log(l),se.formItems=T(ee,o),t=l.data.data,console.log("setFormValues"),console.log(t),se.formValues.id=t.id,se.formValues.app_name=t.app_name,se.formValues.app_desc=t.app_desc,se.formValues.app_icon=t.app_icon,se.formValues.app_sites=[{protocol:"",serverAddress:"",port:""}],t.app_sites&&(se.formValues.app_sites=t.app_sites),se.formValues.app_status=t.app_status.toString(),se.formValues.corp_id=t.corp_id,se.formValues.group_id=t.group_id.toString(),se.formValues.web_url=t.web_url,se.formValues.sdp=t.bind_se.map((e=>e.appliance_id))[0],se.formValues.app_icon={type:"1"},E.value=!0})),o("handleDelete",((e,a)=>{console.log("handleDelete"),console.log(a),y.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个应用吗？</strong><br><strong>删除后用户将无法访问该应用，请谨慎操作。</strong>',"删除应用",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=await C({id:a.id});console.log(e),0===e.data.code?(v({type:"success",message:"删除成功"}),await $()):v({type:"error",message:"删除失败"}),ie()})).catch((()=>{ie(),v({type:"info",message:"取消删除"})}))})),(a,s)=>{const o=t("base-button"),l=t("base-aside"),v=t("base-input"),y=t("SuccessFilled"),w=t("el-icon"),x=t("Remove"),k=t("base-main"),j=t("base-container"),A=t("el-dialog");return p(),r("div",D,[i(j,{style:{height:"100%"}},{default:u((()=>[i(l,{width:"200px",style:{"min-height":"calc(100vh - 200px)"}},{default:u((()=>[n("div",F,[s[4]||(s[4]=n("span",{class:"menu-label"},"应用分组",-1)),i(o,{class:"organize-but",icon:a.FolderAdd,onClick:s[0]||(s[0]=e=>pe("group"))},null,8,["icon"])]),n("div",L,[i(V,{class:"tree-panel","load-operate":G.value,"load-node":ae,"tree-props":W,edit:me},null,8,["load-operate"])])])),_:1}),i(k,null,{default:u((()=>[n("div",R,[i(o,{icon:a.Plus,onClick:s[1]||(s[1]=e=>pe("app"))},{default:u((()=>s[5]||(s[5]=[d("新增应用")]))),_:1,__:[5]},8,["icon"]),i(o,{icon:a.RefreshRight,onClick:$},{default:u((()=>s[6]||(s[6]=[d("刷新")]))),_:1,__:[6]},8,["icon"]),i(v,{modelValue:U.value,"onUpdate:modelValue":s[2]||(s[2]=e=>U.value=e),class:"w-50 m-2 organize-search",placeholder:"Search","suffix-icon":a.Search,onChange:$},null,8,["modelValue","suffix-icon"])]),i(h,c({"table-data":Q.value},J),{app_sites:u((e=>[n("div",B,[(p(!0),r(m,null,g(e.row.app_sites,((e,a)=>(p(),r("span",{key:e.id,style:{color:"rgba(0, 0, 0, 0.701960784313725)","font-size":"12px","font-weight":"400","font-style":"normal"}},[d(_(e.protocol?e.protocol+"://"+e.address+":"+e.port:""),1),s[7]||(s[7]=n("br",null,null,-1))])))),128))])])),group_name:u((e=>[n("div",O,[n("span",null,_(e.row.group_name||"默认分组"),1)])])),app_status:u((e=>[n("div",P,[e.row.app_status?(p(),f(w,{key:0,style:{color:"#52c41a"}},{default:u((()=>[i(y)])),_:1})):(p(),f(w,{key:1},{default:u((()=>[i(x)])),_:1}))])])),_:1},16,["table-data"])])),_:1})])),_:1}),E.value?(p(),f(A,{key:0,modelValue:E.value,"onUpdate:modelValue":s[3]||(s[3]=e=>E.value=e),title:M.value,width:"30%","custom-class":"custom-dialog"},{default:u((()=>[i(e,c(se,{"form-options":oe,cancel:re,"submit-form":ne,"add-app-address":de,"remove-app-address":ce}),null,16,["form-options"])])),_:1},8,["modelValue","title"])):b("",!0)])}}}),[["__scopeId","data-v-756b5444"]]);export{z as default};
