{"name": "postcss-modules-scope", "version": "2.2.0", "description": "A CSS Modules transform to extract export statements from local-scope classes", "main": "src/index.js", "engines": {"node": ">= 6"}, "scripts": {"lint": "eslint src test", "pretest": "yarn lint", "test": "mocha", "autotest": "chokidar src test -c 'yarn test'", "precover": "yarn lint", "cover": "nyc mocha", "travis": "yarn cover", "prepublish": "yarn run test"}, "repository": {"type": "git", "url": "https://github.com/css-modules/postcss-modules-scope.git"}, "keywords": ["css-modules", "postcss", "plugin"], "files": ["src"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/css-modules/postcss-modules-scope/issues"}, "homepage": "https://github.com/css-modules/postcss-modules-scope", "prettier": {"semi": true, "singleQuote": true, "trailingComma": "es5"}, "dependencies": {"postcss": "^7.0.6", "postcss-selector-parser": "^6.0.0"}, "devDependencies": {"cssesc": "^3.0.0", "chokidar-cli": "^1.0.1", "codecov.io": "^0.1.2", "coveralls": "^3.0.2", "eslint": "^5.9.0", "mocha": "^6.0.2", "nyc": "^14.1.0"}}