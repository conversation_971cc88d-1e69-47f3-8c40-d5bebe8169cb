/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
System.register(["./header-legacy.ed05bf1f.js","./menu-legacy.3c760efe.js","./index-legacy.04f34b53.js","./ASD-legacy.b6ffb1bc.js"],(function(e,t){"use strict";var a,n,i,u,o,l,r,c,f=document.createElement("style");return f.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(f),{setters:[function(e){a=e.default},function(e){n=e.default},function(e){i=e.h,u=e.o,o=e.d,l=e.j,r=e.e,c=e.f},function(){}],execute:function(){var t={class:"layout-page"},f={class:"layout-wrap"},d={id:"layoutMain",class:"layout-main"};e("default",Object.assign({name:"Client"},{setup:function(e){return function(e,s){var h=i("router-view");return u(),o("div",t,[l(a),r("div",f,[l(n),r("div",d,[(u(),c(h,{key:e.$route.fullPath}))])])])}}}))}}}));
