/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{a1 as e,j as a,o as s,a as n,i as t,w as o,T as l,d as u,a2 as r,l as i,z as d}from"./index.bfaf04e1.js";const f=Object.assign({name:"RouterHolder"},{setup(f){const c=e();return(e,f)=>{const m=a("router-view");return s(),n("div",null,[t(m,null,{default:o((({Component:e})=>[t(l,{mode:"out-in",name:"el-fade-in-linear"},{default:o((()=>[(s(),u(r,{include:i(c).keepAliveRouters},[(s(),u(d(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{f as default};
