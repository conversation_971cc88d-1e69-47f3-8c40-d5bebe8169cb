/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e,n,r){return(n=function(e){var n=function(e,n){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,n||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==t(n)?n:n+""}(n))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function u(n,o,a,i){var u=o&&o.prototype instanceof f?o:f,l=Object.create(u.prototype);return r(l,"_invoke",function(n,r,o){var a,i,u,f=0,l=o||[],s=!1,p={p:0,n:0,v:t,a:d,f:d.bind(t,4),d:function(e,n){return a=e,i=0,u=t,p.n=n,c}};function d(n,r){for(i=n,u=r,e=0;!s&&f&&!o&&e<l.length;e++){var o,a=l[e],d=p.p,v=a[2];n>3?(o=v===r)&&(u=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=t):a[0]<=d&&((o=n<2&&d<a[1])?(i=0,p.v=r,p.n=a[1]):d<v&&(o=n<3||a[0]>r||r>v)&&(a[4]=n,a[5]=r,p.n=v,i=0))}if(o||n>1)return c;throw s=!0,r}return function(o,l,v){if(f>1)throw TypeError("Generator is already running");for(s&&1===l&&d(l,v),i=l,u=v;(e=i<2?t:u)||!s;){a||(i?i<3?(i>1&&(p.n=-1),d(i,u)):p.n=u:p.v=u);try{if(f=2,a){if(i||(o="next"),e=a[o]){if(!(e=e.call(a,u)))throw TypeError("iterator result is not an object");if(!e.done)return e;u=e.value,i<2&&(i=0)}else 1===i&&(e=a.return)&&e.call(a),i<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=t}else if((e=(s=p.n<0)?u:n.call(r,p))!==c)break}catch(e){a=t,i=1,u=e}finally{f=1}}return{value:e,done:s}}}(n,a,i),!0),l}var c={};function f(){}function l(){}function s(){}e=Object.getPrototypeOf;var p=[][a]?e(e([][a]())):(r(e={},a,(function(){return this})),e),d=s.prototype=f.prototype=Object.create(p);function v(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,r(t,i,"GeneratorFunction")),t.prototype=Object.create(d),t}return l.prototype=s,r(d,"constructor",s),r(s,"constructor",l),l.displayName="GeneratorFunction",r(s,i,"GeneratorFunction"),r(d),r(d,i,"Generator"),r(d,a,(function(){return this})),r(d,"toString",(function(){return"[object Generator]"})),(n=function(){return{w:u,m:v}})()}function r(t,e,n,o){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}r=function(t,e,n,o){if(e)a?a(t,e,{value:n,enumerable:!o,configurable:!o,writable:!o}):t[e]=n;else{var i=function(e,n){r(t,e,(function(t){return this._invoke(e,n,t)}))};i("next",0),i("throw",1),i("return",2)}},r(t,e,n,o)}function o(t,e,n,r,o,a,i){try{var u=t[a](i),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,o)}function a(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function u(t){o(i,r,a,u,c,"next",t)}function c(t){o(i,r,a,u,c,"throw",t)}u(void 0)}))}}System.register(["./api-legacy.16c002e6.js","./index-legacy.dbc04544.js"],(function(t,r){"use strict";var o,i,u,c,f,l,s,p,d,v,y,h,b,m=document.createElement("style");return m.textContent='@charset "UTF-8";.sticky-button[data-v-9b701872]{position:sticky;top:2px;z-index:2;background-color:#fff}.fitler[data-v-9b701872]{width:60%}\n',document.head.appendChild(m),{setters:[function(t){o=t.e},function(t){i=t.x,u=t._,c=t.r,f=t.y,l=t.h,s=t.o,p=t.d,d=t.e,v=t.j,y=t.w,h=t.k,b=t.M}],execute:function(){var r={class:"clearfix sticky-button"},m={class:"tree-content"},g=Object.assign({name:"Apis"},{props:{row:{default:function(){return{}},type:Object}},setup:function(t,u){var g=u.expose,w=t,j=c({children:"children",label:"description"}),k=c(""),O=c([]),x=c([]),S=c(""),_=function(){var t=a(n().m((function t(){var e,r,a;return n().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,o();case 1:return e=t.v,r=e.data.apis,O.value=G(r),t.n=2,n={authorityId:w.row.authorityId},i({url:"/casbin/getPolicyPathByAuthorityId",method:"post",data:n});case 2:a=t.v,S.value=w.row.authorityId,x.value=[],a.data.paths&&a.data.paths.forEach((function(t){x.value.push("p:"+t.path+"m:"+t.method)}));case 3:return t.a(2)}var n}),t)})));return function(){return t.apply(this,arguments)}}();_();var P=c(!1),I=function(){P.value=!0},G=function(t){var n={};t&&t.forEach((function(t){t.onlyId="p:"+t.path+"m:"+t.method,Object.prototype.hasOwnProperty.call(n,t.apiGroup)?n[t.apiGroup].push(t):Object.assign(n,e({},t.apiGroup,[t]))}));var r=[];for(var o in n){var a={ID:o,description:o+"组",children:n[o]};r.push(a)}return r},T=c(null),E=function(){var t=a(n().m((function t(){var e,r;return n().w((function(t){for(;;)switch(t.n){case 0:return e=T.value.getCheckedNodes(!0),r=[],e&&e.forEach((function(t){var e={path:t.path,method:t.method};r.push(e)})),t.n=1,n={authorityId:S.value,casbinInfos:r},i({url:"/casbin/updateCasbin",method:"post",data:n});case 1:0===t.v.code&&b({type:"success",message:"api设置成功"});case 2:return t.a(2)}var n}),t)})));return function(){return t.apply(this,arguments)}}();g({needConfirm:P,enterAndNext:function(){E()}});var C=function(t,e){return!t||-1!==e.description.indexOf(t)};return f(k,(function(t){T.value.filter(t)})),function(t,e){var n=l("base-input"),o=l("base-button"),a=l("el-tree");return s(),p("div",null,[d("div",r,[v(n,{modelValue:k.value,"onUpdate:modelValue":e[0]||(e[0]=function(t){return k.value=t}),class:"fitler",placeholder:"筛选"},null,8,["modelValue"]),v(o,{class:"fl-right",size:"small",type:"primary",onClick:E},{default:y((function(){return e[1]||(e[1]=[h("确 定")])})),_:1,__:[1]})]),d("div",m,[v(a,{ref_key:"apiTree",ref:T,data:O.value,"default-checked-keys":x.value,props:j.value,"default-expand-all":"","highlight-current":"","node-key":"onlyId","show-checkbox":"","filter-node-method":C,onCheck:I},null,8,["data","default-checked-keys","props"])])])}}});t("default",u(g,[["__scopeId","data-v-9b701872"]]))}}}))}();
