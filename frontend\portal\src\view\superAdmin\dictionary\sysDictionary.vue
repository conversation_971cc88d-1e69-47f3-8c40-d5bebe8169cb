<template>
  <div>
    <warning-bar
      title="获取字典且缓存方法已在前端utils/dictionary 已经封装完成 不必自己书写 使用方法查看文件内注释"
    />
    <div class="gva-search-box">
      <base-form :inline="true" :model="searchInfo">
        <base-form-item label="字典名（中）">
          <base-input v-model="searchInfo.name" placeholder="搜索条件" />
        </base-form-item>
        <base-form-item label="字典名（英）">
          <base-input v-model="searchInfo.type" placeholder="搜索条件" />
        </base-form-item>
        <base-form-item label="状态" prop="status">
          <base-select v-model="searchInfo.status" clear placeholder="请选择">
            <base-option key="true" label="是" value="true" />
            <base-option key="false" label="否" value="false" />
          </base-select>
        </base-form-item>
        <base-form-item label="描述">
          <base-input v-model="searchInfo.desc" placeholder="搜索条件" />
        </base-form-item>
        <base-form-item>
          <base-button
            size="small"
            type="primary"
            icon="search"
            @click="onSubmit"
          >查询</base-button>
          <base-button
            size="small"
            icon="refresh"
            @click="onReset"
          >重置</base-button>
        </base-form-item>
      </base-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <base-button
          size="small"
          type="primary"
          icon="plus"
          @click="openDialog"
        >新增</base-button>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        style="width: 100%"
        tooltip-effect="dark"
        row-key="ID"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="日期" width="180">
          <template #default="scope">{{
            formatDate(scope.row.CreatedAt)
          }}</template>
        </el-table-column>

        <el-table-column
          align="left"
          label="字典名（中）"
          prop="name"
          width="160"
        />

        <el-table-column
          align="left"
          label="字典名（英）"
          prop="type"
          width="120"
        />

        <el-table-column align="left" label="状态" prop="status" width="120">
          <template #default="scope">{{
            formatBoolean(scope.row.status)
          }}</template>
        </el-table-column>

        <el-table-column align="left" label="描述" prop="desc" width="280" />

        <el-table-column align="left" label="按钮组">
          <template #default="scope">
            <base-button
              size="small"
              icon="document"
              type="primary"
              link
              @click="toDetail(scope.row)"
            >详情</base-button>
            <base-button
              size="small"
              icon="edit"
              type="primary"
              link
              @click="updateSysDictionaryFunc(scope.row)"
            >变更</base-button>
            <el-popover
              v-model="scope.row.visible"
              placement="top"
              width="160"
            >
              <p>确定要删除吗？</p>
              <div style="text-align: right; margin-top: 8px">
                <base-button
                  size="small"
                  type="primary"
                  link
                  @click="scope.row.visible = false"
                >取消</base-button>
                <base-button
                  type="primary"
                  size="small"
                  @click="deleteSysDictionaryFunc(scope.row)"
                >确定</base-button>
              </div>
              <template #reference>
                <base-button
                  type="primary"
                  link
                  icon="delete"
                  size="small"
                  style="margin-left: 10px"
                  @click="scope.row.visible = true"
                >删除</base-button>
              </template>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>

      <div class="gva-pagination">
        <el-pagination
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogFormVisible"
      :before-close="closeDialog"
      title="弹窗操作"
    >
      <base-form
        ref="dialogForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="110px"
      >
        <base-form-item label="字典名（中）" prop="name">
          <base-input
            v-model="formData.name"
            placeholder="请输入字典名（中）"
            clearable
            :style="{ width: '100%' }"
          />
        </base-form-item>
        <base-form-item label="字典名（英）" prop="type">
          <base-input
            v-model="formData.type"
            placeholder="请输入字典名（英）"
            clearable
            :style="{ width: '100%' }"
          />
        </base-form-item>
        <base-form-item label="状态" prop="status" required>
          <el-switch
            v-model="formData.status"
            active-text="开启"
            inactive-text="停用"
          />
        </base-form-item>
        <base-form-item label="描述" prop="desc">
          <base-input
            v-model="formData.desc"
            placeholder="请输入描述"
            clearable
            :style="{ width: '100%' }"
          />
        </base-form-item>
      </base-form>
      <template #footer>
        <div class="dialog-footer">
          <base-button size="small" @click="closeDialog">取 消</base-button>
          <base-button
            size="small"
            type="primary"
            @click="enterDialog"
          >确 定</base-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SysDictionary',
}
</script>

<script setup>
import {
  createSysDictionary,
  deleteSysDictionary,
  updateSysDictionary,
  findSysDictionary,
  getSysDictionaryList,
} from '@/api/sysDictionary' // 此处请自行替换地址
import WarningBar from '@/components/warningBar/warningBar.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { formatBoolean, formatDate } from '@/utils/format'

const router = useRouter()

const formData = ref({
  name: null,
  type: null,
  status: true,
  desc: null,
})
const rules = ref({
  name: [
    {
      required: true,
      message: '请输入字典名（中）',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请输入字典名（英）',
      trigger: 'blur',
    },
  ],
  desc: [
    {
      required: true,
      message: '请输入描述',
      trigger: 'blur',
    },
  ],
})

const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

const onReset = () => {
  searchInfo.value = {}
}

// 条件搜索前端看此方法
const onSubmit = () => {
  page.value = 1
  pageSize.value = 10
  if (searchInfo.value.status === '') {
    searchInfo.value.status = null
  }
  getTableData()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getSysDictionaryList({
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value,
  })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

const toDetail = (row) => {
  router.push({
    name: 'dictionaryDetail',
    params: {
      id: row.ID,
    },
  })
}

const dialogFormVisible = ref(false)
const type = ref('')
const updateSysDictionaryFunc = async(row) => {
  const res = await findSysDictionary({ ID: row.ID, status: row.status })
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data.resysDictionary
    dialogFormVisible.value = true
  }
}
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    name: null,
    type: null,
    status: true,
    desc: null,
  }
}
const deleteSysDictionaryFunc = async(row) => {
  row.visible = false
  const res = await deleteSysDictionary({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功',
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

const dialogForm = ref(null)
const enterDialog = async() => {
  dialogForm.value.validate(async(valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createSysDictionary(formData.value)
        break
      case 'update':
        res = await updateSysDictionary(formData.value)
        break
      default:
        res = await createSysDictionary(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage.success('操作成功')
      closeDialog()
      getTableData()
    }
  })
}
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}
</script>

<style></style>
