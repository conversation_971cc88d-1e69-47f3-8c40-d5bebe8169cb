const set = require('regenerate')(0x24, 0x2B, 0x5E, 0x60, 0x7C, 0x7E, 0xAC, 0xB4, 0xB8, 0xD7, 0xF7, 0x2ED, 0x375, 0x3F6, 0x482, 0x60B, 0x6DE, 0x6E9, 0x7F6, 0x888, 0xAF1, 0xB70, 0xC7F, 0xD4F, 0xD79, 0xE3F, 0xF13, 0xF34, 0xF36, 0xF38, 0x166D, 0x17DB, 0x1940, 0x1FBD, 0x2044, 0x2052, 0x2114, 0x2125, 0x2127, 0x2129, 0x212E, 0x214F, 0x3004, 0x3020, 0x31EF, 0x3250, 0xAB5B, 0xFB29, 0xFDCF, 0xFE62, 0xFE69, 0xFF04, 0xFF0B, 0xFF3E, 0xFF40, 0xFF5C, 0xFF5E, 0x101A0, 0x10AC8, 0x1173F, 0x16B45, 0x1BC9C, 0x1D245, 0x1D6C1, 0x1D6DB, 0x1D6FB, 0x1D715, 0x1D735, 0x1D74F, 0x1D76F, 0x1D789, 0x1D7A9, 0x1D7C3, 0x1E14F, 0x1E2FF, 0x1ECAC, 0x1ECB0, 0x1ED2E, 0x1F7F0);
set.addRange(0x3C, 0x3E).addRange(0xA2, 0xA6).addRange(0xA8, 0xA9).addRange(0xAE, 0xB1).addRange(0x2C2, 0x2C5).addRange(0x2D2, 0x2DF).addRange(0x2E5, 0x2EB).addRange(0x2EF, 0x2FF).addRange(0x384, 0x385).addRange(0x58D, 0x58F).addRange(0x606, 0x608).addRange(0x60E, 0x60F).addRange(0x6FD, 0x6FE).addRange(0x7FE, 0x7FF).addRange(0x9F2, 0x9F3).addRange(0x9FA, 0x9FB).addRange(0xBF3, 0xBFA).addRange(0xF01, 0xF03).addRange(0xF15, 0xF17).addRange(0xF1A, 0xF1F).addRange(0xFBE, 0xFC5).addRange(0xFC7, 0xFCC).addRange(0xFCE, 0xFCF).addRange(0xFD5, 0xFD8).addRange(0x109E, 0x109F).addRange(0x1390, 0x1399).addRange(0x19DE, 0x19FF).addRange(0x1B61, 0x1B6A).addRange(0x1B74, 0x1B7C).addRange(0x1FBF, 0x1FC1).addRange(0x1FCD, 0x1FCF).addRange(0x1FDD, 0x1FDF).addRange(0x1FED, 0x1FEF).addRange(0x1FFD, 0x1FFE).addRange(0x207A, 0x207C).addRange(0x208A, 0x208C).addRange(0x20A0, 0x20C0).addRange(0x2100, 0x2101).addRange(0x2103, 0x2106).addRange(0x2108, 0x2109).addRange(0x2116, 0x2118).addRange(0x211E, 0x2123).addRange(0x213A, 0x213B).addRange(0x2140, 0x2144).addRange(0x214A, 0x214D).addRange(0x218A, 0x218B).addRange(0x2190, 0x2307).addRange(0x230C, 0x2328).addRange(0x232B, 0x2429).addRange(0x2440, 0x244A).addRange(0x249C, 0x24E9);
set.addRange(0x2500, 0x2767).addRange(0x2794, 0x27C4).addRange(0x27C7, 0x27E5).addRange(0x27F0, 0x2982).addRange(0x2999, 0x29D7).addRange(0x29DC, 0x29FB).addRange(0x29FE, 0x2B73).addRange(0x2B76, 0x2B95).addRange(0x2B97, 0x2BFF).addRange(0x2CE5, 0x2CEA).addRange(0x2E50, 0x2E51).addRange(0x2E80, 0x2E99).addRange(0x2E9B, 0x2EF3).addRange(0x2F00, 0x2FD5).addRange(0x2FF0, 0x2FFF).addRange(0x3012, 0x3013).addRange(0x3036, 0x3037).addRange(0x303E, 0x303F).addRange(0x309B, 0x309C).addRange(0x3190, 0x3191).addRange(0x3196, 0x319F).addRange(0x31C0, 0x31E5).addRange(0x3200, 0x321E).addRange(0x322A, 0x3247).addRange(0x3260, 0x327F).addRange(0x328A, 0x32B0).addRange(0x32C0, 0x33FF).addRange(0x4DC0, 0x4DFF).addRange(0xA490, 0xA4C6).addRange(0xA700, 0xA716).addRange(0xA720, 0xA721).addRange(0xA789, 0xA78A).addRange(0xA828, 0xA82B).addRange(0xA836, 0xA839).addRange(0xAA77, 0xAA79).addRange(0xAB6A, 0xAB6B).addRange(0xFBB2, 0xFBC2).addRange(0xFD40, 0xFD4F).addRange(0xFDFC, 0xFDFF).addRange(0xFE64, 0xFE66).addRange(0xFF1C, 0xFF1E).addRange(0xFFE0, 0xFFE6).addRange(0xFFE8, 0xFFEE).addRange(0xFFFC, 0xFFFD).addRange(0x10137, 0x1013F).addRange(0x10179, 0x10189).addRange(0x1018C, 0x1018E).addRange(0x10190, 0x1019C).addRange(0x101D0, 0x101FC).addRange(0x10877, 0x10878).addRange(0x10D8E, 0x10D8F);
set.addRange(0x11FD5, 0x11FF1).addRange(0x16B3C, 0x16B3F).addRange(0x1CC00, 0x1CCEF).addRange(0x1CD00, 0x1CEB3).addRange(0x1CF50, 0x1CFC3).addRange(0x1D000, 0x1D0F5).addRange(0x1D100, 0x1D126).addRange(0x1D129, 0x1D164).addRange(0x1D16A, 0x1D16C).addRange(0x1D183, 0x1D184).addRange(0x1D18C, 0x1D1A9).addRange(0x1D1AE, 0x1D1EA).addRange(0x1D200, 0x1D241).addRange(0x1D300, 0x1D356).addRange(0x1D800, 0x1D9FF).addRange(0x1DA37, 0x1DA3A).addRange(0x1DA6D, 0x1DA74).addRange(0x1DA76, 0x1DA83).addRange(0x1DA85, 0x1DA86).addRange(0x1EEF0, 0x1EEF1).addRange(0x1F000, 0x1F02B).addRange(0x1F030, 0x1F093).addRange(0x1F0A0, 0x1F0AE).addRange(0x1F0B1, 0x1F0BF).addRange(0x1F0C1, 0x1F0CF).addRange(0x1F0D1, 0x1F0F5).addRange(0x1F10D, 0x1F1AD).addRange(0x1F1E6, 0x1F202).addRange(0x1F210, 0x1F23B).addRange(0x1F240, 0x1F248).addRange(0x1F250, 0x1F251).addRange(0x1F260, 0x1F265).addRange(0x1F300, 0x1F6D7).addRange(0x1F6DC, 0x1F6EC).addRange(0x1F6F0, 0x1F6FC).addRange(0x1F700, 0x1F776).addRange(0x1F77B, 0x1F7D9).addRange(0x1F7E0, 0x1F7EB).addRange(0x1F800, 0x1F80B).addRange(0x1F810, 0x1F847).addRange(0x1F850, 0x1F859).addRange(0x1F860, 0x1F887).addRange(0x1F890, 0x1F8AD).addRange(0x1F8B0, 0x1F8BB).addRange(0x1F8C0, 0x1F8C1).addRange(0x1F900, 0x1FA53).addRange(0x1FA60, 0x1FA6D).addRange(0x1FA70, 0x1FA7C).addRange(0x1FA80, 0x1FA89).addRange(0x1FA8F, 0x1FAC6).addRange(0x1FACE, 0x1FADC);
set.addRange(0x1FADF, 0x1FAE9).addRange(0x1FAF0, 0x1FAF8).addRange(0x1FB00, 0x1FB92).addRange(0x1FB94, 0x1FBEF);
exports.characters = set;
