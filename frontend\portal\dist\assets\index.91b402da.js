/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{s as e,u as a,r as l,c as t,b as u,v as s,p as i,h as n,o as v,d as o,e as r,k as c,t as d,g as p,f as y,x as h,j as f,w as m,L as g,F as x,i as w}from"./index.2320e6b9.js";import k from"./localLogin.b992c365.js";import b from"./wechat.c1f13d45.js";import _ from"./feishu.f938b652.js";import j from"./dingtalk.6d3289a1.js";import q from"./oauth2.a56e4a54.js";import T from"./sms.1bf9553b.js";import S from"./secondaryAuth.e4383672.js";import"./verifyCode.c72175ce.js";const L={class:"login-page"},O={class:"content"},P={class:"right-panel"},C={key:0},I={key:0,class:"title"},B={key:1,class:"title"},E={style:{"text-align":"center"}},K={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},N={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},R=["xlink:href"],U={key:2,class:"login_panel_form"},z={key:3},J=["onClick"],A={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},F=["xlink:href"],M={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},V={key:1,class:"auth-waiting"},$={class:"waiting-icon"},D={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},G=["xlink:href"],H={class:"waiting-title"},Q=Object.assign({name:"Login"},{setup(Q){const W=a(),X=l(0),Y=l([]),Z=l("local"),ee=l(""),ae=l(""),le=l(""),te=l([]),ue=l([]),se=l(!1),ie=l(),ne=l(""),ve=l(!1),oe=l(""),re=l(!1),ce=l(""),de=l(""),pe=l(""),ye=l({}),he=t((()=>{const e=se.value?ce.value:ae.value;return Y.value.filter((a=>a.id!==e))})),fe=u();t((()=>ue.value.filter((e=>e.id!==ae.value))));(async()=>{var a,l,t,u,s,i,n,v,o,r,c,d;try{const p=(()=>{const e={};if(W.query.type&&(e.type=W.query.type),W.query.wp&&(e.wp=W.query.wp),W.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(W.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const y=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===y.status){Y.value=y.data.idpList;const e=W.query.idp_id||fe.loginType;if(e&&"undefined"!==e){let n=!1;for(const a of y.data.idpList)e===a.id&&(n=!0,ae.value=a.id,Z.value=a.type,ee.value=a.templateType,te.value=a.attrs,te.value.name=a.name,te.value.authType=a.type);n||(le.value=null==(a=Y.value[0])?void 0:a.id,ae.value=null==(l=Y.value[0])?void 0:l.id,Z.value=null==(t=Y.value[0])?void 0:t.type,ee.value=null==(u=Y.value[0])?void 0:u.templateType,te.value=null==(s=Y.value[0])?void 0:s.attrs,te.value.name=Y.value[0].name,te.value.authType=null==(i=Y.value[0])?void 0:i.type)}else le.value=null==(n=Y.value[0])?void 0:n.id,ae.value=null==(v=Y.value[0])?void 0:v.id,Z.value=null==(o=Y.value[0])?void 0:o.type,ee.value=null==(r=Y.value[0])?void 0:r.templateType,te.value=null==(c=Y.value[0])?void 0:c.attrs,te.value.name=Y.value[0].name,te.value.authType=null==(d=Y.value[0])?void 0:d.type;++X.value}}catch(p){console.error(p)}})();const me=t((()=>{switch(Z.value){case"local":case"msad":case"ldap":case"web":case"email":return k;case"qiyewx":return b;case"feishu":return _;case"dingtalk":return j;case"oauth2":case"cas":return q;case"sms":return T;default:return"oauth2"===ee.value?q:"local"}})),ge=t((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===oe.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===oe.value}])),xe=()=>{se.value=!1,ue.value=[],ie.value="",ne.value="",oe.value="",re.value=!1,ce.value&&(ae.value=ce.value,Z.value=de.value,ee.value=pe.value,te.value={...ye.value},ce.value="",de.value="",pe.value="",ye.value={}),++X.value,console.log("取消后恢复的状态:",{isSecondary:se.value,auth_id:ae.value,auth_type:Z.value})},we=async e=>{const a=g.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=W.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},ke=t((()=>!["dingtalk","feishu","qiyewx"].includes(Z.value)&&("oauth2"!==ee.value&&"cas"!==Z.value||("cas"===Z.value?1===parseInt(te.value.casOpenType):"oauth2"===ee.value&&1===parseInt(te.value.oauth2OpenType))))),be=e=>{le.value=e.id,te.value=e.attrs||{},te.value.name=e.name,te.value.authType=e.type,se.value&&(te.value.uniqKey=ie.value,te.value.notPhone=ve.value),ae.value=e.id,Z.value=e.type,ee.value=e.templateType,++X.value};return s(se,(async(e,a)=>{se.value&&(ce.value=ae.value,de.value=Z.value,pe.value=ee.value,ye.value={...te.value},console.log("二次认证数据:",{secondary:ue.value,secondaryLength:ue.value.length}),ue.value.length>0&&be(ue.value[0]))})),i("secondary",ue),i("isSecondary",se),i("uniqKey",ie),i("userName",ne),i("notPhone",ve),i("last_id",le),i("contactType",oe),i("hasContactInfo",re),(e,a)=>{const l=n("base-divider"),t=n("base-avatar"),u=n("base-carousel-item"),s=n("base-carousel");return v(),o("div",L,[r("div",O,[a[3]||(a[3]=r("div",{class:"left-panel"},null,-1)),r("div",P,[se.value?(v(),o("div",V,[r("div",$,[(v(),o("svg",D,[r("use",{"xlink:href":`#icon-auth-${de.value||Z.value}`},null,8,G)]))]),r("h4",H,d(ye.value.name||te.value.name)+" 登录成功",1),a[1]||(a[1]=r("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),a[2]||(a[2]=r("div",{class:"security-tips"},[r("i",{class:"el-icon-shield",style:{color:"#67c23a"}}),r("span",null,"为了您的账户安全，请完成二次身份验证")],-1))])):(v(),o("div",C,["local"===Z.value?(v(),o("span",I,"本地账号登录")):ke.value?(v(),o("span",B,[r("div",E,[r("span",K,[(v(),o("svg",N,[r("use",{"xlink:href":"#icon-auth-"+Z.value},null,8,R)])),c(" "+d(te.value.name),1)])])])):p("",!0),ae.value?(v(),o("div",U,[(v(),y(h(me.value),{auth_id:ae.value,auth_info:te.value},null,8,["auth_id","auth_info"]))])):p("",!0),he.value.length>0?(v(),o("div",z,[f(l,null,{default:m((()=>a[0]||(a[0]=[r("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)]))),_:1,__:[0]}),(v(),y(s,{key:X.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:m((()=>[(v(!0),o(x,null,w(Math.ceil(he.value.length/2),(e=>(v(),y(u,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:m((()=>[(v(!0),o(x,null,w(he.value.slice(2*(e-1),2*(e-1)+2),(e=>(v(),o("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:a=>be(e)},[r("div",null,[f(t,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:m((()=>[(v(),o("svg",A,[r("use",{"xlink:href":"#icon-auth-"+e.type},null,8,F)]))])),_:2},1024)]),r("div",M,d(e.name),1)],8,J)))),128))])),_:2},1024)))),128))])),_:1}))])):p("",!0)]))])]),se.value?(v(),y(S,{key:0,"auth-info":{uniqKey:ie.value,contactType:oe.value,hasContactInfo:re.value},"auth-id":ae.value,"user-name":ne.value,"last-id":le.value,"auth-methods":ge.value,onVerificationSuccess:we,onCancel:xe},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):p("",!0)])}}});export{Q as default};
