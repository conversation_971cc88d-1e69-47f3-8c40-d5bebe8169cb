/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var l,t,o="function"==typeof Symbol?Symbol:{},u=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function r(e,o,u,a){var r=o&&o.prototype instanceof c?o:c,s=Object.create(r.prototype);return n(s,"_invoke",function(e,n,o){var u,a,r,c=0,s=o||[],d=!1,p={p:0,n:0,v:l,a:f,f:f.bind(l,4),d:function(e,n){return u=e,a=0,r=l,p.n=n,i}};function f(e,n){for(a=e,r=n,t=0;!d&&c&&!o&&t<s.length;t++){var o,u=s[t],f=p.p,m=u[2];e>3?(o=m===n)&&(r=u[(a=u[4])?5:(a=3,3)],u[4]=u[5]=l):u[0]<=f&&((o=e<2&&f<u[1])?(a=0,p.v=n,p.n=u[1]):f<m&&(o=e<3||u[0]>n||n>m)&&(u[4]=e,u[5]=n,p.n=m,a=0))}if(o||e>1)return i;throw d=!0,n}return function(o,s,m){if(c>1)throw TypeError("Generator is already running");for(d&&1===s&&f(s,m),a=s,r=m;(t=a<2?l:r)||!d;){u||(a?a<3?(a>1&&(p.n=-1),f(a,r)):p.n=r:p.v=r);try{if(c=2,u){if(a||(o="next"),t=u[o]){if(!(t=t.call(u,r)))throw TypeError("iterator result is not an object");if(!t.done)return t;r=t.value,a<2&&(a=0)}else 1===a&&(t=u.return)&&t.call(u),a<2&&(r=TypeError("The iterator does not provide a '"+o+"' method"),a=1);u=l}else if((t=(d=p.n<0)?r:e.call(n,p))!==i)break}catch(t){u=l,a=1,r=t}finally{c=1}}return{value:t,done:d}}}(e,u,a),!0),s}var i={};function c(){}function s(){}function d(){}t=Object.getPrototypeOf;var p=[][u]?t(t([][u]())):(n(t={},u,(function(){return this})),t),f=d.prototype=c.prototype=Object.create(p);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,n(e,a,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=d,n(f,"constructor",d),n(d,"constructor",s),s.displayName="GeneratorFunction",n(d,a,"GeneratorFunction"),n(f),n(f,a,"Generator"),n(f,u,(function(){return this})),n(f,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:r,m:m}})()}function n(e,l,t,o){var u=Object.defineProperty;try{u({},"",{})}catch(e){u=0}n=function(e,l,t,o){if(l)u?u(e,l,{value:t,enumerable:!o,configurable:!o,writable:!o}):e[l]=t;else{var a=function(l,t){n(e,l,(function(e){return this._invoke(l,t,e)}))};a("next",0),a("throw",1),a("return",2)}},n(e,l,t,o)}function l(e,n,l,t,o,u,a){try{var r=e[u](a),i=r.value}catch(e){return void l(e)}r.done?n(i):Promise.resolve(i).then(t,o)}System.register(["./stringFun-legacy.41a4a108.js","./index-legacy.dbc04544.js","./autoCode-legacy.8a6c710a.js"],(function(n,t){"use strict";var o,u,a,r,i,c,s,d,p,f,m,b,y,v,h,V,g,k=document.createElement("style");return k.textContent='@charset "UTF-8";.plug-form[data-v-634ba231]{width:680px}.plug-row[data-v-634ba231]{display:flex;align-items:center;width:100%}.plug-row+.plug-row[data-v-634ba231]{margin-top:12px}.plug-row>span[data-v-634ba231]{margin-left:8px}\n',document.head.appendChild(k),{setters:[function(e){o=e.b},function(e){u=e._,a=e.B,r=e.h,i=e.o,c=e.d,s=e.e,d=e.j,p=e.w,f=e.f,m=e.F,b=e.i,y=e.g,v=e.k,h=e.M,V=e.P},function(e){g=e.k}],execute:function(){var t={class:"gva-table-box"},k={__name:"autoPlug",setup:function(n){var u=a({plugName:"",routerGroup:"",hasGlobal:!0,hasRequest:!0,hasResponse:!0,global:[{key:"",type:"",desc:""}],request:[{key:"",type:"",desc:""}],response:[{key:"",type:"",desc:""}]}),k=function(){u.plugName=o(u.plugName)},_=function(){var n,t=(n=e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:if(u.plugName&&u.routerGroup){e.n=1;break}return h.error("插件名称和插件路由组为必填项"),e.a(2);case 1:if(!u.hasGlobal){e.n=2;break}if(!u.global.some((function(e){if(!e.key||!e.type)return!0}))){e.n=2;break}return h.error("全局属性的key和type为必填项"),e.a(2);case 2:if(!u.hasRequest){e.n=3;break}if(!u.request.some((function(e){if(!e.key||!e.type)return!0}))){e.n=3;break}return h.error("请求属性的key和type为必填项"),e.a(2);case 3:if(!u.hasResponse){e.n=4;break}if(!u.response.some((function(e){if(!e.key||!e.type)return!0}))){e.n=4;break}return h.error("响应属性的key和type为必填项"),e.a(2);case 4:return e.n=5,g(u);case 5:0===e.v.code&&V("创建成功，插件已自动写入后端plugin目录下，请按照自己的逻辑进行创造");case 6:return e.a(2)}}),n)})),function(){var e=this,t=arguments;return new Promise((function(o,u){var a=n.apply(e,t);function r(e){l(a,o,u,r,i,"next",e)}function i(e){l(a,o,u,r,i,"throw",e)}r(void 0)}))});return function(){return t.apply(this,arguments)}}(),U=function(e){e.push({key:"",value:""})},w=function(e,n){1!==e.length?e.splice(n,1):h.warning("至少有一个全局属性")};return function(e,n){var l=r("base-input"),o=r("base-form-item"),a=r("base-checkbox"),h=r("base-option"),V=r("base-select"),g=r("base-button"),G=r("base-form");return i(),c("div",null,[s("div",t,[d(G,{"label-width":"140px",class:"plug-form"},{default:p((function(){return[d(o,{label:"插件名"},{default:p((function(){return[d(l,{modelValue:u.plugName,"onUpdate:modelValue":n[0]||(n[0]=function(e){return u.plugName=e}),placeholder:"必填（英文大写字母开头）",onBlur:k},null,8,["modelValue"])]})),_:1}),d(o,{label:"路由组"},{default:p((function(){return[d(l,{modelValue:u.routerGroup,"onUpdate:modelValue":n[1]||(n[1]=function(e){return u.routerGroup=e}),placeholder:"将会作为插件路由组使用"},null,8,["modelValue"])]})),_:1}),d(o,{label:"使用全局属性"},{default:p((function(){return[d(a,{modelValue:u.hasGlobal,"onUpdate:modelValue":n[2]||(n[2]=function(e){return u.hasGlobal=e})},null,8,["modelValue"])]})),_:1}),u.hasGlobal?(i(),f(o,{key:0,label:"全局属性"},{default:p((function(){return[(i(!0),c(m,null,b(u.global,(function(t,o){return i(),c("div",{key:o,class:"plug-row"},[s("span",null,[d(l,{modelValue:t.key,"onUpdate:modelValue":function(e){return t.key=e},placeholder:"key 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(V,{modelValue:t.type,"onUpdate:modelValue":function(e){return t.type=e},placeholder:"type 必填"},{default:p((function(){return[d(h,{label:"string",value:"string"}),d(h,{label:"int",value:"int"}),d(h,{label:"float32",value:"float32"}),d(h,{label:"float64",value:"float64"}),d(h,{label:"bool",value:"bool"})]})),_:2},1032,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(l,{modelValue:t.desc,"onUpdate:modelValue":function(e){return t.desc=e},placeholder:"备注 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(g,{icon:e.Plus,circle:"",onClick:n[3]||(n[3]=function(e){return U(u.global)})},null,8,["icon"])]),s("span",null,[d(g,{icon:e.Minus,circle:"",onClick:function(e){return w(u.global,o)}},null,8,["icon","onClick"])])])})),128))]})),_:1})):y("",!0),d(o,{label:"使用Request"},{default:p((function(){return[d(a,{modelValue:u.hasRequest,"onUpdate:modelValue":n[4]||(n[4]=function(e){return u.hasRequest=e})},null,8,["modelValue"])]})),_:1}),u.hasRequest?(i(),f(o,{key:1,label:"Request"},{default:p((function(){return[(i(!0),c(m,null,b(u.request,(function(t,o){return i(),c("div",{key:o,class:"plug-row"},[s("span",null,[d(l,{modelValue:t.key,"onUpdate:modelValue":function(e){return t.key=e},placeholder:"key 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(V,{modelValue:t.type,"onUpdate:modelValue":function(e){return t.type=e},placeholder:"type 必填"},{default:p((function(){return[d(h,{label:"string",value:"string"}),d(h,{label:"int",value:"int"}),d(h,{label:"float32",value:"float32"}),d(h,{label:"float64",value:"float64"}),d(h,{label:"bool",value:"bool"})]})),_:2},1032,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(l,{modelValue:t.desc,"onUpdate:modelValue":function(e){return t.desc=e},placeholder:"备注 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(g,{icon:e.Plus,circle:"",onClick:n[5]||(n[5]=function(e){return U(u.request)})},null,8,["icon"])]),s("span",null,[d(g,{icon:e.Minus,circle:"",onClick:function(e){return w(u.request,o)}},null,8,["icon","onClick"])])])})),128))]})),_:1})):y("",!0),d(o,{label:"使用Response"},{default:p((function(){return[d(a,{modelValue:u.hasResponse,"onUpdate:modelValue":n[6]||(n[6]=function(e){return u.hasResponse=e})},null,8,["modelValue"])]})),_:1}),u.hasResponse?(i(),f(o,{key:2,label:"Response"},{default:p((function(){return[(i(!0),c(m,null,b(u.response,(function(t,o){return i(),c("div",{key:o,class:"plug-row"},[s("span",null,[d(l,{modelValue:t.key,"onUpdate:modelValue":function(e){return t.key=e},placeholder:"key 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(V,{modelValue:t.type,"onUpdate:modelValue":function(e){return t.type=e},placeholder:"type 必填"},{default:p((function(){return[d(h,{label:"string",value:"string"}),d(h,{label:"int",value:"int"}),d(h,{label:"float32",value:"float32"}),d(h,{label:"float64",value:"float64"}),d(h,{label:"bool",value:"bool"})]})),_:2},1032,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(l,{modelValue:t.desc,"onUpdate:modelValue":function(e){return t.desc=e},placeholder:"备注 必填"},null,8,["modelValue","onUpdate:modelValue"])]),s("span",null,[d(g,{icon:e.Plus,circle:"",onClick:n[7]||(n[7]=function(e){return U(u.response)})},null,8,["icon"])]),s("span",null,[d(g,{icon:e.Minus,circle:"",onClick:function(e){return w(u.response,o)}},null,8,["icon","onClick"])])])})),128))]})),_:1})):y("",!0),d(o,null,{default:p((function(){return[d(g,{type:"primary",onClick:_},{default:p((function(){return n[8]||(n[8]=[v("创建")])})),_:1,__:[8]})]})),_:1})]})),_:1})])])}}};n("default",u(k,[["__scopeId","data-v-634ba231"]]))}}}))}();
