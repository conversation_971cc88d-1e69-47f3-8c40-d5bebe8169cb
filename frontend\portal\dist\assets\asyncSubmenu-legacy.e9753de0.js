/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
System.register(["./index-legacy.11b10372.js"],(function(e,t){"use strict";var n,a,o,u,r,i,c,l,f,s,d,m,v,p,_,b,x=document.createElement("style");return x.textContent='@charset "UTF-8";.el-sub-menu[data-v-1f4df5c8] .el-sub-menu__title{padding:6px;color:var(--1f4df5c8-normalText);color:rgba(255,255,255,.675)}.el-sub-menu[data-v-1f4df5c8] .el-sub-menu__title .el-sub-menu__icon-arrow{transform:rotate(0)}.el-sub-menu[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title .el-sub-menu__icon-arrow{transform:rotate(-90deg)}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title{padding-left:18px!important;flex:1;opacity:100%;height:40px;line-height:40px;border-left:4px #71BDDF solid;background:#465566!important;border-radius:4px}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title .el-sub-menu__icon-arrow{transform:rotate(-90deg)}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title i{color:var(--1f4df5c8-activeText)}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title span{opacity:100%;color:var(--1f4df5c8-activeText)}.is-active[data-v-1f4df5c8]:not(.is-opened){background:#465566!important}\n',document.head.appendChild(x),{setters:[function(e){n=e._,a=e.I,o=e.r,u=e.v,r=e.h,i=e.o,c=e.g,l=e.w,f=e.d,s=e.j,d=e.C,m=e.f,v=e.e,p=e.t,_=e.F,b=e.O}],execute:function(){var t={key:0,class:"gva-subMenu"},x=Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup:function(e){a((function(e){return{"1f4df5c8-normalText":h.value,"1f4df5c8-activeText":g.value}}));var n=e,x=o(n.theme.activeBackground),g=o(n.theme.activeText),h=o(n.theme.normalText);return u((function(){return n.theme}),(function(){x.value=n.theme.activeBackground,g.value=n.theme.activeText,h.value=n.theme.normalText})),function(n,a){var o=r("component"),u=r("el-icon"),x=r("el-sub-menu");return i(),c(x,{ref:"subMenu",index:e.routerInfo.name},{title:l((function(){return[e.isCollapse?(i(),f(_,{key:1},[e.routerInfo.meta.icon?(i(),c(u,{key:0},{default:l((function(){return[s(o,{class:d(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])]})),_:1})):m("v-if",!0),v("span",null,p(e.routerInfo.meta.title),1)],64)):(i(),f("div",t,[e.routerInfo.meta.icon?(i(),c(u,{key:0},{default:l((function(){return[s(o,{class:d(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])]})),_:1})):m("v-if",!0),v("span",null,p(e.routerInfo.meta.title),1)]))]})),default:l((function(){return[b(n.$slots,"default",{},void 0,!0)]})),_:3},8,["index"])}}});e("default",n(x,[["__scopeId","data-v-1f4df5c8"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/asyncSubmenu.vue"]]))}}}));
