/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{_ as a,g as e,a1 as s,r as l,j as o,o as u,a as n,i as c,w as t,W as r,b as v,F as i,h as d,l as p,d as b,Z as m,T as f,E as g,e as h,S as k,K as y}from"./index.bfaf04e1.js";import x from"./index.dba87d46.js";const I={class:"search-component"},_={class:"transition-box",style:{display:"inline-block"}},j={key:0,class:"user-box"},w={key:1,class:"user-box"},C={key:2,class:"user-box"},B={key:3,class:"user-box"},T=a(Object.assign({name:"BtnBox"},{setup(a){const T=e(),V=s(),q=l(""),E=()=>{T.push({name:q.value}),q.value=""},F=l(!1),K=l(!0),L=()=>{F.value=!1,setTimeout((()=>{K.value=!0}),500)},O=l(null),S=async()=>{K.value=!1,F.value=!0,await k(),O.value.focus()},U=l(!1),W=()=>{U.value=!0,y.emit("reload"),setTimeout((()=>{U.value=!1}),500)},Z=()=>{window.open("https://support.qq.com/product/371961")};return(a,e)=>{const s=o("base-option"),l=o("base-select");return u(),n("div",I,[c(f,{name:"el-fade-in-linear"},{default:t((()=>[r(v("div",_,[c(l,{ref_key:"searchInput",ref:O,modelValue:q.value,"onUpdate:modelValue":e[0]||(e[0]=a=>q.value=a),filterable:"",placeholder:"请选择",onBlur:L,onChange:E},{default:t((()=>[(u(!0),n(i,null,d(p(V).routerList,(a=>(u(),b(s,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])],512),[[m,F.value]])])),_:1}),K.value?(u(),n("div",j,[v("div",{class:g(["gvaIcon gvaIcon-refresh",[U.value?"reloading":""]]),onClick:W},null,2)])):h("",!0),K.value?(u(),n("div",w,[v("div",{class:"gvaIcon gvaIcon-search",onClick:S})])):h("",!0),K.value?(u(),n("div",C,[c(x,{class:"search-icon",style:{cursor:"pointer"}})])):h("",!0),K.value?(u(),n("div",B,[v("div",{class:"gvaIcon gvaIcon-customer-service",onClick:Z})])):h("",!0)])}}}),[["__scopeId","data-v-97ccbcef"]]);export{T as default};
