/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import"./iconfont.2d75af05.js";import{r as e,u as a,h as t,o as i,d as l,e as s,k as n,t as d,j as r,w as o,f as u,g as p,s as v,n as c,M as y,v as g}from"./index.74d1ee23.js";const h={style:{width:"100%",height:"100%",background:"#FFFFFF"}},x={style:{width:"442px",height:"175px","padding-left":"38.5%","padding-top":"21%","text-align":"center"}},f={style:{"font-size":"24px",display:"flex","justify-content":"center","align-items":"center"}},m={class:"icon",style:{"margin-right":"10px","font-size":"14px",width:"24px",height:"24px"},"aria-hidden":"true"},_={key:0,style:{"margin-top":"20px"}},w={style:{float:"left","margin-left":"34px"}},b={key:1,style:{"margin-top":"20px"}},k=Object.assign({name:"Appverify"},{setup(k){const q=e(),F=e({}),j=a();(async()=>{var e;const a={user_id:j.query.user_id,idp_id:j.query.idp_id},t=await v(a);200===t.status&&(F.value=t.data.data,(null==(e=F.value)?void 0:e.notPhone)||await P())})();const z=e(60);let C;const N=()=>{clearInterval(C)},P=async()=>{const e={uniq_key:F.value.uniqKey,idp_id:j.query.idp_id},a=await c(e);200===a.status&&-1!==a.data.code?(z.value=60,C=setInterval((()=>{z.value--,0===z.value&&N()}),1e3)):(y({showClose:!0,message:a.data.msg,type:"error"}),z.value=0)},V=async()=>{const e={uniq_key:F.value.uniqKey,auth_code:q.value,user_name:F.value.userName,idp_id:j.query.idp_id,redirect_uri:"app_redirect",grant_type:"implicit",client_id:"client_portal"},a=await g(e);200===a.status&&-1!==a.data.code?location.href=j.query.redirect_url:y({showClose:!0,message:a.data.msg,type:"error"})};return(e,a)=>{var v,c,y,g;const k=t("base-input"),j=t("base-button");return i(),l("div",h,[s("div",x,[s("div",f,[(i(),l("svg",m,a[1]||(a[1]=[n("--\x3e "),s("use",{"xlink:href":"#icon-shuoming2"},null,-1)]))),a[2]||(a[2]=n("--\x3e 该应用需通过安全认证后才可继续访问 "))]),!1===(null==(v=F.value)?void 0:v.notPhone)?(i(),l("div",_,[s("span",w,"验证码已发送至您的账号("+d(null==(c=F.value)?void 0:c.userName)+")关联的手机，请注意查收",1),r(k,{modelValue:q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>q.value=e),placeholder:"请输入短信验证码",style:{float:"left","margin-left":"34px","font-size":"12px","margin-top":"12px",width:"258px",height:"32px"},class:"input-with-select"},null,8,["modelValue"]),r(j,{style:{"border-radius":"4px","font-size":"12px",float:"left","margin-top":"12px",position:"relative","margin-left":"10px",width:"92px",height:"32px"},disabled:z.value>0,onClick:P},{default:o((()=>[n("重新发送 "+d(z.value>0?`(${z.value}秒)`:""),1)])),_:1},8,["disabled"])])):(i(),l("div",b,[s("span",null,"您的账号("+d(null==(y=F.value)?void 0:y.userName)+")未关联手机号码，请联系管理员",1)])),!1===(null==(g=F.value)?void 0:g.notPhone)?(i(),u(j,{key:2,type:"primary",size:"large",style:{float:"left","margin-left":"34px",height:"44px","margin-top":"14px",width:"365px"},disabled:!q.value,onClick:V},{default:o((()=>a[3]||(a[3]=[n("确 定 ")]))),_:1,__:[3]},8,["disabled"])):p("",!0)])])}}});export{k as default};
