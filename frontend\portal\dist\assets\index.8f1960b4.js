/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import a from"./header.b3e5db44.js";import s from"./menu.719f4d48.js";import{h as e,o as t,d as o,j as r,e as i,f as l}from"./index.2320e6b9.js";import"./ASD.492c8837.js";const u={class:"layout-page"},d={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},n=Object.assign({name:"Client"},{setup:n=>(n,p)=>{const c=e("router-view");return t(),o("div",u,[r(a),i("div",d,[r(s),i("div",m,[(t(),l(c,{key:n.$route.fullPath}))])])])}});export{n as default};
