/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as r,u as a,a as e,b as t,r as s,E as i,p as c,f as o,L as l,M as n}from"./index.4982c0f9.js";import"./iconfont.2d75af05.js";const u=r(Object.assign({name:"WxOAuthCallback"},{setup(r){const u=a(),p=e(),y=t(),{code:d,state:f,auth_type:_,redirect_url:h}=u.query,m=s(Array.isArray(f)?f[0]:f),x=s("");return i((async()=>{const r=l.service({fullscreen:!0,text:"登录中，请稍候..."});try{const r={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(r,"qiyewx_oauth",m.value)?await p.push({name:"verify",query:{redirect_url:h}}):n.error("登录失败，请重试")}catch(a){console.error("登录过程出错:",a),n.error("登录过程出错，请重试")}finally{r.close()}})),c("userName",x),(r,a)=>o(" 空模板，因为所有逻辑都在 script 中处理 ")}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/wx_oauth_callback.vue"]]);export{u as default};
