/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(t,o,i,a){var c=o&&o.prototype instanceof f?o:f,s=Object.create(c.prototype);return n(s,"_invoke",function(t,n,o){var i,a,c,f=0,s=o||[],l=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,a=0,c=e,d.n=n,u}};function p(t,n){for(a=t,c=n,r=0;!l&&f&&!o&&r<s.length;r++){var o,i=s[r],p=d.p,h=i[2];t>3?(o=h===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=t<2&&p<i[1])?(a=0,d.v=n,d.n=i[1]):p<h&&(o=t<3||i[0]>n||n>h)&&(i[4]=t,i[5]=n,d.n=h,a=0))}if(o||t>1)return u;throw l=!0,n}return function(o,s,h){if(f>1)throw TypeError("Generator is already running");for(l&&1===s&&p(s,h),a=s,c=h;(r=a<2?e:c)||!l;){i||(a?a<3?(a>1&&(d.n=-1),p(a,c)):d.n=c:d.v=c);try{if(f=2,i){if(a||(o="next"),r=i[o]){if(!(r=r.call(i,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,a<2&&(a=0)}else 1===a&&(r=i.return)&&r.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=e}else if((r=(l=d.n<0)?c:t.call(n,d))!==u)break}catch(r){i=e,a=1,c=r}finally{f=1}}return{value:r,done:l}}}(t,i,a),!0),s}var u={};function f(){}function s(){}function l(){}r=Object.getPrototypeOf;var d=[][i]?r(r([][i]())):(n(r={},i,(function(){return this})),r),p=l.prototype=f.prototype=Object.create(d);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,n(t,a,"GeneratorFunction")),t.prototype=Object.create(p),t}return s.prototype=l,n(p,"constructor",l),n(l,"constructor",s),s.displayName="GeneratorFunction",n(l,a,"GeneratorFunction"),n(p),n(p,a,"Generator"),n(p,i,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:c,m:h}})()}function n(t,e,r,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}n=function(t,e,r,o){if(e)i?i(t,e,{value:r,enumerable:!o,configurable:!o,writable:!o}):t[e]=r;else{var a=function(e,r){n(t,e,(function(t){return this._invoke(e,r,t)}))};a("next",0),a("throw",1),a("return",2)}},n(t,e,r,o)}function e(t,n,e,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void e(t)}c.done?n(u):Promise.resolve(u).then(r,o)}function r(t){return function(){var n=this,r=arguments;return new Promise((function(o,i){var a=t.apply(n,r);function c(t){e(a,o,i,c,u,"next",t)}function u(t){e(a,o,i,c,u,"throw",t)}c(void 0)}))}}System.register(["./index-legacy.dbc04544.js"],(function(n,e){"use strict";var o,i,a,c,u,f,s,l,d;return{setters:[function(t){o=t.a,i=t.r,a=t.b,c=t.y,u=t.o,f=t.d,s=t.e,l=t.k,d=t.D}],execute:function(){var e={style:{"text-align":"center"}},p={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},h={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},y={name:"Dingtalk",mounted:function(){this.loadThirdPartyScript()},methods:{loadThirdPartyScript:function(){var t=this,n=document.createElement("script");n.src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js",n.onload=function(){t.doSomethingWithThirdPartyLibrary()},document.body.appendChild(n)}}};n("default",Object.assign(y,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup:function(n){var y=o(),v=i(0),g=n,m=function(){var n=r(t().m((function n(){var e,r;return t().w((function(t){for(;;)switch(t.n){case 0:return e={type:"dingtalk",data:{idpId:g.auth_id}},t.n=1,d(e);case 1:if(200!==(r=t.v).status){t.n=2;break}return t.a(2,r.data.uniqKey);case 2:return t.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();a();var b=function(){var n=r(t().m((function n(){var e,r,o,i;return t().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,m();case 1:e=g.auth_info.dingtalkAppKey,r=window.location.host,o=window.location.protocol,i="".concat(o,"//").concat(r),setTimeout((function(){window.DTFrameLogin({id:"self_defined_element",width:300,height:300},{redirect_uri:encodeURIComponent(i),client_id:e,scope:"openid",response_type:"code",state:g.auth_id,prompt:"consent"},(function(t){t.redirectUrl;var n=t.authCode,e=t.state;y.push({name:"Status",query:{code:n,state:e,auth_type:"dingtalk"},replace:!0})}),(function(t){t&&console.error("钉钉登录错误:",t)}))}),100);case 2:return t.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();return b(),c(g,function(){var n=r(t().m((function n(e,r){return t().w((function(t){for(;;)switch(t.n){case 0:return v.value++,t.n=1,b();case 1:return t.a(2)}}),n)})));return function(t,e){return n.apply(this,arguments)}}()),function(t,n){return u(),f("div",{key:v.value},[s("div",e,[s("span",p,[(u(),f("svg",h,n[0]||(n[0]=[s("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),n[1]||(n[1]=l(" 钉钉认证 "))])]),n[2]||(n[2]=s("div",{id:"self_defined_element",class:"self-defined-classname"},null,-1))])}}}))}}}))}();
