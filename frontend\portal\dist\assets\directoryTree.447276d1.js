/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import"./iconfont.2d75af05.js";import{l as e,h as o,o as l,d as n,f as a,w as i,e as t,j as s,t as c,g as d,m as r,_ as p}from"./index.74d1ee23.js";const u={style:{width:"200px",height:"calc(100% - 50px)"}},y=["onClick"],g={style:{position:"absolute","margin-left":"17px"}},k={class:"tree-operate"},f=p(Object.assign({name:"DirectoryTree"},{props:{loadOperate:{type:Boolean,default:!0},edit:{type:Function,required:!1},append:{type:Function,required:!1},loadNode:{type:Function,required:!1},treeProps:{type:Object,required:!0}},setup(p){const f=e("cascaderKey"),h=e("treeRef"),m=e("open"),C=e("getTableData");return(e,x)=>{const v=o("Folder"),b=o("el-icon"),F=o("el-link"),j=o("el-tree");return l(),n("div",u,[(l(),a(j,{key:r(f),icon:e.CirclePlusFilled,style:{"font-size":"12px",height:"100%",overflow:"auto","max-height":"100%"},props:p.treeProps,load:p.loadNode,lazy:"","highlight-current":"","expand-on-click-node":!1,"node-key":"id",class:"policy-tree"},{default:i((({node:o,data:n})=>[t("span",{class:"custom-tree-node",onClick:e=>(async e=>{console.log("handleNodeClick"),console.log(e),h.value===e.id?h.value="":h.value=e.id,await C()})(n)},[s(b,{class:"icon svg-icon"},{default:i((()=>[s(v)])),_:1}),t("span",g,c(o.label),1),t("span",k,[p.loadOperate?(l(),a(F,{key:0,underline:!1,icon:e.CirclePlus,onClick:e=>p.append(n)},null,8,["icon","onClick"])):d("",!0),n.loadDel?d("",!0):(l(),a(F,{key:1,underline:!1,style:{"margin-left":"5px"},icon:e.DeleteFilled,onClick:e=>((e,o)=>{console.log("remove"),console.log(o),m(o.id)})(0,n)},null,8,["icon","onClick"])),s(F,{underline:!1,style:{"margin-left":"5px"},icon:e.Edit,onClick:e=>p.edit(o,n)},null,8,["icon","onClick"])])],8,y)])),_:1},8,["icon","props","load"]))])}}}),[["__scopeId","data-v-c8609b35"]]);export{f as D};
