<template class="consumer">
  <el-tabs type="card" class="demo-tabs">
    <el-tab-pane>
      <template #label>
        <span class="custom-tabs-label">
          <span>所有用户</span>
        </span>
      </template>
      <div>
        <div style="padding-top: 5px;width: 100%;height: 37px;background: #FFFFFF">
          <base-select
              class="m-2 risk-select"
              size="small"
              style="width: 94px; float: right;height: 28px;font-size: 12px;margin-right: 5px"
              v-model="period"
              placeholder="最近90天"
          >
            <base-option
                v-for="item in periods"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="risk-option"
                style="font-size: 13px;color: rgb(215, 215, 215)"
            />
          </base-select>
          <base-button
              style="font-size: 12px;float: right;height: 28px;margin-right: 5px"
              type="primary"
              :icon="Tools"
              color="#256EBF"
          >风险设置
          </base-button>
        </div>
        <div style="width: 100%;">
          <base-row :gutter="12" class="concern">
            <base-col :span="6" style="background-color: whitesmoke">
              <base-card shadow="never" class="alarm">
                <div>
                  <div style="width: 50%;float: left">
                    <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                      <div style="float: left">
                        <el-icon style="color: #D9001B" size="25px">
                          <WarningFilled/>
                        </el-icon>
                      </div>
                      <div>
                        <span style="padding-left: 15px;font-size: 20px">5</span>
                      </div>
                    </div>
                    <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >严重风险用户</span>
                    </div>
                  </div>
                  <div style="width: 50%;float: right">
                    <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                    </div>
                    <div style="margin-top: 70px">
                      <el-link type="primary"
                               style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                      >查看用户
                      </el-link>
                    </div>

                  </div>
                </div>
              </base-card>
            </base-col>
            <base-col :span="6" style="background-color: whitesmoke">
              <base-card shadow="never" class="alarm">
                <div>
                  <div style="width: 50%;float: left">
                    <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                      <div style="float: left">
                        <el-icon style="color: #F59A23" size="25px">
                          <WarningFilled/>
                        </el-icon>
                      </div>
                      <div>
                        <span style="padding-left: 15px;font-size: 20px">5</span>
                      </div>
                    </div>
                    <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >高危风险用户</span>
                    </div>
                  </div>
                  <div style="width: 50%;float: right">
                    <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                    </div>
                    <div style="margin-top: 70px">
                      <el-link type="primary"
                               style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                      >查看用户
                      </el-link>
                    </div>

                  </div>
                </div>
              </base-card>
            </base-col>
            <base-col :span="6" style="background-color: whitesmoke">
              <base-card shadow="never" class="alarm">
                <div>
                  <div style="width: 50%;float: left">
                    <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                      <div style="float: left">
                        <el-icon style="color: #F5CA92" size="25px">
                          <WarningFilled/>
                        </el-icon>
                      </div>
                      <div>
                        <span style="padding-left: 15px;font-size: 20px">5</span>
                      </div>
                    </div>
                    <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >中危风险用户</span>
                    </div>
                  </div>
                  <div style="width: 50%;float: right">
                    <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                    </div>
                    <div style="margin-top: 70px">
                      <el-link type="primary"
                               style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                      >查看用户
                      </el-link>
                    </div>

                  </div>
                </div>
              </base-card>
            </base-col>
            <base-col :span="6" style="background-color: whitesmoke">
              <base-card shadow="never" class="alarm">
                <div>
                  <div style="width: 50%;float: left">
                    <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                      <div style="float: left">
                        <el-icon style="color: #F6E8D7" size="25px">
                          <WarningFilled/>
                        </el-icon>
                      </div>
                      <div>
                        <span style="padding-left: 15px;font-size: 20px">5</span>
                      </div>
                    </div>
                    <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >低危风险用户</span>
                    </div>
                  </div>
                  <div style="width: 50%;float: right">
                    <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                    </div>
                    <div style="margin-top: 70px">
                      <el-link type="primary"
                               style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                      >查看用户
                      </el-link>
                    </div>

                  </div>
                </div>
              </base-card>
            </base-col>
          </base-row>
        </div>
      </div>
      <div style="border: rgba(215, 215, 215, 1) 1px solid;background: #FFFFFF">
        <div style="padding: 10px 10px;border-bottom: 1px #DCDCDC solid">
          <base-button icon @click="alarmExport" style="font-size: 12px;border: 0px">
            <template #icon>
              <ExportOutlined class="alarmExport"/>
            </template>
            导出
          </base-button>
          <base-button icon style="font-size: 12px;border: 0px">
            <template #icon>
              <el-icon size="16px">
                <RefreshRight/>
              </el-icon>
            </template>
            刷新
          </base-button>
          <span
              style="margin-right: 15px;margin-top: 5px;float: right;font-size: 12px;color: #AAAAAA;font-weight: 400"
          >最近90天</span>
          <base-input
              v-model="searchUser"
              class="w-50 m-2 organize-search"
              placeholder="请输入用户名"
              :suffix-icon="Search"
              style="width: 200px;height: 26px;float: right;color: #AAAAAA;margin-right: 15px"
          />
          <base-button :icon="Filter" style="width: 83px;height:26px;float: right;margin-right: 5px;color: #AAAAAA">筛选
          </base-button>
          <base-select
              style="margin-right: 5px;
              width: 10%;
              float: right;"
              v-model="state"
              class="alarmSelect"
              placeholder="全部状态"
          >
            <base-option
                v-for="item in stateList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="risk-option"
                :disabled="item.disabled"
            />
          </base-select>

        </div>
        <el-table
            ref="multipleTableRef"
            stripe="true"
            :data="alarmList"
            class="alarmTable"
            style="width: 100%;margin-top: 5px;min-width: 1200px"
            row-class-name="alarm-table-row-style"
            header-row-class-name="alarm-table-header-style"
        >
          <el-table-column label="用户" width="180">
            <template #header>
              <div
                  style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
              ><span>用户</span></div>
            </template>
            <template #default="scope">
              <div>
                <span
                    style="font-weight: 700;font-style: normal;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                >
                  {{ scope.row.user }}
                </span>
                <el-tag
                    v-for="item of scope.row.state"
                    class="ml-2"
                    type="info"
                    style="margin-right: 50px;line-height:28px"
                    :key="item"
                >
                  {{ item }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="180">
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <el-icon style="color: #D9001B">
                  <WarningFilled/>
                </el-icon>
                严重事件
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
                <span
                    style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                >
                {{ scope.row.severity.amount }}
              </span>
                <br>
                <span
                    style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                >
                {{ scope.row.severity.size }}
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column>
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <el-icon style="color: #F59A23">
                  <WarningFilled/>
                </el-icon>
                高危事件
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.highRisk.amount }}
              </span>
                <br>
                <span
                    style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                >
                {{ scope.row.highRisk.size }}
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip>
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <el-icon style="color: #FACD91">
                  <WarningFilled/>
                </el-icon>
                中危事件
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.mediumRisk.amount }}
              </span>
                <br>
                <span
                    style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                >
                {{ scope.row.mediumRisk.size }}
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip>
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <el-icon style="color: #D7D7D7">
                  <WarningFilled/>
                </el-icon>
                低危事件
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.lowRisk.amount }}
              </span>
                <br>
                <span
                    style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                >
                {{ scope.row.lowRisk.size }}
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="外泄途径指标" show-overflow-tooltip>
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <span>外泄途径指标</span>
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
              <span
                  v-for="(item,index) in scope.row.leakedWay"
                  :key="index"
                  style="
                    color: rgba(0, 0, 0, 0.701960784313725);
                    font-size: 12px;
                    font-weight: 400;
                    font-style: normal;
                    "
              >
                {{ item }}<br>
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="数据类型指标" show-overflow-tooltip>
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <span>数据类型指标</span>
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
              <span
                  v-for="(item,index) in scope.row.dataType"
                  :key="index"
                  style="
                    color: rgba(0, 0, 0, 0.701960784313725);
                    font-size: 12px;
                    font-weight: 400;
                    font-style: normal;
                    "
              >
                {{ item }}<br>
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="用户行为指标" show-overflow-tooltip>
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <span>用户行为指标</span>
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
              <span
                  v-for="(item,index) in scope.row.userBehavior"
                  :key="index"
                  style="
                    color: rgba(0, 0, 0, 0.701960784313725);
                    font-size: 12px;
                    font-weight: 400;
                    font-style: normal;
                    "
              >
                {{ item }}<br>
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="operate" label="操作">
            <template #header>
              <div style="text-align: center;font-weight: 700;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">
                <span>操作</span>
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
                <el-link type="primary"
                         :underline="false"
                         style="
                       color: rgba(2, 167, 240, 0.996078431372549);
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700"
                         @click="details(scope.row)"
                >
                  查看详情
                </el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
            v-model:currentPage="currentPage4"
            v-model:page-size="pageSize4"
            :page-sizes="[100, 200, 300, 400]"
            :small="small"
            :disabled="disabled"
            :background="background"
            layout="total, sizes, prev, pager, next, jumper"
            :total="10000"
            style="float: right"
            class="risk-pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-tab-pane>
    <el-tab-pane label="关注用户">
      <template #label>
        <span class="custom-tabs-label">
          <span>关注用户</span>
        </span>
      </template>
      <div style="padding-top: 10px;width: 220px;height: 100%;background-color: #FFFFFF;float: left">
        <span style="color: #333333;padding-left: 5px;font-size: 16px">关注用户</span>
        <el-icon style="float: right" size="18px">
          <Fold/>
        </el-icon>
        <base-radio-group v-model="userState" style="height: 100%;padding-top: 80px" class="concern-radio-group">
          <el-radio-button border="false" class="concern-radio" label="1">
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">离职员工</span>
            <span style="float: right;width: 10px;font-size: 15px;color: #333333">5</span>
            <el-icon style="color: red;float: right;padding-right: 5px">
              <WarningFilled/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="2">
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">离职倾向</span>
            <span style="float: right;width: 10px;font-size: 15px;color: #333333">5</span>
            <el-icon style="color: red;float: right;padding-right: 5px">
              <WarningFilled/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="3">
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">新入职</span>
            <span style="float: right;width: 10px;font-size: 15px;color: #333333">5</span>
            <el-icon style="color: red;float: right;padding-right: 5px">
              <WarningFilled/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="4">
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">合作方</span>
            <span style="float: right;width: 10px;font-size: 15px;color: #333333">5</span>
            <el-icon style="color: darkorange;float: right;padding-right: 5px">
              <WarningFilled/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="5">
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">外包团队</span>
            <span style="float: right;width: 10px;font-size: 15px;color: #333333">5</span>
            <el-icon style="color: red;float: right;padding-right: 5px">
              <WarningFilled/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="6">
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">远程办公</span>
            <span style="float: right;width: 10px;font-size: 15px;color: #333333">5</span>
            <el-icon style="color: red;float: right;padding-right: 5px">
              <WarningFilled/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="7">
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">财务数据</span>
            <span style="float: right;width: 10px;font-size: 15px;color: #333333">5</span>
            <el-icon style="color: red;float: right;padding-right: 5px;">
              <WarningFilled/>
            </el-icon>
          </el-radio-button>
        </base-radio-group>
      </div>
      <div style="width: calc(100% - 225px);float: right">
        <div style="background-color: #FFFFFF;padding-top: 5px">
          <div style="width: 100%;height: 37px">
            <span
                style="padding-left: 5px;padding-top:25px;font-weight: 700;font-style: normal;font-size: 16px;color: #333333"
            >
            离职员工</span>
            <base-select
                class="m-2 risk-select"
                size="small"
                style="width: 94px; float: right;height: 28px;font-size: 13px;margin-right: 5px"
                v-model="period"
                placeholder="最近90天"
            >
              <base-option
                  v-for="item in periods"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  class="risk-option"
                  style="font-size: 13px;color: rgb(215, 215, 215)"
              />
            </base-select>
            <base-button
                style="font-size: 12px;float: right;height: 28px;margin-right: 5px"
                type="primary"
                :icon="Tools"
                color="#256EBF"
            >风险设置
            </base-button>
          </div>
          <div style="width: 100%;">
            <base-row :gutter="12" class="concern">
              <base-col :span="6" style="background-color: whitesmoke">
                <base-card shadow="never" class="alarm">
                  <div>
                    <div style="width: 50%;float: left">
                      <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                        <div style="float: left">
                          <el-icon style="color: #D9001B" size="25px">
                            <WarningFilled/>
                          </el-icon>
                        </div>
                        <div>
                          <span style="padding-left: 15px;font-size: 20px">5</span>
                        </div>
                      </div>
                      <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >严重风险用户</span>
                      </div>
                    </div>
                    <div style="width: 50%;float: right">
                      <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                      </div>
                      <div style="margin-top: 70px">
                        <el-link type="primary"
                                 style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                        >查看用户
                        </el-link>
                      </div>

                    </div>
                  </div>
                </base-card>
              </base-col>
              <base-col :span="6" style="background-color: whitesmoke">
                <base-card shadow="never" class="alarm">
                  <div>
                    <div style="width: 50%;float: left">
                      <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                        <div style="float: left">
                          <el-icon style="color: #F59A23" size="25px">
                            <WarningFilled/>
                          </el-icon>
                        </div>
                        <div>
                          <span style="padding-left: 15px;font-size: 20px">5</span>
                        </div>
                      </div>
                      <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >高危风险用户</span>
                      </div>
                    </div>
                    <div style="width: 50%;float: right">
                      <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                      </div>
                      <div style="margin-top: 70px">
                        <el-link type="primary"
                                 style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                        >查看用户
                        </el-link>
                      </div>

                    </div>
                  </div>
                </base-card>
              </base-col>
              <base-col :span="6" style="background-color: whitesmoke">
                <base-card shadow="never" class="alarm">
                  <div>
                    <div style="width: 50%;float: left">
                      <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                        <div style="float: left">
                          <el-icon style="color: #F5CA92" size="25px">
                            <WarningFilled/>
                          </el-icon>
                        </div>
                        <div>
                          <span style="padding-left: 15px;font-size: 20px">5</span>
                        </div>
                      </div>
                      <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >中危风险用户</span>
                      </div>
                    </div>
                    <div style="width: 50%;float: right">
                      <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                      </div>
                      <div style="margin-top: 70px">
                        <el-link type="primary"
                                 style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                        >查看用户
                        </el-link>
                      </div>

                    </div>
                  </div>
                </base-card>
              </base-col>
              <base-col :span="6" style="background-color: whitesmoke">
                <base-card shadow="never" class="alarm">
                  <div>
                    <div style="width: 50%;float: left">
                      <div style="margin-top: 10px;margin-bottom: 20px;margin-left: 15px">
                        <div style="float: left">
                          <el-icon style="color: #F6E8D7" size="25px">
                            <WarningFilled/>
                          </el-icon>
                        </div>
                        <div>
                          <span style="padding-left: 15px;font-size: 20px">5</span>
                        </div>
                      </div>
                      <div style="margin-bottom: 20px">
                  <span
                      style="margin-right: 20px;font-weight:400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >低危风险用户</span>
                      </div>
                    </div>
                    <div style="width: 50%;float: right">
                      <div>
                  <span
                      style="
                    float: right;
                    font-weight:400;
                    font-size: 12px;
                    color: #AAAAAA"
                  >最近90天</span>
                      </div>
                      <div style="margin-top: 70px">
                        <el-link type="primary"
                                 style="float: right;font-weight:700;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
                        >查看用户
                        </el-link>
                      </div>

                    </div>
                  </div>
                </base-card>
              </base-col>
            </base-row>
          </div>
        </div>
        <div style="border: rgba(215, 215, 215, 1) 1px solid ">
          <div style="padding: 10px 10px;border-bottom: 1px #DCDCDC solid;background-color: #FFFFFF">
            <base-button icon @click="alarmExport" style="font-size: 12px;border: 0px">
              <template #icon>
                <ExportOutlined class="alarmExport"/>
              </template>
              导出
            </base-button>
            <base-button icon style="font-size: 12px;border: 0px">
              <template #icon>
                <el-icon size="16px">
                  <RefreshRight/>
                </el-icon>
              </template>
              刷新
            </base-button>
            <span
                style="margin-right: 15px;margin-top: 5px;float: right;font-size: 12px;color: #AAAAAA;font-weight: 400"
            >最近90天</span>
            <base-input
                v-model="searchUser"
                class="w-50 m-2 organize-search"
                placeholder="请输入用户名"
                :suffix-icon="Search"
            />
            <base-button :icon="Filter" style="width: 83px;height: 26px;float: right;margin-right: 5px;color: #AAAAAA">
              筛选
            </base-button>
            <base-select
                style="margin-right: 5px;
              width: 125px;
              height:26px;
              float: right;"
                v-model="state"
                class="alarmSelect"
                placeholder="全部状态"
            >
              <base-option
                  v-for="item in stateList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
              />
            </base-select>

          </div>
          <el-table
              ref="multipleTableRef"
              stripe="true"
              :data="alarmList"
              class="alarmTable"
              style="width: 100%;margin-top: 5px;min-width: 1200px"
              row-class-name="alarm-table-row-style"
              header-row-class-name="alarm-table-header-style"
          >
            <el-table-column label="用户" width="180">
              <template #header>
                <div style="text-align: center;font-weight: 700;font-size: 12px">
                  <span>用户</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                <span
                    style="font-weight: 700;font-style: normal;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                >
                  {{ scope.row.user }}
                </span>
                  <el-tag
                      v-for="item of scope.row.state"
                      class="ml-2"
                      type="info"
                      style="margin-right: 50px;line-height:28px"
                      :key="item"
                  >
                    {{ item }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="180">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <el-icon style="color: #D9001B">
                    <WarningFilled/>
                  </el-icon>
                  严重事件
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.severity.amount }}
              </span>
                  <br>
                  <span
                      style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                  >
                {{ scope.row.severity.size }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column>
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <el-icon style="color: #F59A23">
                    <WarningFilled/>
                  </el-icon>
                  高危事件
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.highRisk.amount }}
              </span>
                  <br>
                  <span
                      style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                  >
                {{ scope.row.highRisk.size }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip>
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <el-icon style="color: #FACD91">
                    <WarningFilled/>
                  </el-icon>
                  中危事件
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.mediumRisk.amount }}
              </span>
                  <br>
                  <span
                      style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                  >
                {{ scope.row.mediumRisk.size }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip>
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <el-icon style="color: #D7D7D7">
                    <WarningFilled/>
                  </el-icon>
                  低危事件
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.lowRisk.amount }}
              </span>
                  <br>
                  <span
                      style="color:rgba(170, 170, 170, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal"
                  >
                {{ scope.row.lowRisk.size }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="外泄途径指标" show-overflow-tooltip>
              <template #header>
                <div style="text-align: center;font-weight: 700;font-size: 12px">
                  <span>外泄途径指标</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span
                  v-for="(item,index) in scope.row.leakedWay"
                  :key="index"
                  style="
                    color: rgba(0, 0, 0, 0.701960784313725);
                    font-size: 12px;
                    font-weight: 400;
                    font-style: normal;
                    "
              >
                {{ item }}<br>
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="operate" label="操作">
              <template #header>
                <div style="font-size: 12px;font-weight: 700;text-align: center">
                  <span>操作</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <el-link type="primary"
                           :underline="false"
                           style="
                       color: rgba(2, 167, 240, 0.996078431372549);
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700"
                           @click="details(scope.row)"
                  >
                    查看详情
                  </el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
              v-model:currentPage="currentPage4"
              v-model:page-size="pageSize4"
              :page-sizes="[100, 200, 300, 400]"
              :small="small"
              :disabled="disabled"
              :background="background"
              layout="total, sizes, prev, pager, next, jumper"
              :total="10000"
              class="risk-pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'DataSecurityOverview',
}
</script>
<script setup>
import {
  RefreshRight,
  Search,
  Filter,
  Tools,
} from '@element-plus/icons-vue'

const state = ref('0')
const stateList = [{
  value: '0',
  label: '全部状态',
}, {
  value: '1',
  label: '未处理',
}, {
  value: '2',
  label: '已处理',
}]

const searchUser = ref('')
const alarmList = ref([{
  user: '<EMAIL>',
  state: ['离职倾向', '新入职'],
  severity: {
    amount: '20',
    size: '15.5K',
  },
  highRisk: {
    amount: '20',
    size: '15.5K',
  },
  mediumRisk: {
    amount: '20',
    size: '15.5K',
  },
  lowRisk: {
    amount: '20',
    size: '15.5K',
  },
  leakedWay: ['百度网盘上传', 'Github 上传'],
  dataType: ['源代码', '设计文档'],
  userBehavior: ['文件分片', '文件压缩'],
}, {
  user: '<EMAIL>',
  state: ['离职中'],
  severity: {
    amount: '20',
    size: '15.5K',
  },
  highRisk: {
    amount: '20',
    size: '15.5K',
  },
  mediumRisk: {
    amount: '20',
    size: '15.5K',
  },
  lowRisk: {
    amount: '20',
    size: '15.5K',
  },
  leakedWay: ['百度网盘上传', 'Github 上传'],
  dataType: ['源代码', '设计文档'],
  userBehavior: ['文件分片', '文件压缩'],
}, {
  user: '<EMAIL>',
  state: ['离职倾向', '新入职', '合作方'],
  severity: {
    amount: '20',
    size: '15.5K',
  },
  highRisk: {
    amount: '20',
    size: '15.5K',
  },
  mediumRisk: {
    amount: '20',
    size: '15.5K',
  },
  lowRisk: {
    amount: '20',
    size: '15.5K',
  },
  leakedWay: ['百度网盘上传', 'Github 上传'],
  dataType: ['源代码', '设计文档'],
  userBehavior: ['文件分片', '文件压缩'],
}, {
  user: '<EMAIL>',
  state: ['离职倾向', '新入职'],
  severity: {
    amount: '20',
    size: '15.5K',
  },
  highRisk: {
    amount: '20',
    size: '15.5K',
  },
  mediumRisk: {
    amount: '20',
    size: '15.5K',
  },
  lowRisk: {
    amount: '20',
    size: '15.5K',
  },
  leakedWay: ['百度网盘上传', 'Github 上传'],
  dataType: ['源代码', '设计文档'],
  userBehavior: ['文件压缩'],
}, {
  user: '<EMAIL>',
  state: ['离职倾向', '新入职'],
  severity: {
    amount: '20',
    size: '15.5K',
  },
  highRisk: {
    amount: '20',
    size: '15.5K',
  },
  mediumRisk: {
    amount: '20',
    size: '15.5K',
  },
  lowRisk: {
    amount: '20',
    size: '15.5K',
  },
  leakedWay: ['百度网盘上传', 'Github 上传'],
  dataType: ['源代码', '设计文档'],
  userBehavior: ['文件分片'],
}])

const details = (data) => {
  console.log(data)
}

// 分页
const currentPage4 = ref(4)
const pageSize4 = ref(100)
const small = ref(true)
const background = ref(true)
const disabled = ref(false)
const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
}

const period = ref('1')
const periods = [{
  value: '1',
  label: '最近90天',
}, {
  value: '2',
  label: '最近30天',
}]

const userState = ref('1')
</script>
<style scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
  float: left;
}

.concern-radio {
  width: 100%;
}

</style>
<style>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

.demo-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}

.demo-tabs .custom-tabs-label span {
  vertical-align: middle;
  margin-left: 4px;
}

.demo-tabs .el-tabs__item {
  font-size: 12px;
}

.demo-tabs .el-tabs__nav {
  background-color: #FFFFFF;
}

.demo-tabs .is-active {
  background-color: rgba(37, 110, 191, 1);
  color: #FFFFFF;
  font-size: 12px;
}

.alarm .el-card__body {
  padding: 10px 20px;
}

.alarmExport > svg {
  width: 16px;
  height: 16px;
}

.alarm-table-row-style .el-table__cell div {
  line-height: 30px !important;
}

.alarm-table-header-style {
  height: 34px;
  font-size: 12px;
}

.admin-box .el-table td .cell {
  height: auto;
  line-height: 30px;
}

.admin-box .el-table td .cell div {
  height: auto;
}

.demo-tabs > .el-tabs__content {
  padding: 0px 0px;
}

.concern-radio span {
  width: 100%;
}

.risk-select > div * {
  height: 28px;
  padding-bottom: 0px !important;
  padding-top: 0px !important;
}

.risk-select > div * input {
  height: 28px !important;
  font-size: 13px;
  color: #D7D7D7;
}

.risk-option {
  color: #D7D7D7;
}

.risk-option:hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}

.risk-option.hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}
</style>
<style lang="scss">
.alarmSelect {
  height: 20px;
  width: 125px !important;

  .el-input {
    height: 26px;
    width: 125px;
  }

  .el-input__inner {
    color: #AAAAAA;
  }
}

.alarmTable th.is-leaf {
  background: #FFFFFF !important;
}

.alarmTable th, .el-table tr {
  background: #FFFFFF;
}

.risk-pagination {
  float: right;

  .el-pager {
    li {
      background-color: #FFFFFF !important;
    }

    .is-active {
      background-color: #4E8DDA !important;
    }
  }

  .btn-prev {
    background-color: #FFFFFF !important;
  }

  .btn-next {
    background-color: #FFFFFF !important;
  }
}

.concern-radio-group {
  padding-bottom: 223%;

  .is-active {
    --el-color-primary: #B2E4FB
  }
}

.concern {
  margin: 0px !important;
  background: #FFFFFF;

  .el-col {
    background-color: #FFFFFF !important;
  }

  .alarm {
    background-color: #FFFFFF;
  }
}

.demo-tabs {
  .el-tabs__header {
    margin: 0px!important;
  }

  .el-tabs__nav {
    border-bottom: 1px solid var(--el-border-color-light) !important;
  }
}
</style>
