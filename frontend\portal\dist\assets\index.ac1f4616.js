/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{W as e}from"./warningBar.4338ec87.js";import{x as a,r as l,B as t,h as o,o as s,d,j as u,e as m,w as n,k as i,M as r}from"./index.74d1ee23.js";const c=e=>a({url:"/email/emailTest",method:"post",data:e}),b={class:"gva-form-box"},f=Object.assign({name:"Email"},{setup(a){const f=l(null),p=t({to:"",subject:"",body:""}),_=async()=>{0===(await c()).code&&r.success("发送成功")},V=async()=>{0===(await c()).code&&r.success("发送成功,请查收")};return(a,l)=>{const t=o("base-input"),r=o("base-form-item"),c=o("base-button"),j=o("base-form");return s(),d("div",null,[u(e,{title:"需要提前配置email配置文件，为防止不必要的垃圾邮件，在线体验功能不开放此功能体验。"}),m("div",b,[u(j,{ref_key:"emailForm",ref:f,"label-position":"right","label-width":"80px",model:p},{default:n((()=>[u(r,{label:"目标邮箱"},{default:n((()=>[u(t,{modelValue:p.to,"onUpdate:modelValue":l[0]||(l[0]=e=>p.to=e)},null,8,["modelValue"])])),_:1}),u(r,{label:"邮件"},{default:n((()=>[u(t,{modelValue:p.subject,"onUpdate:modelValue":l[1]||(l[1]=e=>p.subject=e)},null,8,["modelValue"])])),_:1}),u(r,{label:"邮件内容"},{default:n((()=>[u(t,{modelValue:p.body,"onUpdate:modelValue":l[2]||(l[2]=e=>p.body=e),type:"textarea"},null,8,["modelValue"])])),_:1}),u(r,null,{default:n((()=>[u(c,{onClick:_},{default:n((()=>l[3]||(l[3]=[i("发送测试邮件")]))),_:1,__:[3]}),u(c,{onClick:V},{default:n((()=>l[4]||(l[4]=[i("发送邮件")]))),_:1,__:[4]})])),_:1})])),_:1},8,["model"])])])}}});export{f as default};
