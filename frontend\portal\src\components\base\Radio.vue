<template>
  <label class="base-radio" :class="{ 'is-disabled': disabled, 'is-checked': isChecked }">
    <span class="base-radio__input">
      <span class="base-radio__inner"></span>
      <input
        type="radio"
        class="base-radio__original"
        :disabled="disabled"
        :value="label"
        v-model="model"
        @change="handleChange"
      >
    </span>
    <span v-if="$slots.default || label" class="base-radio__label">
      <slot>{{ label }}</slot>
    </span>
  </label>
</template>

<script>
export default {
  name: 'BaseRadio',
  props: {
    modelValue: {
      type: [String, Number, Boolean],
      default: ''
    },
    label: {
      type: [String, Number, Boolean],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'default',
      validator: (value) => ['large', 'default', 'small'].includes(value)
    }
  },
  emits: ['update:modelValue', 'change'],
  computed: {
    model: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    },
    isChecked() {
      return this.modelValue === this.label
    }
  },
  methods: {
    handleChange(e) {
      this.$emit('change', e.target.value)
    }
  }
}
</script>

<style scoped>
.base-radio {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  user-select: none;
  margin-right: 30px;
}

.base-radio.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.base-radio__input {
  white-space: nowrap;
  cursor: pointer;
  outline: none;
  display: inline-flex;
  position: relative;
}

.base-radio__inner {
  border: 1px solid #dcdfe6;
  border-radius: 100%;
  width: 14px;
  height: 14px;
  background-color: #fff;
  position: relative;
  cursor: pointer;
  display: inline-block;
  box-sizing: border-box;
  transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
    background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}

.base-radio__inner::after {
  width: 4px;
  height: 4px;
  border-radius: 100%;
  background-color: #fff;
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.15s ease-in;
}

.base-radio.is-checked .base-radio__inner {
  border-color: #409eff;
  background: #409eff;
}

.base-radio.is-checked .base-radio__inner::after {
  transform: translate(-50%, -50%) scale(1);
}

.base-radio.is-disabled .base-radio__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.base-radio__original {
  opacity: 0;
  outline: none;
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}

.base-radio__label {
  display: inline-block;
  padding-left: 8px;
  line-height: 19px;
  font-size: 14px;
}
</style>
