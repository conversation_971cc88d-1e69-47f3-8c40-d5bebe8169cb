/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
!function(){function t(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var e,o,i,u,a=[],c=!0,f=!1;try{if(i=(r=r.call(t)).next,0===n){if(Object(r)!==r)return;c=!1}else for(;!(c=(e=i.call(r)).done)&&(a.push(e.value),a.length!==n);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(t,r)||function(t,r){if(t){if("string"==typeof t)return n(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?n(t,r):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var r=0,e=Array(n);r<n;r++)e[r]=t[r];return e}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.toStringTag||"@@toStringTag";function a(r,o,i,u){var a=o&&o.prototype instanceof f?o:f,l=Object.create(a.prototype);return e(l,"_invoke",function(r,e,o){var i,u,a,f=0,l=o||[],s=!1,d={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(n,r){return i=n,u=0,a=t,d.n=r,c}};function y(r,e){for(u=r,a=e,n=0;!s&&f&&!o&&n<l.length;n++){var o,i=l[n],y=d.p,p=i[2];r>3?(o=p===e)&&(a=i[(u=i[4])?5:(u=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(u=0,d.v=e,d.n=i[1]):y<p&&(o=r<3||i[0]>e||e>p)&&(i[4]=r,i[5]=e,d.n=p,u=0))}if(o||r>1)return c;throw s=!0,e}return function(o,l,p){if(f>1)throw TypeError("Generator is already running");for(s&&1===l&&y(l,p),u=l,a=p;(n=u<2?t:a)||!s;){i||(u?u<3?(u>1&&(d.n=-1),y(u,a)):d.n=a:d.v=a);try{if(f=2,i){if(u||(o="next"),n=i[o]){if(!(n=n.call(i,a)))throw TypeError("iterator result is not an object");if(!n.done)return n;a=n.value,u<2&&(u=0)}else 1===u&&(n=i.return)&&n.call(i),u<2&&(a=TypeError("The iterator does not provide a '"+o+"' method"),u=1);i=t}else if((n=(s=d.n<0)?a:r.call(e,d))!==c)break}catch(n){i=t,u=1,a=n}finally{f=1}}return{value:n,done:s}}}(r,i,u),!0),l}var c={};function f(){}function l(){}function s(){}n=Object.getPrototypeOf;var d=[][i]?n(n([][i]())):(e(n={},i,(function(){return this})),n),y=s.prototype=f.prototype=Object.create(d);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,e(t,u,"GeneratorFunction")),t.prototype=Object.create(y),t}return l.prototype=s,e(y,"constructor",s),e(s,"constructor",l),l.displayName="GeneratorFunction",e(s,u,"GeneratorFunction"),e(y),e(y,u,"Generator"),e(y,i,(function(){return this})),e(y,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:a,m:p}})()}function e(t,n,r,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}e=function(t,n,r,o){if(n)i?i(t,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):t[n]=r;else{var u=function(n,r){e(t,n,(function(t){return this._invoke(n,r,t)}))};u("next",0),u("throw",1),u("return",2)}},e(t,n,r,o)}function o(t,n,r,e,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?n(c):Promise.resolve(c).then(e,o)}function i(t){return function(){var n=this,r=arguments;return new Promise((function(e,i){var u=t.apply(n,r);function a(t){o(u,e,i,a,c,"next",t)}function c(t){o(u,e,i,a,c,"throw",t)}a(void 0)}))}}System.register(["./index-legacy.04f34b53.js"],(function(n,e){"use strict";var o,u,a,c,f,l,s,d=document.createElement("style");return d.textContent='@charset "UTF-8";.wechat-class{height:320px;overflow:hidden}\n',document.head.appendChild(d),{setters:[function(t){o=t.r,u=t.u,a=t.v,c=t.o,f=t.d,l=t.e,s=t.B}],execute:function(){n("default",Object.assign({name:"Wechat"},{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup:function(n){var e=o(0),d=n,y=function(){var t=i(r().m((function t(){var n,e;return r().w((function(t){for(;;)switch(t.n){case 0:return n={type:"qiyewx",data:{idpId:d.auth_id}},t.n=1,s(n);case 1:if(200!==(e=t.v).status){t.n=2;break}return t.a(2,e.data.uniqKey);case 2:return t.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),p=u(),v=function(){var n=i(r().m((function n(){var e,o,i,u,a,c,f,l,s,v,h,m,w,b;return r().w((function(n){for(;;)switch(n.n){case 0:if(o=window.location.host,i=window.location.protocol,u="".concat(i,"//").concat(o,"/#/status"),null!==(e=p.query)&&void 0!==e&&e.redirect)l=(null===(a=p.query)||void 0===a?void 0:a.redirect.indexOf("?"))>-1?null===(c=p.query)||void 0===c?void 0:c.redirect.substring((null===(f=p.query)||void 0===f?void 0:f.redirect.indexOf("?"))+1):"",u=u+"?"+l;else if(p.query){for(s=new URLSearchParams,v=0,h=Object.entries(p.query);v<h.length;v++)m=t(h[v],2),w=m[0],b=m[1],s.append(w,b);u=u+"?"+s.toString()}return n.n=1,y();case 1:setTimeout((function(){window.getQRCode({id:"qr_login",appid:d.auth_info.wxCorpId,agentid:d.auth_info.wxAgentId,redirect_uri:encodeURIComponent(u+"&auth_type=qiyewx"),state:d.auth_id,href:"",lang:"zh"});var t=document.querySelector("iframe");t.contentWindow.location.href!==t.src?console.log("iframe已重新加载"):console.log("iframe未重新加载")}),100);case 2:return n.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();return v(),a(d,function(){var t=i(r().m((function t(n,o){return r().w((function(t){for(;;)switch(t.n){case 0:return e.value++,t.n=1,v();case 1:return t.a(2)}}),t)})));return function(n,r){return t.apply(this,arguments)}}()),function(t,n){return c(),f("div",{key:e.value},n[0]||(n[0]=[l("div",{id:"qr_login",slot:"content",class:"wechat-class"},null,-1)]))}}}))}}}))}();
