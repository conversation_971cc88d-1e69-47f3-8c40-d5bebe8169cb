/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var u,n,t="function"==typeof Symbol?Symbol:{},a=t.iterator||"@@iterator",o=t.toStringTag||"@@toStringTag";function r(e,t,a,o){var r=t&&t.prototype instanceof i?t:i,c=Object.create(r.prototype);return l(c,"_invoke",function(e,l,t){var a,o,r,i=0,c=t||[],m=!1,f={p:0,n:0,v:u,a:s,f:s.bind(u,4),d:function(e,l){return a=e,o=0,r=u,f.n=l,d}};function s(e,l){for(o=e,r=l,n=0;!m&&i&&!t&&n<c.length;n++){var t,a=c[n],s=f.p,p=a[2];e>3?(t=p===l)&&(r=a[(o=a[4])?5:(o=3,3)],a[4]=a[5]=u):a[0]<=s&&((t=e<2&&s<a[1])?(o=0,f.v=l,f.n=a[1]):s<p&&(t=e<3||a[0]>l||l>p)&&(a[4]=e,a[5]=l,f.n=p,o=0))}if(t||e>1)return d;throw m=!0,l}return function(t,c,p){if(i>1)throw TypeError("Generator is already running");for(m&&1===c&&s(c,p),o=c,r=p;(n=o<2?u:r)||!m;){a||(o?o<3?(o>1&&(f.n=-1),s(o,r)):f.n=r:f.v=r);try{if(i=2,a){if(o||(t="next"),n=a[t]){if(!(n=n.call(a,r)))throw TypeError("iterator result is not an object");if(!n.done)return n;r=n.value,o<2&&(o=0)}else 1===o&&(n=a.return)&&n.call(a),o<2&&(r=TypeError("The iterator does not provide a '"+t+"' method"),o=1);a=u}else if((n=(m=f.n<0)?r:e.call(l,f))!==d)break}catch(n){a=u,o=1,r=n}finally{i=1}}return{value:n,done:m}}}(e,a,o),!0),c}var d={};function i(){}function c(){}function m(){}n=Object.getPrototypeOf;var f=[][a]?n(n([][a]())):(l(n={},a,(function(){return this})),n),s=m.prototype=i.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,l(e,o,"GeneratorFunction")),e.prototype=Object.create(s),e}return c.prototype=m,l(s,"constructor",m),l(m,"constructor",c),c.displayName="GeneratorFunction",l(m,o,"GeneratorFunction"),l(s),l(s,o,"Generator"),l(s,a,(function(){return this})),l(s,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:r,m:p}})()}function l(e,u,n,t){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}l=function(e,u,n,t){if(u)a?a(e,u,{value:n,enumerable:!t,configurable:!t,writable:!t}):e[u]=n;else{var o=function(u,n){l(e,u,(function(e){return this._invoke(u,n,e)}))};o("next",0),o("throw",1),o("return",2)}},l(e,u,n,t)}function u(e,l,u,n,t,a,o){try{var r=e[a](o),d=r.value}catch(e){return void u(e)}r.done?l(d):Promise.resolve(d).then(n,t)}function n(e){return function(){var l=this,n=arguments;return new Promise((function(t,a){var o=e.apply(l,n);function r(e){u(o,t,a,r,d,"next",e)}function d(e){u(o,t,a,r,d,"throw",e)}r(void 0)}))}}System.register(["./system-legacy.b5128538.js","./index-legacy.dbc04544.js"],(function(l,u){"use strict";var t,a,o,r,d,i,c,m,f,s,p,V,v,b,y,_,U,h=document.createElement("style");return h.textContent='@charset "UTF-8";.system{background:#fff;padding:36px;border-radius:2px}.system h2{padding:10px;margin:10px 0;font-size:16px;box-shadow:-4px 0 #e7e8e8}.system ::v-deep(.el-input-number__increase){top:5px!important}.system .gva-btn-list{margin-top:16px}\n',document.head.appendChild(h),{setters:[function(e){t=e.a,a=e.s},function(e){o=e.x,r=e.B,d=e.r,i=e.h,c=e.o,m=e.d,f=e.j,s=e.w,p=e.k,V=e.F,v=e.g,b=e.e,y=e.i,_=e.f,U=e.M}],execute:function(){var u={class:"system"},h={class:"gva-btn-list"};l("default",Object.assign({name:"Config"},{setup:function(l){var g=r([]),w=d({system:{"iplimit-count":0,"iplimit-time":0},jwt:{},mysql:{},pgsql:{},excel:{},autocode:{},redis:{},qiniu:{},"tencent-cos":{},"aliyun-oss":{},"hua-wei-obs":{},captcha:{},zap:{},local:{},email:{},timer:{detail:{}}}),q=function(){var l=n(e().m((function l(){var u;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t();case 1:0===(u=e.v).code&&(w.value=u.data.config);case 2:return e.a(2)}}),l)})));return function(){return l.apply(this,arguments)}}();q();var k=function(){},x=function(){var l=n(e().m((function l(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,a({config:w.value});case 1:if(0!==e.v.code){e.n=2;break}return U({type:"success",message:"配置文件设置成功"}),e.n=2,q();case 2:return e.a(2)}}),l)})));return function(){return l.apply(this,arguments)}}(),z=function(){var l=n(e().m((function l(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o({url:"/email/emailTest",method:"post",data:void 0});case 1:if(0!==e.v.code){e.n=3;break}return U({type:"success",message:"邮件发送成功"}),e.n=2,q();case 2:e.n=4;break;case 3:U({type:"error",message:"邮件发送失败"});case 4:return e.a(2)}}),l)})));return function(){return l.apply(this,arguments)}}();return function(e,l){var n=i("base-option"),t=i("base-select"),a=i("base-form-item"),o=i("base-input"),r=i("base-checkbox"),d=i("el-input-number"),U=i("el-collapse-item"),q=i("base-button"),j=i("el-collapse"),S=i("base-form");return c(),m("div",u,[f(S,{ref:"form",model:w.value,"label-width":"240px"},{default:s((function(){return[f(j,{modelValue:g,"onUpdate:modelValue":l[88]||(l[88]=function(e){return function(e){throw new TypeError('"'+e+'" is read-only')}("activeNames")})},{default:s((function(){return[f(U,{title:"系统配置",name:"1"},{default:s((function(){return[f(a,{label:"环境值"},{default:s((function(){return[f(t,{modelValue:w.value.system.env,"onUpdate:modelValue":l[0]||(l[0]=function(e){return w.value.system.env=e}),style:{width:"100%"}},{default:s((function(){return[f(n,{value:"public"}),f(n,{value:"develop"})]})),_:1},8,["modelValue"])]})),_:1}),f(a,{label:"端口值"},{default:s((function(){return[f(o,{modelValue:w.value.system.addr,"onUpdate:modelValue":l[1]||(l[1]=function(e){return w.value.system.addr=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"数据库类型"},{default:s((function(){return[f(t,{modelValue:w.value.system["db-type"],"onUpdate:modelValue":l[2]||(l[2]=function(e){return w.value.system["db-type"]=e}),style:{width:"100%"}},{default:s((function(){return[f(n,{value:"mysql"}),f(n,{value:"pgsql"})]})),_:1},8,["modelValue"])]})),_:1}),f(a,{label:"Oss类型"},{default:s((function(){return[f(t,{modelValue:w.value.system["oss-type"],"onUpdate:modelValue":l[3]||(l[3]=function(e){return w.value.system["oss-type"]=e}),style:{width:"100%"}},{default:s((function(){return[f(n,{value:"local"}),f(n,{value:"qiniu"}),f(n,{value:"tencent-cos"}),f(n,{value:"aliyun-oss"}),f(n,{value:"huawei-obs"})]})),_:1},8,["modelValue"])]})),_:1}),f(a,{label:"多点登录拦截"},{default:s((function(){return[f(r,{modelValue:w.value.system["use-multipoint"],"onUpdate:modelValue":l[4]||(l[4]=function(e){return w.value.system["use-multipoint"]=e})},{default:s((function(){return l[89]||(l[89]=[p("开启")])})),_:1,__:[89]},8,["modelValue"])]})),_:1}),f(a,{label:"开启redis"},{default:s((function(){return[f(r,{modelValue:w.value.system["use-redis"],"onUpdate:modelValue":l[5]||(l[5]=function(e){return w.value.system["use-redis"]=e})},{default:s((function(){return l[90]||(l[90]=[p("开启")])})),_:1,__:[90]},8,["modelValue"])]})),_:1}),f(a,{label:"限流次数"},{default:s((function(){return[f(d,{modelValue:w.value.system["iplimit-count"],"onUpdate:modelValue":l[6]||(l[6]=function(e){return w.value.system["iplimit-count"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"限流时间"},{default:s((function(){return[f(d,{modelValue:w.value.system["iplimit-time"],"onUpdate:modelValue":l[7]||(l[7]=function(e){return w.value.system["iplimit-time"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1})]})),_:1}),f(U,{title:"jwt签名",name:"2"},{default:s((function(){return[f(a,{label:"jwt签名"},{default:s((function(){return[f(o,{modelValue:w.value.jwt["signing-key"],"onUpdate:modelValue":l[8]||(l[8]=function(e){return w.value.jwt["signing-key"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"有效期（秒）"},{default:s((function(){return[f(o,{modelValue:w.value.jwt["expires-time"],"onUpdate:modelValue":l[9]||(l[9]=function(e){return w.value.jwt["expires-time"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"缓冲期（秒）"},{default:s((function(){return[f(o,{modelValue:w.value.jwt["buffer-time"],"onUpdate:modelValue":l[10]||(l[10]=function(e){return w.value.jwt["buffer-time"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"签发者"},{default:s((function(){return[f(o,{modelValue:w.value.jwt.issuer,"onUpdate:modelValue":l[11]||(l[11]=function(e){return w.value.jwt.issuer=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),f(U,{title:"Zap日志配置",name:"3"},{default:s((function(){return[f(a,{label:"级别"},{default:s((function(){return[f(o,{modelValue:w.value.zap.level,"onUpdate:modelValue":l[12]||(l[12]=function(e){return w.value.zap.level=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"输出"},{default:s((function(){return[f(o,{modelValue:w.value.zap.format,"onUpdate:modelValue":l[13]||(l[13]=function(e){return w.value.zap.format=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"日志前缀"},{default:s((function(){return[f(o,{modelValue:w.value.zap.prefix,"onUpdate:modelValue":l[14]||(l[14]=function(e){return w.value.zap.prefix=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"日志文件夹"},{default:s((function(){return[f(o,{modelValue:w.value.zap.director,"onUpdate:modelValue":l[15]||(l[15]=function(e){return w.value.zap.director=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"编码级"},{default:s((function(){return[f(o,{modelValue:w.value.zap["encode-level"],"onUpdate:modelValue":l[16]||(l[16]=function(e){return w.value.zap["encode-level"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"栈名"},{default:s((function(){return[f(o,{modelValue:w.value.zap["stacktrace-key"],"onUpdate:modelValue":l[17]||(l[17]=function(e){return w.value.zap["stacktrace-key"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"日志留存时间(默认以天为单位)"},{default:s((function(){return[f(o,{modelValue:w.value.zap["max-age"],"onUpdate:modelValue":l[18]||(l[18]=function(e){return w.value.zap["max-age"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"显示行"},{default:s((function(){return[f(r,{modelValue:w.value.zap["show-line"],"onUpdate:modelValue":l[19]||(l[19]=function(e){return w.value.zap["show-line"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"输出控制台"},{default:s((function(){return[f(r,{modelValue:w.value.zap["log-in-console"],"onUpdate:modelValue":l[20]||(l[20]=function(e){return w.value.zap["log-in-console"]=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),f(U,{title:"Redis admin数据库配置",name:"4"},{default:s((function(){return[f(a,{label:"库"},{default:s((function(){return[f(o,{modelValue:w.value.redis.db,"onUpdate:modelValue":l[21]||(l[21]=function(e){return w.value.redis.db=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"地址"},{default:s((function(){return[f(o,{modelValue:w.value.redis.addr,"onUpdate:modelValue":l[22]||(l[22]=function(e){return w.value.redis.addr=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"密码"},{default:s((function(){return[f(o,{modelValue:w.value.redis.password,"onUpdate:modelValue":l[23]||(l[23]=function(e){return w.value.redis.password=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),f(U,{title:"邮箱配置",name:"5"},{default:s((function(){return[f(a,{label:"接收者邮箱"},{default:s((function(){return[f(o,{modelValue:w.value.email.to,"onUpdate:modelValue":l[24]||(l[24]=function(e){return w.value.email.to=e}),placeholder:"可多个，以逗号分隔"},null,8,["modelValue"])]})),_:1}),f(a,{label:"端口"},{default:s((function(){return[f(o,{modelValue:w.value.email.port,"onUpdate:modelValue":l[25]||(l[25]=function(e){return w.value.email.port=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"发送者邮箱"},{default:s((function(){return[f(o,{modelValue:w.value.email.from,"onUpdate:modelValue":l[26]||(l[26]=function(e){return w.value.email.from=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"host"},{default:s((function(){return[f(o,{modelValue:w.value.email.host,"onUpdate:modelValue":l[27]||(l[27]=function(e){return w.value.email.host=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"是否为ssl"},{default:s((function(){return[f(r,{modelValue:w.value.email["is-ssl"],"onUpdate:modelValue":l[28]||(l[28]=function(e){return w.value.email["is-ssl"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"secret"},{default:s((function(){return[f(o,{modelValue:w.value.email.secret,"onUpdate:modelValue":l[29]||(l[29]=function(e){return w.value.email.secret=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"测试邮件"},{default:s((function(){return[f(q,{onClick:z},{default:s((function(){return l[91]||(l[91]=[p("测试邮件")])})),_:1,__:[91]})]})),_:1})]})),_:1}),f(U,{title:"验证码配置",name:"7"},{default:s((function(){return[f(a,{label:"字符长度"},{default:s((function(){return[f(o,{modelValue:w.value.captcha["key-long"],"onUpdate:modelValue":l[30]||(l[30]=function(e){return w.value.captcha["key-long"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"平台宽度"},{default:s((function(){return[f(o,{modelValue:w.value.captcha["img-width"],"onUpdate:modelValue":l[31]||(l[31]=function(e){return w.value.captcha["img-width"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"图片高度"},{default:s((function(){return[f(o,{modelValue:w.value.captcha["img-height"],"onUpdate:modelValue":l[32]||(l[32]=function(e){return w.value.captcha["img-height"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1})]})),_:1}),f(U,{title:"数据库配置",name:"9"},{default:s((function(){return["mysql"===w.value.system["db-type"]?(c(),m(V,{key:0},[f(a,{label:"用户名"},{default:s((function(){return[f(o,{modelValue:w.value.mysql.username,"onUpdate:modelValue":l[33]||(l[33]=function(e){return w.value.mysql.username=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"密码"},{default:s((function(){return[f(o,{modelValue:w.value.mysql.password,"onUpdate:modelValue":l[34]||(l[34]=function(e){return w.value.mysql.password=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"地址"},{default:s((function(){return[f(o,{modelValue:w.value.mysql.path,"onUpdate:modelValue":l[35]||(l[35]=function(e){return w.value.mysql.path=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"数据库"},{default:s((function(){return[f(o,{modelValue:w.value.mysql["db-name"],"onUpdate:modelValue":l[36]||(l[36]=function(e){return w.value.mysql["db-name"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"maxIdleConns"},{default:s((function(){return[f(o,{modelValue:w.value.mysql["max-idle-conns"],"onUpdate:modelValue":l[37]||(l[37]=function(e){return w.value.mysql["max-idle-conns"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"maxOpenConns"},{default:s((function(){return[f(o,{modelValue:w.value.mysql["max-open-conns"],"onUpdate:modelValue":l[38]||(l[38]=function(e){return w.value.mysql["max-open-conns"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"日志模式"},{default:s((function(){return[f(r,{modelValue:w.value.mysql["log-mode"],"onUpdate:modelValue":l[39]||(l[39]=function(e){return w.value.mysql["log-mode"]=e})},null,8,["modelValue"])]})),_:1})],64)):v("",!0),"pgsql"===w.value.system.dbType?(c(),m(V,{key:1},[f(a,{label:"用户名"},{default:s((function(){return[f(o,{modelValue:w.value.pgsql.username,"onUpdate:modelValue":l[40]||(l[40]=function(e){return w.value.pgsql.username=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"密码"},{default:s((function(){return[f(o,{modelValue:w.value.pgsql.password,"onUpdate:modelValue":l[41]||(l[41]=function(e){return w.value.pgsql.password=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"地址"},{default:s((function(){return[f(o,{modelValue:w.value.pgsql.path,"onUpdate:modelValue":l[42]||(l[42]=function(e){return w.value.pgsql.path=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"数据库"},{default:s((function(){return[f(o,{modelValue:w.value.pgsql.dbname,"onUpdate:modelValue":l[43]||(l[43]=function(e){return w.value.pgsql.dbname=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"maxIdleConns"},{default:s((function(){return[f(o,{modelValue:w.value.pgsql["max-idle-conns"],"onUpdate:modelValue":l[44]||(l[44]=function(e){return w.value.pgsql["max-idle-conns"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"maxOpenConns"},{default:s((function(){return[f(o,{modelValue:w.value.pgsql["max-open-conns"],"onUpdate:modelValue":l[45]||(l[45]=function(e){return w.value.pgsql["max-open-conns"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(a,{label:"日志模式"},{default:s((function(){return[f(r,{modelValue:w.value.pgsql["log-mode"],"onUpdate:modelValue":l[46]||(l[46]=function(e){return w.value.pgsql["log-mode"]=e})},null,8,["modelValue"])]})),_:1})],64)):v("",!0)]})),_:1}),f(U,{title:"oss配置",name:"10"},{default:s((function(){return["local"===w.value.system["oss-type"]?(c(),m(V,{key:0},[l[92]||(l[92]=b("h2",null,"本地文件配置",-1)),f(a,{label:"本地文件访问路径"},{default:s((function(){return[f(o,{modelValue:w.value.local.path,"onUpdate:modelValue":l[47]||(l[47]=function(e){return w.value.local.path=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"本地文件存储路径"},{default:s((function(){return[f(o,{modelValue:w.value.local["store-path"],"onUpdate:modelValue":l[48]||(l[48]=function(e){return w.value.local["store-path"]=e})},null,8,["modelValue"])]})),_:1})],64)):v("",!0),"qiniu"===w.value.system["oss-type"]?(c(),m(V,{key:1},[l[95]||(l[95]=b("h2",null,"qiniu上传配置",-1)),f(a,{label:"存储区域"},{default:s((function(){return[f(o,{modelValue:w.value.qiniu.zone,"onUpdate:modelValue":l[49]||(l[49]=function(e){return w.value.qiniu.zone=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"空间名称"},{default:s((function(){return[f(o,{modelValue:w.value.qiniu.bucket,"onUpdate:modelValue":l[50]||(l[50]=function(e){return w.value.qiniu.bucket=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"CDN加速域名"},{default:s((function(){return[f(o,{modelValue:w.value.qiniu["img-path"],"onUpdate:modelValue":l[51]||(l[51]=function(e){return w.value.qiniu["img-path"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"是否使用https"},{default:s((function(){return[f(r,{modelValue:w.value.qiniu["use-https"],"onUpdate:modelValue":l[52]||(l[52]=function(e){return w.value.qiniu["use-https"]=e})},{default:s((function(){return l[93]||(l[93]=[p("开启")])})),_:1,__:[93]},8,["modelValue"])]})),_:1}),f(a,{label:"accessKey"},{default:s((function(){return[f(o,{modelValue:w.value.qiniu["access-key"],"onUpdate:modelValue":l[53]||(l[53]=function(e){return w.value.qiniu["access-key"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"secretKey"},{default:s((function(){return[f(o,{modelValue:w.value.qiniu["secret-key"],"onUpdate:modelValue":l[54]||(l[54]=function(e){return w.value.qiniu["secret-key"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"上传是否使用CDN上传加速"},{default:s((function(){return[f(r,{modelValue:w.value.qiniu["use-cdn-domains"],"onUpdate:modelValue":l[55]||(l[55]=function(e){return w.value.qiniu["use-cdn-domains"]=e})},{default:s((function(){return l[94]||(l[94]=[p("开启")])})),_:1,__:[94]},8,["modelValue"])]})),_:1})],64)):v("",!0),"tencent-cos"===w.value.system["oss-type"]?(c(),m(V,{key:2},[l[96]||(l[96]=b("h2",null,"腾讯云COS上传配置",-1)),f(a,{label:"存储桶名称"},{default:s((function(){return[f(o,{modelValue:w.value["tencent-cos"].bucket,"onUpdate:modelValue":l[56]||(l[56]=function(e){return w.value["tencent-cos"].bucket=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"所属地域"},{default:s((function(){return[f(o,{modelValue:w.value["tencent-cos"].region,"onUpdate:modelValue":l[57]||(l[57]=function(e){return w.value["tencent-cos"].region=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"secretID"},{default:s((function(){return[f(o,{modelValue:w.value["tencent-cos"]["secret-id"],"onUpdate:modelValue":l[58]||(l[58]=function(e){return w.value["tencent-cos"]["secret-id"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"secretKey"},{default:s((function(){return[f(o,{modelValue:w.value["tencent-cos"]["secret-key"],"onUpdate:modelValue":l[59]||(l[59]=function(e){return w.value["tencent-cos"]["secret-key"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"路径前缀"},{default:s((function(){return[f(o,{modelValue:w.value["tencent-cos"]["path-prefix"],"onUpdate:modelValue":l[60]||(l[60]=function(e){return w.value["tencent-cos"]["path-prefix"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"访问域名"},{default:s((function(){return[f(o,{modelValue:w.value["tencent-cos"]["base-url"],"onUpdate:modelValue":l[61]||(l[61]=function(e){return w.value["tencent-cos"]["base-url"]=e})},null,8,["modelValue"])]})),_:1})],64)):v("",!0),"aliyun-oss"===w.value.system["oss-type"]?(c(),m(V,{key:3},[l[97]||(l[97]=b("h2",null,"阿里云OSS上传配置",-1)),f(a,{label:"区域"},{default:s((function(){return[f(o,{modelValue:w.value["aliyun-oss"].endpoint,"onUpdate:modelValue":l[62]||(l[62]=function(e){return w.value["aliyun-oss"].endpoint=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"accessKeyId"},{default:s((function(){return[f(o,{modelValue:w.value["aliyun-oss"]["access-key-id"],"onUpdate:modelValue":l[63]||(l[63]=function(e){return w.value["aliyun-oss"]["access-key-id"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"accessKeySecret"},{default:s((function(){return[f(o,{modelValue:w.value["aliyun-oss"]["access-key-secret"],"onUpdate:modelValue":l[64]||(l[64]=function(e){return w.value["aliyun-oss"]["access-key-secret"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"存储桶名称"},{default:s((function(){return[f(o,{modelValue:w.value["aliyun-oss"]["bucket-name"],"onUpdate:modelValue":l[65]||(l[65]=function(e){return w.value["aliyun-oss"]["bucket-name"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"访问域名"},{default:s((function(){return[f(o,{modelValue:w.value["aliyun-oss"]["bucket-url"],"onUpdate:modelValue":l[66]||(l[66]=function(e){return w.value["aliyun-oss"]["bucket-url"]=e})},null,8,["modelValue"])]})),_:1})],64)):v("",!0),"huawei-obs"===w.value.system["oss-type"]?(c(),m(V,{key:4},[l[98]||(l[98]=b("h2",null,"华为云Obs上传配置",-1)),f(a,{label:"路径"},{default:s((function(){return[f(o,{modelValue:w.value["hua-wei-obs"].path,"onUpdate:modelValue":l[67]||(l[67]=function(e){return w.value["hua-wei-obs"].path=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"存储桶名称"},{default:s((function(){return[f(o,{modelValue:w.value["hua-wei-obs"].bucket,"onUpdate:modelValue":l[68]||(l[68]=function(e){return w.value["hua-wei-obs"].bucket=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"区域"},{default:s((function(){return[f(o,{modelValue:w.value["hua-wei-obs"].endpoint,"onUpdate:modelValue":l[69]||(l[69]=function(e){return w.value["hua-wei-obs"].endpoint=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"accessKey"},{default:s((function(){return[f(o,{modelValue:w.value["hua-wei-obs"]["access-key"],"onUpdate:modelValue":l[70]||(l[70]=function(e){return w.value["hua-wei-obs"]["access-key"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"secretKey"},{default:s((function(){return[f(o,{modelValue:w.value["hua-wei-obs"]["secret-key"],"onUpdate:modelValue":l[71]||(l[71]=function(e){return w.value["hua-wei-obs"]["secret-key"]=e})},null,8,["modelValue"])]})),_:1})],64)):v("",!0)]})),_:1}),f(U,{title:"Excel上传配置",name:"11"},{default:s((function(){return[f(a,{label:"合成目标地址"},{default:s((function(){return[f(o,{modelValue:w.value.excel.dir,"onUpdate:modelValue":l[72]||(l[72]=function(e){return w.value.excel.dir=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),f(U,{title:"自动化代码配置",name:"12"},{default:s((function(){return[f(a,{label:"是否自动重启(linux)"},{default:s((function(){return[f(r,{modelValue:w.value.autocode["transfer-restart"],"onUpdate:modelValue":l[73]||(l[73]=function(e){return w.value.autocode["transfer-restart"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"root(项目根路径)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode.root,"onUpdate:modelValue":l[74]||(l[74]=function(e){return w.value.autocode.root=e}),disabled:""},null,8,["modelValue"])]})),_:1}),f(a,{label:"Server(后端代码地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["transfer-restart"],"onUpdate:modelValue":l[75]||(l[75]=function(e){return w.value.autocode["transfer-restart"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"SApi(后端api文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["server-api"],"onUpdate:modelValue":l[76]||(l[76]=function(e){return w.value.autocode["server-api"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"SInitialize(后端Initialize文件夹)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["server-initialize"],"onUpdate:modelValue":l[77]||(l[77]=function(e){return w.value.autocode["server-initialize"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"SModel(后端Model文件地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["server-model"],"onUpdate:modelValue":l[78]||(l[78]=function(e){return w.value.autocode["server-model"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"SRequest(后端Request文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["server-request"],"onUpdate:modelValue":l[79]||(l[79]=function(e){return w.value.autocode["server-request"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"SRouter(后端Router文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["server-router"],"onUpdate:modelValue":l[80]||(l[80]=function(e){return w.value.autocode["server-router"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"SService(后端Service文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["server-service"],"onUpdate:modelValue":l[81]||(l[81]=function(e){return w.value.autocode["server-service"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"Web(前端文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode.web,"onUpdate:modelValue":l[82]||(l[82]=function(e){return w.value.autocode.web=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"WApi(后端WApi文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["web-api"],"onUpdate:modelValue":l[83]||(l[83]=function(e){return w.value.autocode["web-api"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"WForm(后端WForm文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["web-form"],"onUpdate:modelValue":l[84]||(l[84]=function(e){return w.value.autocode["web-form"]=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"WTable(后端WTable文件夹地址)"},{default:s((function(){return[f(o,{modelValue:w.value.autocode["web-table"],"onUpdate:modelValue":l[85]||(l[85]=function(e){return w.value.autocode["web-table"]=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),f(U,{title:"Timer(定时任务)",name:"13"},{default:s((function(){return[f(a,{label:"Start（是否启用）"},{default:s((function(){return[f(r,{modelValue:w.value.timer.start,"onUpdate:modelValue":l[86]||(l[86]=function(e){return w.value.timer.start=e})},null,8,["modelValue"])]})),_:1}),f(a,{label:"Spec(CRON表达式)"},{default:s((function(){return[f(o,{modelValue:w.value.timer.spec,"onUpdate:modelValue":l[87]||(l[87]=function(e){return w.value.timer.spec=e})},null,8,["modelValue"])]})),_:1}),(c(!0),m(V,null,y(w.value.timer.detail,(function(e,l){return c(),m(V,null,[(c(!0),m(V,null,y(e,(function(u,n){return c(),m("div",{key:n},[(c(),_(a,{key:l+n,label:n},{default:s((function(){return[f(o,{modelValue:e[n],"onUpdate:modelValue":function(l){return e[n]=l}},null,8,["modelValue","onUpdate:modelValue"])]})),_:2},1032,["label"]))])})),128))],64)})),256))]})),_:1})]})),_:1},8,["modelValue"])]})),_:1},8,["model"]),b("div",h,[f(q,{type:"primary",size:"small",onClick:x},{default:s((function(){return l[99]||(l[99]=[p("立即更新")])})),_:1,__:[99]}),f(q,{type:"primary",size:"small",onClick:k},{default:s((function(){return l[100]||(l[100]=[p("重启服务（开发中）")])})),_:1,__:[100]})])])}}}))}}}))}();
