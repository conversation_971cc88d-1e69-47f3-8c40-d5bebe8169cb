/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{r as e,b as a,c as t,o,d as s,f as i,F as n,_ as l,a as r,u as c,R as d,E as u,J as v,S as p,p as m,h as f,K as g,g as h,w as y,e as b,C as w,j as x,H as I,T as k,i as S,k as C,t as _,m as F,P as j,N,U as O,V as U,x as z}from"./index.4982c0f9.js";import{_ as A}from"./ASD.492c8837.js";import $ from"./index.e4cdb417.js";import{J as R}from"./index-browser-esm.c2d3b5c9.js";import"./index.981b0fdd.js";import"./menuItem.208f05b7.js";import"./asyncSubmenu.dd863e39.js";const D=""+new URL("noBody.745c3d16.png",import.meta.url).href,M=l(Object.assign({name:"CustomPic"},{props:{picType:{type:String,required:!1,default:"avatar"},picSrc:{type:String,required:!1,default:""}},setup(l){const r=l,c=e("/auth/");e(D);const d=a();return t((()=>""===r.picSrc?""!==R("$..headerImg[0]",d.userInfo)[0]&&"http"===R("$..headerImg[0]",d.userInfo)[0].slice(0,4)?R("$..headerImg[0]",d.userInfo)[0]:c.value+R("$..headerImg[0]",d.userInfo)[0]:""!==r.picSrc&&"http"===r.picSrc.slice(0,4)?r.picSrc:c.value+r.picSrc)),t((()=>r.picSrc&&"http"!==r.picSrc.slice(0,4)?c.value+r.picSrc:r.picSrc)),(e,a)=>(o(),s(n,null,[i('  <span class="headerAvatar">'),i("    <template v-if=\"picType === 'avatar'\">"),i('      <base-avatar v-if="JSONPath(\'$..headerImg[0]\',userStore.userInfo)[0]" :size="30" :src="avatar"/>'),i('      <base-avatar v-else :size="30" :src="noAvatar"/>'),i("    </template>"),i("    <template v-if=\"picType === 'img'\">"),i('      <img v-if="JSONPath(\'$..headerImg[0]\',userStore.userInfo)[0]" :src="avatar" class="avatar">'),i('      <img v-else :src="noAvatar" class="avatar">'),i("    </template>"),i("    <template v-if=\"picType === 'file'\">"),i('      <img :src="file" class="file">'),i("    </template>"),i("  </span>")],64))}}),[["__scopeId","data-v-fed37862"],["__file","D:/asec-platform/frontend/portal/src/components/customPic/index.vue"]]);
/*! js-cookie v3.0.5 | MIT */
function T(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var o in t)e[o]=t[o]}return e}var B=function e(a,t){function o(e,o,s){if("undefined"!=typeof document){"number"==typeof(s=T({},t,s)).expires&&(s.expires=new Date(Date.now()+864e5*s.expires)),s.expires&&(s.expires=s.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i="";for(var n in s)s[n]&&(i+="; "+n,!0!==s[n]&&(i+="="+s[n].split(";")[0]));return document.cookie=e+"="+a.write(o,e)+i}}return Object.create({set:o,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],o={},s=0;s<t.length;s++){var i=t[s].split("="),n=i.slice(1).join("=");try{var l=decodeURIComponent(i[0]);if(o[l]=a.read(n,l),e===l)break}catch(r){}}return e?o[e]:o}},remove:function(e,a){o(e,"",T({},a,{expires:-1}))},withAttributes:function(a){return e(this.converter,T({},this.attributes,a))},withConverter:function(a){return e(T({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(a)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const J={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},L={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},P={class:"header-row"},E={class:"header-col"},V={class:"header-cont"},H={class:"header-content pd-0"},q={class:"breadcrumb-col"},G={class:"breadcrumb"},W={class:"user-col"},K={class:"right-box"},Q={class:"dp-flex justify-content-center align-items height-full width-full"},X={class:"header-avatar",style:{cursor:"pointer"}},Y={style:{"margin-right":"9px",color:"#252631"}},Z={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},ee={key:0,class:"dropdown-menu"},ae=l(Object.assign({name:"Layout"},{setup(l){const R=a(),D=r(),T=c(),ae=d(),te=e(!0),oe=e(!1),se=e(!1),ie=e("7"),ne=()=>{document.body.clientWidth;se.value=!1,oe.value=!1,te.value=!0};ne();const le=e(!1);u((()=>{v.emit("collapse",te.value),v.emit("mobile",se.value),v.on("reload",ve),v.on("showLoading",(()=>{le.value=!0})),v.on("closeLoading",(()=>{le.value=!1})),window.onresize=()=>(ne(),v.emit("collapse",te.value),void v.emit("mobile",se.value)),R.loadingInstance&&R.loadingInstance.close()})),t((()=>"dark"===R.sideMode?"#fff":"light"===R.sideMode?"#273444":R.baseColor));const re=t((()=>"dark"===R.sideMode?"#273444":"light"===R.sideMode?"#fff":R.sideMode)),ce=t((()=>T.meta.matched)),de=e(!0);let ue=null;const ve=async()=>{ue&&window.clearTimeout(ue),ue=window.setTimeout((async()=>{if(T.meta.keepAlive)de.value=!1,await p(),de.value=!0;else{const e=T.meta.title;D.push({name:"Reload",params:{title:e}})}}),400)},pe=e(!1),me=e(!1),fe=()=>{te.value=!te.value,oe.value=!te.value,pe.value=!te.value,v.emit("collapse",te.value)},ge=()=>{me.value=!me.value},he=()=>{D.push({name:"person"})};return m("day",ie),(a,t)=>{const l=f("base-aside"),r=f("router-view"),c=f("base-main"),d=f("base-container"),u=g("loading");return o(),h(d,{class:"layout-cont"},{default:y((()=>[b("div",{class:w([[oe.value?"openside":"hideside",se.value?"mobile":""],"layout-wrapper"])},[b("div",{class:w([[pe.value?"shadowBg":""],"shadow-overlay"]),onClick:t[0]||(t[0]=e=>(pe.value=!pe.value,oe.value=!!te.value,void fe()))},null,2),x(l,{class:"main-cont main-left gva-aside",collapsed:te.value},{default:y((()=>[b("div",{class:w(["tilte",[oe.value?"openlogoimg":"hidelogoimg"]]),style:I({background:re.value})},[t[3]||(t[3]=b("img",{alt:"",class:"logoimg",src:A},null,-1)),i("          <div>"),i('            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>'),i('            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>'),i("          </div>")],6),x($,{class:"aside"}),b("div",{class:"footer",style:I({background:re.value})},[b("div",{class:"menu-total",onClick:fe},[te.value?(o(),s("svg",J,t[4]||(t[4]=[b("use",{"xlink:href":"#icon-expand"},null,-1)]))):(o(),s("svg",L,t[5]||(t[5]=[b("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)])),_:1},8,["collapsed"]),i(" 分块滑动功能 "),x(c,{class:"main-cont main-right"},{default:y((()=>[x(k,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:y((()=>[b("div",{style:I({width:`calc(100% - ${se.value?"0px":te.value?"54px":"220px"})`}),class:"topfix"},[b("div",P,[b("div",E,[b("header",V,[b("div",H,[t[10]||(t[10]=b("div",{class:"header-menu-col",style:{"z-index":"100"}},[i('                      <div class="menu-total" @click="totalCollapse">'),i('                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>'),i('                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>'),i("                      </div>")],-1)),b("div",q,[b("nav",G,[(o(!0),s(n,null,S(ce.value.slice(1,ce.value.length),(e=>(o(),s("div",{key:e.path,class:"breadcrumb-item"},[C(_(F(j)(e.meta.topTitle||"",F(T)))+" ",1),"总览"===e.meta.title?N((o(),s("select",{key:0,"onUpdate:modelValue":t[1]||(t[1]=e=>ie.value=e),class:"day-select form-select"},[...t[6]||(t[6]=[b("option",{value:"7"},"最近7天",-1),b("option",{value:"30"},"最近30天",-1),b("option",{value:"90"},"最近90天",-1)])],512)),[[O,ie.value]]):i("v-if",!0)])))),128))])]),b("div",W,[b("div",K,[i("                        <Search />"),b("div",{class:"dropdown",onClick:ge},[b("div",Q,[b("span",X,[x(M),i(" 展示当前登录用户名 "),b("span",Y,_(F(R).userInfo.displayName?F(R).userInfo.displayName:F(R).userInfo.name),1),(o(),s("svg",Z,t[7]||(t[7]=[b("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),me.value?(o(),s("div",ee,[i(' <div class="dropdown-item">\r\n                              <span style="font-weight: 600;">\r\n                                当前角色：{{ JSONPath(\'$..roles[0][name]\', userStore.userInfo)[0] }}\r\n                              </span>\r\n                            </div> '),b("div",{class:"dropdown-item",onClick:he},t[8]||(t[8]=[b("svg",{class:"icon","aria-hidden":"true"},[b("use",{"xlink:href":"#icon-avatar"})],-1),C(" 个人信息 ")])),b("div",{class:"dropdown-item",onClick:t[2]||(t[2]=a=>(async()=>{document.location.protocol,document.location.host;const a={action:1,msg:"",platform:document.location.hostname},t=e({}),o=e("ws://127.0.0.1:50001"),s=navigator.platform;0!==s.indexOf("Mac")&&"MacIntel"!==s||(o.value="wss://127.0.0.1:50001");const i=async e=>{console.log(e,"0"),await t.value.send(e)},n=async()=>{console.log("socket断开链接"),await t.value.close()};console.log(`asecagent://?web=${JSON.stringify(a)}`),await R.LoginOut(),t.value=new WebSocket(o.value),t.value.onopen=async()=>{console.log("socket连接成功"),await i(JSON.stringify(a))},t.value.onmessage=async e=>{console.log(e),await n()},t.value.onerror=()=>{console.log("socket连接错误")},B.remove("asce_sms")})())},t[9]||(t[9]=[b("svg",{class:"icon","aria-hidden":"true"},[b("use",{"xlink:href":"#icon-reading-lamp"})],-1),C(" 登 出 ")]))])):i("v-if",!0)]),i('                        <base-button type="text"'),i('                                   class="iconfont icon-rizhi1"'),i('                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"'),i('                                   @click="toLog"'),i("                        >日志中心"),i("                        </base-button>")])])])])])]),i(" 当前面包屑用路由自动生成可根据需求修改 "),i('\r\n            :to="{ path: item.path }" 暂时注释不用'),i('            <HistoryComponent ref="layoutHistoryComponent"/>')],4)])),_:1}),de.value?N((o(),h(r,{key:0,"element-loading-text":"正在加载中",class:"admin-box"},{default:y((({Component:e})=>[b("div",null,[x(k,{mode:"out-in",name:"el-fade-in-linear"},{default:y((()=>[(o(),h(U,{include:F(ae).keepAliveRouters},[(o(),h(z(e)))],1032,["include"]))])),_:2},1024)])])),_:1})),[[u,le.value]]):i("v-if",!0),i("        <BottomInfo />"),i("        <setting />")])),_:1})],2)])),_:1})}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/index.vue"]]);export{ae as default};
