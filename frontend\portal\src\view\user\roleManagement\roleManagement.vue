<template>
  <div class="role">
    <div class="header">
      <base-button :icon="Plus" @click="add()">新增角色</base-button>
      <base-button :icon="RefreshRight">刷新</base-button>
      <base-input
          v-model="searchUser"
          class="w-50 m-2 organize-search"
          placeholder="Search"
          :suffix-icon="Search"
          style="width: 15%;float: right"
      />
    </div>
    <el-table
        ref="multipleTableRef"
        :data="tableData"
        name="roleTable"
        stripe
        style="width: 100%;margin-top: 5px;min-width: 1200px"
        highlight-current-row
        class-name="table-row-style"
        row-class-name="app-table-style"
        @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"/>
      <el-table-column prop="name" label="角色名称" width="180">
        <template #header>
          <div style="text-align: center">
            <span style="font-size: 12px;font-weight: 700">角色名称</span>
          </div>
        </template>
        <template #default="scope">
          <div style="text-align: center">
            <span style="font-size: 12px">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" width="180">
        <template #header>
          <div style="text-align: center">
            <span style="font-size: 12px;font-weight: 700">描述</span>
          </div>
        </template>
        <template #default="scope">
          <div style="text-align: center">
            <span style="font-size: 12px">{{ scope.row.description }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="member" label="角色成员" show-overflow-tooltip>
        <template #header>
          <div style="text-align: center">
            <span style="font-size: 12px;font-weight: 700">角色成员</span>
          </div>
        </template>
        <template #default="scope">
          <div style="text-align: center">
            <span style="font-size: 12px">{{ scope.row.member }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="state" label="状态">
        <template #header>
          <div style="text-align: center">
            <span style="font-size: 12px;font-weight: 700">状态</span>
          </div>
        </template>
        <template #default="scope">
          <div style="text-align: center">
            <el-icon v-if="scope.row.state === 1" style="color: #52c41a">
              <SuccessFilled/>
            </el-icon>
            <el-icon v-else>
              <Remove/>
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="operate" label="操作">
        <template #header>
          <div style="text-align: center">
            <span style="font-size: 12px;font-weight: 700">操作</span>
          </div>
        </template>
        <template #default="scope">
          <div style="text-align: center">
            <el-link type="primary"
                     :underline="false"
                     style="
                         color: rgba(2, 167, 240, 0.996078431372549);
                         margin-right: 10px;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700"
                     @click="handleEdit(scope.$index,scope.row)"
            >
              编辑
            </el-link>
            <el-link type="primary"
                     :underline="false"
                     style="
                       color: rgba(2, 167, 240, 0.996078431372549);
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700"
                     @click="handleDelete(scope.$index,scope.row)"
            >
              删除
            </el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <el-pagination
        v-model:currentPage="currentPage4"
        v-model:page-size="pageSize4"
        :page-sizes="[100, 200, 300, 400]"
        :small="small"
        :disabled="disabled"
        :background="background"
        layout="total, sizes, prev, pager, next, jumper"
        :total="10000"
        style="float: right"
        class="risk-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    <!--弹窗-->
    <el-dialog
        v-model="dialogVisible"
        :title="title"
        width="30%"
        custom-class="custom-dialog"
        :before-close="handleClose"
    >
      <base-form
          label-position="right"
          label-width="100px"
          :model="formLabelAlign"
          style="max-width: 500px"
      >
        <base-form-item
            label="角色名："
            prop="name"
            :rules="[
            {
              required:true,
              message:'角色不能为空',
              trigger: ['blur']
            }]"
        >
          <base-input v-model="formLabelAlign.name" style="width: calc(100% - 20px)"/>
        </base-form-item>
        <base-form-item label="描述：" prop="description">
          <base-input v-model="formLabelAlign.description" style="width: calc(100% - 20px)"/>
        </base-form-item>
        <base-form-item
            label="角色成员："
            :rules="[
            {
              required:true,
              message: '请选择成员',
              trigger: ['blur']
            }
          ]"
        >
          <base-select
              v-model="formLabelAlign.member"
              style="width: calc(100% - 20px)"
              placeholder="请选择"
          >
            <base-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
            />
          </base-select>
        </base-form-item>
      </base-form>
      <template #footer>
        <span class="dialog-footer">
          <base-button @click="submitForm()">取消</base-button>
          <base-button
              color="#256EBF"
              type="primary"
              @click="submitForm()"
          >确定</base-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RoleManagement',
}
</script>
<script setup>
import { reactive, ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Plus,
  RefreshRight,
  Search,
} from '@element-plus/icons-vue'

const searchUser = ref('')

const tableData = [
  {
    empno: '0001',
    name: '销售人员',
    description: '销售',
    member: ['test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa', 'test', 'aaa'],
    state: 1,
  },
  {
    empno: '0002',
    name: 'ST组织',
    description: 'ST',
    member: ['aaa', 'bbb'],
    state: 1,
  },
  {
    empno: '0003',
    name: '测试',
    description: 'test',
    member: ['ccc', 'ddd'],
    state: 1,
  },
]

const formLabelAlign = reactive({
  name: '',
  description: '',
  member: '',
})

const options = [
  {
    value: 'zs',
    label: '张三',
  },
  {
    value: 'ls',
    label: '李四',
    disabled: true,
  },
  {
    value: 'ww',
    label: '王五',
  },
  {
    value: 'zl',
    label: '赵六',
  },
]

const type = ref('add')
const title = ref('新增角色')
const dialogVisible = ref(false)
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  console.log(val)
  multipleSelection.value = val
}

const handleClose = (done) => {
  ElMessageBox.confirm('是否取消新增?')
      .then(() => {
        done()
      })
      .catch(() => {
        // catch error
      })
}

const submitForm = () => {
  console.log(formLabelAlign)
  type.value = 'add'
  title.value = '新增角色'
  dialogVisible.value = false
}

// 表格
const handleEdit = (index, row) => {
  console.log(index, row)
  title.value = '编辑角色'
  type.value = 'edit'
  formLabelAlign.name = row.name
  formLabelAlign.description = '描述'
  formLabelAlign.member = 'zs'
  dialogVisible.value = true
}

const add = () => {
  console.log(1)
  title.value = '新增角色'
  type.value = 'add'
  dialogVisible.value = true
}

const handleDelete = (index, row) => {
  console.log(index)
  console.log(row)
  ElMessageBox.confirm(
      '<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个角色吗？</strong><br><strong>删除后关联的用户将无法访问此角色关联的应用，请谨慎操作。</strong>',
      '删除用户',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
  )
      .then(() => {
        if (row.empno === '0001') {
          ElMessage({
            type: 'success',
            message: '删除成功',
          })
        } else {
          ElMessage({
            type: 'error',
            message: '删除失败',
          })
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
}
// 分页
const currentPage4 = ref(4)
const pageSize4 = ref(100)
const small = ref(true)
const background = ref(true)
const disabled = ref(false)
const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
}
</script>
<style scoped>

</style>
<style>
/* 用来设置当前页面element全局table 选中某行时的背景色*/
.el-table__body tr.current-row > td {
  background-color: rgba(206, 233, 253, 1) !important;
  /* color: #f19944; */ /* 设置文字颜色，可以选择不设置 */
}

/* 用来设置当前页面element全局table 鼠标移入某行时的背景色*/
.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: rgba(206, 233, 253, 1);
  /* color: #f19944; */ /* 设置文字颜色，可以选择不设置 */
}

.table-row-style .el-table__cell .cell .cell {
  color: #256EBF !important;
  font-size: 12px;
}
</style>
