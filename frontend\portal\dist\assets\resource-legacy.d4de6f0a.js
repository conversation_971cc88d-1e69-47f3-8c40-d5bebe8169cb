/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js"],(function(t,o){"use strict";var n;return{setters:[function(t){n=t.x}],execute:function(){t("a",(function(t){return n({url:"/console/v1/application/group",method:"get",params:{corp_id:t}})})),t("f",(function(t){return n({url:"/console/v1/application/group",method:"post",data:t})})),t("e",(function(t){return n({url:"/console/v1/application/group",method:"put",data:t})})),t("h",(function(t){return n({url:"/console/v1/application/group",method:"delete",params:t})})),t("d",(function(t){return n({url:"/console/v1/application",method:"post",data:t})})),t("u",(function(t){return n({url:"/console/v1/application",method:"put",data:t})})),t("i",(function(t){return n({url:"/console/v1/application",method:"delete",params:t})})),t("b",(function(t){return n({url:"/console/v1/application/list",method:"post",data:t})})),t("c",(function(t){return n({url:"/console/v1/application",method:"get",params:t})})),t("g",(function(){return n({url:"/console/v1/application/getuserapp",method:"get"})}))}}}));
