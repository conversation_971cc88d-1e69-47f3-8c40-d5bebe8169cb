/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{g as e,f as l,d as a,c as t,u}from"./sysDictionary.cef56f98.js";import{W as s}from"./warningBar.0cee9251.js";import{g as o,r as i,j as d,o as r,a as n,i as p,b as c,w as m,k as v,t as f,l as y,M as _}from"./index.bfaf04e1.js";import{f as b,a as g}from"./format.5148d109.js";import"./date.23f5a973.js";import"./dictionary.20c9ce22.js";const w={class:"gva-search-box"},V={class:"gva-table-box"},h={class:"gva-btn-list"},k={style:{"text-align":"right","margin-top":"8px"}},C={class:"gva-pagination"},z={class:"dialog-footer"},x=Object.assign({name:"SysDictionary"},{setup(x){const U=o(),D=i({name:null,type:null,status:!0,desc:null}),j=i({name:[{required:!0,message:"请输入字典名（中）",trigger:"blur"}],type:[{required:!0,message:"请输入字典名（英）",trigger:"blur"}],desc:[{required:!0,message:"请输入描述",trigger:"blur"}]}),I=i(1),q=i(0),S=i(10),A=i([]),B=i({}),F=()=>{B.value={}},M=()=>{I.value=1,S.value=10,""===B.value.status&&(B.value.status=null),W()},O=e=>{S.value=e,W()},T=e=>{I.value=e,W()},W=async()=>{const l=await e({page:I.value,pageSize:S.value,...B.value});0===l.code&&(A.value=l.data.list,q.value=l.data.total,I.value=l.data.page,S.value=l.data.pageSize)};W();const E=i(!1),G=i(""),H=()=>{E.value=!1,D.value={name:null,type:null,status:!0,desc:null}},J=i(null),K=async()=>{J.value.validate((async e=>{if(!e)return;let l;switch(G.value){case"create":default:l=await t(D.value);break;case"update":l=await u(D.value)}0===l.code&&(_.success("操作成功"),H(),W())}))},L=()=>{G.value="create",E.value=!0};return(e,t)=>{const u=d("base-input"),o=d("base-form-item"),i=d("base-option"),x=d("base-select"),N=d("base-button"),P=d("base-form"),Q=d("el-table-column"),R=d("el-popover"),X=d("el-table"),Y=d("el-pagination"),Z=d("el-switch"),$=d("el-dialog");return r(),n("div",null,[p(s,{title:"获取字典且缓存方法已在前端utils/dictionary 已经封装完成 不必自己书写 使用方法查看文件内注释"}),c("div",w,[p(P,{inline:!0,model:B.value},{default:m((()=>[p(o,{label:"字典名（中）"},{default:m((()=>[p(u,{modelValue:B.value.name,"onUpdate:modelValue":t[0]||(t[0]=e=>B.value.name=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),p(o,{label:"字典名（英）"},{default:m((()=>[p(u,{modelValue:B.value.type,"onUpdate:modelValue":t[1]||(t[1]=e=>B.value.type=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),p(o,{label:"状态",prop:"status"},{default:m((()=>[p(x,{modelValue:B.value.status,"onUpdate:modelValue":t[2]||(t[2]=e=>B.value.status=e),clear:"",placeholder:"请选择"},{default:m((()=>[p(i,{key:"true",label:"是",value:"true"}),p(i,{key:"false",label:"否",value:"false"})])),_:1},8,["modelValue"])])),_:1}),p(o,{label:"描述"},{default:m((()=>[p(u,{modelValue:B.value.desc,"onUpdate:modelValue":t[3]||(t[3]=e=>B.value.desc=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),p(o,null,{default:m((()=>[p(N,{size:"small",type:"primary",icon:"search",onClick:M},{default:m((()=>t[9]||(t[9]=[v("查询")]))),_:1,__:[9]}),p(N,{size:"small",icon:"refresh",onClick:F},{default:m((()=>t[10]||(t[10]=[v("重置")]))),_:1,__:[10]})])),_:1})])),_:1},8,["model"])]),c("div",V,[c("div",h,[p(N,{size:"small",type:"primary",icon:"plus",onClick:L},{default:m((()=>t[11]||(t[11]=[v("新增")]))),_:1,__:[11]})]),p(X,{ref:"multipleTable",data:A.value,style:{width:"100%"},"tooltip-effect":"dark","row-key":"ID"},{default:m((()=>[p(Q,{type:"selection",width:"55"}),p(Q,{align:"left",label:"日期",width:"180"},{default:m((e=>[v(f(y(b)(e.row.CreatedAt)),1)])),_:1}),p(Q,{align:"left",label:"字典名（中）",prop:"name",width:"160"}),p(Q,{align:"left",label:"字典名（英）",prop:"type",width:"120"}),p(Q,{align:"left",label:"状态",prop:"status",width:"120"},{default:m((e=>[v(f(y(g)(e.row.status)),1)])),_:1}),p(Q,{align:"left",label:"描述",prop:"desc",width:"280"}),p(Q,{align:"left",label:"按钮组"},{default:m((e=>[p(N,{size:"small",icon:"document",type:"primary",link:"",onClick:l=>{return a=e.row,void U.push({name:"dictionaryDetail",params:{id:a.ID}});var a}},{default:m((()=>t[12]||(t[12]=[v("详情")]))),_:2,__:[12]},1032,["onClick"]),p(N,{size:"small",icon:"edit",type:"primary",link:"",onClick:a=>(async e=>{const a=await l({ID:e.ID,status:e.status});G.value="update",0===a.code&&(D.value=a.data.resysDictionary,E.value=!0)})(e.row)},{default:m((()=>t[13]||(t[13]=[v("变更")]))),_:2,__:[13]},1032,["onClick"]),p(R,{modelValue:e.row.visible,"onUpdate:modelValue":l=>e.row.visible=l,placement:"top",width:"160"},{reference:m((()=>[p(N,{type:"primary",link:"",icon:"delete",size:"small",style:{"margin-left":"10px"},onClick:l=>e.row.visible=!0},{default:m((()=>t[16]||(t[16]=[v("删除")]))),_:2,__:[16]},1032,["onClick"])])),default:m((()=>[t[17]||(t[17]=c("p",null,"确定要删除吗？",-1)),c("div",k,[p(N,{size:"small",type:"primary",link:"",onClick:l=>e.row.visible=!1},{default:m((()=>t[14]||(t[14]=[v("取消")]))),_:2,__:[14]},1032,["onClick"]),p(N,{type:"primary",size:"small",onClick:l=>(async e=>{e.visible=!1,0===(await a({ID:e.ID})).code&&(_({type:"success",message:"删除成功"}),1===A.value.length&&I.value>1&&I.value--,W())})(e.row)},{default:m((()=>t[15]||(t[15]=[v("确定")]))),_:2,__:[15]},1032,["onClick"])])])),_:2,__:[17]},1032,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"]),c("div",C,[p(Y,{"current-page":I.value,"page-size":S.value,"page-sizes":[10,30,50,100],total:q.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:T,onSizeChange:O},null,8,["current-page","page-size","total"])])]),p($,{modelValue:E.value,"onUpdate:modelValue":t[8]||(t[8]=e=>E.value=e),"before-close":H,title:"弹窗操作"},{footer:m((()=>[c("div",z,[p(N,{size:"small",onClick:H},{default:m((()=>t[18]||(t[18]=[v("取 消")]))),_:1,__:[18]}),p(N,{size:"small",type:"primary",onClick:K},{default:m((()=>t[19]||(t[19]=[v("确 定")]))),_:1,__:[19]})])])),default:m((()=>[p(P,{ref_key:"dialogForm",ref:J,model:D.value,rules:j.value,size:"medium","label-width":"110px"},{default:m((()=>[p(o,{label:"字典名（中）",prop:"name"},{default:m((()=>[p(u,{modelValue:D.value.name,"onUpdate:modelValue":t[4]||(t[4]=e=>D.value.name=e),placeholder:"请输入字典名（中）",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),p(o,{label:"字典名（英）",prop:"type"},{default:m((()=>[p(u,{modelValue:D.value.type,"onUpdate:modelValue":t[5]||(t[5]=e=>D.value.type=e),placeholder:"请输入字典名（英）",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),p(o,{label:"状态",prop:"status",required:""},{default:m((()=>[p(Z,{modelValue:D.value.status,"onUpdate:modelValue":t[6]||(t[6]=e=>D.value.status=e),"active-text":"开启","inactive-text":"停用"},null,8,["modelValue"])])),_:1}),p(o,{label:"描述",prop:"desc"},{default:m((()=>[p(u,{modelValue:D.value.desc,"onUpdate:modelValue":t[7]||(t[7]=e=>D.value.desc=e),placeholder:"请输入描述",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}});export{x as default};
