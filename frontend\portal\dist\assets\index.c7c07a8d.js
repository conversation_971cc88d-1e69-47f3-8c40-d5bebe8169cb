/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import a from"./header.69db326d.js";import s from"./menu.f0c9d459.js";import{h as t,o,d as e,j as i,e as r,f as d}from"./index.74d1ee23.js";import"./ASD.492c8837.js";import"./iconfont.2d75af05.js";const n={class:"layout-page"},l={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},u=Object.assign({name:"Client"},{setup:u=>(u,c)=>{const f=t("router-view");return o(),e("div",n,[i(a),r("div",l,[i(s),r("div",m,[(o(),d(f,{key:u.$route.fullPath}))])])])}});export{u as default};
