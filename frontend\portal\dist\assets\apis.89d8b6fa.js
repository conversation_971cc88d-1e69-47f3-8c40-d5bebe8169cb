/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{e}from"./api.34c114d1.js";import{x as a,_ as t,r as s,y as o,h as r,o as l,d as n,e as d,j as c,w as i,k as p,M as u}from"./index.74d1ee23.js";const h={class:"clearfix sticky-button"},f={class:"tree-content"},y=t(Object.assign({name:"Apis"},{props:{row:{default:function(){return{}},type:Object}},setup(t,{expose:y}){const m=t,v=s({children:"children",label:"description"}),b=s(""),k=s([]),I=s([]),w=s("");(async()=>{const t=(await e()).data.apis;k.value=j(t);const s=await(o={authorityId:m.row.authorityId},a({url:"/casbin/getPolicyPathByAuthorityId",method:"post",data:o}));var o;w.value=m.row.authorityId,I.value=[],s.data.paths&&s.data.paths.forEach((e=>{I.value.push("p:"+e.path+"m:"+e.method)}))})();const x=s(!1),g=()=>{x.value=!0},j=e=>{const a={};e&&e.forEach((e=>{e.onlyId="p:"+e.path+"m:"+e.method,Object.prototype.hasOwnProperty.call(a,e.apiGroup)?a[e.apiGroup].push(e):Object.assign(a,{[e.apiGroup]:[e]})}));const t=[];for(const s in a){const e={ID:s,description:s+"组",children:a[s]};t.push(e)}return t},_=s(null),O=async()=>{const e=_.value.getCheckedNodes(!0);var t=[];e&&e.forEach((e=>{var a={path:e.path,method:e.method};t.push(a)}));var s;0===(await(s={authorityId:w.value,casbinInfos:t},a({url:"/casbin/updateCasbin",method:"post",data:s}))).code&&u({type:"success",message:"api设置成功"})};y({needConfirm:x,enterAndNext:()=>{O()}});const C=(e,a)=>!e||-1!==a.description.indexOf(e);return o(b,(e=>{_.value.filter(e)})),(e,a)=>{const t=r("base-input"),s=r("base-button"),o=r("el-tree");return l(),n("div",null,[d("div",h,[c(t,{modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=e=>b.value=e),class:"fitler",placeholder:"筛选"},null,8,["modelValue"]),c(s,{class:"fl-right",size:"small",type:"primary",onClick:O},{default:i((()=>a[1]||(a[1]=[p("确 定")]))),_:1,__:[1]})]),d("div",f,[c(o,{ref_key:"apiTree",ref:_,data:k.value,"default-checked-keys":I.value,props:v.value,"default-expand-all":"","highlight-current":"","node-key":"onlyId","show-checkbox":"","filter-node-method":C,onCheck:g},null,8,["data","default-checked-keys","props"])])])}}}),[["__scopeId","data-v-9b701872"]]);export{y as default};
