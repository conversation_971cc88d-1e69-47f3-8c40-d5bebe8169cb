import { createApp, h } from 'vue'

const MessageComponent = {
  name: 'BaseMessage',
  props: {
    message: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'warning', 'info', 'error'].includes(value)
    },
    showClose: {
      type: Boolean,
      default: false
    },
    duration: {
      type: Number,
      default: 3000
    }
  },
  data() {
    return {
      visible: true
    }
  },
  mounted() {
    if (this.duration > 0) {
      setTimeout(() => {
        this.close()
      }, this.duration)
    }
  },
  methods: {
    close() {
      this.visible = false
      setTimeout(() => {
        this.$el.remove()
      }, 300)
    }
  },
  render() {
    if (!this.visible) return null
    
    return h('div', {
      class: [
        'base-message',
        `base-message--${this.type}`,
        { 'base-message--closable': this.showClose }
      ],
      style: {
        position: 'fixed',
        top: '20px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 9999,
        padding: '12px 16px',
        borderRadius: '4px',
        color: '#fff',
        fontSize: '14px',
        boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
        transition: 'all 0.3s',
        backgroundColor: this.getBackgroundColor()
      }
    }, [
      h('span', this.message),
      this.showClose && h('span', {
        style: {
          marginLeft: '8px',
          cursor: 'pointer',
          fontSize: '16px'
        },
        onClick: this.close
      }, '×')
    ])
  },
  methods: {
    getBackgroundColor() {
      const colors = {
        success: '#67c23a',
        warning: '#e6a23c',
        error: '#f56c6c',
        info: '#909399'
      }
      return colors[this.type] || colors.info
    }
  }
}

const Message = (options) => {
  if (typeof options === 'string') {
    options = { message: options }
  }
  
  const container = document.createElement('div')
  document.body.appendChild(container)
  
  const app = createApp(MessageComponent, options)
  app.mount(container)
  
  return {
    close: () => {
      app.unmount()
      document.body.removeChild(container)
    }
  }
}

// 添加快捷方法
Message.success = (message) => Message({ message, type: 'success' })
Message.warning = (message) => Message({ message, type: 'warning' })
Message.error = (message) => Message({ message, type: 'error' })
Message.info = (message) => Message({ message, type: 'info' })

export { Message }
