<template>
  <base-form
      ref="ruleFormRef"
      label-position="left"
      label-width="100px"
      style="max-width: 550px;margin-left: 30px"
      :model="formValues"
  >
    <base-row>
      <template
          v-for="formItem in formItems"
      >
        <base-col v-bind="colLayout">
          <!-- 通过条件判断渲染不同的表单 -->
          <base-form-item
              :label="formItem.rules?formItem.label:'&nbsp;&nbsp;'+formItem.label"
              :rules="formItem.rules"
              :style="formItem.rules?'margin-right: 35px':'margin-left:1px !important;margin-right: 35px'"
              :prop="formItem.field"
              v-if="!formItem.isHidden"
          >
            <!-- 渲染普通input和password -->
            <template
                v-if="formItem.type === 'input' || formItem.type === 'password'"
            >
              <base-input
                  :type="formItem.type"
                  :placeholder="formItem.placeholder"
                  :disabled="formItem.disabled"
                  v-model="formValues[`${formItem.field}`]"
                  :class="{'inputDisabled':formItem.disabled}"
                  autocomplete="new-passwprd"
              ></base-input>
              <base-button
                  v-if="formItem.disabled"
                  style="margin-left: 7px;float:right;"
                  @click="copy(formValues[`${formItem.field}`])"
              >复制
              </base-button>
            </template>
            <!--  时间选择  -->
            <template v-if="formItem.type === 'date'">
              <base-select v-model="date" class="m-2" placeholder="Select" size="small">
                <base-option label="全天" value="allDay">全天</base-option>
                <base-option label="其它" value="other">其它</base-option>
              </base-select>
            </template>
            <!--  树形选择  -->
            <template v-if="formItem.type === 'treeSelect'">
              <el-tree-select
                  :props="{value:'id',label:'name',children:'subGroups'}"
                  v-model="formValues[`${formItem.field}`]"
                  :data="formItem.options.value"
                  check-strictly
                  :render-after-expand="false"
              />
            </template>
            <!-- 渲染select表单 -->
            <template v-if="formItem.type === 'select'">
              <base-select
                  :placeholder="formItem.placeholder"
                  style="width: 100%"
                  v-model="formValues[`${formItem.field}`]"
              >
                <base-option
                    v-for="optionItem in formItem.options.value"
                    :key="optionItem[`${formItem.optionsKey}`]"
                    :label="optionItem[`${formItem.optionsLabe}`]"
                    :value="optionItem[`${formItem.optionsValue}`]"
                ></base-option>
              </base-select>
            </template>
            <!-- 渲染select多选表单 -->
            <template v-if="formItem.type === 'multiSelect'">
              <base-select
                  :placeholder="formItem.placeholder"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  style="width: 100%"
                  v-model="formValues[`${formItem.field}`]"
              >
                <base-option
                    v-for="optionItem in formItem.options.value"
                    :key="optionItem[`${formItem.optionsKey}`]||optionItem.id"
                    :label="optionItem[`${formItem.optionsLabe}`]||optionItem.name"
                    :value="optionItem[`${formItem.optionsValue}`]||optionItem.name"
                ></base-option>
              </base-select>
            </template>
            <!--  渲染选择框checkbox  -->
            <template v-if="formItem.type === 'checkbox'">
              <base-checkbox v-model="formValues[`${formItem.field}`]" size="large"/>
            </template>
            <!-- 渲染radio表单 -->
            <template v-if="formItem.type === 'radio'">
              <base-radio-group text-color="#252631" v-model="formValues[`${formItem.field}`]" class="ml-4">
                <base-radio
                    size="large"
                    :label="item.value"
                    v-for="item in formItem.options"
                    :key="item.value"
                >
                  {{ item.label }}
                </base-radio>
              </base-radio-group>
            </template>
            <!-- 渲染date表单 -->
            <template v-if="formItem.type === 'datepicker' || formItem.type === 'timepicker'">
              <base-radio-group v-model="formValues[`${formItem.dateType}`]" class="ml-4">
                <base-radio label="0" size="large" v-if="formItem.type === 'timepicker'">全天</base-radio>
                <base-radio label="0" size="large" v-else>永不过期</base-radio>
                <base-radio label="1" size="large">自定义</base-radio>
                <el-date-picker
                    v-if="formValues[`${formItem.dateType}`] === '1' && formItem.type === 'datepicker'"
                    v-model="formValues[`${formItem.field}`]"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="date"
                    placeholder="请选择日期"
                    style="width: 44%"
                />
                <el-time-picker
                    v-else-if="formValues[`${formItem.dateType}`] === '1' && formItem.type === 'timepicker'"
                    v-model="formValues[`${formItem.field}`]"
                    value-format="hh:mm:ss"
                    is-range
                    format="hh:mm:ss"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="width: 44%"
                />
              </base-radio-group>
            </template>
            <!--  渲染table表格 -->
            <template v-if="formItem.type === 'table'">
              <el-table :data="formValues[`${formItem.field}`]" style="width: 100%">
                <el-table-column align="center">
                  <template #header>
                    <span style="color: red">*</span><span> 协议</span>
                  </template>
                  <template #default="scope">
                    <base-form-item
                        :prop="`${formItem.field}[${scope.$index}].protocol`"
                        :rules="formItem.subRules.protocol"
                    >
                      <base-select
                          v-model="formValues[`${formItem.field}`][scope.$index].protocol"
                          size="small"
                          placeholder="请选择"
                      >
                        <base-option
                            v-for="optionItem in formItem.options"
                            :key="optionItem.value"
                            :label="optionItem.label"
                            :value="optionItem.value"
                        />
                      </base-select>
                    </base-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center">
                  <template #header>
                    <span style="color: red">*</span><span> 服务器地址</span>
                  </template>
                  <template #default="scope">
                    <base-form-item
                        :prop="`${formItem.field}[${scope.$index}].address`"
                        :rules="formItem.subRules.address"
                    >
                      <base-input
                          v-model="formValues[`${formItem.field}`][scope.$index].address"
                          size="small"
                          placeholder="请输入地址"
                      />
                    </base-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center">
                  <template #header>
                    <span style="color: red">*</span><span> 端口</span>
                  </template>
                  <template #default="scope">
                    <base-form-item
                        :prop="`${formItem.field}[${scope.$index}].port`"
                        :rules="formItem.subRules.port"
                    >

                      <base-input
                          v-model="formValues[`${formItem.field}`][scope.$index].port"
                          @blur="formValues[`${formItem.field}`][scope.$index].port=Number(formValues[`${formItem.field}`][scope.$index].port)"
                          size="small"
                          placeholder="请输入端口"
                      />
                    </base-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center">
                  <template #header>
                    <span>操作</span>
                  </template>
                  <template #default="scope">
                    <div style="color: darkgrey;font-size: 20px">
                      <el-icon @click="addAppAddress">
                        <CirclePlusFilled/>
                      </el-icon>
                      <el-icon v-if="formValues[`${formItem.field}`].length >1" @click="removeAppAddress(scope.$index)">
                        <RemoveFilled/>
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </template>
            <!--  弹窗 -->
            <template v-if="formItem.type === 'dialog'">
              <div style="width: 100%;border: 1px #DCDFE6 double;border-radius:4px"
                   @click="open(formItem.treeType,formItem.title,formItem.dataType)"
              >
                <div
                    class="custom-div"
                    style="width: calc(100% - 21px);float: left"
                    :placeholder="formItem.placeholder"
                    contenteditable="false"
                >
                  <el-tag
                      v-for="item in formValues[`${formItem.groupCheck}`]"
                      :key="item.id||item.user_group_id"
                      type="info"
                      effect="light"
                  >
                    {{ item.name || item.user_group_name }}
                  </el-tag>
                  <el-tag v-for="item in formValues[`${formItem.userCheck}`]" :key="item.id||item.user_id" type="info">
                    {{
                      item.name || item.user_name
                    }}
                  </el-tag>
                  <el-tag
                      v-for="item in formValues[`${formItem.appGroupCheck}`]"
                      :key="item.id||item.app_group_id"
                      type="info"
                  >{{
                      item.name || item.app_group_name
                    }}
                  </el-tag>
                  <el-tag v-for="item in formValues[`${formItem.appCheck}`]" :key="item.id||item.app_id" type="info">{{
                      item.name || item.app_name
                    }}
                  </el-tag>
                </div>
                <span style="float: right;margin-right: 5px"><el-icon><ArrowDown/></el-icon></span>
              </div>
            </template>
            <!--  渲染图片选择  -->
            <template v-if="formItem.type === 'imgRadio'">
              <span style="width: 100%">
                <base-radio-group v-model="formValues[`${formItem.field}`].type">
                  <base-radio label="1" size="large">图标库选择</base-radio>
                  <base-radio label="2" size="large">自定义上传</base-radio>
                </base-radio-group>
              </span>
              <span>
                <img
                    v-if="formValues[`${formItem.field}`].type === '1'"
                    style="height: 70px"
                    :src="formItem.localIcon"
                    class="avatar"
                    @click="iconSelect"
                >
                <el-upload
                    v-if="formValues[`${formItem.field}`].type === '2'"
                    style="height: 70px;width: 70px"
                    class="avatar-uploader"
                    action="127.0.0.1:3000"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload"
                >
                  <img
                      v-if="formValues[`${formItem.field}`].iconUrl"
                      style="height: 70px;width: 70px"
                      :src="formValues[`${formItem.field}`].iconUrl"
                      class="avatar"
                  >
                  <el-icon v-else class="avatar-uploader-icon" style="height: 70px;width: 70px">
                    <Plus/>
                  </el-icon>
                </el-upload>
              </span>
            </template>
          </base-form-item>
        </base-col>
      </template>
    </base-row>
    <span v-if="isFooter" class="dialog-footer">
          <base-button @click="cancel">取消</base-button>
          <base-button
              color="#256EBF"
              style="margin-left: 10px"
              type="primary"
              @click="submitForm(ruleFormRef)"
          >确定</base-button>
        </span>
  </base-form>
  <el-dialog
      v-model="dialogVisible"
      :title="treeTitle"
      width="600px"
      draggable
  >
    <CustomTransfer ref="treeCheck"/>
    <template #footer>
      <span class="dialog-footer">
        <base-button @click="dialogVisible = false">取消</base-button>
        <base-button type="primary" @click="submit">
          确定
        </base-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
export default {
  name: 'CustomFrom',
  props: {
    formValues: {
      type: Object,
      required: true,
    },
    rules: {
      type: Object,
      required: false,
    },
    formItems: {
      type: Array,
      default: () => {
        return []
      },
    },
    isFooter: {
      type: Boolean,
      required: false,
      default: true,
    },
    itemStyle: {
      type: Object,
      default: () => {
        return {}
      },
    },
    colLayout: {
      type: Object,
      default: () => ({
        xl: 24,
        lg: 24,
        md: 12,
        sm: 24,
      }),
    },
    cancel: {
      type: Function,
      required: true,
    },
    submitForm: {
      type: Function,
      required: true,
    },
    addAppAddress: {
      type: Function,
      required: false,
    },
    removeAppAddress: {
      type: Function,
      required: false,
    },
  },
}
</script>
<script setup>
import { inject, provide, ref } from 'vue'
import {
  Plus,
} from '@element-plus/icons-vue'
import CustomTransfer from '@/components/customFrom/customTransfer.vue'
import { ElMessage } from 'element-plus'
import useClipboard from 'vue-clipboard3'

const date = ref('allDay')
const treeCheck = ref()
const props = defineProps()
const getTreeData = inject('getTreeData')
const dialogVisible = ref(false)
const treeTitle = ref()
const dataType = inject('dataType')
const ruleFormRef = ref()
const treeType = inject('treeType')
const treeLabel = inject('treeLabel')
const checkedData = inject('checkedData')
const open = (type, title, dType) => {
  console.log('open1')
  // console.log(treeCheck.value)
  checkedData.value.length = 0
  treeType.value = type
  treeTitle.value = title
  dataType.value = dType
  if (dType === 'user') {
    treeLabel.value = [{ text: '组织', value: 'group' }, { text: '用户', value: 'user' }]
  } else {
    treeLabel.value = [{ text: '分组', value: 'group' }, { text: '资源', value: 'user' }]
  }
  getTreeData()
  dialogVisible.value = true
}

const submit = () => {
  console.log(treeCheck.value)
  props.formValues.groupCheck = treeCheck.value.groupCheck
  props.formValues.userCheck = treeCheck.value.userCheck
  props.formValues.appGroupCheck = treeCheck.value.appGroupCheck
  props.formValues.appCheck = treeCheck.value.appCheck
  props.formValues.user = [...treeCheck.value.groupCheck, treeCheck.value.userCheck]
  props.formValues.resource = [...treeCheck.value.appGroupCheck, treeCheck.value.appCheck]
  dialogVisible.value = false
}

const { toClipboard } = useClipboard()
const copy = async(value) => {
  if (!value) {
    ElMessage.error({
      message: '命令未生成，请保存后再复制',
    })
    return
  }
  try {
    await toClipboard(value.toString())
    ElMessage.success({
      message: '复制成功',
    })
  } catch (e) {
    ElMessage.error({
      message: '复制失败请重试',
    })
  }
}

provide('checkedData', checkedData)
defineExpose({ ruleFormRef: ruleFormRef })
</script>
<style scoped>

</style>
<style lang="scss">
.inputDisabled {
  width: 345px;
  float: left;
}

.custom-div:empty::before {
  content: attr(placeholder);
  color: #b1b3b8;
  margin-left: 10px;
}
</style>
