/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{L as e}from"./index.22c7594f.js";import{r as t,H as l,o as a,d as o}from"./index.74d1ee23.js";const n=Object.assign({name:"CustormLine"},{props:{data:{default:function(){return[]},type:Array}},setup(n){const s=n,r=t(null);return l((()=>{let t=tableRef.value.getSelectionRows();console.log(t),console.log(t.length),t.length<1&&(console.log(trendData),t=trendData.value[0]),console.log("selectTable"),console.log(t),data=t.data,line.value=new e(r.value,{data:s.data,xField:"date",yField:"count",yAxis:{tickCount:3},label:null,title:null,point:{size:3,shape:"circle",style:{fill:"white",stroke:"#7EBEEF",lineWidth:2}},tooltip:{showMarkers:!1},state:{active:{style:{shadowBlur:4,stroke:"#7EBEEF",fill:"#7EBEEF"}}},theme:{styleSheet:{brandColor:"#7EBEEF"}},interactions:[{type:"marker-active"}]}),line.value.render()})),(e,t)=>(a(),o("div",{ref_key:"container",ref:r},null,512))}});export{n as default};
