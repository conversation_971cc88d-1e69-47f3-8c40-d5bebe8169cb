/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function n(e){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{};n%2?t(Object(o),!0).forEach((function(t){i(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function i(t,n,i){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var o=i.call(t,n||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i,t}System.register(["./iconfont-legacy.37c53566.js","./index-legacy.dbc04544.js"],(function(e,t){"use strict";var i,o,r,u,a,c,m,d,l=document.createElement("style");return l.textContent='@charset "UTF-8";.layout-aside[data-v-4220c5e1]{width:56px;height:100%;background:#F5F5F7;overflow:auto;z-index:10}.layout-aside .u-offlineTips[data-v-4220c5e1]{width:100%;padding:10px;background:#fceded;display:flex;justify-content:center}.layout-aside .u-offlineTips .off-tip-content[data-v-4220c5e1]{display:flex;line-height:20px;font-size:14px;color:#e65353}.layout-aside .u-offlineTips .off-tip-content i[data-v-4220c5e1]{padding-right:10px;font-size:14px}.layout-aside .menu-wrapper[data-v-4220c5e1]{padding-bottom:60px;margin:0}.layout-aside .menu-wrapper .menu-item[data-v-4220c5e1]{height:65px;font-size:13px;color:#b3b6c1;font-weight:400;display:flex;flex-direction:column;align-items:center;justify-content:center}.layout-aside .menu-wrapper .menu-item .menu-item-title[data-v-4220c5e1]{height:17px;font-size:12px;font-family:PingFang SC,PingFang SC-Medium;font-weight:Medium;color:#686e84}.layout-aside .menu-wrapper .menu-item .menu-item-icon[data-v-4220c5e1]{height:18px;width:18px;margin-bottom:6px;fill:currentColor}.layout-aside .menu-wrapper .menu-item[data-v-4220c5e1]:hover{background:#EBEBED;color:#536ce6;border-radius:4px;cursor:pointer}.layout-aside .menu-wrapper .menu-item:hover .iconfont[data-v-4220c5e1]{color:#536ce6}.layout-aside .menu-wrapper .active-menu-item[data-v-4220c5e1]{border-radius:4px;color:#fff}.layout-aside .menu-wrapper .active-menu-item .iconfont[data-v-4220c5e1]{color:#fff}.layout-aside .menu-wrapper .active-menu-item .menu-item-title[data-v-4220c5e1]{color:#536ce6}.layout-aside .menu-wrapper .active-menu-item[data-v-4220c5e1]:hover{color:#fff;border-radius:4px}.layout-aside .menu-wrapper .active-menu-item:hover .iconfont[data-v-4220c5e1]{color:#fff}.layout-aside .version-wrapper[data-v-4220c5e1]{position:fixed;bottom:1px;left:1px;width:200px;background:#F5F5F7;font-size:12px;line-height:33px;text-align:center;color:#b3b6c1;z-index:11}\n',document.head.appendChild(l),{setters:[function(){},function(e){i=e._,o=e.o,r=e.d,u=e.e,a=e.F,c=e.i,m=e.E,d=e.t}],execute:function(){var t=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],l={name:"ClientMenu",data:function(){return{currentRouteCode:"101"}},computed:{computedMenu:function(){return this.computedMenuFun()}},mounted:function(){this.$router.push({path:"/client/main",query:[]})},watch:{$route:{handler:function(e,t){if(logger.log("路由变化",e,t),e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun:function(){var e=[];return t&&t.forEach((function(t){if(t.meta&&t.meta.menu){var n=t.meta.menu,i=n.name,o=n.icon,r=n.uiId,u={name:i,icon:o,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:r};e.push(u)}})),e},changeMenu:function(e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=n(n({},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),{menuClick:!0});logger.log(e,i),this.$router.push({path:e,query:i}),this.currentRouteCode=this.cutOut(t)},routerInterceptor:function(e){var t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:function(e){return e&&e.length?e.substr(0,3):e}}},s={class:"layout-aside"},p={class:"menu-wrapper"},f=["onClick"],h={class:"icon menu-item-icon","aria-hidden":"true"},y=["xlink:href"],g={class:"menu-item-title"};e("default",i(l,[["render",function(e,t,n,i,l,v){return o(),r("div",s,[u("ul",p,[(o(!0),r(a,null,c(v.computedMenu,(function(e){return o(),r("li",{key:e.code,class:m(["menu-item",v.cutOut(e.code)===l.currentRouteCode?"active-menu-item":""]),onClick:function(t){return v.changeMenu(e.url,e.params,e.code)}},[(o(),r("svg",h,[u("use",{"xlink:href":"#"+e.icon+(v.cutOut(e.code)===l.currentRouteCode?"-active":"")},null,8,y)])),u("div",g,d(e.name),1)],10,f)})),128))])])}],["__scopeId","data-v-4220c5e1"]]))}}}))}();
