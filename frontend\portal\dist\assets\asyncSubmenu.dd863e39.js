/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as e,I as t,r as a,v as n,h as o,o as s,g as u,w as l,d as r,j as c,C as f,f as i,e as m,t as d,F as v,O as p}from"./index.4982c0f9.js";const x={key:0,class:"gva-subMenu"},y=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:<PERSON><PERSON>an},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"1f4df5c8-normalText":b.value,"1f4df5c8-activeText":h.value})));const y=e,I=a(y.theme.activeBackground),h=a(y.theme.activeText),b=a(y.theme.normalText);return n((()=>y.theme),(()=>{I.value=y.theme.activeBackground,h.value=y.theme.activeText,b.value=y.theme.normalText})),(t,a)=>{const n=o("component"),y=o("el-icon"),I=o("el-sub-menu");return s(),u(I,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(v,{key:1},[e.routerInfo.meta.icon?(s(),u(y,{key:0},{default:l((()=>[c(n,{class:f(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):i("v-if",!0),m("span",null,d(e.routerInfo.meta.title),1)],64)):(s(),r("div",x,[e.routerInfo.meta.icon?(s(),u(y,{key:0},{default:l((()=>[c(n,{class:f(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):i("v-if",!0),m("span",null,d(e.routerInfo.meta.title),1)]))])),default:l((()=>[p(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-1f4df5c8"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/asyncSubmenu.vue"]]);export{y as default};
