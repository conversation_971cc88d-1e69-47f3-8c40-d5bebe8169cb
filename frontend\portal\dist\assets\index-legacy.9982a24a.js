/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(t,n,a){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a,t}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function l(n,a,r,o){var l=a&&a.prototype instanceof c?a:c,s=Object.create(l.prototype);return i(s,"_invoke",function(n,a,r){var i,o,l,c=0,s=r||[],f=!1,p={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return i=t,o=0,l=e,p.n=n,u}};function v(n,a){for(o=n,l=a,t=0;!f&&c&&!r&&t<s.length;t++){var r,i=s[t],v=p.p,d=i[2];n>3?(r=d===a)&&(l=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=v&&((r=n<2&&v<i[1])?(o=0,p.v=a,p.n=i[1]):v<d&&(r=n<3||i[0]>a||a>d)&&(i[4]=n,i[5]=a,p.n=d,o=0))}if(r||n>1)return u;throw f=!0,a}return function(r,s,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===s&&v(s,d),o=s,l=d;(t=o<2?e:l)||!f;){i||(o?o<3?(o>1&&(p.n=-1),v(o,l)):p.n=l:p.v=l);try{if(c=2,i){if(o||(r="next"),t=i[r]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,o<2&&(o=0)}else 1===o&&(t=i.return)&&t.call(i),o<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),o=1);i=e}else if((t=(f=p.n<0)?l:n.call(a,p))!==u)break}catch(t){i=e,o=1,l=t}finally{c=1}}return{value:t,done:f}}}(n,r,o),!0),s}var u={};function c(){}function s(){}function f(){}t=Object.getPrototypeOf;var p=[][a]?t(t([][a]())):(i(t={},a,(function(){return this})),t),v=f.prototype=c.prototype=Object.create(p);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,i(e,o,"GeneratorFunction")),e.prototype=Object.create(v),e}return s.prototype=f,i(v,"constructor",f),i(f,"constructor",s),s.displayName="GeneratorFunction",i(f,o,"GeneratorFunction"),i(v),i(v,o,"Generator"),i(v,a,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:l,m:d}})()}function i(e,t,n,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,a){if(t)r?r(e,t,{value:n,enumerable:!a,configurable:!a,writable:!a}):e[t]=n;else{var o=function(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))};o("next",0),o("throw",1),o("return",2)}},i(e,t,n,a)}function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw i}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function u(e,t,n,a,r,i,o){try{var l=e[i](o),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(a,r)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function o(e){u(i,a,r,o,l,"next",e)}function l(e){u(i,a,r,o,l,"throw",e)}o(void 0)}))}}System.register(["./index-legacy.04f34b53.js","./localLogin-legacy.259f647b.js","./wechat-legacy.458a3570.js","./feishu-legacy.35c246a0.js","./dingtalk-legacy.432ac4eb.js","./oauth2-legacy.14078acc.js","./sms-legacy.d0ec82cf.js","./secondaryAuth-legacy.764b384e.js","./verifyCode-legacy.acb55512.js"],(function(e,t){"use strict";var a,i,l,u,s,f,p,v,d,h,y,g,m,b,x,w,k,j,O,S,_,P,T,z,q,E,C,I,L=document.createElement("style");return L.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+");background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}\n",document.head.appendChild(L),{setters:[function(e){a=e.s,i=e.u,l=e.r,u=e.c,s=e.b,f=e.v,p=e.p,v=e.h,d=e.o,h=e.d,y=e.e,g=e.k,m=e.t,b=e.g,x=e.f,w=e.x,k=e.j,j=e.w,O=e.L,S=e.F,_=e.i},function(e){P=e.default},function(e){T=e.default},function(e){z=e.default},function(e){q=e.default},function(e){E=e.default},function(e){C=e.default},function(e){I=e.default},function(){}],execute:function(){var t={class:"login-page"},L={class:"content"},A={class:"right-panel"},G={key:0},U={key:0,class:"title"},D={key:1,class:"title"},F={style:{"text-align":"center"}},N={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},R={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},B=["xlink:href"],K={key:2,class:"login_panel_form"},J={key:3},M=["onClick"],V={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},$=["xlink:href"],H={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},Q={key:1,class:"auth-waiting"},W={class:"waiting-icon"},X={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},Y=["xlink:href"],Z={class:"waiting-title"};e("default",Object.assign({name:"Login"},{setup:function(e){var ee=i(),te=l(0),ne=l([]),ae=l("local"),re=l(""),ie=l(""),oe=l(""),le=l([]),ue=l([]),ce=l(!1),se=l(),fe=l(""),pe=l(!1),ve=l(""),de=l(!1),he=l(""),ye=l(""),ge=l(""),me=l({}),be=u((function(){var e=ce.value?he.value:ie.value;return ne.value.filter((function(t){return t.id!==e}))})),xe=s();u((function(){return ue.value.filter((function(e){return e.id!==ie.value}))}));var we=function(){var e={};if(ee.query.type&&(e.type=ee.query.type),ee.query.wp&&(e.wp=ee.query.wp),ee.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(ee.query.redirect);if(t.includes("?")){var n=t.substring(t.indexOf("?")+1),a=new URLSearchParams(n);a.get("type")&&(e.type=a.get("type")),a.get("wp")&&(e.wp=a.get("wp"))}}catch(r){console.warn("解析redirect参数失败:",r)}return e},ke=function(){var e=c(r().m((function e(){var t,n,i,l,u,c,s,f,p,v,d,h,y,g,m,b,x,w,k,j;return r().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,t=we(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),e.n=1,a({url:"/auth/login/v1/user/main_idp/list",method:"get"});case 1:if(200===(n=e.v).status){if(ne.value=n.data.idpList,(i=ee.query.idp_id||xe.loginType)&&"undefined"!==i){l=!1,u=o(n.data.idpList);try{for(u.s();!(c=u.n()).done;)s=c.value,i===s.id&&(l=!0,ie.value=s.id,ae.value=s.type,re.value=s.templateType,le.value=s.attrs,le.value.name=s.name,le.value.authType=s.type)}catch(r){u.e(r)}finally{u.f()}l||(oe.value=null===(f=ne.value[0])||void 0===f?void 0:f.id,ie.value=null===(p=ne.value[0])||void 0===p?void 0:p.id,ae.value=null===(v=ne.value[0])||void 0===v?void 0:v.type,re.value=null===(d=ne.value[0])||void 0===d?void 0:d.templateType,le.value=null===(h=ne.value[0])||void 0===h?void 0:h.attrs,le.value.name=ne.value[0].name,le.value.authType=null===(y=ne.value[0])||void 0===y?void 0:y.type)}else oe.value=null===(g=ne.value[0])||void 0===g?void 0:g.id,ie.value=null===(m=ne.value[0])||void 0===m?void 0:m.id,ae.value=null===(b=ne.value[0])||void 0===b?void 0:b.type,re.value=null===(x=ne.value[0])||void 0===x?void 0:x.templateType,le.value=null===(w=ne.value[0])||void 0===w?void 0:w.attrs,le.value.name=ne.value[0].name,le.value.authType=null===(k=ne.value[0])||void 0===k?void 0:k.type;++te.value}e.n=3;break;case 2:e.p=2,j=e.v,console.error(j);case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}();ke();var je=u((function(){switch(ae.value){case"local":case"msad":case"ldap":case"web":case"email":return P;case"qiyewx":return T;case"feishu":return z;case"dingtalk":return q;case"oauth2":case"cas":return E;case"sms":return C;default:return"oauth2"===re.value?E:"local"}})),Oe=u((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===ve.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===ve.value}]})),Se=function(){ce.value=!1,ue.value=[],se.value="",fe.value="",ve.value="",de.value=!1,he.value&&(ie.value=he.value,ae.value=ye.value,re.value=ge.value,le.value=n({},me.value),he.value="",ye.value="",ge.value="",me.value={}),++te.value,console.log("取消后恢复的状态:",{isSecondary:ce.value,auth_id:ie.value,auth_type:ae.value})},_e=function(){var e=c(r().m((function e(t){var n,a,i;return r().w((function(e){for(;;)switch(e.n){case 0:n=O.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{a=ee.query.redirect_url||"/",t.clientParams&&((i=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&i.set("wp",t.clientParams.wp),a+=(a.includes("?")?"&":"?")+i.toString()),window.location.href=a}finally{null==n||n.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),Pe=u((function(){return!["dingtalk","feishu","qiyewx"].includes(ae.value)&&("oauth2"!==re.value&&"cas"!==ae.value||("cas"===ae.value?1===parseInt(le.value.casOpenType):"oauth2"===re.value&&1===parseInt(le.value.oauth2OpenType)))})),Te=function(e){oe.value=e.id,le.value=e.attrs||{},le.value.name=e.name,le.value.authType=e.type,ce.value&&(le.value.uniqKey=se.value,le.value.notPhone=pe.value),ie.value=e.id,ae.value=e.type,re.value=e.templateType,++te.value};return f(ce,function(){var e=c(r().m((function e(t,a){return r().w((function(e){for(;;)switch(e.n){case 0:ce.value&&(he.value=ie.value,ye.value=ae.value,ge.value=re.value,me.value=n({},le.value),console.log("二次认证数据:",{secondary:ue.value,secondaryLength:ue.value.length}),ue.value.length>0&&Te(ue.value[0]));case 1:return e.a(2)}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),p("secondary",ue),p("isSecondary",ce),p("uniqKey",se),p("userName",fe),p("notPhone",pe),p("last_id",oe),p("contactType",ve),p("hasContactInfo",de),function(e,n){var a=v("base-divider"),r=v("base-avatar"),i=v("base-carousel-item"),o=v("base-carousel");return d(),h("div",t,[y("div",L,[n[3]||(n[3]=y("div",{class:"left-panel"},null,-1)),y("div",A,[ce.value?(d(),h("div",Q,[y("div",W,[(d(),h("svg",X,[y("use",{"xlink:href":"#icon-auth-".concat(ye.value||ae.value)},null,8,Y)]))]),y("h4",Z,m(me.value.name||le.value.name)+" 登录成功",1),n[1]||(n[1]=y("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),n[2]||(n[2]=y("div",{class:"security-tips"},[y("i",{class:"el-icon-shield",style:{color:"#67c23a"}}),y("span",null,"为了您的账户安全，请完成二次身份验证")],-1))])):(d(),h("div",G,["local"===ae.value?(d(),h("span",U,"本地账号登录")):Pe.value?(d(),h("span",D,[y("div",F,[y("span",N,[(d(),h("svg",R,[y("use",{"xlink:href":"#icon-auth-"+ae.value},null,8,B)])),g(" "+m(le.value.name),1)])])])):b("",!0),ie.value?(d(),h("div",K,[(d(),x(w(je.value),{auth_id:ie.value,auth_info:le.value},null,8,["auth_id","auth_info"]))])):b("",!0),be.value.length>0?(d(),h("div",J,[k(a,null,{default:j((function(){return n[0]||(n[0]=[y("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)])})),_:1,__:[0]}),(d(),x(o,{key:te.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:j((function(){return[(d(!0),h(S,null,_(Math.ceil(be.value.length/2),(function(e){return d(),x(i,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:j((function(){return[(d(!0),h(S,null,_(be.value.slice(2*(e-1),2*(e-1)+2),(function(e){return d(),h("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:function(t){return Te(e)}},[y("div",null,[k(r,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:j((function(){return[(d(),h("svg",V,[y("use",{"xlink:href":"#icon-auth-"+e.type},null,8,$)]))]})),_:2},1024)]),y("div",H,m(e.name),1)],8,M)})),128))]})),_:2},1024)})),128))]})),_:1}))])):b("",!0)]))])]),ce.value?(d(),x(I,{key:0,"auth-info":{uniqKey:se.value,contactType:ve.value,hasContactInfo:de.value},"auth-id":ie.value,"user-name":fe.value,"last-id":oe.value,"auth-methods":Oe.value,onVerificationSuccess:_e,onCancel:Se},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):b("",!0)])}}}))}}}))}();
