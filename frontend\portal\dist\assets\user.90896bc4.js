/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{x as e,_ as a,r as l,y as t,h as u,o as i,d as o,e as s,I as n,j as r,F as d,i as m,t as c,f as p,w as v,k as g,P as h,M as y,g as f,ad as b,ae as w,af as _,ag as k,ah as V,S as I,ai as C}from"./index.74d1ee23.js";import{g as x}from"./authority.8a89f398.js";import{C as U}from"./index.562be0ce.js";import{W as z}from"./warningBar.4338ec87.js";import"./index-browser-esm.c2d3b5c9.js";const N={class:"upload-image"},S={key:0,class:"image-preview"},D=["src"],j={class:"image-actions"},O={key:1,class:"upload-placeholder"},F=a({__name:"image",props:{modelValue:{type:String,default:""}},emits:["update:modelValue","change"],setup(e,{emit:a}){const d=e,m=a,c=l(null),p=l(d.modelValue);t((()=>d.modelValue),(e=>{p.value=e}));const v=()=>{c.value.click()},g=e=>{const a=e.target.files[0];if(a){const e=new FileReader;e.onload=e=>{p.value=e.target.result,m("update:modelValue",e.target.result),m("change",a)},e.readAsDataURL(a)}},h=()=>{p.value="",m("update:modelValue",""),m("change",null),c.value&&(c.value.value="")};return(e,a)=>{const l=u("base-icon");return i(),o("div",N,[s("input",{ref_key:"fileInput",ref:c,type:"file",accept:"image/*",style:{display:"none"},onChange:g},null,544),s("div",{class:"upload-area",onClick:v},[p.value?(i(),o("div",S,[s("img",{src:p.value,alt:"preview"},null,8,D),s("div",j,[s("button",{onClick:n(h,["stop"])},"删除")])])):(i(),o("div",O,[r(l,{name:"plus"}),a[0]||(a[0]=s("span",null,"点击上传图片",-1))]))])])}}},[["__scopeId","data-v-8d3cf6d2"]]),B={class:"upload-common"},T=["accept"],A={key:0,class:"file-list"},q={class:"file-name"},J=["onClick"],P={key:1,class:"upload-placeholder"},R=a({__name:"common",props:{modelValue:{type:Array,default:()=>[]},accept:{type:String,default:"*"},multiple:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(e,{emit:a}){const p=e,v=a,g=l(null),h=l([...p.modelValue]);t((()=>p.modelValue),(e=>{h.value=[...e]}));const y=()=>{g.value.click()},f=e=>{const a=Array.from(e.target.files);a.length>0&&(p.multiple?h.value.push(...a):h.value=[a[0]],v("update:modelValue",h.value),v("change",h.value))};return(a,l)=>{const t=u("base-icon");return i(),o("div",B,[s("input",{ref_key:"fileInput",ref:g,type:"file",accept:e.accept,style:{display:"none"},onChange:f},null,40,T),s("div",{class:"upload-area",onClick:y},[h.value.length>0?(i(),o("div",A,[(i(!0),o(d,null,m(h.value,((e,a)=>(i(),o("div",{key:a,class:"file-item"},[r(t,{name:"document"}),s("span",q,c(e.name),1),s("button",{onClick:n((e=>(e=>{h.value.splice(e,1),v("update:modelValue",h.value),v("change",h.value)})(a)),["stop"])},"删除",8,J)])))),128))])):(i(),o("div",P,[r(t,{name:"plus"}),l[0]||(l[0]=s("span",null,"点击上传文件",-1))]))])])}}},[["__scopeId","data-v-58a7cfc4"]]),E={class:"gva-btn-list"},K={class:"media"},L={class:"header-img-box-list"},M={class:"header-img-box-list"},W=["onClick"],G={__name:"index",props:{target:{type:Object,default:null},targetKey:{type:String,default:""}},emits:["enterImg"],setup(a,{expose:t,emit:n}){const f=l(""),b=l(""),w=l({}),_=l(1),k=l(0),V=l(20),I=e=>{V.value=e,D()},C=e=>{_.value=e,D()},x=n,U=l(!1),N=l([]),S=l("/auth/"),D=async()=>{const a=await(l={page:_.value,pageSize:V.value,...w.value},e({url:"/fileUploadAndDownload/getFileList",method:"post",data:l}));var l;0===a.code&&(N.value=a.data.list,k.value=a.data.total,_.value=a.data.page,V.value=a.data.pageSize,U.value=!0)},j=async a=>{h.prompt("请输入文件名或者备注","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/\S/,inputErrorMessage:"不能为空",inputValue:a.name}).then((async({value:l})=>{a.name=l;var t;0===(await(t=a,e({url:"/fileUploadAndDownload/editFileName",method:"post",data:t}))).code&&(y({type:"success",message:"编辑成功!"}),D())})).catch((()=>{y({type:"info",message:"取消修改"})}))};return t({open:D}),(e,l)=>{const t=u("base-input"),n=u("base-form-item"),h=u("base-button"),y=u("base-form"),O=u("el-icon"),B=u("el-image"),T=u("el-pagination"),A=u("el-drawer");return i(),p(A,{modelValue:U.value,"onUpdate:modelValue":l[3]||(l[3]=e=>U.value=e),title:"媒体库",size:"650px"},{default:v((()=>[r(z,{title:"点击“文件名/备注”可以编辑文件名或者备注内容。"}),s("div",E,[r(R,{imageCommon:b.value,"onUpdate:imageCommon":l[0]||(l[0]=e=>b.value=e),class:"upload-btn-media-library",onOnSuccess:D},null,8,["imageCommon"]),r(F,{imageUrl:f.value,"onUpdate:imageUrl":l[1]||(l[1]=e=>f.value=e),"file-size":512,"max-w-h":1080,class:"upload-btn-media-library",onOnSuccess:D},null,8,["imageUrl"]),r(y,{ref:"searchForm",inline:!0,model:w.value},{default:v((()=>[r(n,{label:""},{default:v((()=>[r(t,{modelValue:w.value.keyword,"onUpdate:modelValue":l[2]||(l[2]=e=>w.value.keyword=e),class:"keyword",placeholder:"请输入文件名或备注"},null,8,["modelValue"])])),_:1}),r(n,null,{default:v((()=>[r(h,{size:"small",type:"primary",icon:"search",onClick:D},{default:v((()=>l[4]||(l[4]=[g("查询")]))),_:1,__:[4]})])),_:1})])),_:1},8,["model"])]),s("div",K,[(i(!0),o(d,null,m(N.value,((e,t)=>(i(),o("div",{key:t,class:"media-box"},[s("div",L,[(i(),p(B,{key:t,src:e.url&&"http"!==e.url.slice(0,4)?S.value+e.url:e.url,onClick:l=>{return t=e.url,u=a.target,i=a.targetKey,u&&i&&(u[i]=t),x("enterImg",t),void(U.value=!1);var t,u,i}},{error:v((()=>[s("div",M,[r(O,null,{default:v((()=>l[5]||(l[5]=[s("picture",null,null,-1)]))),_:1,__:[5]})])])),_:2},1032,["src","onClick"]))]),s("div",{class:"img-title",onClick:a=>j(e)},c(e.name),9,W)])))),128))]),r(T,{"current-page":_.value,"page-size":V.value,total:k.value,style:{"justify-content":"center"},layout:"total, prev, pager, next, jumper",onCurrentChange:C,onSizeChange:I},null,8,["current-page","page-size","total"])])),_:1},8,["modelValue"])}}},H={class:"gva-table-box"},Q={class:"gva-btn-list"},X={style:{"text-align":"right","margin-top":"8px"}},Y={class:"gva-pagination"},Z={style:{height:"60vh",overflow:"auto",padding:"0 12px"}},$=["src"],ee={key:1,class:"header-img-box"},ae={class:"dialog-footer"},le=Object.assign({name:"User"},{setup(e){const a=l("/auth/"),n=(e,a)=>{e&&e.forEach((e=>{if(e.children&&e.children.length){const l={authorityId:e.authorityId,authorityName:e.authorityName,children:[]};n(e.children,l.children),a.push(l)}else{const l={authorityId:e.authorityId,authorityName:e.authorityName};a.push(l)}}))},d=l(1),m=l(0),c=l(10),N=l([]),S=e=>{c.value=e,j()},D=e=>{d.value=e,j()},j=async()=>{const e=await b({page:d.value,pageSize:c.value});0===e.code&&(N.value=e.data.list,m.value=e.data.total,d.value=e.data.page,c.value=e.data.pageSize)};t((()=>N.value),(()=>{O()}));(async()=>{j();const e=await x({page:1,pageSize:999});A(e.data.list)})();const O=()=>{N.value&&N.value.forEach((e=>{const a=e.authorities&&e.authorities.map((e=>e.authorityId));e.authorityIds=a}))},F=l(null),B=()=>{F.value.open()},T=l([]),A=e=>{T.value=[],n(e,T.value)},q=l({username:"",password:"",nickName:"",headerImg:"",authorityId:"",authorityIds:[],enable:1}),J=l({userName:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:5,message:"最低5位字符",trigger:"blur"}],password:[{required:!0,message:"请输入用户密码",trigger:"blur"},{min:6,message:"最低6位字符",trigger:"blur"}],nickName:[{required:!0,message:"请输入用户昵称",trigger:"blur"}],authorityId:[{required:!0,message:"请选择用户角色",trigger:"blur"}]}),P=l(null),R=async()=>{q.value.authorityId=q.value.authorityIds[0],P.value.validate((async e=>{if(e){const e={...q.value};if("add"===L.value){0===(await k(e)).code&&(y({type:"success",message:"创建成功"}),await j(),K())}if("edit"===L.value){0===(await V(e)).code&&(y({type:"success",message:"编辑成功"}),await j(),K())}}}))},E=l(!1),K=()=>{P.value.resetFields(),q.value.headerImg="",q.value.authorityIds=[],E.value=!1},L=l("add"),M=()=>{L.value="add",E.value=!0},W={},le=async(e,a,l)=>{if(a)return void(l||(W[e.ID]=[...e.authorityIds]));await I();0===(await C({ID:e.ID,authorityIds:e.authorityIds})).code?y({type:"success",message:"角色设置成功"}):l?e.authorityIds=[l,...e.authorityIds]:(e.authorityIds=[...W[e.ID]],delete W[e.ID])};return(e,l)=>{const t=u("base-button"),n=u("el-table-column"),b=u("el-cascader"),k=u("el-switch"),C=u("el-popover"),x=u("el-table"),O=u("el-pagination"),A=u("base-input"),W=u("base-form-item"),te=u("base-form"),ue=u("el-dialog");return i(),o("div",null,[r(z,{title:"注：右上角头像下拉可切换角色"}),s("div",H,[s("div",Q,[r(t,{size:"small",type:"primary",icon:"plus",onClick:M},{default:v((()=>l[8]||(l[8]=[g("新增用户")]))),_:1,__:[8]})]),r(x,{data:N.value,"row-key":"ID"},{default:v((()=>[r(n,{align:"left",label:"头像","min-width":"75"},{default:v((e=>[r(U,{style:{"margin-top":"8px"},"pic-src":e.row.headerImg},null,8,["pic-src"])])),_:1}),r(n,{align:"left",label:"ID","min-width":"50",prop:"ID"}),r(n,{align:"left",label:"用户名","min-width":"150",prop:"userName"}),r(n,{align:"left",label:"昵称","min-width":"150",prop:"nickName"}),r(n,{align:"left",label:"手机号","min-width":"180",prop:"phone"}),r(n,{align:"left",label:"邮箱","min-width":"180",prop:"email"}),r(n,{align:"left",label:"用户角色","min-width":"200"},{default:v((e=>[r(b,{modelValue:e.row.authorityIds,"onUpdate:modelValue":a=>e.row.authorityIds=a,options:T.value,"show-all-levels":!1,"collapse-tags":"",props:{multiple:!0,checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},clearable:!1,onVisibleChange:a=>{le(e.row,a,0)},onRemoveTag:a=>{le(e.row,!1,a)}},null,8,["modelValue","onUpdate:modelValue","options","onVisibleChange","onRemoveTag"])])),_:1}),r(n,{align:"left",label:"启用","min-width":"150"},{default:v((e=>[r(k,{modelValue:e.row.enable,"onUpdate:modelValue":a=>e.row.enable=a,"inline-prompt":"","active-value":1,"inactive-value":2,onChange:()=>{(async e=>{q.value=JSON.parse(JSON.stringify(e)),await I();const a={...q.value};0===(await V(a)).code&&(y({type:"success",message:(2===a.enable?"禁用":"启用")+"成功"}),await j(),q.value.headerImg="",q.value.authorityIds=[])})(e.row)}},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),r(n,{label:"操作","min-width":"250",fixed:"right"},{default:v((e=>[r(C,{modelValue:e.row.visible,"onUpdate:modelValue":a=>e.row.visible=a,placement:"top",width:"160"},{reference:v((()=>[r(t,{type:"primary",link:"",icon:"delete",size:"small"},{default:v((()=>l[11]||(l[11]=[g("删除")]))),_:1,__:[11]})])),default:v((()=>[l[12]||(l[12]=s("p",null,"确定要删除此用户吗",-1)),s("div",X,[r(t,{size:"small",type:"primary",link:"",onClick:a=>e.row.visible=!1},{default:v((()=>l[9]||(l[9]=[g("取消")]))),_:2,__:[9]},1032,["onClick"]),r(t,{type:"primary",size:"small",onClick:a=>(async e=>{0===(await _({id:e.ID})).code&&(y.success("删除成功"),e.visible=!1,await j())})(e.row)},{default:v((()=>l[10]||(l[10]=[g("确定")]))),_:2,__:[10]},1032,["onClick"])])])),_:2,__:[12]},1032,["modelValue","onUpdate:modelValue"]),r(t,{type:"primary",link:"",icon:"edit",size:"small",onClick:a=>{return l=e.row,L.value="edit",q.value=JSON.parse(JSON.stringify(l)),void(E.value=!0);var l}},{default:v((()=>l[13]||(l[13]=[g("编辑")]))),_:2,__:[13]},1032,["onClick"]),r(t,{type:"primary",link:"",icon:"magic-stick",size:"small",onClick:a=>{return l=e.row,void h.confirm("是否将此用户密码重置为123456?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=await w({ID:l.ID});0===e.code?y({type:"success",message:e.msg}):y({type:"error",message:e.msg})}));var l}},{default:v((()=>l[14]||(l[14]=[g("重置密码")]))),_:2,__:[14]},1032,["onClick"])])),_:1})])),_:1},8,["data"]),s("div",Y,[r(O,{"current-page":d.value,"page-size":c.value,"page-sizes":[10,30,50,100],total:m.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:D,onSizeChange:S},null,8,["current-page","page-size","total"])])]),r(ue,{modelValue:E.value,"onUpdate:modelValue":l[7]||(l[7]=e=>E.value=e),"custom-class":"user-dialog",title:"用户","show-close":!1,"close-on-press-escape":!1,"close-on-click-modal":!1},{footer:v((()=>[s("div",ae,[r(t,{size:"small",onClick:K},{default:v((()=>l[15]||(l[15]=[g("取 消")]))),_:1,__:[15]}),r(t,{size:"small",type:"primary",onClick:R},{default:v((()=>l[16]||(l[16]=[g("确 定")]))),_:1,__:[16]})])])),default:v((()=>[s("div",Z,[r(te,{ref_key:"userForm",ref:P,rules:J.value,model:q.value,"label-width":"80px"},{default:v((()=>["add"===L.value?(i(),p(W,{key:0,label:"用户名",prop:"userName"},{default:v((()=>[r(A,{modelValue:q.value.userName,"onUpdate:modelValue":l[0]||(l[0]=e=>q.value.userName=e)},null,8,["modelValue"])])),_:1})):f("",!0),"add"===L.value?(i(),p(W,{key:1,label:"密码",prop:"password"},{default:v((()=>[r(A,{modelValue:q.value.password,"onUpdate:modelValue":l[1]||(l[1]=e=>q.value.password=e)},null,8,["modelValue"])])),_:1})):f("",!0),r(W,{label:"昵称",prop:"nickName"},{default:v((()=>[r(A,{modelValue:q.value.nickName,"onUpdate:modelValue":l[2]||(l[2]=e=>q.value.nickName=e)},null,8,["modelValue"])])),_:1}),r(W,{label:"手机号",prop:"phone"},{default:v((()=>[r(A,{modelValue:q.value.phone,"onUpdate:modelValue":l[3]||(l[3]=e=>q.value.phone=e)},null,8,["modelValue"])])),_:1}),r(W,{label:"邮箱",prop:"email"},{default:v((()=>[r(A,{modelValue:q.value.email,"onUpdate:modelValue":l[4]||(l[4]=e=>q.value.email=e)},null,8,["modelValue"])])),_:1}),r(W,{label:"用户角色",prop:"authorityId"},{default:v((()=>[r(b,{modelValue:q.value.authorityIds,"onUpdate:modelValue":l[5]||(l[5]=e=>q.value.authorityIds=e),style:{width:"100%"},options:T.value,"show-all-levels":!1,props:{multiple:!0,checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},clearable:!1},null,8,["modelValue","options"])])),_:1}),r(W,{label:"启用",prop:"disabled"},{default:v((()=>[r(k,{modelValue:q.value.enable,"onUpdate:modelValue":l[6]||(l[6]=e=>q.value.enable=e),"inline-prompt":"","active-value":1,"inactive-value":2},null,8,["modelValue"])])),_:1}),r(W,{label:"头像","label-width":"80px"},{default:v((()=>[s("div",{style:{display:"inline-block"},onClick:B},[q.value.headerImg?(i(),o("img",{key:0,class:"header-img-box",src:q.value.headerImg&&"http"!==q.value.headerImg.slice(0,4)?a.value+q.value.headerImg:q.value.headerImg},null,8,$)):(i(),o("div",ee,"从媒体库选择"))])])),_:1})])),_:1},8,["rules","model"])])])),_:1},8,["modelValue"]),r(G,{ref_key:"chooseImg",ref:F,target:q.value,"target-key":"headerImg"},null,8,["target"])])}}});export{le as default};
