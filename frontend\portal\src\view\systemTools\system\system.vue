<template>
  <div class="system">
    <base-form ref="form" :model="config" label-width="240px">
      <!--  System start  -->
      <el-collapse v-model="activeNames">
        <el-collapse-item title="系统配置" name="1">
          <base-form-item label="环境值">
            <!-- <base-input v-model="config.system.env" />-->
            <base-select v-model="config.system.env" style="width:100%">
              <base-option value="public" />
              <base-option value="develop" />
            </base-select>
          </base-form-item>
          <base-form-item label="端口值">
            <base-input v-model.number="config.system.addr" />
          </base-form-item>
          <base-form-item label="数据库类型">
            <base-select v-model="config.system['db-type']" style="width:100%">
              <base-option value="mysql" />
              <base-option value="pgsql" />
            </base-select>
          </base-form-item>
          <base-form-item label="Oss类型">
            <base-select v-model="config.system['oss-type']" style="width:100%">
              <base-option value="local" />
              <base-option value="qiniu" />
              <base-option value="tencent-cos" />
              <base-option value="aliyun-oss" />
              <base-option value="huawei-obs" />
            </base-select>
          </base-form-item>
          <base-form-item label="多点登录拦截">
            <base-checkbox v-model="config.system['use-multipoint']">开启</base-checkbox>
          </base-form-item>
          <base-form-item label="开启redis">
            <base-checkbox v-model="config.system['use-redis']">开启</base-checkbox>
          </base-form-item>
          <base-form-item label="限流次数">
            <el-input-number v-model.number="config.system['iplimit-count']" />
          </base-form-item>
          <base-form-item label="限流时间">
            <el-input-number v-model.number="config.system['iplimit-time']" />
          </base-form-item>
        </el-collapse-item>
        <el-collapse-item title="jwt签名" name="2">
          <base-form-item label="jwt签名">
            <base-input v-model="config.jwt['signing-key']" />
          </base-form-item>
          <base-form-item label="有效期（秒）">
            <base-input v-model="config.jwt['expires-time']" />
          </base-form-item>
          <base-form-item label="缓冲期（秒）">
            <base-input v-model="config.jwt['buffer-time']" />
          </base-form-item>
          <base-form-item label="签发者">
            <base-input v-model="config.jwt.issuer" />
          </base-form-item>
        </el-collapse-item>
        <el-collapse-item title="Zap日志配置" name="3">
          <base-form-item label="级别">
            <base-input v-model.number="config.zap.level" />
          </base-form-item>
          <base-form-item label="输出">
            <base-input v-model="config.zap.format" />
          </base-form-item>
          <base-form-item label="日志前缀">
            <base-input v-model="config.zap.prefix" />
          </base-form-item>
          <base-form-item label="日志文件夹">
            <base-input v-model="config.zap.director" />
          </base-form-item>
          <base-form-item label="编码级">
            <base-input v-model="config.zap['encode-level']" />
          </base-form-item>
          <base-form-item label="栈名">
            <base-input v-model="config.zap['stacktrace-key']" />
          </base-form-item>
          <base-form-item label="日志留存时间(默认以天为单位)">
            <base-input v-model.number="config.zap['max-age']" />
          </base-form-item>
          <base-form-item label="显示行">
            <base-checkbox v-model="config.zap['show-line']" />
          </base-form-item>
          <base-form-item label="输出控制台">
            <base-checkbox v-model="config.zap['log-in-console']" />
          </base-form-item>
        </el-collapse-item>
        <el-collapse-item title="Redis admin数据库配置" name="4">
          <base-form-item label="库">
            <base-input v-model="config.redis.db" />
          </base-form-item>
          <base-form-item label="地址">
            <base-input v-model="config.redis.addr" />
          </base-form-item>
          <base-form-item label="密码">
            <base-input v-model="config.redis.password" />
          </base-form-item>
        </el-collapse-item>

        <el-collapse-item title="邮箱配置" name="5">
          <base-form-item label="接收者邮箱">
            <base-input v-model="config.email.to" placeholder="可多个，以逗号分隔" />
          </base-form-item>
          <base-form-item label="端口">
            <base-input v-model.number="config.email.port" />
          </base-form-item>
          <base-form-item label="发送者邮箱">
            <base-input v-model="config.email.from" />
          </base-form-item>
          <base-form-item label="host">
            <base-input v-model="config.email.host" />
          </base-form-item>
          <base-form-item label="是否为ssl">
            <base-checkbox v-model="config.email['is-ssl']" />
          </base-form-item>
          <base-form-item label="secret">
            <base-input v-model="config.email.secret" />
          </base-form-item>
          <base-form-item label="测试邮件">
            <base-button @click="email">测试邮件</base-button>
          </base-form-item>
        </el-collapse-item>
        <el-collapse-item title="验证码配置" name="7">
          <base-form-item label="字符长度">
            <base-input v-model.number="config.captcha['key-long']" />
          </base-form-item>
          <base-form-item label="平台宽度">
            <base-input v-model.number="config.captcha['img-width']" />
          </base-form-item>
          <base-form-item label="图片高度">
            <base-input v-model.number="config.captcha['img-height']" />
          </base-form-item>
        </el-collapse-item>
        <el-collapse-item title="数据库配置" name="9">
          <template v-if="config.system['db-type'] === 'mysql'">
            <base-form-item label="用户名">
              <base-input v-model="config.mysql.username" />
            </base-form-item>
            <base-form-item label="密码">
              <base-input v-model="config.mysql.password" />
            </base-form-item>
            <base-form-item label="地址">
              <base-input v-model="config.mysql.path" />
            </base-form-item>
            <base-form-item label="数据库">
              <base-input v-model="config.mysql['db-name']" />
            </base-form-item>
            <base-form-item label="maxIdleConns">
              <base-input v-model.number="config.mysql['max-idle-conns']" />
            </base-form-item>
            <base-form-item label="maxOpenConns">
              <base-input v-model.number="config.mysql['max-open-conns']" />
            </base-form-item>
            <base-form-item label="日志模式">
              <base-checkbox v-model="config.mysql['log-mode']" />
            </base-form-item>
          </template>
          <template v-if="config.system.dbType === 'pgsql'">
            <base-form-item label="用户名">
              <base-input v-model="config.pgsql.username" />
            </base-form-item>
            <base-form-item label="密码">
              <base-input v-model="config.pgsql.password" />
            </base-form-item>
            <base-form-item label="地址">
              <base-input v-model="config.pgsql.path" />
            </base-form-item>
            <base-form-item label="数据库">
              <base-input v-model="config.pgsql.dbname" />
            </base-form-item>
            <base-form-item label="maxIdleConns">
              <base-input v-model.number="config.pgsql['max-idle-conns']" />
            </base-form-item>
            <base-form-item label="maxOpenConns">
              <base-input v-model.number="config.pgsql['max-open-conns']" />
            </base-form-item>
            <base-form-item label="日志模式">
              <base-checkbox v-model="config.pgsql['log-mode']" />
            </base-form-item>
          </template>
        </el-collapse-item>

        <el-collapse-item title="oss配置" name="10">
          <template v-if="config.system['oss-type'] === 'local'">
            <h2>本地文件配置</h2>
            <base-form-item label="本地文件访问路径">
              <base-input v-model="config.local.path" />
            </base-form-item>
            <base-form-item label="本地文件存储路径">
              <base-input v-model="config.local['store-path']" />
            </base-form-item>
          </template>
          <template v-if="config.system['oss-type'] === 'qiniu'">
            <h2>qiniu上传配置</h2>
            <base-form-item label="存储区域">
              <base-input v-model="config.qiniu.zone" />
            </base-form-item>
            <base-form-item label="空间名称">
              <base-input v-model="config.qiniu.bucket" />
            </base-form-item>
            <base-form-item label="CDN加速域名">
              <base-input v-model="config.qiniu['img-path']" />
            </base-form-item>
            <base-form-item label="是否使用https">
              <base-checkbox v-model="config.qiniu['use-https']">开启</base-checkbox>
            </base-form-item>
            <base-form-item label="accessKey">
              <base-input v-model="config.qiniu['access-key']" />
            </base-form-item>
            <base-form-item label="secretKey">
              <base-input v-model="config.qiniu['secret-key']" />
            </base-form-item>
            <base-form-item label="上传是否使用CDN上传加速">
              <base-checkbox v-model="config.qiniu['use-cdn-domains']">开启</base-checkbox>
            </base-form-item>
          </template>
          <template v-if="config.system['oss-type'] === 'tencent-cos'">
            <h2>腾讯云COS上传配置</h2>
            <base-form-item label="存储桶名称">
              <base-input v-model="config['tencent-cos']['bucket']" />
            </base-form-item>
            <base-form-item label="所属地域">
              <base-input v-model="config['tencent-cos'].region" />
            </base-form-item>
            <base-form-item label="secretID">
              <base-input v-model="config['tencent-cos']['secret-id']" />
            </base-form-item>
            <base-form-item label="secretKey">
              <base-input v-model="config['tencent-cos']['secret-key']" />
            </base-form-item>
            <base-form-item label="路径前缀">
              <base-input v-model="config['tencent-cos']['path-prefix']" />
            </base-form-item>
            <base-form-item label="访问域名">
              <base-input v-model="config['tencent-cos']['base-url']" />
            </base-form-item>
          </template>
          <template v-if="config.system['oss-type'] === 'aliyun-oss'">
            <h2>阿里云OSS上传配置</h2>
            <base-form-item label="区域">
              <base-input v-model="config['aliyun-oss'].endpoint" />
            </base-form-item>
            <base-form-item label="accessKeyId">
              <base-input v-model="config['aliyun-oss']['access-key-id']" />
            </base-form-item>
            <base-form-item label="accessKeySecret">
              <base-input v-model="config['aliyun-oss']['access-key-secret']" />
            </base-form-item>
            <base-form-item label="存储桶名称">
              <base-input v-model="config['aliyun-oss']['bucket-name']" />
            </base-form-item>
            <base-form-item label="访问域名">
              <base-input v-model="config['aliyun-oss']['bucket-url']" />
            </base-form-item>
          </template>
          <template v-if="config.system['oss-type'] === 'huawei-obs'">
            <h2>华为云Obs上传配置</h2>
            <base-form-item label="路径">
              <base-input v-model="config['hua-wei-obs'].path" />
            </base-form-item>
            <base-form-item label="存储桶名称">
              <base-input v-model="config['hua-wei-obs'].bucket" />
            </base-form-item>
            <base-form-item label="区域">
              <base-input v-model="config['hua-wei-obs'].endpoint" />
            </base-form-item>
            <base-form-item label="accessKey">
              <base-input v-model="config['hua-wei-obs']['access-key']" />
            </base-form-item>
            <base-form-item label="secretKey">
              <base-input v-model="config['hua-wei-obs']['secret-key']" />
            </base-form-item>
          </template>

        </el-collapse-item>

        <el-collapse-item title="Excel上传配置" name="11">
          <base-form-item label="合成目标地址">
            <base-input v-model="config.excel.dir" />
          </base-form-item>
        </el-collapse-item>

        <el-collapse-item title="自动化代码配置" name="12">
          <base-form-item label="是否自动重启(linux)">
            <base-checkbox v-model="config.autocode['transfer-restart']" />
          </base-form-item>
          <base-form-item label="root(项目根路径)">
            <base-input v-model="config.autocode.root" disabled />
          </base-form-item>
          <base-form-item label="Server(后端代码地址)">
            <base-input v-model="config.autocode['transfer-restart']" />
          </base-form-item>
          <base-form-item label="SApi(后端api文件夹地址)">
            <base-input v-model="config.autocode['server-api']" />
          </base-form-item>
          <base-form-item label="SInitialize(后端Initialize文件夹)">
            <base-input v-model="config.autocode['server-initialize']" />
          </base-form-item>
          <base-form-item label="SModel(后端Model文件地址)">
            <base-input v-model="config.autocode['server-model']" />
          </base-form-item>
          <base-form-item label="SRequest(后端Request文件夹地址)">
            <base-input v-model="config.autocode['server-request']" />
          </base-form-item>
          <base-form-item label="SRouter(后端Router文件夹地址)">
            <base-input v-model="config.autocode['server-router']" />
          </base-form-item>
          <base-form-item label="SService(后端Service文件夹地址)">
            <base-input v-model="config.autocode['server-service']" />
          </base-form-item>
          <base-form-item label="Web(前端文件夹地址)">
            <base-input v-model="config.autocode.web" />
          </base-form-item>
          <base-form-item label="WApi(后端WApi文件夹地址)">
            <base-input v-model="config.autocode['web-api']" />
          </base-form-item>
          <base-form-item label="WForm(后端WForm文件夹地址)">
            <base-input v-model="config.autocode['web-form']" />
          </base-form-item>
          <base-form-item label="WTable(后端WTable文件夹地址)">
            <base-input v-model="config.autocode['web-table']" />
          </base-form-item>
        </el-collapse-item>

        <el-collapse-item title="Timer(定时任务)" name="13">
          <base-form-item label="Start（是否启用）">
            <base-checkbox v-model="config.timer['start']" />
          </base-form-item>
          <base-form-item label="Spec(CRON表达式)">
            <base-input v-model="config.timer.spec" />
          </base-form-item>
          <template v-for="(item,k) in config.timer.detail">
            <div v-for="(key,k2) in item" :key="k2">
              <base-form-item :key="k+k2" :label="k2">
                <base-input v-model="item[k2]" />
              </base-form-item>
            </div>
          </template>
        </el-collapse-item>
      </el-collapse>
    </base-form>
    <div class="gva-btn-list">
      <base-button type="primary" size="small" @click="update">立即更新</base-button>
      <base-button type="primary" size="small" @click="reload">重启服务（开发中）</base-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Config'
}
</script>
<script setup>
import { getSystemConfig, setSystemConfig } from '@/api/system'
import { emailTest } from '@/api/email'
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const activeNames = reactive([])
const config = ref({
  system: {
    'iplimit-count': 0,
    'iplimit-time': 0
  },
  jwt: {},
  mysql: {},
  pgsql: {},
  excel: {},
  autocode: {},
  redis: {},
  qiniu: {},
  'tencent-cos': {},
  'aliyun-oss': {},
  'hua-wei-obs': {},
  captcha: {},
  zap: {},
  local: {},
  email: {},
  timer: {
    detail: {}
  }
})

const initForm = async() => {
  const res = await getSystemConfig()
  if (res.code === 0) {
    config.value = res.data.config
  }
}
initForm()
const reload = () => {}
const update = async() => {
  const res = await setSystemConfig({ config: config.value })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '配置文件设置成功'
    })
    await initForm()
  }
}
const email = async() => {
  const res = await emailTest()
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '邮件发送成功'
    })
    await initForm()
  } else {
    ElMessage({
      type: 'error',
      message: '邮件发送失败'
    })
  }
}

</script>

<style lang="scss">
.system {
  background: #fff;
  padding:36px;
  border-radius: 2px;
  h2 {
    padding: 10px;
    margin: 10px 0;
    font-size: 16px;
    box-shadow: -4px 0px 0px 0px #e7e8e8;
  }
  ::v-deep(.el-input-number__increase){
    top:5px !important;
  }
  .gva-btn-list{
    margin-top:16px;
  }
}
</style>
