/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},u=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(e,o,u,a){var c=o&&o.prototype instanceof l?o:l,f=Object.create(c.prototype);return t(f,"_invoke",function(e,t,o){var u,a,c,l=0,f=o||[],s=!1,d={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(e,t){return u=e,a=0,c=n,d.n=t,i}};function p(e,t){for(a=e,c=t,r=0;!s&&l&&!o&&r<f.length;r++){var o,u=f[r],p=d.p,y=u[2];e>3?(o=y===t)&&(c=u[(a=u[4])?5:(a=3,3)],u[4]=u[5]=n):u[0]<=p&&((o=e<2&&p<u[1])?(a=0,d.v=t,d.n=u[1]):p<y&&(o=e<3||u[0]>t||t>y)&&(u[4]=e,u[5]=t,d.n=y,a=0))}if(o||e>1)return i;throw s=!0,t}return function(o,f,y){if(l>1)throw TypeError("Generator is already running");for(s&&1===f&&p(f,y),a=f,c=y;(r=a<2?n:c)||!s;){u||(a?a<3?(a>1&&(d.n=-1),p(a,c)):d.n=c:d.v=c);try{if(l=2,u){if(a||(o="next"),r=u[o]){if(!(r=r.call(u,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,a<2&&(a=0)}else 1===a&&(r=u.return)&&r.call(u),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);u=n}else if((r=(s=d.n<0)?c:e.call(t,d))!==i)break}catch(r){u=n,a=1,c=r}finally{l=1}}return{value:r,done:s}}}(e,u,a),!0),f}var i={};function l(){}function f(){}function s(){}r=Object.getPrototypeOf;var d=[][u]?r(r([][u]())):(t(r={},u,(function(){return this})),r),p=s.prototype=l.prototype=Object.create(d);function y(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,t(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return f.prototype=s,t(p,"constructor",s),t(s,"constructor",f),f.displayName="GeneratorFunction",t(s,a,"GeneratorFunction"),t(p),t(p,a,"Generator"),t(p,u,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:c,m:y}})()}function t(e,n,r,o){var u=Object.defineProperty;try{u({},"",{})}catch(e){u=0}t=function(e,n,r,o){if(n)u?u(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var a=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};a("next",0),a("throw",1),a("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,u,a){try{var c=e[u](a),i=c.value}catch(e){return void n(e)}c.done?t(i):Promise.resolve(i).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(o,u){var a=e.apply(t,r);function c(e){n(a,o,u,c,i,"next",e)}function i(e){n(a,o,u,c,i,"throw",e)}c(void 0)}))}}System.register(["./index-legacy.dbc04544.js","./authority-legacy.db22ecf3.js","./authorityBtn-legacy.a84008d4.js"],(function(t,n){"use strict";var o,u,a,c,i,l,f,s,d,p,y,v,h,m,b,w,g,k,_,I,j=document.createElement("style");return j.textContent='@charset "UTF-8";.sticky-button{position:sticky;top:2px;z-index:2;background-color:#fff}.fitler{width:60%}.custom-tree-node span+span{margin-left:12px}\n',document.head.appendChild(j),{setters:[function(e){o=e.r,u=e.y,a=e.h,c=e.o,i=e.d,l=e.e,f=e.j,s=e.w,d=e.k,p=e.t,y=e.J,v=e.g,h=e.a4,m=e.a5,b=e.M,w=e.a6,g=e.S},function(e){k=e.u},function(e){_=e.g,I=e.s}],execute:function(){var n={class:"clearfix sticky-button"},j={class:"tree-content"},x={class:"custom-tree-node"},C={key:0},O={class:"dialog-footer"};t("default",Object.assign({name:"Menus"},{props:{row:{default:function(){return{}},type:Object}},emits:["changeRow"],setup:function(t,R){var S=R.expose,T=R.emit,D=t,E=T,z=o(""),G=o([]),N=o([]),P=o(!1),V=o({children:"children",label:function(e){return e.meta.title}}),F=function(){var t=r(e().m((function t(){var n,r,o,u;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,h();case 1:return n=e.v,G.value=n.data.menus,e.n=2,m({authorityId:D.row.authorityId});case 2:r=e.v,o=r.data.menus,u=[],o.forEach((function(e){o.some((function(t){return t.parentId===e.menuId}))||u.push(Number(e.menuId))})),N.value=u;case 3:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();F();var A=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,k({authorityId:D.row.authorityId,AuthorityName:D.row.authorityName,parentId:D.row.parentId,defaultRouter:n.name});case 1:0===(r=e.v).code&&(b({type:"success",message:"设置成功"}),E("changeRow","defaultRouter",r.data.authority.defaultRouter));case 2:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),B=function(){P.value=!0},U=o(null),M=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return n=U.value.getCheckedNodes(!1,!0),e.n=1,w({menus:n,authorityId:D.row.authorityId});case 1:0===e.v.code&&b({type:"success",message:"菜单设置成功!"});case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();S({enterAndNext:function(){M()},needConfirm:P});var J=o(!1),q=o([]),H=o([]),K=o(),L="",Q=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return L=n.ID,e.n=1,_({menuID:L,authorityId:D.row.authorityId});case 1:if(0!==(r=e.v).code){e.n=3;break}return X(n),e.n=2,g();case 2:r.data.selected&&r.data.selected.forEach((function(e){q.value.some((function(t){t.ID===e&&K.value.toggleRowSelection(t,!0)}))}));case 3:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),W=function(e){H.value=e},X=function(e){J.value=!0,q.value=e.menuBtn},Y=function(){J.value=!1},Z=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return n=H.value.map((function(e){return e.ID})),e.n=1,I({menuID:L,selected:n,authorityId:D.row.authorityId});case 1:0===e.v.code&&(b({type:"success",message:"设置成功"}),J.value=!1);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),$=function(e,t){return!e||-1!==t.meta.title.indexOf(e)};return u(z,(function(e){U.value.filter(e)})),function(e,r){var o=a("base-input"),u=a("base-button"),h=a("el-tree"),m=a("el-table-column"),b=a("el-table"),w=a("el-dialog");return c(),i("div",null,[l("div",n,[f(o,{modelValue:z.value,"onUpdate:modelValue":r[0]||(r[0]=function(e){return z.value=e}),class:"fitler",placeholder:"筛选"},null,8,["modelValue"]),f(u,{class:"fl-right",size:"small",type:"primary",onClick:M},{default:s((function(){return r[2]||(r[2]=[d("确 定")])})),_:1,__:[2]})]),l("div",j,[f(h,{ref_key:"menuTree",ref:U,data:G.value,"default-checked-keys":N.value,props:V.value,"default-expand-all":"","highlight-current":"","node-key":"ID","show-checkbox":"","filter-node-method":$,onCheck:B},{default:s((function(e){var n=e.node,o=e.data;return[l("span",x,[l("span",null,p(n.label),1),l("span",null,[f(u,{type:"primary",link:"",size:"small",style:y({color:t.row.defaultRouter===o.name?"#E6A23C":"#85ce61"}),disabled:!n.checked,onClick:function(){return A(o)}},{default:s((function(){return[d(p(t.row.defaultRouter===o.name?"总览":"设为总览"),1)]})),_:2},1032,["style","disabled","onClick"])]),o.menuBtn.length?(c(),i("span",C,[f(u,{type:"primary",link:"",size:"small",onClick:function(){return Q(o)}},{default:s((function(){return r[3]||(r[3]=[d(" 分配按钮 ")])})),_:2,__:[3]},1032,["onClick"])])):v("",!0)])]})),_:1},8,["data","default-checked-keys","props"])]),f(w,{modelValue:J.value,"onUpdate:modelValue":r[1]||(r[1]=function(e){return J.value=e}),title:"分配按钮","destroy-on-close":""},{footer:s((function(){return[l("div",O,[f(u,{size:"small",onClick:Y},{default:s((function(){return r[4]||(r[4]=[d("取 消")])})),_:1,__:[4]}),f(u,{size:"small",type:"primary",onClick:Z},{default:s((function(){return r[5]||(r[5]=[d("确 定")])})),_:1,__:[5]})])]})),default:s((function(){return[f(b,{ref_key:"btnTableRef",ref:K,data:q.value,"row-key":"ID",onSelectionChange:W},{default:s((function(){return[f(m,{type:"selection",width:"55"}),f(m,{label:"按钮名称",prop:"name"}),f(m,{label:"按钮备注",prop:"desc"})]})),_:1},8,["data"])]})),_:1},8,["modelValue"])])}}}))}}}))}();
