/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import o from"./index.f0915e41.js";import{o as s,f as t}from"./index.74d1ee23.js";import"./localLogin.ca8a8b4b.js";import"./wechat.c6b3684b.js";import"./feishu.9eaed167.js";import"./dingtalk.9bda7b4c.js";import"./oauth2.0f97bca4.js";import"./iconfont.2d75af05.js";import"./sms.767211b1.js";import"./secondaryAuth.72a70630.js";import"./verifyCode.19f5cb14.js";const i=Object.assign({name:"ClientNewLogin"},{setup:i=>(i,e)=>(s(),t(o))});export{i as default};
