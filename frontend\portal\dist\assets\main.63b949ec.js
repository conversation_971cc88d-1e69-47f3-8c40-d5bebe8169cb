/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import s from"./index.25c2c499.js";import{_ as a,h as c,o as e,d as t,e as p,j as n,w as o,k as l}from"./index.74d1ee23.js";import"./resource.29b70792.js";const r={class:"access-main"},u={class:"content-wrapper"},m={class:"access-proxy-status"},i={class:"access-proxy-status-span"},d={class:"access-app"};const x=a({name:"Access",components:{AppPage:s}},[["render",function(s,a,x,y,f,_){const b=c("base-button"),j=c("AppPage");return e(),t("div",r,[p("ul",u,[p("li",m,[a[2]||(a[2]=p("span",{class:"access-proxy-status-text"}," 连接状态 ",-1)),p("span",i,[a[1]||(a[1]=p("span",{class:"access-proxy-status-tips"}," 点击连接，即可安全便捷地访问应用 ",-1)),n(b,{class:"access-proxy-status-btn",color:"#626aef",type:"primary"},{default:o((()=>a[0]||(a[0]=[l(" 一键连接 ")]))),_:1,__:[0]})])]),a[3]||(a[3]=p("li",{class:"access-common-status"},[p("span",{class:"access-common-status-span"},[p("span",null,"准入状态（企业网络下使用）："),p("span",{style:{color:"red"}},"未入网"),p("span",null,"（请重新建立连接）")]),p("span",{class:"access-common-status-detail"},[p("span",null,"查看详情")])],-1)),p("li",d,[n(j,{class:"access-app-page"})])])])}],["__scopeId","data-v-79882dcf"]]);export{x as default};
