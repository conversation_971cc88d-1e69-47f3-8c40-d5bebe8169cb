<template>
  <div>
    <base-form
        label-position="right"
        label-width="100px"
        :model="formData"
        style="max-width: 550px"
    >
      <base-form-item
          v-if="type === 'app'"
          label="应用名："
          :rules="[
          {
            required:true,
            message: '应用名不能为空',
            trigger: ['blur']
          }
        ]"
      >
        <base-input v-model="formLabelAlign.name"/>
      </base-form-item>
      <base-form-item label="描述：" prop="description">
        <base-input v-model="formLabelAlign.description"/>
      </base-form-item>
      <base-form-item
          v-if="type === 'app'"
          label="状态："
          prop="state"
      >
        <base-radio-group v-model="radio" class="ml-4">
          <base-radio label="1" size="large">启用</base-radio>
          <base-radio label="2" size="large">禁用</base-radio>
        </base-radio-group>
      </base-form-item>
      <base-form-item label="所属分组：" prop="group">
        <base-select
            v-model="formLabelAlign.group"
            style="width: 100%;"
            placeholder="请选择"
        >
          <base-option
              v-for="item in options"
              :key="item.id"
              :label="item.GroupName"
              :value="item.id"
              :disabled="item.disabled"
          />
        </base-select>
      </base-form-item>
      <base-form-item
          v-if="type === 'app'"
          label="应用地址："
      >
        <el-table :data="formLabelAlign.appAddress" style="width: 100%">
          <el-table-column align="center">
            <template #header>
              <span style="color: red">*</span><span> 协议</span>
            </template>
            <template #default="scope">
              <base-select
                  v-model="formLabelAlign.appAddress[scope.$index].protocol"
                  size="small"
                  placeholder="请选择"
              >
                <base-option
                    v-for="item in protocolType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </base-select>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template #header>
              <span style="color: red">*</span><span> 服务器地址</span>
            </template>
            <template #default="scope">
              <base-input
                  v-model="formLabelAlign.appAddress[scope.$index].serverAddress"
                  size="small"
                  placeholder="请输入地址"
              />
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template #header>
              <span style="color: red">*</span><span> 端口</span>
            </template>
            <template #default="scope">
              <base-input v-model="formLabelAlign.appAddress[scope.$index].port" size="small" placeholder="请输入端口"/>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template #header>
              <span>操作</span>
            </template>
            <template #default="scope">
              <div style="color: darkgrey;font-size: 20px">
                <el-icon @click="addAppAddress">
                  <CirclePlusFilled/>
                </el-icon>
                <el-icon v-if="formLabelAlign.appAddress.length >1" @click="removeAppAddress(scope.$index)">
                  <RemoveFilled/>
                </el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </base-form-item>
      <base-form-item label="所属SDP：" prop="sdp">
        <base-input v-model="formLabelAlign.sdp"/>
      </base-form-item>
      <base-form-item label="Web入口：" prop="webPortal">
        <base-input v-model="formLabelAlign.webPortal"/>
      </base-form-item>
      <base-form-item label="应用图标：" prop="icon">
        <base-radio-group v-model="formLabelAlign.appIcon">
          <base-radio label="1" size="large">图标库选择</base-radio>
          <base-radio label="2" size="large">自定义上传</base-radio>
        </base-radio-group>
      </base-form-item>
      <base-form-item>
        <img
            v-if="formLabelAlign.appIcon === '1'"
            style="height: 70px"
            :src="formLabelAlign.localIcon"
            class="avatar"
            @click="iconSelect"
        >
        <el-upload
            v-if="formLabelAlign.appIcon === '2'"
            style="height: 70px;width: 70px"
            class="avatar-uploader"
            action="127.0.0.1:3000"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
        >
          <img
              v-if="formLabelAlign.iconUrl"
              style="height: 70px;width: 70px"
              :src="formLabelAlign.iconUrl"
              class="avatar"
          >
          <el-icon v-else class="avatar-uploader-icon" style="height: 70px;width: 70px">
            <Plus/>
          </el-icon>
        </el-upload>
      </base-form-item>
    </base-form>
    <span class="dialog-footer" style="padding-left: 65%">
      <base-button @click="submitForm()">取消</base-button>
      <base-button
          color="#256EBF"
          type="primary"
          @click="submitForm()"
      >确定</base-button>
    </span>
    <el-dialog
        v-model="isIconSelect"
        title="请选择图标"
        width="30%"
        :before-close="handleClose"
    >
      <div class="demo-term-box">
        <img
            style="height: 70px;margin-right: 5px"
            src="@/assets/noBody.png"
            alt=""
            @click="selectIcon('/src/assets/noBody.png')"
        >
<!--        <img-->
<!--            style="height: 70px;margin-right: 5px"-->
<!--            src="@/assets/dashboard.png"-->
<!--            alt=""-->
<!--            @click="selectIcon('/src/assets/dashboard.png')"-->
<!--        >-->
        <img
            style="height: 70px;margin-right: 5px"
            src="@/assets/docs.png"
            alt=""
            @click="selectIcon('/src/assets/docs.png')"
        >
        <img
            style="height: 70px;margin-right: 5px"
            src="@/assets/kefu.png"
            alt=""
            @click="selectIcon('/src/assets/kefu.png')"
        >
        <img
            style="height: 70px;margin-right: 5px"
            src="@/assets/video.png"
            alt=""
            @click="selectIcon('/src/assets/video.png')"
        >
<!--        <img-->
<!--            style="height: 70px;margin-right: 5px"-->
<!--            src="@/assets/flipped-aurora.png"-->
<!--            alt=""-->
<!--            @click="selectIcon('/src/assets/flipped-aurora.png')"-->
<!--        >-->
      </div>
      <template #footer>
        <span class="dialog-footer">
          <base-button @click="isIconSelect = false">取消</base-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CustomDialog',
  props: ['type', 'formLabelAlign'], // 控制显示的字段
}
</script>
<script setup>
import { reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  type: {
    type: String,
  },
  formData: {
    type: Object,
    required: true
  },
  options: {
    type: Array,
    required: true
  },

})
const radio = ref('1')
const formLabelAlign = reactive({
  group: '',
  name: '',
  description: '',
  webPortal: '',
  sdp: '',
  appAddress: [{
    protocol: '',
    serverAddress: '',
    port: '',
  }],
  appIcon: '1',
  iconUrl: '',
  localIcon: '/src/assets/noBody.png',
})

if (Object.values(props.formData).length > 0) {
  formLabelAlign.group = props.formData.group
  formLabelAlign.name = props.formData.name
  formLabelAlign.description = props.formData.description
  formLabelAlign.webPortal = props.formData.webPortal
  formLabelAlign.sdp = props.formData.sdp
  formLabelAlign.appAddress = props.formData.appAddress
  formLabelAlign.appIcon = props.formData.appIcon
  formLabelAlign.iconUrl = props.formData.iconUrl
  formLabelAlign.localIcon = props.formData.localIcon
}

console.log('options')
console.log(props.options)
// const options = [
//   {
//     value: 'local',
//     label: '销管系统',
//   },
//   {
//     value: 'LDAP',
//     label: '深圳研发认证服务(LDAP)',
//     disabled: true,
//   },
//   {
//     value: 'qywx',
//     label: '长沙总部微信认证服务(企业微信)',
//   },
//   {
//     value: 'add',
//     label: '新增认证服务',
//   },
// ]

const emits = defineEmits(['submitForm'])
const submitForm = () => {
  console.log(formLabelAlign)
  // 子组件值

  emits('submitForm', false)
}

const protocolType = [{
  value: 'http',
  label: 'http',
}, {
  value: 'https',
  label: 'https',
}]

const handleAvatarSuccess = (response, uploadFile) => {
  console.log(response)
  console.log(uploadFile)
  formLabelAlign.iconUrl = 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
}

const beforeAvatarUpload = (rawFile) => {
  if (rawFile.type !== 'image/jpeg') {
    ElMessage.error('Avatar picture must be JPG format!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('Avatar picture size can not exceed 2MB!')
    return false
  }
  formLabelAlign.iconUrl = 'https://lmg.jj20.com/up/allimg/tp05/1Z9291T012CB-0-lp.jpg'
  return true
}

const isIconSelect = ref(false)
const iconSelect = () => {
  isIconSelect.value = true
}

const handleClose = () => {
  ElMessageBox.confirm('Are you sure to close this dialog?')
      .then(() => {
        done()
      })
      .catch(() => {
        // catch error
      })
}

const selectIcon = (iconUrl) => {
  console.log(iconUrl)
  formLabelAlign.localIcon = iconUrl
  console.log(formLabelAlign.iconUrl)
  isIconSelect.value = false
}
</script>
<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
<style>
.el-table td.el-table__cell div {
  height: 34px;
}
</style>
