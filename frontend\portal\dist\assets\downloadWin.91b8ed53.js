/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as e,I as a,r as o,E as l,J as t,h as n,o as s,g as i,w as d,j as r,C as c,e as u,H as v,T as m,d as p,k as f,f as g,M as w}from"./index.4982c0f9.js";import{_ as h}from"./ASD.492c8837.js";import"./iconfont.2d75af05.js";import{g as b}from"./browser.dd769507.js";const y={style:{background:"'#273444'"}},x={class:"downloadWin"},_={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},k={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-bottom":"42px","margin-top":"60px",display:"none"}},F={key:1,class:"download-complete"},T=e(Object.assign({name:"downloadWin"},{setup(e){a((e=>({"dc46818e-activeBackground":e.activeBackground,"dc46818e-normalText":e.normalText})));const T=o(!1),E=o(!0),j=o(!1),z=o("1"),L=o({});L.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};const B=o(!1),C=o(0),S=o(!1);let R=0;const W=()=>{const e=document.body.clientWidth;e<1e3||e>=1e3&&e<1200?(j.value=!1,E.value=!1,T.value=!0):(j.value=!1,E.value=!0,T.value=!1)};W();const D=o(!1);l((()=>{t.emit("collapse",T.value),t.emit("mobile",j.value),t.on("showLoading",(()=>{D.value=!0})),t.on("closeLoading",(()=>{D.value=!1})),window.onresize=()=>(W(),t.emit("collapse",T.value),void t.emit("mobile",j.value))}));const M=o("#1f2a36"),O=o(!1),U=()=>{T.value=!T.value,E.value=!T.value,O.value=!T.value,t.emit("collapse",T.value)},$=e=>100===e?"下载完成":`${e}%`,q=async(e,a)=>{try{const o=await H(e);I(o,a)}catch(o){if(R<3&&"网络连接超时"===o.message)return R++,q(e,a);throw new Error(`安装包下载失败，请检查网络连接或联系管理员。错误: ${o.message}`)}},H=e=>new Promise(((a,o)=>{const l=new XMLHttpRequest;l.open("GET",e,!0),l.responseType="blob",l.timeout=3e5;let t=Date.now();l.onprogress=e=>{if(e.lengthComputable){const a=e.loaded/e.total*100;C.value=Math.round(a)}else{const a=(Date.now()-t)/1e3,o=60*(e.loaded/a),l=e.loaded/o*100;C.value=Math.min(99,Math.round(l))}},l.onload=()=>{200===l.status?a(l.response):o(new Error(`HTTP 错误: ${l.status}`))},l.onerror=()=>{o(new Error("网络错误"))},l.ontimeout=()=>{o(new Error("网络连接超时"))},l.send()})),I=(e,a)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,a);else{const o=document.createElement("a"),l=document.querySelector("body");o.href=window.URL.createObjectURL(e),o.download=a,o.style.display="none",l.appendChild(o),o.click(),l.removeChild(o),window.URL.revokeObjectURL(o.href)}};return(e,a)=>{const o=n("base-row"),l=n("component"),t=n("el-icon"),W=n("el-menu-item"),D=n("el-menu"),H=n("el-scrollbar"),I=n("Expand"),P=n("Fold"),A=n("base-aside"),G=n("el-link"),J=n("el-progress"),X=n("base-main"),K=n("base-container");return s(),i(K,{class:"layout-cont"},{default:d((()=>[r(K,{class:c([E.value?"openside":"hideside",j.value?"mobile":""])},{default:d((()=>[r(o,{class:c([O.value?"shadowBg":""]),onClick:a[0]||(a[0]=e=>(O.value=!O.value,E.value=!!T.value,void U()))},null,8,["class"]),r(A,{class:"main-cont main-left gva-aside"},{default:d((()=>[u("div",{class:c(["tilte",[E.value?"openlogoimg":"hidelogoimg"]]),style:v({background:M.value})},a[2]||(a[2]=[u("img",{alt:"",class:"logoimg",src:h},null,-1)]),6),u("div",y,[r(H,{style:{height:"calc(100vh - 110px)"}},{default:d((()=>[r(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:d((()=>[r(D,{collapse:T.value,"collapse-transition":!1,"default-active":z.value,"background-color":L.value.background,"active-text-color":L.value.activeText,class:"el-menu-vertical","unique-opened":""},{default:d((()=>[r(W,{index:"1"},{default:d((()=>[r(t,null,{default:d((()=>[r(l,{class:"iconfont icon-zuhu-kehuduanxiazai"})])),_:1}),a[3]||(a[3]=u("span",null,"客户端下载",-1))])),_:1,__:[3]})])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})]),u("div",{class:"footer",style:v({background:M.value})},[u("div",{class:"menu-total",onClick:U},[T.value?(s(),i(t,{key:0,color:"#FFFFFF",size:"14px"},{default:d((()=>[r(I)])),_:1})):(s(),i(t,{key:1,color:"#FFFFFF",size:"14px"},{default:d((()=>[r(P)])),_:1}))])],4)])),_:1}),r(X,{class:"main-cont main-right client"},{default:d((()=>[u("div",x,[u("div",{style:{"margin-bottom":"5%",float:"left","margin-right":"5%",width:"205px",height:"209px",background:"#F1F8FF",position:"relative"},onClick:a[1]||(a[1]=e=>(async e=>{if("windows"===e){B.value=!0,C.value=0,S.value=!1,R=0;try{const a=await b({platform:e});if(0!==a.data.code)throw new Error(a.data.msg);{const e=window.location.port,o=new URL(a.data.data.download_url);let l;e?o.toString().includes("asec-deploy")?l=a.data.data.download_url:(o.port=e,l=o.toString()):(o.port="",l=o.toString());const t=e?a.data.data.latest_filename.replace(/@(\d+)/,`@${e}`):a.data.data.latest_filename;await q(l,t),S.value=!0,w({type:"success",message:"下载完成"})}}catch(a){w({type:"error",message:a.message||"下载失败，请联系管理员"})}finally{B.value=!1,setTimeout((()=>{S.value=!1}),3e3)}}})("windows"))},[(s(),p("svg",_,a[4]||(a[4]=[u("use",{"xlink:href":"#icon-windows"},null,-1)]))),(s(),p("svg",k,a[5]||(a[5]=[u("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),a[8]||(a[8]=u("br",null,null,-1)),r(G,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:d((()=>a[6]||(a[6]=[f(" Windows客户端 ")]))),_:1,__:[6]}),r(G,{class:"window-hidden",underline:!1,style:{"margin-top":"42px",display:"none"}},{default:d((()=>a[7]||(a[7]=[f(" 点击下载Windows客户端 ")]))),_:1,__:[7]}),B.value?(s(),i(J,{key:0,percentage:C.value,format:$,"stroke-width":10,style:{"margin-top":"20px"}},null,8,["percentage"])):g("v-if",!0),S.value?(s(),p("div",F,"下载完成")):g("v-if",!0)])])])),_:1})])),_:1},8,["class"])])),_:1})}}}),[["__scopeId","data-v-dc46818e"],["__file","D:/asec-platform/frontend/portal/src/view/login/downloadWin.vue"]]);export{T as default};
