/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{_ as a,j as n,o as e,a as t,i as s,w as r,b as i,t as l,E as o}from"./index.bfaf04e1.js";const c=a({__name:"warningBar",props:{title:{type:String,default:""},href:{type:String,default:""}},setup(a){const c=a,f=()=>{c.href&&window.open(c.href)};return(c,d)=>{const p=n("warning-filled"),u=n("el-icon");return e(),t("div",{class:o(["warning-bar",a.href&&"can-click"]),onClick:f},[s(u,null,{default:r((()=>[s(p)])),_:1}),i("span",null,l(a.title),1)],2)}}},[["__scopeId","data-v-64538dc2"]]);export{c as W};
