<!doctype html><html lang="zh-cn"><head><undefined></undefined><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="./assets/favicon.2521b69c.ico"><title></title><script>// 实现日志打印封装方法
        class Logger {
            constructor(name, debug) {
                this.name = name
                this.debug = debug
            }
            log(...args) {
                if (!this.debug) {
                    return
                }
                const date = new Date()
                const currTime = date.toLocaleString('zh-CN') + ' ' + date.getMilliseconds()
                console.log(currTime, this.name, ...args)
            }
        }
        const urlHashParams = new URLSearchParams(window.location.hash)
        const logger = new Logger('portal', urlHashParams.get('asec_debug'))
        logger.log('启动')</script><script type="module" crossorigin src="./assets/index.2320e6b9.js"></script><link rel="stylesheet" href="./assets/index.d9de825b.css"><script type="module">try{import.meta.url;import("_").catch(()=>1);}catch(e){}window.__vite_is_modern_browser=true;</script><script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy build because dynamic import or import.meta.url is unsupported, syntax error above should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script></head><body><div id="app"></div><script>try {
            window.globalData = JSON.parse('<?- global_data_string ?>');
        } catch (error) {

        }
        window.getQRCode = function(config){
            new WwLogin(config);
        };</script><script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script><script nomodule crossorigin id="vite-legacy-polyfill" src="./assets/polyfills-legacy.f7123482.js"></script><script nomodule crossorigin id="vite-legacy-entry" data-src="./assets/index-legacy.04f34b53.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script></body><script>// 检测浏览器是否为IE
    var isIE = (!!window.ActiveXObject || "ActiveXObject" in window)

    // 如果是IE浏览器，则提示不支持
    if (isIE) {
        var unsupportedMessage = "<div style='padding: 20px; background-color: #f8d7da; color: #721c24;'>" +
            "<h3>对不起，您正在使用的浏览器版本过低。</h3>" +
            "<p>本网站不支持IE浏览器，请使用其它内核浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。</p>" +
            "<p>如果使用360浏览器、搜狗浏览器等请不要使用兼容模式。</p>" +
            "</div>";

        var body = document.getElementsByTagName("body")[0];
        body.innerHTML = unsupportedMessage;
    }</script></html>