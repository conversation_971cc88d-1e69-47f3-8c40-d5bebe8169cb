/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register([],(function(t,e){"use strict";return{execute:function(){var e,r=t("b",{}),n={},o={},i=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];o.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},o.getSymbolTotalCodewords=function(t){return i[t]},o.getBCHDigit=function(t){for(var e=0;0!==t;)e++,t>>>=1;return e},o.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');e=t},o.isKanjiModeEnabled=function(){return void 0!==e},o.toSJIS=function(t){return e(t)};var a={};function u(){this.buffer=[],this.length=0}!function(t){t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2},t.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},t.from=function(e,r){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw new Error("Unknown EC Level: "+e)}}(e)}catch(n){return r}}}(a),u.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var r=0;r<e;r++)this.putBit(1==(t>>>e-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var s=u;function f(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}f.prototype.set=function(t,e,r,n){var o=t*this.size+e;this.data[o]=r,n&&(this.reservedBit[o]=!0)},f.prototype.get=function(t,e){return this.data[t*this.size+e]},f.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},f.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var c=f,h={};!function(t){var e=o.getSymbolSize;t.getRowColCoords=function(t){if(1===t)return[];for(var r=Math.floor(t/7)+2,n=e(t),o=145===n?26:2*Math.ceil((n-13)/(2*r-2)),i=[n-7],a=1;a<r-1;a++)i[a]=i[a-1]-o;return i.push(6),i.reverse()},t.getPositions=function(e){for(var r=[],n=t.getRowColCoords(e),o=n.length,i=0;i<o;i++)for(var a=0;a<o;a++)0===i&&0===a||0===i&&a===o-1||i===o-1&&0===a||r.push([n[i],n[a]]);return r}}(h);var g={},d=o.getSymbolSize;g.getPositions=function(t){var e=d(t);return[[0,0],[e-7,0],[0,e-7]]};var l={};!function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var e=3,r=3,n=40,o=10;function i(e,r,n){switch(e){case t.Patterns.PATTERN000:return(r+n)%2==0;case t.Patterns.PATTERN001:return r%2==0;case t.Patterns.PATTERN010:return n%3==0;case t.Patterns.PATTERN011:return(r+n)%3==0;case t.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2==0;case t.Patterns.PATTERN101:return r*n%2+r*n%3==0;case t.Patterns.PATTERN110:return(r*n%2+r*n%3)%2==0;case t.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}t.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(t){for(var r=t.size,n=0,o=0,i=0,a=null,u=null,s=0;s<r;s++){o=i=0,a=u=null;for(var f=0;f<r;f++){var c=t.get(s,f);c===a?o++:(o>=5&&(n+=e+(o-5)),a=c,o=1),(c=t.get(f,s))===u?i++:(i>=5&&(n+=e+(i-5)),u=c,i=1)}o>=5&&(n+=e+(o-5)),i>=5&&(n+=e+(i-5))}return n},t.getPenaltyN2=function(t){for(var e=t.size,n=0,o=0;o<e-1;o++)for(var i=0;i<e-1;i++){var a=t.get(o,i)+t.get(o,i+1)+t.get(o+1,i)+t.get(o+1,i+1);4!==a&&0!==a||n++}return n*r},t.getPenaltyN3=function(t){for(var e=t.size,r=0,o=0,i=0,a=0;a<e;a++){o=i=0;for(var u=0;u<e;u++)o=o<<1&2047|t.get(a,u),u>=10&&(1488===o||93===o)&&r++,i=i<<1&2047|t.get(u,a),u>=10&&(1488===i||93===i)&&r++}return r*n},t.getPenaltyN4=function(t){for(var e=0,r=t.data.length,n=0;n<r;n++)e+=t.data[n];return Math.abs(Math.ceil(100*e/r/5)-10)*o},t.applyMask=function(t,e){for(var r=e.size,n=0;n<r;n++)for(var o=0;o<r;o++)e.isReserved(o,n)||e.xor(o,n,i(t,o,n))},t.getBestMask=function(e,r){for(var n=Object.keys(t.Patterns).length,o=0,i=1/0,a=0;a<n;a++){r(a),t.applyMask(a,e);var u=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(a,e),u<i&&(i=u,o=a)}return o}}(l);var v={},p=a,w=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],m=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];v.getBlocksCount=function(t,e){switch(e){case p.L:return w[4*(t-1)+0];case p.M:return w[4*(t-1)+1];case p.Q:return w[4*(t-1)+2];case p.H:return w[4*(t-1)+3];default:return}},v.getTotalCodewordsCount=function(t,e){switch(e){case p.L:return m[4*(t-1)+0];case p.M:return m[4*(t-1)+1];case p.Q:return m[4*(t-1)+2];case p.H:return m[4*(t-1)+3];default:return}};var E={},y={},A=new Uint8Array(512),C=new Uint8Array(256);!function(){for(var t=1,e=0;e<255;e++)A[e]=t,C[t]=e,256&(t<<=1)&&(t^=285);for(var r=255;r<512;r++)A[r]=A[r-255]}(),y.log=function(t){if(t<1)throw new Error("log("+t+")");return C[t]},y.exp=function(t){return A[t]},y.mul=function(t,e){return 0===t||0===e?0:A[C[t]+C[e]]},function(t){var e=y;t.mul=function(t,r){for(var n=new Uint8Array(t.length+r.length-1),o=0;o<t.length;o++)for(var i=0;i<r.length;i++)n[o+i]^=e.mul(t[o],r[i]);return n},t.mod=function(t,r){for(var n=new Uint8Array(t);n.length-r.length>=0;){for(var o=n[0],i=0;i<r.length;i++)n[i]^=e.mul(r[i],o);for(var a=0;a<n.length&&0===n[a];)a++;n=n.slice(a)}return n},t.generateECPolynomial=function(r){for(var n=new Uint8Array([1]),o=0;o<r;o++)n=t.mul(n,new Uint8Array([1,e.exp(o)]));return n}}(E);var B=E;function I(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}I.prototype.initialize=function(t){this.degree=t,this.genPoly=B.generateECPolynomial(this.degree)},I.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");var e=new Uint8Array(t.length+this.degree);e.set(t);var r=B.mod(e,this.genPoly),n=this.degree-r.length;if(n>0){var o=new Uint8Array(this.degree);return o.set(r,n),o}return r};var M=I,T={},N={},P={isValid:function(t){return!isNaN(t)&&t>=1&&t<=40}},b={},R="[0-9]+",L="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",U="(?:(?![A-Z0-9 $%*+\\-./:]|"+(L=L.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";b.KANJI=new RegExp(L,"g"),b.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),b.BYTE=new RegExp(U,"g"),b.NUMERIC=new RegExp(R,"g"),b.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");var x=new RegExp("^"+L+"$"),S=new RegExp("^"+R+"$"),k=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");b.testKanji=function(t){return x.test(t)},b.testNumeric=function(t){return S.test(t)},b.testAlphanumeric=function(t){return k.test(t)},function(t){var e=P,r=b;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(t,r){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!e.isValid(r))throw new Error("Invalid version: "+r);return r>=1&&r<10?t.ccBits[0]:r<27?t.ccBits[1]:t.ccBits[2]},t.getBestModeForData=function(e){return r.testNumeric(e)?t.NUMERIC:r.testAlphanumeric(e)?t.ALPHANUMERIC:r.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},t.isValid=function(t){return t&&t.bit&&t.ccBits},t.from=function(e,r){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(n){return r}}}(N),function(t){var e=o,r=v,n=a,i=N,u=P,s=e.getBCHDigit(7973);function f(t,e){return i.getCharCountIndicator(t,e)+4}function c(t,e){var r=0;return t.forEach((function(t){var n=f(t.mode,e);r+=n+t.getBitsLength()})),r}t.from=function(t,e){return u.isValid(t)?parseInt(t,10):e},t.getCapacity=function(t,n,o){if(!u.isValid(t))throw new Error("Invalid QR Code version");void 0===o&&(o=i.BYTE);var a=8*(e.getSymbolTotalCodewords(t)-r.getTotalCodewordsCount(t,n));if(o===i.MIXED)return a;var s=a-f(o,t);switch(o){case i.NUMERIC:return Math.floor(s/10*3);case i.ALPHANUMERIC:return Math.floor(s/11*2);case i.KANJI:return Math.floor(s/13);case i.BYTE:default:return Math.floor(s/8)}},t.getBestVersionForData=function(e,r){var o,a=n.from(r,n.M);if(Array.isArray(e)){if(e.length>1)return function(e,r){for(var n=1;n<=40;n++)if(c(e,n)<=t.getCapacity(n,r,i.MIXED))return n}(e,a);if(0===e.length)return 1;o=e[0]}else o=e;return function(e,r,n){for(var o=1;o<=40;o++)if(r<=t.getCapacity(o,n,e))return o}(o.mode,o.getLength(),a)},t.getEncodedBits=function(t){if(!u.isValid(t)||t<7)throw new Error("Invalid QR Code version");for(var r=t<<12;e.getBCHDigit(r)-s>=0;)r^=7973<<e.getBCHDigit(r)-s;return t<<12|r}}(T);var F={},z=o,D=z.getBCHDigit(1335);F.getEncodedBits=function(t,e){for(var r=t.bit<<3|e,n=r<<10;z.getBCHDigit(n)-D>=0;)n^=1335<<z.getBCHDigit(n)-D;return 21522^(r<<10|n)};var H={},J=N;function K(t){this.mode=J.NUMERIC,this.data=t.toString()}K.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},K.prototype.getLength=function(){return this.data.length},K.prototype.getBitsLength=function(){return K.getBitsLength(this.data.length)},K.prototype.write=function(t){var e,r,n;for(e=0;e+3<=this.data.length;e+=3)r=this.data.substr(e,3),n=parseInt(r,10),t.put(n,10);var o=this.data.length-e;o>0&&(r=this.data.substr(e),n=parseInt(r,10),t.put(n,3*o+1))};var Y=K,_=N,V=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function j(t){this.mode=_.ALPHANUMERIC,this.data=t}j.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},j.prototype.getLength=function(){return this.data.length},j.prototype.getBitsLength=function(){return j.getBitsLength(this.data.length)},j.prototype.write=function(t){var e;for(e=0;e+2<=this.data.length;e+=2){var r=45*V.indexOf(this.data[e]);r+=V.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(V.indexOf(this.data[e]),6)};var O=j,q=N;function Q(t){this.mode=q.BYTE,this.data="string"==typeof t?(new TextEncoder).encode(t):new Uint8Array(t)}Q.getBitsLength=function(t){return 8*t},Q.prototype.getLength=function(){return this.data.length},Q.prototype.getBitsLength=function(){return Q.getBitsLength(this.data.length)},Q.prototype.write=function(t){for(var e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)};var $=Q,Z=N,X=o;function W(t){this.mode=Z.KANJI,this.data=t}W.getBitsLength=function(t){return 13*t},W.prototype.getLength=function(){return this.data.length},W.prototype.getBitsLength=function(){return W.getBitsLength(this.data.length)},W.prototype.write=function(t){var e;for(e=0;e<this.data.length;e++){var r=X.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else{if(!(r>=57408&&r<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r-=49472}r=192*(r>>>8&255)+(255&r),t.put(r,13)}};var G=W,tt={exports:{}};!function(t){var e={single_source_shortest_paths:function(t,r,n){var o={},i={};i[r]=0;var a,u,s,f,c,h,g,d=e.PriorityQueue.make();for(d.push(r,0);!d.empty();)for(s in u=(a=d.pop()).value,f=a.cost,c=t[u]||{})c.hasOwnProperty(s)&&(h=f+c[s],g=i[s],(void 0===i[s]||g>h)&&(i[s]=h,d.push(s,h),o[s]=u));if(void 0!==n&&void 0===i[n]){var l=["Could not find a path from ",r," to ",n,"."].join("");throw new Error(l)}return o},extract_shortest_path_from_predecessor_list:function(t,e){for(var r=[],n=e;n;)r.push(n),t[n],n=t[n];return r.reverse(),r},find_path:function(t,r,n){var o=e.single_source_shortest_paths(t,r,n);return e.extract_shortest_path_from_predecessor_list(o,n)},PriorityQueue:{make:function(t){var r,n=e.PriorityQueue,o={};for(r in t=t||{},n)n.hasOwnProperty(r)&&(o[r]=n[r]);return o.queue=[],o.sorter=t.sorter||n.default_sorter,o},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var r={value:t,cost:e};this.queue.push(r),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e}(tt),function(t){var e=N,r=Y,n=O,i=$,a=G,u=b,s=o,f=tt.exports;function c(t){return unescape(encodeURIComponent(t)).length}function h(t,e,r){for(var n,o=[];null!==(n=t.exec(r));)o.push({data:n[0],index:n.index,mode:e,length:n[0].length});return o}function g(t){var r,n,o=h(u.NUMERIC,e.NUMERIC,t),i=h(u.ALPHANUMERIC,e.ALPHANUMERIC,t);return s.isKanjiModeEnabled()?(r=h(u.BYTE,e.BYTE,t),n=h(u.KANJI,e.KANJI,t)):(r=h(u.BYTE_KANJI,e.BYTE,t),n=[]),o.concat(i,r,n).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function d(t,o){switch(o){case e.NUMERIC:return r.getBitsLength(t);case e.ALPHANUMERIC:return n.getBitsLength(t);case e.KANJI:return a.getBitsLength(t);case e.BYTE:return i.getBitsLength(t)}}function l(t,o){var u,f=e.getBestModeForData(t);if((u=e.from(o,f))!==e.BYTE&&u.bit<f.bit)throw new Error('"'+t+'" cannot be encoded with mode '+e.toString(u)+".\n Suggested mode is: "+e.toString(f));switch(u!==e.KANJI||s.isKanjiModeEnabled()||(u=e.BYTE),u){case e.NUMERIC:return new r(t);case e.ALPHANUMERIC:return new n(t);case e.KANJI:return new a(t);case e.BYTE:return new i(t)}}t.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(l(e,null)):e.data&&t.push(l(e.data,e.mode)),t}),[])},t.fromString=function(r,n){for(var o=function(t){for(var r=[],n=0;n<t.length;n++){var o=t[n];switch(o.mode){case e.NUMERIC:r.push([o,{data:o.data,mode:e.ALPHANUMERIC,length:o.length},{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.ALPHANUMERIC:r.push([o,{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.KANJI:r.push([o,{data:o.data,mode:e.BYTE,length:c(o.data)}]);break;case e.BYTE:r.push([{data:o.data,mode:e.BYTE,length:c(o.data)}])}}return r}(g(r,s.isKanjiModeEnabled())),i=function(t,r){for(var n={},o={start:{}},i=["start"],a=0;a<t.length;a++){for(var u=t[a],s=[],f=0;f<u.length;f++){var c=u[f],h=""+a+f;s.push(h),n[h]={node:c,lastCount:0},o[h]={};for(var g=0;g<i.length;g++){var l=i[g];n[l]&&n[l].node.mode===c.mode?(o[l][h]=d(n[l].lastCount+c.length,c.mode)-d(n[l].lastCount,c.mode),n[l].lastCount+=c.length):(n[l]&&(n[l].lastCount=c.length),o[l][h]=d(c.length,c.mode)+4+e.getCharCountIndicator(c.mode,r))}}i=s}for(var v=0;v<i.length;v++)o[i[v]].end=0;return{map:o,table:n}}(o,n),a=f.find_path(i.map,"start","end"),u=[],h=1;h<a.length-1;h++)u.push(i.table[a[h]].node);return t.fromArray(function(t){return t.reduce((function(t,e){var r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(u))},t.rawSplit=function(e){return t.fromArray(g(e,s.isKanjiModeEnabled()))}}(H);var et=o,rt=a,nt=s,ot=c,it=h,at=g,ut=l,st=v,ft=M,ct=T,ht=F,gt=N,dt=H;function lt(t,e,r){var n,o,i=t.size,a=ht.getEncodedBits(e,r);for(n=0;n<15;n++)o=1==(a>>n&1),n<6?t.set(n,8,o,!0):n<8?t.set(n+1,8,o,!0):t.set(i-15+n,8,o,!0),n<8?t.set(8,i-n-1,o,!0):n<9?t.set(8,15-n-1+1,o,!0):t.set(8,15-n-1,o,!0);t.set(i-8,8,1,!0)}function vt(t,e,r){var n=new nt;r.forEach((function(e){n.put(e.mode.bit,4),n.put(e.getLength(),gt.getCharCountIndicator(e.mode,t)),e.write(n)}));var o=8*(et.getSymbolTotalCodewords(t)-st.getTotalCodewordsCount(t,e));for(n.getLengthInBits()+4<=o&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);for(var i=(o-n.getLengthInBits())/8,a=0;a<i;a++)n.put(a%2?17:236,8);return function(t,e,r){for(var n=et.getSymbolTotalCodewords(e),o=st.getTotalCodewordsCount(e,r),i=n-o,a=st.getBlocksCount(e,r),u=a-n%a,s=Math.floor(n/a),f=Math.floor(i/a),c=f+1,h=s-f,g=new ft(h),d=0,l=new Array(a),v=new Array(a),p=0,w=new Uint8Array(t.buffer),m=0;m<a;m++){var E=m<u?f:c;l[m]=w.slice(d,d+E),v[m]=g.encode(l[m]),d+=E,p=Math.max(p,E)}var y,A,C=new Uint8Array(n),B=0;for(y=0;y<p;y++)for(A=0;A<a;A++)y<l[A].length&&(C[B++]=l[A][y]);for(y=0;y<h;y++)for(A=0;A<a;A++)C[B++]=v[A][y];return C}(n,t,e)}function pt(t,e,r,n){var o;if(Array.isArray(t))o=dt.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");var i=e;if(!i){var a=dt.rawSplit(t);i=ct.getBestVersionForData(a,r)}o=dt.fromString(t,i||40)}var u=ct.getBestVersionForData(o,r);if(!u)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<u)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+u+".\n")}else e=u;var s=vt(e,r,o),f=et.getSymbolSize(e),c=new ot(f);return function(t,e){for(var r=t.size,n=at.getPositions(e),o=0;o<n.length;o++)for(var i=n[o][0],a=n[o][1],u=-1;u<=7;u++)if(!(i+u<=-1||r<=i+u))for(var s=-1;s<=7;s++)a+s<=-1||r<=a+s||(u>=0&&u<=6&&(0===s||6===s)||s>=0&&s<=6&&(0===u||6===u)||u>=2&&u<=4&&s>=2&&s<=4?t.set(i+u,a+s,!0,!0):t.set(i+u,a+s,!1,!0))}(c,e),function(t){for(var e=t.size,r=8;r<e-8;r++){var n=r%2==0;t.set(r,6,n,!0),t.set(6,r,n,!0)}}(c),function(t,e){for(var r=it.getPositions(e),n=0;n<r.length;n++)for(var o=r[n][0],i=r[n][1],a=-2;a<=2;a++)for(var u=-2;u<=2;u++)-2===a||2===a||-2===u||2===u||0===a&&0===u?t.set(o+a,i+u,!0,!0):t.set(o+a,i+u,!1,!0)}(c,e),lt(c,r,0),e>=7&&function(t,e){for(var r,n,o,i=t.size,a=ct.getEncodedBits(e),u=0;u<18;u++)r=Math.floor(u/3),n=u%3+i-8-3,o=1==(a>>u&1),t.set(r,n,o,!0),t.set(n,r,o,!0)}(c,e),function(t,e){for(var r=t.size,n=-1,o=r-1,i=7,a=0,u=r-1;u>0;u-=2)for(6===u&&u--;;){for(var s=0;s<2;s++)if(!t.isReserved(o,u-s)){var f=!1;a<e.length&&(f=1==(e[a]>>>i&1)),t.set(o,u-s,f),-1===--i&&(a++,i=7)}if((o+=n)<0||r<=o){o-=n,n=-n;break}}}(c,s),isNaN(n)&&(n=ut.getBestMask(c,lt.bind(null,c,r))),ut.applyMask(n,c),lt(c,r,n),{modules:c,version:e,errorCorrectionLevel:r,maskPattern:n,segments:o}}n.create=function(t,e){if(void 0===t||""===t)throw new Error("No input text");var r,n,o=rt.M;return void 0!==e&&(o=rt.from(e.errorCorrectionLevel,rt.M),r=ct.from(e.version),n=ut.from(e.maskPattern),e.toSJISFunc&&et.setToSJISFunction(e.toSJISFunc)),pt(t,r,o,n)};var wt={},mt={};!function(t){function e(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");var e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");var r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}t.getOptions=function(t){t||(t={}),t.color||(t.color={});var r=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:n,scale:n?4:o,margin:r,color:{dark:e(t.color.dark||"#000000ff"),light:e(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},t.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},t.getImageWidth=function(e,r){var n=t.getScale(e,r);return Math.floor((e+2*r.margin)*n)},t.qrToImageData=function(e,r,n){for(var o=r.modules.size,i=r.modules.data,a=t.getScale(o,n),u=Math.floor((o+2*n.margin)*a),s=n.margin*a,f=[n.color.light,n.color.dark],c=0;c<u;c++)for(var h=0;h<u;h++){var g=4*(c*u+h),d=n.color.light;c>=s&&h>=s&&c<u-s&&h<u-s&&(d=f[i[Math.floor((c-s)/a)*o+Math.floor((h-s)/a)]?1:0]),e[g++]=d.r,e[g++]=d.g,e[g++]=d.b,e[g]=d.a}}}(mt),function(t){var e=mt;t.render=function(t,r,n){var o=n,i=r;void 0!==o||r&&r.getContext||(o=r,r=void 0),r||(i=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),o=e.getOptions(o);var a=e.getImageWidth(t.modules.size,o),u=i.getContext("2d"),s=u.createImageData(a,a);return e.qrToImageData(s.data,t,o),function(t,e,r){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=r,e.width=r,e.style.height=r+"px",e.style.width=r+"px"}(u,i,a),u.putImageData(s,0,0),i},t.renderToDataURL=function(e,r,n){var o=n;void 0!==o||r&&r.getContext||(o=r,r=void 0),o||(o={});var i=t.render(e,r,o),a=o.type||"image/png",u=o.rendererOpts||{};return i.toDataURL(a,u.quality)}}(wt);var Et={},yt=mt;function At(t,e){var r=t.a/255,n=e+'="'+t.hex+'"';return r<1?n+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function Ct(t,e,r){var n=t+e;return void 0!==r&&(n+=" "+r),n}Et.render=function(t,e,r){var n=yt.getOptions(e),o=t.modules.size,i=t.modules.data,a=o+2*n.margin,u=n.color.light.a?"<path "+At(n.color.light,"fill")+' d="M0 0h'+a+"v"+a+'H0z"/>':"",s="<path "+At(n.color.dark,"stroke")+' d="'+function(t,e,r){for(var n="",o=0,i=!1,a=0,u=0;u<t.length;u++){var s=Math.floor(u%e),f=Math.floor(u/e);s||i||(i=!0),t[u]?(a++,u>0&&s>0&&t[u-1]||(n+=i?Ct("M",s+r,.5+f+r):Ct("m",o,0),o=0,i=!1),s+1<e&&t[u+1]||(n+=Ct("h",a),a=0)):o++}return n}(i,o,n.margin)+'"/>',f='viewBox="0 0 '+a+" "+a+'"',c='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+f+' shape-rendering="crispEdges">'+u+s+"</svg>\n";return"function"==typeof r&&r(null,c),c};var Bt=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},It=n,Mt=wt,Tt=Et;function Nt(t,e,r,n,o){var i=[].slice.call(arguments,1),a=i.length,u="function"==typeof i[a-1];if(!u&&!Bt())throw new Error("Callback required as last argument");if(!u){if(a<1)throw new Error("Too few arguments provided");return 1===a?(r=e,e=n=void 0):2!==a||e.getContext||(n=r,r=e,e=void 0),new Promise((function(o,i){try{var a=It.create(r,n);o(t(a,e,n))}catch(u){i(u)}}))}if(a<2)throw new Error("Too few arguments provided");2===a?(o=r,r=e,e=n=void 0):3===a&&(e.getContext&&void 0===o?(o=n,n=void 0):(o=n,n=r,r=e,e=void 0));try{var s=It.create(r,n);o(null,t(s,e,n))}catch(f){o(f)}}r.create=It.create,r.toCanvas=Nt.bind(null,Mt.render),r.toDataURL=Nt.bind(null,Mt.renderToDataURL),r.toString=Nt.bind(null,(function(t,e,r){return Tt.render(t,r)}))}}}));
