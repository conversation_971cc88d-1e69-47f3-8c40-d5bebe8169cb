/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{r as e,u as t,v as i,o as n,d as a,e as s,k as o,B as r,_ as d}from"./index.4982c0f9.js";const u={style:{"text-align":"center"}},c={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},l={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},h=d(Object.assign({name:"Feishu"},{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(d){const h=e(0),p=d,f=t(),g=async()=>{var e,t,i,n;await(async()=>{const e={type:"feishu",data:{idpId:p.auth_id}},t=await r(e);if(200===t.status)return t.data.uniqKey})();const a=p.auth_info.fsAppId;let s=`${window.location.origin}/#/status?stat=0`;if(null==(e=f.query)?void 0:e.redirect){const e=(null==(t=f.query)?void 0:t.redirect.indexOf("?"))>-1?null==(n=f.query)?void 0:n.redirect.substring((null==(i=f.query)?void 0:i.redirect.indexOf("?"))+1):"";s=s+"&"+e}else if(f.query){const e=new URLSearchParams;for(const[t,i]of Object.entries(f.query))e.append(t,i);s=s+"&"+e.toString()}setTimeout((()=>{var e=`https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=${a}&redirect_uri=${encodeURIComponent(s+"&auth_type=feishu")}&response_type=code&state=${p.auth_id}`,t=window.QRLogin({id:"login_container",goto:`${e}`,style:"width:300px;height:300px; border: 0; background-size: cover"}),i=function(i){var n=i.origin;if(t.matchOrigin(n)&&window.location.href.indexOf("/login")>-1){var a=i.data;window.location.href=`${e}&tmp_code=${a}`}};void 0!==window.addEventListener?window.addEventListener("message",i,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",i)}),100)};return g(),i(p,(async(e,t)=>{h.value++,await g()})),(e,t)=>(n(),a("div",{key:h.value},[s("div",u,[s("span",c,[(n(),a("svg",l,t[0]||(t[0]=[s("use",{"xlink:href":"#icon-auth-feishu"},null,-1)]))),t[1]||(t[1]=o(" 飞书认证 "))])]),t[2]||(t[2]=s("div",{id:"login_container",slot:"content",class:"wechat-class"},null,-1))]))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/feishu/feishu.vue"]]);export{h as default};
