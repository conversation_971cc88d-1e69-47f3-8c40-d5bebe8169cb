<template xmlns="http://www.w3.org/1999/html">
  <base-container class="layout-cont">
    <div :class="[isSider?'openside':'hideside',isMobile ? 'mobile': '']" class="layout-wrapper">
      <div :class="[isShadowBg?'shadowBg':'']" @click="changeShadow()" class="shadow-overlay" />
      <base-aside class="main-cont main-left gva-aside" :collapsed="isCollapse">
        <div class="tilte" :class="[isSider?'openlogoimg':'hidelogoimg']" :style="{background: backgroundColor}">
          <img alt class="logoimg" src="@/assets/ASD.png">
          <!--          <div>-->
          <!--            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>-->
          <!--            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>-->
          <!--          </div>-->
        </div>
        <Aside class="aside" />
        <div class="footer" :style="{background: backgroundColor}">
          <div class="menu-total" @click="totalCollapse">
            <svg v-if="isCollapse" class="icon" style="color: #FFFFFF; font-size: 14px;" aria-hidden="true">
              <use xlink:href="#icon-expand" />
            </svg>
            <svg v-else class="icon" style="color: #FFFFFF; font-size: 14px;" aria-hidden="true">
              <use xlink:href="#icon-fold" />
            </svg>
          </div>
        </div>
      </base-aside>
      <!-- 分块滑动功能 -->
      <base-main class="main-cont main-right">
        <transition :duration="{ enter: 800, leave: 100 }" mode="out-in" name="el-fade-in-linear">
          <div
            :style="{width: `calc(100% - ${isMobile?'0px':isCollapse?'54px':'220px'})`}"
            class="topfix"
          >
            <div class="header-row">
              <div class="header-col">
                <header class="header-cont">
                  <div class="header-content pd-0">
                    <div class="header-menu-col" style="z-index:100">
                      <!--                      <div class="menu-total" @click="totalCollapse">-->
                      <!--                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>-->
                      <!--                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>-->
                      <!--                      </div>-->
                    </div>
                    <div class="breadcrumb-col">
                      <nav class="breadcrumb">
                        <div
                          v-for="item in matched.slice(1,matched.length)"
                          :key="item.path"
                          class="breadcrumb-item"
                        >{{ fmtTitle(item.meta.topTitle || '', route) }}
                          <select
                            v-if="item.meta.title === '总览'"
                            v-model="day"
                            class="day-select form-select"
                          >
                            <option value="7">最近7天</option>
                            <option value="30">最近30天</option>
                            <option value="90">最近90天</option>
                          </select>
                        </div>
                      </nav>
                    </div>
                    <div class="user-col">
                      <div class="right-box">
                        <!--                        <Search />-->
                        <div class="dropdown" @click="toggleDropdown">
                          <div class="dp-flex justify-content-center align-items height-full width-full">
                            <span class="header-avatar" style="cursor: pointer">
                              <!-- 展示当前登录用户名 -->
                              <span style="margin-right: 9px;color: #252631">{{
                                userStore.userInfo.displayName ? userStore.userInfo.displayName : userStore.userInfo.name
                              }}</span>
                              <svg class="icon" style="font-size: 10px; color: #252631; opacity: 0.5;" aria-hidden="true">
                                <use xlink:href="#icon-caret-bottom" />
                              </svg>
                            </span>
                          </div>
                          <div v-if="dropdownVisible" class="dropdown-menu">
                            <!-- <div class="dropdown-item">
                              <span style="font-weight: 600;">
                                当前角色：{{ JSONPath('$..roles[0][name]', userStore.userInfo)[0] }}
                              </span>
                            </div> -->
                            <div class="dropdown-item" @click="toPerson">
                              <svg class="icon" aria-hidden="true"><use xlink:href="#icon-avatar" /></svg>
                              个人信息
                            </div>
                            <div class="dropdown-item" @click="loginOut()">
                              <svg class="icon" aria-hidden="true"><use xlink:href="#icon-reading-lamp" /></svg>
                              登 出
                            </div>
                          </div>
                        </div>
                        <!--                        <base-button type="text"-->
                        <!--                                   class="iconfont icon-rizhi1"-->
                        <!--                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"-->
                        <!--                                   @click="toLog"-->
                        <!--                        >日志中心-->
                        <!--                        </base-button>-->
                      </div>
                    </div>
                  </div>
                </header>
              </div>
            </div>
            <!-- 当前面包屑用路由自动生成可根据需求修改 -->
            <!--
            :to="{ path: item.path }" 暂时注释不用-->
            <!--            <HistoryComponent ref="layoutHistoryComponent"/>-->
          </div>
        </transition>
        <router-view
          v-if="reloadFlag"
          v-slot="{ Component }"
          v-loading="loadingFlag"
          element-loading-text="正在加载中"
          class="admin-box"
        >
          <div>
            <transition mode="out-in" name="el-fade-in-linear">
              <keep-alive :include="routerStore.keepAliveRouters">
                <component :is="Component" />
              </keep-alive>
            </transition>
          </div>
        </router-view>
        <!--        <BottomInfo />-->
        <!--        <setting />-->
      </base-main>
    </div>

  </base-container>
</template>

<script>
export default {
  name: 'Layout',
}
</script>

<script setup>
import Aside from '@/view/layout/aside/index.vue'
// 使用轻量级 SVG 图标，已在 main.js 中全局加载
// import HistoryComponent from '@/view/layout/aside/historyComponent/history.vue'
// CustomPic 组件已删除
import { setUserAuthority } from '@/api/user'
import { emitter } from '@/utils/bus.js'
import { computed, ref, onMounted, nextTick, getCurrentInstance, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useRouterStore } from '@/pinia/modules/router'
import { fmtTitle } from '@/utils/fmtRouterTitle'
import { JSONPath } from 'jsonpath-plus'
import { useUserStore } from '@/pinia/modules/user'
import { getUserInfo } from '@/api/user'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const routerStore = useRouterStore()
// 三种窗口适配
const isCollapse = ref(true)
const isSider = ref(false)
const isMobile = ref(false)

// 时间选择
const day = ref('7')

const initPage = () => {
  const screenWidth = document.body.clientWidth
  if (screenWidth < 1000) {
    isMobile.value = false
    isSider.value = false
    isCollapse.value = true
  } else if (screenWidth >= 1000 && screenWidth < 1200) {
    isMobile.value = false
    isSider.value = false
    isCollapse.value = true
  } else {
    isMobile.value = false
    isSider.value = false
    isCollapse.value = true
  }
}

initPage()

const loadingFlag = ref(false)
onMounted(() => {
  // 挂载一些通用的事件
  emitter.emit('collapse', isCollapse.value)
  emitter.emit('mobile', isMobile.value)
  emitter.on('reload', reload)
  emitter.on('showLoading', () => {
    loadingFlag.value = true
  })
  emitter.on('closeLoading', () => {
    loadingFlag.value = false
  })
  window.onresize = () => {
    return (() => {
      initPage()
      emitter.emit('collapse', isCollapse.value)
      emitter.emit('mobile', isMobile.value)
    })()
  }
  if (userStore.loadingInstance) {
    userStore.loadingInstance.close()
  }
})

const textColor = computed(() => {
  if (userStore.sideMode === 'dark') {
    return '#fff'
  } else if (userStore.sideMode === 'light') {
    return '#273444'
  } else {
    return userStore.baseColor
  }
})

const backgroundColor = computed(() => {
  if (userStore.sideMode === 'dark') {
    return '#273444'
  } else if (userStore.sideMode === 'light') {
    return '#fff'
  } else {
    return userStore.sideMode
  }
})

const matched = computed(() => route.meta.matched)

const changeUserAuth = async(id) => {
  const res = await setUserAuthority({
    authorityId: id,
  })
  if (res.code === 0) {
    window.sessionStorage.setItem('needCloseAll', 'true')
    window.location.reload()
  }
}

const reloadFlag = ref(true)
let reloadTimer = null
const reload = async() => {
  if (reloadTimer) {
    window.clearTimeout(reloadTimer)
  }
  reloadTimer = window.setTimeout(async() => {
    if (route.meta.keepAlive) {
      reloadFlag.value = false
      await nextTick()
      reloadFlag.value = true
    } else {
      const title = route.meta.title
      router.push({ name: 'Reload', params: { title }})
    }
  }, 400)
}

const isShadowBg = ref(false)
const dropdownVisible = ref(false)

const totalCollapse = () => {
  isCollapse.value = !isCollapse.value
  isSider.value = !isCollapse.value
  isShadowBg.value = !isCollapse.value
  emitter.emit('collapse', isCollapse.value)
}

const toggleDropdown = () => {
  dropdownVisible.value = !dropdownVisible.value
}

const toPerson = () => {
  router.push({ name: 'person' })
}

const toLog = () => {
  router.push({ name: 'log' })
}

const changeShadow = () => {
  isShadowBg.value = !isShadowBg.value
  isSider.value = !!isCollapse.value
  totalCollapse()
}

import Cookies from 'js-cookie'
const instance = getCurrentInstance()
const loginOut = async () => {
  const host = document.location.protocol + '//' + document.location.host
  const clineData = {
    action: 1,
    msg: '',
    platform: document.location.hostname
  }
  const websocket = ref({})
  const wsUrl = ref('ws://127.0.0.1:50001')
  const platform = navigator.platform
  if (platform.indexOf('Mac') === 0 || platform === 'MacIntel') {
    wsUrl.value = 'wss://127.0.0.1:50001'
  }
  const initWebSocket = () => {
    websocket.value = new WebSocket(wsUrl.value)
    websocket.value.onopen = async () => {
      console.log('socket连接成功')
      await  sendMessage(JSON.stringify(clineData))
    }
    websocket.value.onmessage = async (e) => {
      console.log(e)
      await closeWebSocket()
    }
    websocket.value.onerror = () => {
      console.log('socket连接错误')
    }
  }
  // 发送消息
  const sendMessage = async (msg) => {
    console.log(msg, '0')
    await websocket.value.send(msg)
  }
  // 关闭链接（在页面销毁时可销毁链接）
  const closeWebSocket = async () => {
    console.log('socket断开链接')
    await websocket.value.close()
  }
  console.log(`asecagent://?web=${JSON.stringify(clineData)}`)
  await userStore.LoginOut()
  initWebSocket()
  Cookies.remove('asce_sms')
//   instance.appContext.config.globalProperties.$keycloak.loadUserProfile()
  // instance.appContext.config.globalProperties.$keycloak.logout()
//   instance.appContext.config.globalProperties.$keycloak.logout('http://*************:8080/auth/realms/default')
}
provide('day', day)
</script>

<style lang="scss">
@import '@/style/mobile.scss';

.dark {
  background-color: #273444 !important;
  color: #fff !important;
}

.light {
  background-color: #fff !important;
  color: #000 !important;
}

.icon-rizhi1 {
  span {
    margin-left: 5px;
  }
}

.day-select {
  height: 23px;
  width: 88px;
  margin-left: 15px;

  div {
    height: 23px;
    width: 88px;

    input {
      height: 23px;
      width: 50px;
      font-size: 12px;
      color: #2972C8;
    }
  }
}

.right-box{
  margin-top: 9px;
}

.hidelogoimg {
  overflow: hidden !important;
  width: 54px !important;
  padding-left: 9px !important;
  .logoimg{
    margin-left: 7px;
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

.layout-wrapper {
  display: flex;
  min-height: 100vh;
}

.shadow-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

.shadowBg {
  display: block !important;
}

.header-row {
  display: flex;
  width: 100%;
}

.header-col {
  flex: 1;
}

.header-content {
  display: flex;
  align-items: center;
  padding: 0;
}

.header-menu-col {
  flex: 0 0 auto;
}

.breadcrumb-col {
  flex: 1;
  padding: 0 20px;
}

.user-col {
  flex: 0 0 auto;
  min-width: 200px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 8px;
  color: #c0c4cc;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  color: #606266;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
}

.dropdown-item .icon {
  margin-right: 8px;
  font-size: 14px;
}
</style>
