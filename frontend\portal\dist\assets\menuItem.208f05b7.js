/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as e,I as t,r as a,v as o,h as n,o as l,d as r,e as i,t as s,j as u,w as c,g as m,C as f,f as d}from"./index.4982c0f9.js";const p={key:0,style:{height:"34px","margin-top":"6px","margin-bottom":"6px",border:"4px","line-height":"34px","margin-left":"14px",background:"#2F3C4B","padding-left":"35px","margin-right":"29px"}},v={style:{"font-size":"12px",color:"#FFFFFF",opacity:"1"}},h={key:1,class:"gva-menu-item"},x={class:"gva-menu-item-title"},g={name:"MenuItem",setup(){}},y=e(Object.assign(g,{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"e04a4d90-activeBackground":y.value,"e04a4d90-normalText":F.value,"e04a4d90-hoverText":_.value,"e04a4d90-activeText":I.value})));const g=e,y=a(g.theme.activeBackground),I=a(g.theme.activeText),F=a(g.theme.normalText),k=a(g.theme.hoverBackground),_=a(g.theme.hoverText);return o((()=>g.theme),(()=>{y.value=g.theme.activeBackground,I.value=g.theme.activeText,F.value=g.theme.normalText,k.value=g.theme.hoverBackground,_.value=g.theme.hoverText})),(t,a)=>{const o=n("Plus"),g=n("el-icon"),y=n("component"),I=n("el-tooltip"),F=n("el-menu-item");return e.routerInfo.meta.isDisabled?(l(),r("div",p,[i("span",v,s(e.routerInfo.meta.title),1),u(g,{color:"#FFFFFF",size:"12px",style:{"padding-left":"17px"}},{default:c((()=>[u(o)])),_:1})])):(l(),m(F,{key:1,index:e.routerInfo.name},{default:c((()=>[e.isCollapse?(l(),m(I,{key:0,class:"box-item",effect:"light",content:e.routerInfo.meta.title,placement:"right"},{default:c((()=>[e.routerInfo.meta.icon?(l(),m(g,{key:0},{default:c((()=>[u(y,{class:f(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):d("v-if",!0)])),_:1},8,["content"])):(l(),r("div",h,[e.routerInfo.meta.icon?(l(),m(g,{key:0},{default:c((()=>[u(y,{class:f(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):d("v-if",!0),i("span",x,s(e.routerInfo.meta.title),1)]))])),_:1},8,["index"]))}}}),[["__scopeId","data-v-e04a4d90"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/menuItem.vue"]]);export{y as default};
