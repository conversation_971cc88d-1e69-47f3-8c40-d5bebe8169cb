<template>
  <div>
    <div class="person">
      <el-header>
        <span>基本信息</span>
      </el-header>
      <base-main>
        <base-row>
          <base-col :span="6">用户名：{{ userStore.userInfo.name }}&nbsp;&nbsp;&nbsp;
            <!-- sourceType为local展示：修改密码，否则不展示 -->
            <el-link 
              v-if="userStore.userInfo.sourceType === 'local' && userStore.userInfo.authType === 'password'" 
              :underline="false" 
              style="color: #2972C8" 
              @click="showPassword = true"
            >修改密码</el-link>
          </base-col>
          <base-col :span="6">所属组织： {{ userStore.userInfo.groupName }}</base-col>
          <base-col v-if="JSONPath('$..expireType', userStore.userInfo)[0] === 'forever'" :span="6">到期时间： 永久</base-col>
          <base-col v-else :span="6">到期时间： {{ JSONPath('$..expireEnd', userStore.userInfo)[0] }}</base-col>
          <base-col :span="6">手机号码： {{ JSONPath('$..phone', userStore.userInfo)[0] }}</base-col>
        </base-row>
        <base-row>
          <!-- <base-col :span="6">所属角色： {{ JSONPath('$..roles[0][name]', userStore.userInfo)[0] }}</base-col> -->
          <base-col :span="6">邮箱： {{ userStore.userInfo.email }}</base-col>
          <base-col :span="6"></base-col>
          <base-col :span="6"></base-col>
        </base-row>
      </base-main>
    </div>
    <el-dialog
        v-model="showPassword"
        title="修改密码"
        width="360px"
        @close="clearPassword"
    >
      <base-form
          ref="modifyPwdForm"
          :model="pwdModify"
          :rules="rules"
          label-width="80px"
      >
        <base-form-item :minlength="6" label="原密码" prop="password">
          <base-input v-model="pwdModify.password" show-password />
        </base-form-item>
        <base-form-item :minlength="6" label="新密码" prop="newPassword">
          <base-input v-model="pwdModify.newPassword" show-password />
        </base-form-item>
        <base-form-item :minlength="6" label="确认密码" prop="confirmPassword">
          <base-input v-model="pwdModify.confirmPassword" show-password />
        </base-form-item>
      </base-form>
      <template #footer>
        <div class="dialog-footer">
          <base-button
              size="small"
              @click="showPassword = false"
          >取 消</base-button>
          <base-button
              size="small"
              type="primary"
              @click="savePassword"
          >确 定</base-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Person',
}
</script>
<script setup>
import { useUserStore } from '@/pinia/modules/user'
import { JSONPath } from 'jsonpath-plus'
import { changePassword } from '@/api/user'
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const modifyPwdForm = ref(null)
const showPassword = ref(false)
const pwdModify = ref({})
const rules = reactive({
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, max:128,message: '密码长度最少8个字符至128个字符', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, max:128,message: '密码长度最少8个字符至128个字符', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        // 密码复杂度校验
        const hasNumber = /\d/.test(value)  // 包含数字
        const hasLetter = /[a-zA-Z]/.test(value)  // 包含字母
        const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value)  // 包含特殊字符
        
        if (!hasNumber || !hasLetter || !hasSpecial) {
          callback(new Error('密码必须包含数字、字母和特殊字符'))
        } else if (value.length < 8) {
          callback(new Error('密码长度不能少于8个字符'))
        } else if (value.length > 20) {
          callback(new Error('密码长度不能超过20个字符'))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change']
    }
  ],
  confirmPassword: [
    { required: true, message: '请输入确认密码', trigger: 'blur' },
    { min: 8, max:128,message: '密码长度最少8个字符至128个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== pwdModify.value.newPassword) {
          callback(new Error('两次密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
})

const savePassword = async() => {
  console.log('修改密码')
  modifyPwdForm.value.validate((valid) => {
    if (valid) {
      changePassword({
        password: pwdModify.value.password,
        newPassword: pwdModify.value.newPassword,
      }).then((res) => {
        if (res.status === 200) {
          // 检查业务逻辑状态码
          if (res.data && res.data.code === -1) {
            // 显示错误消息
            ElMessage.error(res.data.msg || '密码错误')
            return
          }
          ElMessage.success('修改密码成功！')
          showPassword.value = false
        } else {
          ElMessage.error('修改密码失败')
        }
      }).catch((error) => {
        console.error('修改密码出错:', error)
        ElMessage.error('修改密码请求失败')
      })
    } else {
      return false
    }
  })
}
</script>

<style lang="scss" scoped>
.person {
  background: #FFFFFF;
  border-radius: 4px;

  .el-header {
    height: 48px;
    line-height: 48px;
    padding: 0 15px;
    border-bottom: 1px #EBEBEB solid;
  }

  .el-main{
    .el-row{
      padding: 14px 0px;
    }
  }
}
</style>
