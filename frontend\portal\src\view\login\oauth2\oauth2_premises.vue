<template>
  <div class="premises-page">
    <div style="text-align: center">
      <span class="title" style="height:24px;line-height: 24px;margin: 0 auto;color: #0082ef;font-size: 20px;text-align: center">
        <svg class="icon" aria-hidden="true" style="height: 24px;width: 29px;vertical-align: top;margin-right: 8px;display: inline-block">
          <use :xlink:href="'#icon-auth-zhezhending'" />
        </svg>
        {{ appName }}
      </span>
    </div>

    <div class="submit-btn-wrapper">
      <base-button
        type="primary"
        size="large"
        class="login_submit_button"
        native-type="submit"
        @click="routeHandle"
      > {{ isAuthState ? '正在获取授权信息': '授权登录' }}</base-button>
    </div>
    <span v-if="!isAuthState" class="premises-tip">若打开失败，请先安装{{ appName }}App</span>
  </div>
</template>
<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Base64 } from 'js-base64'
import dd from 'gdt-jsapi'
import '@/assets/ali/iconfont'

export default {
  name: 'OAuth2Premises',
  setup() {
    const route = useRoute()
    const url = ref('')
    const appName = ref('专有钉钉')
    const loading = ref(false)
    const isAuthState = ref(true)
    const authKey = ref('')
    const state = ref({
      callback_url: '',
      state: '',
    })

    const init = async () => {
      isAuthState.value = window.self === window.top
      loading.value = true
      const ret = await getDdAuthInfo()
      loading.value = false
      if (route.query.state) {
        const tmpState = Base64.decode(route.query.state)
        state.value = JSON.parse(tmpState)
        state.value.state = route.query.state
      }
      if (ret.errorCode === 0) {
        window.location.href = fomateAuthSuccessUrl(ret.data)
      } else {
        url.value = formateAppLink()
      }
    }

    const routeHandle = () => {
      console.log('isAuthState:', isAuthState.value, 'url:', url.value)
      if (isAuthState.value || loading.value) {
        return
      }
      const data = {
        event: 'wakeup-app',
        params: { url: url.value, authKey: authKey.value }
      }
      console.log('peurl', url.value, state.value.state)
      window.parent.postMessage(data, window.location.origin)
    }

    const getDdAuthInfo = () => {
      return new Promise((resolve, reject) => {
        dd.ready(function() {
          dd.getAuthCode({
            corpId: route.query.corpId
          }).then(res => {
            resolve({ errorCode: 0, data: res })
          }).catch(err => {
            resolve({ errorCode: 1, data: err })
          })
        })
      })
    }

    const fomateAuthSuccessUrl = (data) => {
      const query = []
      data.state = route.query.state || ''
      Object.keys(data).forEach(key => {
        query.push(`${key}=${data[key]}`)
      })
      return state.value.callback_url + '?' + query.join('&')
    }

    const formateAppLink = () => {
      const url = state.value.callback_url + `?state=` + state.value.state
      return `taurus://taurusclient/action/open_app?type=1&offline=false&url=${encodeURIComponent(url)}`
    }

    onMounted(() => {
      init()
    })

    return {
      url,
      appName,
      loading,
      isAuthState,
      authKey,
      routeHandle
    }
  }
}
</script>

<style>
.premises-page {
  height: 100%;
  background-color: white;
  background-size: cover;
}

.ui-logo {
  padding-top: 10px;
}

.ui-logo img {
  width: 140px;
  margin: 0 auto;
}

.ui-logo p {
  text-align: center;
  font-size: 30px;
  font-family: PingFang SC, PingFang SC-Regular;
  line-height: 42px;
  margin-top: 30px;
  color: @title-color;
}

.premises-tip{
  line-height: 33px;
  text-align: center;
  margin-left: 38px;
}

.bind-title {
  padding: 20px 60px 30px 60px;
  color: white;
  font-size: 30px;
  line-height: 42px;
  text-align: center;
}

.submit-btn-wrapper {
  padding: 80px 60px 30px 60px;
}

.login_submit_button {
  width: 100%;
  height: 40px;
  margin-top: 20px;
  font-size: 16px;
  color: #fff;
  background-color: #2972C8;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
</style>
