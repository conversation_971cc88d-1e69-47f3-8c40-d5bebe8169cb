/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{g as e,d as a,a as l,u as t,c as u,b as o}from"./api.34c114d1.js";import{t as i}from"./stringFun.2b3a18f6.js";import{W as s}from"./warningBar.4338ec87.js";import{_ as d,r as p,h as r,o as n,d as m,e as c,j as v,w as f,F as g,i as b,f as h,k as _,t as y,M as V,P as w}from"./index.74d1ee23.js";const k={class:"gva-search-box"},C={class:"gva-table-box"},z={class:"gva-btn-list"},x={style:{"text-align":"right","margin-top":"8px"}},A={class:"gva-pagination"},U={class:"dialog-footer"},G=d(Object.assign({name:"<PERSON><PERSON>"},{setup(d){const G=e=>{const a=j.value.filter((a=>a.value===e))[0];return a&&`${a.label}`},I=p([]),P=p({path:"",apiGroup:"",method:"",description:""}),j=p([{value:"POST",label:"创建",type:"success"},{value:"GET",label:"查看",type:""},{value:"PUT",label:"更新",type:"warning"},{value:"DELETE",label:"删除",type:"danger"}]),S=p(""),T=p({path:[{required:!0,message:"请输入api路径",trigger:"blur"}],apiGroup:[{required:!0,message:"请输入组名称",trigger:"blur"}],method:[{required:!0,message:"请选择请求方式",trigger:"blur"}],description:[{required:!0,message:"请输入api介绍",trigger:"blur"}]}),D=p(1),F=p(0),$=p(10),q=p([]),E=p({}),B=()=>{E.value={}},O=()=>{D.value=1,$.value=10,W()},K=e=>{$.value=e,W()},L=e=>{D.value=e,W()},M=({prop:e,order:a})=>{e&&("ID"===e&&(e="id"),E.value.orderKey=i(e),E.value.desc="descending"===a),W()},W=async()=>{const a=await e({page:D.value,pageSize:$.value,...E.value});0===a.code&&(q.value=a.data.list,F.value=a.data.total,D.value=a.data.page,$.value=a.data.pageSize)};W();const H=e=>{I.value=e},J=p(!1),N=async()=>{const e=I.value.map((e=>e.ID)),l=await a({ids:e});0===l.code&&(V({type:"success",message:l.msg}),q.value.length===e.length&&D.value>1&&D.value--,J.value=!1,W())},Q=p(null),R=p("新增Api"),X=p(!1),Y=e=>{switch(e){case"addApi":R.value="新增Api";break;case"edit":R.value="编辑Api"}S.value=e,X.value=!0},Z=()=>{Q.value.resetFields(),P.value={path:"",apiGroup:"",method:"",description:""},X.value=!1},ee=async()=>{Q.value.validate((async e=>{if(e)switch(S.value){case"addApi":0===(await u(P.value)).code&&V({type:"success",message:"添加成功",showClose:!0}),W(),Z();break;case"edit":0===(await t(P.value)).code&&V({type:"success",message:"编辑成功",showClose:!0}),W(),Z();break;default:V({type:"error",message:"未知操作",showClose:!0})}}))};return(e,a)=>{const t=r("base-input"),u=r("base-form-item"),i=r("base-option"),d=r("base-select"),p=r("base-button"),S=r("base-form"),ae=r("el-popover"),le=r("el-table-column"),te=r("el-table"),ue=r("el-pagination"),oe=r("el-dialog");return n(),m("div",null,[c("div",k,[v(S,{ref:"searchForm",inline:!0,model:E.value},{default:f((()=>[v(u,{label:"路径"},{default:f((()=>[v(t,{modelValue:E.value.path,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value.path=e),placeholder:"路径"},null,8,["modelValue"])])),_:1}),v(u,{label:"描述"},{default:f((()=>[v(t,{modelValue:E.value.description,"onUpdate:modelValue":a[1]||(a[1]=e=>E.value.description=e),placeholder:"描述"},null,8,["modelValue"])])),_:1}),v(u,{label:"API组"},{default:f((()=>[v(t,{modelValue:E.value.apiGroup,"onUpdate:modelValue":a[2]||(a[2]=e=>E.value.apiGroup=e),placeholder:"api组"},null,8,["modelValue"])])),_:1}),v(u,{label:"请求"},{default:f((()=>[v(d,{modelValue:E.value.method,"onUpdate:modelValue":a[3]||(a[3]=e=>E.value.method=e),clearable:"",placeholder:"请选择"},{default:f((()=>[(n(!0),m(g,null,b(j.value,(e=>(n(),h(i,{key:e.value,label:`${e.label}(${e.value})`,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),v(u,null,{default:f((()=>[v(p,{size:"small",type:"primary",icon:"search",onClick:O},{default:f((()=>a[13]||(a[13]=[_("查询")]))),_:1,__:[13]}),v(p,{size:"small",icon:"refresh",onClick:B},{default:f((()=>a[14]||(a[14]=[_("重置")]))),_:1,__:[14]})])),_:1})])),_:1},8,["model"])]),c("div",C,[c("div",z,[v(p,{size:"small",type:"primary",icon:"plus",onClick:a[4]||(a[4]=e=>Y("addApi"))},{default:f((()=>a[15]||(a[15]=[_("新增")]))),_:1,__:[15]}),v(ae,{modelValue:J.value,"onUpdate:modelValue":a[7]||(a[7]=e=>J.value=e),placement:"top",width:"160"},{reference:f((()=>[v(p,{icon:"delete",size:"small",disabled:!I.value.length,style:{"margin-left":"10px"},onClick:a[6]||(a[6]=e=>J.value=!0)},{default:f((()=>a[18]||(a[18]=[_("删除")]))),_:1,__:[18]},8,["disabled"])])),default:f((()=>[a[19]||(a[19]=c("p",null,"确定要删除吗？",-1)),c("div",x,[v(p,{size:"small",type:"primary",link:"",onClick:a[5]||(a[5]=e=>J.value=!1)},{default:f((()=>a[16]||(a[16]=[_("取消")]))),_:1,__:[16]}),v(p,{size:"small",type:"primary",onClick:N},{default:f((()=>a[17]||(a[17]=[_("确定")]))),_:1,__:[17]})])])),_:1,__:[19]},8,["modelValue"])]),v(te,{data:q.value,onSortChange:M,onSelectionChange:H},{default:f((()=>[v(le,{type:"selection",width:"55"}),v(le,{align:"left",label:"id","min-width":"60",prop:"ID",sortable:"custom"}),v(le,{align:"left",label:"API路径","min-width":"150",prop:"path",sortable:"custom"}),v(le,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup",sortable:"custom"}),v(le,{align:"left",label:"API简介","min-width":"150",prop:"description",sortable:"custom"}),v(le,{align:"left",label:"请求","min-width":"150",prop:"method",sortable:"custom"},{default:f((e=>[c("div",null,y(e.row.method)+" / "+y(G(e.row.method)),1)])),_:1}),v(le,{align:"left",fixed:"right",label:"操作",width:"200"},{default:f((e=>[v(p,{icon:"edit",size:"small",type:"primary",link:"",onClick:a=>(async e=>{const a=await l({id:e.ID});P.value=a.data.api,Y("edit")})(e.row)},{default:f((()=>a[20]||(a[20]=[_("编辑")]))),_:2,__:[20]},1032,["onClick"]),v(p,{icon:"delete",size:"small",type:"primary",link:"",onClick:a=>(async e=>{w.confirm("此操作将永久删除所有角色下该api, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await o(e)).code&&(V({type:"success",message:"删除成功!"}),1===q.value.length&&D.value>1&&D.value--,W())}))})(e.row)},{default:f((()=>a[21]||(a[21]=[_("删除")]))),_:2,__:[21]},1032,["onClick"])])),_:1})])),_:1},8,["data"]),c("div",A,[v(ue,{"current-page":D.value,"page-size":$.value,"page-sizes":[10,30,50,100],total:F.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:L,onSizeChange:K},null,8,["current-page","page-size","total"])])]),v(oe,{modelValue:X.value,"onUpdate:modelValue":a[12]||(a[12]=e=>X.value=e),"before-close":Z,title:R.value},{footer:f((()=>[c("div",U,[v(p,{size:"small",onClick:Z},{default:f((()=>a[22]||(a[22]=[_("取 消")]))),_:1,__:[22]}),v(p,{size:"small",type:"primary",onClick:ee},{default:f((()=>a[23]||(a[23]=[_("确 定")]))),_:1,__:[23]})])])),default:f((()=>[v(s,{title:"新增API，需要在角色管理内配置权限才可使用"}),v(S,{ref_key:"apiForm",ref:Q,model:P.value,rules:T.value,"label-width":"80px"},{default:f((()=>[v(u,{label:"路径",prop:"path"},{default:f((()=>[v(t,{modelValue:P.value.path,"onUpdate:modelValue":a[8]||(a[8]=e=>P.value.path=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),v(u,{label:"请求",prop:"method"},{default:f((()=>[v(d,{modelValue:P.value.method,"onUpdate:modelValue":a[9]||(a[9]=e=>P.value.method=e),placeholder:"请选择",style:{width:"100%"}},{default:f((()=>[(n(!0),m(g,null,b(j.value,(e=>(n(),h(i,{key:e.value,label:`${e.label}(${e.value})`,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),v(u,{label:"api分组",prop:"apiGroup"},{default:f((()=>[v(t,{modelValue:P.value.apiGroup,"onUpdate:modelValue":a[10]||(a[10]=e=>P.value.apiGroup=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),v(u,{label:"api简介",prop:"description"},{default:f((()=>[v(t,{modelValue:P.value.description,"onUpdate:modelValue":a[11]||(a[11]=e=>P.value.description=e),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-1107200c"]]);export{G as default};
