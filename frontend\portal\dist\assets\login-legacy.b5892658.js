/*! 
 Build based on gin-vue-admin 
 Time : 1749553755000 */
System.register(["./index-legacy.0c970cb2.js","./index-legacy.20a725d0.js","./localLogin-legacy.34a668da.js","./wechat-legacy.eaca1035.js","./feishu-legacy.415e9f1c.js","./dingtalk-legacy.42c5b018.js","./oauth2-legacy.b8941147.js","./sms-legacy.885dd11d.js","./secondaryAuth-legacy.379b916b.js","./verifyCode-legacy.c8fcab8c.js"],(function(n,c){"use strict";var e,t,a;return{setters:[function(n){e=n.default},function(n){t=n.o,a=n.f},function(){},function(){},function(){},function(){},function(){},function(){},function(){},function(){}],execute:function(){n("default",Object.assign({name:"ClientNewLogin"},{setup:function(n){return function(n,c){return t(),a(e)}}}))}}}));
