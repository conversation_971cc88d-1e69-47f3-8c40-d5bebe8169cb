/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.fcd86654.js","./index-legacy.dbc04544.js"],(function(e,t){"use strict";var l,n,a,o,r;return{setters:[function(e){l=e.L},function(e){n=e.r,a=e.H,o=e.o,r=e.d}],execute:function(){e("default",Object.assign({name:"CustormLine"},{props:{data:{default:function(){return[]},type:Array}},setup:function(e){var t=e,i=n(null);return a((function(){var e=tableRef.value.getSelectionRows();console.log(e),console.log(e.length),e.length<1&&(console.log(trendData),e=trendData.value[0]),console.log("selectTable"),console.log(e),data=e.data,line.value=new l(i.value,{data:t.data,xField:"date",yField:"count",yAxis:{tickCount:3},label:null,title:null,point:{size:3,shape:"circle",style:{fill:"white",stroke:"#7EBEEF",lineWidth:2}},tooltip:{showMarkers:!1},state:{active:{style:{shadowBlur:4,stroke:"#7EBEEF",fill:"#7EBEEF"}}},theme:{styleSheet:{brandColor:"#7EBEEF"}},interactions:[{type:"marker-active"}]}),line.value.render()})),function(e,t){return o(),r("div",{ref_key:"container",ref:i},null,512)}}}))}}}));
