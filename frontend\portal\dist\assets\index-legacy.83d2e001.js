/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./header-legacy.181bc989.js","./menu-legacy.bc75ed8c.js","./index-legacy.dbc04544.js","./ASD-legacy.b6ffb1bc.js","./iconfont-legacy.37c53566.js"],(function(t,e){"use strict";var a,n,i,o,u,c,l,r,d=document.createElement("style");return d.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(d),{setters:[function(t){a=t.default},function(t){n=t.default},function(t){i=t.h,o=t.o,u=t.d,c=t.j,l=t.e,r=t.f},function(){},function(){}],execute:function(){var e={class:"layout-page"},d={class:"layout-wrap"},f={id:"layoutMain",class:"layout-main"};t("default",Object.assign({name:"Client"},{setup:function(t){return function(t,s){var h=i("router-view");return o(),u("div",e,[c(a),l("div",d,[c(n),l("div",f,[(o(),r(h,{key:t.$route.fullPath}))])])])}}}))}}}));
