/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{a0 as e,h as a,o as s,d as n,j as t,w as o,T as u,f as l,a2 as d,m as i,z as r}from"./index.74d1ee23.js";const m=Object.assign({name:"System"},{setup(m){const c=e();return(e,m)=>{const f=a("router-view");return s(),n("div",null,[t(f,null,{default:o((({Component:e})=>[t(u,{mode:"out-in",name:"el-fade-in-linear"},{default:o((()=>[(s(),l(d,{include:i(c).keepAliveRouters},[(s(),l(r(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{m as default};
