/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js"],(function(e,n){"use strict";var t,a,o,u,l,c,r,i,s,d,f;return{setters:[function(e){t=e.b,a=e.r,o=e.h,u=e.o,l=e.d,c=e.j,r=e.w,i=e.e,s=e.k,d=e.m,f=e.M}],execute:function(){e("default",{__name:"index",setup:function(e){var n=t(),p=a("/auth"),_=function(e){if(0===e.code){var n="";e.data&&e.data.forEach((function(e,t){n+="".concat(t+1,".").concat(e.msg,"\n")})),alert(n)}else f.error(e.msg)};return function(e,t){var a=o("upload-filled"),f=o("el-icon"),m=o("el-upload");return u(),l("div",null,[c(m,{class:"upload-demo",drag:"",action:"".concat(p.value,"/autoCode/installPlugin"),headers:{"x-token":d(n).token},"show-file-list":!1,"on-success":_,"on-error":_,name:"plug"},{tip:r((function(){return t[0]||(t[0]=[i("div",{class:"el-upload__tip"}," 请把安装包的zip拖拽至此处上传 ",-1)])})),default:r((function(){return[c(f,{class:"el-icon--upload"},{default:r((function(){return[c(a)]})),_:1}),t[1]||(t[1]=i("div",{class:"el-upload__text"},[s(" 拖拽或"),i("em",null,"点击上传")],-1))]})),_:1,__:[1]},8,["action","headers"])])}}})}}}));
