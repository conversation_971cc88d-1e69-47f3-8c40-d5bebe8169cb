/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{x as a}from"./index.bfaf04e1.js";const o=o=>a({url:"/console/v1/application/group",method:"get",params:{corp_id:o}}),t=o=>a({url:"/console/v1/application/group",method:"post",data:o}),p=o=>a({url:"/console/v1/application/group",method:"put",data:o}),e=o=>a({url:"/console/v1/application/group",method:"delete",params:o}),l=o=>a({url:"/console/v1/application",method:"post",data:o}),s=o=>a({url:"/console/v1/application",method:"put",data:o}),i=o=>a({url:"/console/v1/application",method:"delete",params:o}),c=o=>a({url:"/console/v1/application/list",method:"post",data:o}),r=o=>a({url:"/console/v1/application",method:"get",params:o}),n=()=>a({url:"/console/v1/application/getuserapp",method:"get"});export{o as a,c as b,r as c,l as d,p as e,t as f,n as g,e as h,i,s as u};
