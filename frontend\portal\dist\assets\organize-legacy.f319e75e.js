/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",u=a.toStringTag||"@@toStringTag";function i(e,a,o,u){var i=a&&a.prototype instanceof s?a:s,c=Object.create(i.prototype);return t(c,"_invoke",function(e,t,a){var o,u,i,s=0,c=a||[],f=!1,d={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(e,t){return o=e,u=0,i=n,d.n=t,l}};function p(e,t){for(u=e,i=t,r=0;!f&&s&&!a&&r<c.length;r++){var a,o=c[r],p=d.p,m=o[2];e>3?(a=m===t)&&(i=o[(u=o[4])?5:(u=3,3)],o[4]=o[5]=n):o[0]<=p&&((a=e<2&&p<o[1])?(u=0,d.v=t,d.n=o[1]):p<m&&(a=e<3||o[0]>t||t>m)&&(o[4]=e,o[5]=t,d.n=m,u=0))}if(a||e>1)return l;throw f=!0,t}return function(a,c,m){if(s>1)throw TypeError("Generator is already running");for(f&&1===c&&p(c,m),u=c,i=m;(r=u<2?n:i)||!f;){o||(u?u<3?(u>1&&(d.n=-1),p(u,i)):d.n=i:d.v=i);try{if(s=2,o){if(u||(a="next"),r=o[a]){if(!(r=r.call(o,i)))throw TypeError("iterator result is not an object");if(!r.done)return r;i=r.value,u<2&&(u=0)}else 1===u&&(r=o.return)&&r.call(o),u<2&&(i=TypeError("The iterator does not provide a '"+a+"' method"),u=1);o=n}else if((r=(f=d.n<0)?i:e.call(t,d))!==l)break}catch(r){o=n,u=1,i=r}finally{s=1}}return{value:r,done:f}}}(e,o,u),!0),c}var l={};function s(){}function c(){}function f(){}r=Object.getPrototypeOf;var d=[][o]?r(r([][o]())):(t(r={},o,(function(){return this})),r),p=f.prototype=s.prototype=Object.create(d);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,t(e,u,"GeneratorFunction")),e.prototype=Object.create(p),e}return c.prototype=f,t(p,"constructor",f),t(f,"constructor",c),c.displayName="GeneratorFunction",t(f,u,"GeneratorFunction"),t(p),t(p,u,"Generator"),t(p,o,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:i,m:m}})()}function t(e,n,r,a){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,n,r,a){if(n)o?o(e,n,{value:r,enumerable:!a,configurable:!a,writable:!a}):e[n]=r;else{var u=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};u("next",0),u("throw",1),u("return",2)}},t(e,n,r,a)}function n(e,t,n,r,a,o,u){try{var i=e[o](u),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,a)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var u=e.apply(t,r);function i(e){n(u,a,o,i,l,"next",e)}function l(e){n(u,a,o,i,l,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.dbc04544.js","./iconfont-legacy.37c53566.js","./index-browser-esm-legacy.6966c248.js","./dayjs.min-legacy.5e08fd6a.js","./directoryTree-legacy.8e961781.js","./customTable-legacy.8e1d28b3.js","./customFrom-legacy.fd23a917.js"],(function(t,n){"use strict";var a,o,u,i,l,s,c,f,d,p,m,g,v,b,h,y,w,V,x,O,I,z,_,k,q=document.createElement("style");return q.textContent=".common-layout[data-v-808ab330]{min-height:calc(100vh - 200px)}.menu-label[data-v-808ab330]{width:81%;float:left;font-size:12px;padding-top:8px;color:rgba(51,51,51,.7);padding-left:10px}.organize-but[data-v-808ab330]{height:28px;width:28px;padding:6px;border-width:0px;position:absolute;font-size:12px}.organize-search[data-v-808ab330]{width:200px;float:right;height:30px}\n",document.head.appendChild(q),{setters:[function(e){a=e.B,o=e._,u=e.b,i=e.r,l=e.p,s=e.h,c=e.o,f=e.d,d=e.j,p=e.w,m=e.e,g=e.k,v=e.O,b=e.t,h=e.m,y=e.f,w=e.g,V=e.M,x=e.P},function(){},function(e){O=e.J},function(e){I=e.d},function(e){z=e.D},function(e){_=e._},function(e){k=e._}],execute:function(){var n=function(e,t,n){return console.log("groupFormItemOptions"),console.log(e.value),console.log(t.value),[{field:"source",label:"用户来源：",type:"select",placeholder:"请选择",options:e,optionsLabe:"name",optionsValue:"type",optionsKey:"type",rules:[{required:!0,message:"用户来源不能为空",trigger:"blur"}]},{field:"group",label:"父级组织：",type:"treeSelect",placeholder:"请选择",options:t,optionsLabe:"name",optionsValue:"type",optionsKey:"type",isHidden:n,rules:[{required:!0,message:"父级组织不能为空",trigger:"blur"}]},{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"description",label:"描述：",type:"input",placeholder:"组织描述"},{field:"uniqueIdentification",label:"唯一标识：",type:"input",placeholder:"请输入唯一标识",rules:[{required:!0,message:"唯一标识不能为空",trigger:"blur"}]}]},q=function(e,t){return console.log("userFormItemOptions"),console.log(e.value),console.log(t.value),[{field:"name",label:"用户名：",type:"input",placeholder:"请输入用户名",rules:[{required:!0,message:"用户名不能为空",trigger:"blur"}]},{field:"statu",label:"状态：",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"0"}]},{field:"description",label:"描述：",type:"input",placeholder:"组织描述"},{field:"password",label:"密码：",type:"password",placeholder:"请输入密码",rules:[{required:!0,message:"密码不能为空",trigger:"blur"}]},{field:"confirmPassword",label:"确认密码：",type:"password",placeholder:"请输入密码",rules:[{required:!0,message:"密码不能为空",trigger:"blur"}]},{field:"source",label:"所属组织：",type:"select",placeholder:"请选择",options:e,optionsLabe:"name",optionsValue:"name",optionsKey:"id",rules:[{required:!0,message:"所属组织不能为空",trigger:"blur"}]},{field:"roles",label:"关联角色：",type:"select",placeholder:"请选择",options:t,optionsLabe:"name",optionsValue:"name",optionsKey:"id",rules:[{required:!0,message:"角色不能为空",trigger:"blur"}]},{field:"uniqueIdentification",label:"身份标识：",type:"input",placeholder:"请输入唯一标识",rules:[{required:!0,message:"身份标识不能为空",trigger:"blur"}]},{field:"email",label:"电子邮箱：",type:"input",placeholder:"请输入邮箱"},{field:"phone",label:"手机号码：",type:"input",placeholder:"请输入手机号码"},{field:"expiration",label:"过期时间：",type:"datepicker",placeholder:"请输入手机号码",dateType:"dateType"}]};a({source:"select"});var T={class:"common-layout organize"},j={style:{height:"35px"}},G={class:"header"},U={style:{"text-align":"center"}},S={style:{"font-size":"12px"}},D={style:{"text-align":"center"}},C={style:{"font-size":"12px"}},P={style:{"text-align":"center"}},F={style:{"font-size":"12px"}},N={style:{"text-align":"center"}},R=Object.assign({name:"Organize"},{setup:function(t){var o=u(),R=i(""),Y=i([]),$=i(0),L=i(1),E=i(50),M=i(!1),B=i(""),H=i(1),K=i("新增用户目录"),A=i("root"),X=i("add"),J=i(!1),Q={label:"name",children:"zones",isLeaf:"leaf",isRoot:"root"},W=a({id:"",source:"",name:"",description:"",uniqueIdentification:"",statu:"1",expirationRadio:"0",expiration:""}),Z={formItems:[],formValues:a({})},ee=i([]),te=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o.GetOrganize("");case 1:(n=e.v).data&&(ee.value=n.data);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),ne=i([]),re=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o.GetUserOrigin();case 1:200===(n=e.v).status&&0===n.data.code&&(ne.value=n.data.data);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),ae=function(){var t=r(e().m((function t(){var n,r,a;return e().w((function(e){for(;;)switch(e.n){case 0:if(console.log("getTableData"),console.log(B.value),n={briefRepresentation:!1,first:(L.value-1)*E.value,max:E.value,search:R.value},r={data:0},a={},!B.value){e.n=2;break}return e.n=1,be();case 1:a=e.v,r.data=a.data.length,e.n=5;break;case 2:return e.n=3,o.GetUserListCount(n);case 3:return r=e.v,e.n=4,o.GetUserList(n);case 4:a=e.v;case 5:a.data&&(Y.value=a.data,$.value=r.data);case 6:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),oe=i([]),ue=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o.GetRoles("");case 1:(n=e.v).data&&(oe.value=n.data);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),ie=function(){var t=r(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,te();case 1:return e.n=2,ae();case 2:return e.n=3,ue();case 3:return e.n=4,re();case 4:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();ie();var le={propList:[{prop:"username",label:"名称",slotName:"username"},{prop:"uniqueIdentification",label:"工号",slotName:"uniqueIdentification"},{prop:"organize",label:"所属组织",slotName:"organize"},{prop:"phone",label:"手机号码",slotName:"phone"},{prop:"expiration",label:"过期时间",slotName:"expiration"},{prop:"enabled",label:"状态",slotName:"enabled"}],isSelectColumn:!1,isOperationColumn:!0},se=a({source:"select"});l("cascaderKey",H),l("treeRef",B),l("operation",X),l("title",K),l("type",A),l("dialogVisible",J),l("formLabelAlign",W),l("currentPage",L),l("pageSize",E),l("total",$),l("getTableData",ae);var ce=function(){fe(),J.value=!1},fe=function(){var e=Object.keys(Z.formValues),t={};e.forEach((function(e){t[e]=""})),Object.assign(Z.formValues,t)},de=i(),pe=function(){var t=r(e().m((function t(n){return e().w((function(t){for(;;)switch(t.n){case 0:return console.log("organize"),console.log(Z.formValues),t.n=1,n.validate(function(){var t=r(e().m((function t(n,r){var a,u,i;return e().w((function(e){for(;;)switch(e.n){case 0:if(n){e.n=1;break}return V({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),e.a(2,"");case 1:console.log(W.expiration),console.log(I(W.expiration).format("YYYY-MM-DD HH:mm:ss")),a="",u="","addUser"===X.value||"updateUser"===X.value?u={id:Z.formValues.id,username:Z.formValues.name,enabled:Number(Z.formValues.statu)?1:0,totp:!1,emailVerified:!1,email:Z.formValues.email,groups:[Z.formValues.source],realmRoles:[Z.formValues.roles],attributes:{defaultRouter:"dashboard",nickName:Z.formValues.name,uniqueIdentification:[Z.formValues.uniqueIdentification],description:[Z.formValues.description||""],phone:[Z.formValues.phone],expiration:[Number(Z.formValues.dateType)?I(Z.formValues.expiration).format("YYYY-MM-DD HH:mm:ss"):"永久"]},credentials:[{type:"password",temporary:!1,value:Z.formValues.password}]}:a={id:Z.formValues.id,name:Z.formValues.name,attributes:{uniqueIdentification:[Z.formValues.uniqueIdentification],description:[Z.formValues.description||""],source:[Z.formValues.source]}},i=X.value,e.n="add"===i?2:"update"===i?4:"addSub"===i?6:"addUser"===i?8:"updateUser"===i?10:12;break;case 2:return e.n=3,o.CreateOrganize(a);case 3:return e.a(3,12);case 4:return e.n=5,o.UpdateOrganize(a);case 5:return e.a(3,12);case 6:return e.n=7,o.AddSubgroup(a);case 7:return e.a(3,12);case 8:return e.n=9,o.CreateUser(u);case 9:return e.a(3,12);case 10:return e.n=11,o.UpdateUser(u);case 11:return e.a(3,12);case 12:return J.value=!1,e.n=13,ae();case 13:return e.n=14,te();case 14:fe(),++H.value,console.log(n),console.log(r);case 15:return e.a(2)}}),t)})));return function(e,n){return t.apply(this,arguments)}}());case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}();l("open",(function(t){x.confirm("删除目录以后将无法恢复，确认删除目录？","删除用户目录",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(r(e().m((function n(){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return console.log(t),e.n=1,o.DelOrganize(t);case 1:if(r=e.v,console.log(r),204!==r.status){e.n=3;break}return V({type:"success",message:"删除成功"}),++H.value,e.n=2,te();case 2:e.n=4;break;case 3:V({type:"error",message:"删除失败"}),me(t);case 4:fe();case 5:return e.a(2)}}),n)})))).catch((function(){fe(),V({type:"info",message:"取消删除"})}))}));var me=function(e){var t=7===e?"删除当前用户目录需先手动清除当前用户目录所有数据再重试，包括角色、用户":"当前用户目录已被认证策略引用（XXX,YYY）关联，请先前往该认证策略修改后再删除";x.alert(t,"删除用户目录",{confirmButtonText:"确认"})},ge=function(e){console.log("setFormValues"),console.log(e),Z.formValues.id=e.data.id,Z.formValues.name=e.data.username||e.data.name,Z.formValues.source=e.data.group||O("$..attributes.source[0]",e.data)[0],Z.formValues.description=O("$..description[0]",e.data)[0],Z.formValues.uniqueIdentification=O("$..uniqueIdentification[0]",e.data)[0],Z.formValues.password="123456",Z.formValues.confirmPassword="123456",Z.formValues.group=e.groupId,Z.formValues.groupId=e.groupId,Z.formValues.email=e.data.email,Z.formValues.phone=O("$..phone[0]",e.data)[0],Z.formValues.statu=e.enabled?"0":"1",O("$..expiration[0]",e.data)[0]&&"永久"===O("$..expiration[0]",e.data)[0]?Z.formValues.dateType="0":(Z.formValues.dateType="1",Z.formValues.expiration=O("$..expiration[0]",e.data)[0])},ve=function(){var t=r(e().m((function t(n,r){var a;return e().w((function(e){for(;;)switch(e.n){case 0:return console.log("handleEdit"),console.log(r),K.value="编辑用户",A.value="user",M.value=!0,X.value="updateUser",e.n=1,o.GetUserInfo(r.id);case 1:a=e.v,console.log(a),Z.formItems=q(ee,oe),ge(a),J.value=!0;case 2:return e.a(2)}}),t)})));return function(e,n){return t.apply(this,arguments)}}();l("handleEdit",ve),l("handleDelete",(function(t,n){console.log("handleDelete"),console.log(n),x.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个用户吗？</strong><br><strong>删除后用户将无法登录和访问应用，请谨慎操作。</strong>',"删除用户",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(r(e().m((function t(){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o.DeleteUser(n.id);case 1:if(r=e.v,console.log(r),204!==r.status){e.n=3;break}return V({type:"success",message:"删除成功"}),e.n=2,ae();case 2:e.n=4;break;case 3:V({type:"error",message:"删除失败"});case 4:fe();case 5:return e.a(2)}}),t)})))).catch((function(){fe(),V({type:"info",message:"取消删除"})}))}));var be=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:return n=B.value,r={briefRepresentation:!1,first:(L.value-1)*E.value,max:E.value,search:R.value},e.n=1,o.GetGroupMembers(n,r);case 1:return e.a(2,e.v)}}),t)})));return function(){return t.apply(this,arguments)}}(),he=function(){var t=r(e().m((function t(r){var a;return e().w((function(e){for(;;)switch(e.n){case 0:return console.log("append"),console.log(r),X.value="addSub",K.value="新增子目录",A.value="sub",e.n=1,o.GetOrganizeDetails(r.id);case 1:a=e.v,console.log("organize"),console.log(a),Z.formValues.id=r.id,Z.formValues.source=O("$...source[0]",a.data)[0],Z.formValues.group=a.data.name,Z.formItems=n(ne,ee,!0),J.value=!0;case 2:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),ye=function(){var t=r(e().m((function t(r,a){var u,i;return e().w((function(e){for(;;)switch(e.n){case 0:return console.log("edit"),console.log(a),console.log(r),e.n=1,o.GetOrganizeDetails(a.id);case 1:u=e.v,K.value="修改子目录",A.value="root",X.value="update",i=!0,r.level>1&&(u.groupId=r.parent.data.id,i=!0),ge(u),Z.formItems=n(ne,ee,i),J.value=!0;case 2:return e.a(2)}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),we="",Ve=function(){var t=r(e().m((function t(n,r){return e().w((function(e){for(;;)switch(e.n){case 0:if(0!==n.level){e.n=2;break}return e.n=1,o.GetOrganize("");case 1:return we=e.v,e.a(2,r(we.data));case 2:if(!(n.data.subGroups.length>0)){e.n=3;break}return e.a(2,r(n.data.subGroups));case 3:return e.a(2,r([]))}}),t)})));return function(e,n){return t.apply(this,arguments)}}();return function(e,t){var r=s("base-button"),a=s("base-aside"),o=s("base-input"),u=s("SuccessFilled"),i=s("el-icon"),l=s("Remove"),V=s("base-main"),x=s("base-container"),I=s("el-dialog");return c(),f("div",T,[d(x,{style:{height:"100%"}},{default:p((function(){return[d(a,{width:"200px",style:{"min-height":"calc(100vh - 200px)"}},{default:p((function(){return[m("div",j,[t[4]||(t[4]=m("span",{class:"menu-label"},"用户目录",-1)),d(r,{class:"organize-but",icon:e.FolderAdd,onClick:t[0]||(t[0]=function(e){return console.log("addOrganize"),K.value="新增目录",A.value="root",X.value="add",Z.formItems=n(ne,ee,!0),void(J.value=!0)})},null,8,["icon"])]),d(z,{loadOperate:true,edit:ye,append:he,loadNode:Ve,treeProps:Q})]})),_:1}),d(V,null,{default:p((function(){return[m("div",G,[d(r,{icon:e.Plus,onClick:t[1]||(t[1]=function(e){return t="user",console.log("add"),K.value="新增用户",A.value=t,X.value="addUser",M.value=!1,Z.formItems=q(ee,oe),console.log(Z.formItems),Z.formValues.statu="1",Z.formValues.dateType="0",void(J.value=!0);var t})},{default:p((function(){return t[5]||(t[5]=[g("新增")])})),_:1,__:[5]},8,["icon"]),d(r,{icon:e.RefreshRight,onClick:ae},{default:p((function(){return t[6]||(t[6]=[g("刷新")])})),_:1,__:[6]},8,["icon"]),d(o,{modelValue:R.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return R.value=e}),class:"w-50 m-2 organize-search",placeholder:"(名称)","suffix-icon":e.Search,onChange:ae},null,8,["modelValue","suffix-icon"])]),d(_,v({"table-data":Y.value},le),{uniqueIdentification:p((function(e){return[m("div",U,[m("span",S,b(h(O)("$...uniqueIdentification[0]",e.row)[0]),1)])]})),phone:p((function(e){return[m("div",D,[m("span",C,b(h(O)("$...phone[0]",e.row)[0]),1)])]})),expiration:p((function(e){return[m("div",P,[m("span",F,b(h(O)("$..expiration[0]",e.row)[0]),1)])]})),enabled:p((function(e){return[m("div",N,[e.row.enabled?(c(),y(i,{key:0,style:{color:"#52c41a"}},{default:p((function(){return[d(u)]})),_:1})):(c(),y(i,{key:1},{default:p((function(){return[d(l)]})),_:1}))])]})),_:1},16,["table-data"])]})),_:1})]})),_:1}),J.value?(c(),y(I,{key:0,modelValue:J.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return J.value=e}),title:K.value,width:"30%","custom-class":"custom-dialog"},{default:p((function(){return[d(k,v({ref_key:"customForm",ref:de},h(Z),{formOptions:se,cancel:ce,submitForm:pe}),null,16,["formOptions"])]})),_:1},8,["modelValue","title"])):w("",!0)])}}});t("default",o(R,[["__scopeId","data-v-808ab330"]]))}}}))}();
