/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{s as e,_ as a,u as l,r as t,c as s,b as i,v as u,p as n,h as o,o as v,d as c,e as r,f as d,k as p,t as h,g as y,x as f,j as m,w as g,F as x,L as w,i as k}from"./index.4982c0f9.js";import _ from"./localLogin.6a42ce4f.js";import b from"./wechat.23adad4b.js";import j from"./feishu.e6ba81d5.js";import q from"./dingtalk.3aa78e6e.js";import T from"./oauth2.bbc060fc.js";import"./iconfont.2d75af05.js";import L from"./sms.829ddc94.js";import S from"./secondaryAuth.95bf0c1b.js";import"./verifyCode.0c196ff0.js";const O={class:"login-page"},P={class:"content"},C={class:"right-panel"},I={key:0},B={key:0,class:"title"},E={key:1,class:"title"},K={style:{"text-align":"center"}},N={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},R={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},U=["xlink:href"],z={key:2,class:"login_panel_form"},J={key:3},A=["onClick"],D={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},F=["xlink:href"],M={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},V={class:"auth-waiting"},$={class:"waiting-icon"},G={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},H=["xlink:href"],Q={class:"waiting-title"},W=a(Object.assign({name:"Login"},{setup(a){const W=l(),X=t(0),Y=t([]),Z=t("local"),ee=t(""),ae=t(""),le=t(""),te=t([]),se=t([]),ie=t(!1),ue=t(),ne=t(""),oe=t(!1),ve=t(""),ce=t(!1),re=t(""),de=t(""),pe=t(""),he=t({}),ye=s((()=>{const e=ie.value?re.value:ae.value;return Y.value.filter((a=>a.id!==e))})),fe=i();s((()=>se.value.filter((e=>e.id!==ae.value))));(async()=>{var a,l,t,s,i,u,n,o,v,c,r,d;try{const p=(()=>{const e={};if(W.query.type&&(e.type=W.query.type),W.query.wp&&(e.wp=W.query.wp),W.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(W.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const h=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===h.status){Y.value=h.data.idpList;const e=W.query.idp_id||fe.loginType;if(e&&"undefined"!==e){let n=!1;for(const a of h.data.idpList)e===a.id&&(n=!0,ae.value=a.id,Z.value=a.type,ee.value=a.templateType,te.value=a.attrs,te.value.name=a.name,te.value.authType=a.type);n||(le.value=null==(a=Y.value[0])?void 0:a.id,ae.value=null==(l=Y.value[0])?void 0:l.id,Z.value=null==(t=Y.value[0])?void 0:t.type,ee.value=null==(s=Y.value[0])?void 0:s.templateType,te.value=null==(i=Y.value[0])?void 0:i.attrs,te.value.name=Y.value[0].name,te.value.authType=null==(u=Y.value[0])?void 0:u.type)}else le.value=null==(n=Y.value[0])?void 0:n.id,ae.value=null==(o=Y.value[0])?void 0:o.id,Z.value=null==(v=Y.value[0])?void 0:v.type,ee.value=null==(c=Y.value[0])?void 0:c.templateType,te.value=null==(r=Y.value[0])?void 0:r.attrs,te.value.name=Y.value[0].name,te.value.authType=null==(d=Y.value[0])?void 0:d.type;++X.value}}catch(p){console.error(p)}})();const me=s((()=>{switch(Z.value){case"local":case"msad":case"ldap":case"web":case"email":return _;case"qiyewx":return b;case"feishu":return j;case"dingtalk":return q;case"oauth2":case"cas":return T;case"sms":return L;default:return"oauth2"===ee.value?T:"local"}})),ge=s((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===ve.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===ve.value}])),xe=()=>{ie.value=!1,se.value=[],ue.value="",ne.value="",ve.value="",ce.value=!1,re.value&&(ae.value=re.value,Z.value=de.value,ee.value=pe.value,te.value={...he.value},re.value="",de.value="",pe.value="",he.value={}),++X.value,console.log("取消后恢复的状态:",{isSecondary:ie.value,auth_id:ae.value,auth_type:Z.value})},we=async e=>{const a=w.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=W.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},ke=s((()=>!["dingtalk","feishu","qiyewx"].includes(Z.value)&&("oauth2"!==ee.value&&"cas"!==Z.value||("cas"===Z.value?1===parseInt(te.value.casOpenType):"oauth2"===ee.value&&1===parseInt(te.value.oauth2OpenType))))),_e=e=>{le.value=e.id,te.value=e.attrs||{},te.value.name=e.name,te.value.authType=e.type,ie.value&&(te.value.uniqKey=ue.value,te.value.notPhone=oe.value),ae.value=e.id,Z.value=e.type,ee.value=e.templateType,++X.value};return u(ie,(async(e,a)=>{ie.value&&(re.value=ae.value,de.value=Z.value,pe.value=ee.value,he.value={...te.value},console.log("二次认证数据:",{secondary:se.value,secondaryLength:se.value.length}),se.value.length>0&&_e(se.value[0]))})),n("secondary",se),n("isSecondary",ie),n("uniqKey",ue),n("userName",ne),n("notPhone",oe),n("last_id",le),n("contactType",ve),n("hasContactInfo",ce),(e,a)=>{const l=o("base-divider"),t=o("base-avatar"),s=o("base-carousel-item"),i=o("base-carousel");return v(),c("div",O,[r("div",P,[a[3]||(a[3]=r("div",{class:"left-panel"},[d(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),d('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),d(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),r("div",C,[d(" 正常登录状态 "),ie.value?(v(),c(x,{key:1},[d(" 二次认证等待状态 "),r("div",V,[r("div",$,[(v(),c("svg",G,[r("use",{"xlink:href":`#icon-auth-${de.value||Z.value}`},null,8,H)]))]),r("h4",Q,h(he.value.name||te.value.name)+" 登录成功",1),a[1]||(a[1]=r("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),a[2]||(a[2]=r("div",{class:"security-tips"},[r("i",{class:"el-icon-shield",style:{color:"#67c23a"}}),r("span",null,"为了您的账户安全，请完成二次身份验证")],-1))])],2112)):(v(),c("div",I,["local"===Z.value?(v(),c("span",B,"本地账号登录")):ke.value?(v(),c("span",E,[r("div",K,[r("span",N,[(v(),c("svg",R,[r("use",{"xlink:href":"#icon-auth-"+Z.value},null,8,U)])),p(" "+h(te.value.name),1)])])])):d("v-if",!0),ae.value?(v(),c("div",z,[d(' <component :is="getLoginType"></component> '),(v(),y(f(me.value),{auth_id:ae.value,auth_info:te.value},null,8,["auth_id","auth_info"])),d(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):d("v-if",!0),ye.value.length>0?(v(),c("div",J,[m(l,null,{default:g((()=>a[0]||(a[0]=[r("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)]))),_:1,__:[0]}),(v(),y(i,{key:X.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:g((()=>[(v(!0),c(x,null,k(Math.ceil(ye.value.length/2),(e=>(v(),y(s,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:g((()=>[(v(!0),c(x,null,k(ye.value.slice(2*(e-1),2*(e-1)+2),(e=>(v(),c("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:a=>_e(e)},[r("div",null,[m(t,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:g((()=>[(v(),c("svg",D,[r("use",{"xlink:href":"#icon-auth-"+e.type},null,8,F)]))])),_:2},1024)]),r("div",M,h(e.name),1)],8,A)))),128))])),_:2},1024)))),128))])),_:1}))])):d("v-if",!0)]))])]),d(" 二次认证弹窗 "),ie.value?(v(),y(S,{key:0,"auth-info":{uniqKey:ue.value,contactType:ve.value,hasContactInfo:ce.value},"auth-id":ae.value,"user-name":ne.value,"last-id":le.value,"auth-methods":ge.value,onVerificationSuccess:we,onCancel:xe},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):d("v-if",!0)])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]);export{W as default};
