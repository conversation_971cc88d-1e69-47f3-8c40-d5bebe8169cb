/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as a,h as s,o as t,d as e,f as o,j as i,e as r,g as n}from"./index.4982c0f9.js";import l from"./header.0b412779.js";import c from"./menu.f88a8c29.js";import"./ASD.492c8837.js";import"./iconfont.2d75af05.js";const f={class:"layout-page"},m={class:"layout-wrap"},u={id:"layoutMain",class:"layout-main"},d=a(Object.assign({name:"Client"},{setup:a=>(a,d)=>{const p=s("router-view");return t(),e("div",f,[o("公共顶部菜单-"),i(l),r("div",m,[o("公共侧边栏菜单"),i(c),r("div",u,[o("主流程路由渲染点"),(t(),n(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{d as default};
