/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function u(t,a,o,i){var u=a&&a.prototype instanceof s?a:s,l=Object.create(u.prototype);return n(l,"_invoke",function(t,n,a){var o,i,u,s=0,l=a||[],f=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return o=t,i=0,u=e,d.n=n,c}};function p(t,n){for(i=t,u=n,r=0;!f&&s&&!a&&r<l.length;r++){var a,o=l[r],p=d.p,v=o[2];t>3?(a=v===n)&&(u=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=p&&((a=t<2&&p<o[1])?(i=0,d.v=n,d.n=o[1]):p<v&&(a=t<3||o[0]>n||n>v)&&(o[4]=t,o[5]=n,d.n=v,i=0))}if(a||t>1)return c;throw f=!0,n}return function(a,l,v){if(s>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,v),i=l,u=v;(r=i<2?e:u)||!f;){o||(i?i<3?(i>1&&(d.n=-1),p(i,u)):d.n=u:d.v=u);try{if(s=2,o){if(i||(a="next"),r=o[a]){if(!(r=r.call(o,u)))throw TypeError("iterator result is not an object");if(!r.done)return r;u=r.value,i<2&&(i=0)}else 1===i&&(r=o.return)&&r.call(o),i<2&&(u=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=e}else if((r=(f=d.n<0)?u:t.call(n,d))!==c)break}catch(r){o=e,i=1,u=r}finally{s=1}}return{value:r,done:f}}}(t,o,i),!0),l}var c={};function s(){}function l(){}function f(){}r=Object.getPrototypeOf;var d=[][o]?r(r([][o]())):(n(r={},o,(function(){return this})),r),p=f.prototype=s.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,n(e,i,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=f,n(p,"constructor",f),n(f,"constructor",l),l.displayName="GeneratorFunction",n(f,i,"GeneratorFunction"),n(p),n(p,i,"Generator"),n(p,o,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:u,m:v}})()}function n(e,t,r,a){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}n=function(e,t,r,a){if(t)o?o(e,t,{value:r,enumerable:!a,configurable:!a,writable:!a}):e[t]=r;else{var i=function(t,r){n(e,t,(function(e){return this._invoke(t,r,e)}))};i("next",0),i("throw",1),i("return",2)}},n(e,t,r,a)}function r(e,t,n,r,a,o,i){try{var u=e[o](i),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,a)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(a,o){var i=e.apply(t,n);function u(e){r(i,a,o,u,c,"next",e)}function c(e){r(i,a,o,u,c,"throw",e)}u(void 0)}))}}System.register(["./index-legacy.11b10372.js","./secondaryAuth-legacy.7277650a.js","./verifyCode-legacy.2153456a.js"],(function(n,r){"use strict";var o,i,u,c,s,l,f,d,p,v,y,m,h,b,g,w,_,x,S=document.createElement("style");return S.textContent=".oauth-result-container[data-v-fe7893b9]{height:100vh;display:flex;justify-content:center;align-items:center;background-color:#f5f7fa}.loading-box[data-v-fe7893b9]{background:#fff;padding:40px;border-radius:8px;text-align:center;box-shadow:0 2px 12px rgba(0,0,0,.1);min-width:320px}.secondary-auth-container[data-v-fe7893b9]{background:transparent;padding:30px;border-radius:8px;box-shadow:none;min-width:340px;display:flex;justify-content:center;align-items:center;position:relative;z-index:10}.loading-icon[data-v-fe7893b9]{animation:rotate-fe7893b9 2s linear infinite;color:#409eff}.message[data-v-fe7893b9]{margin:20px 0;font-size:16px;color:#606266}@keyframes rotate-fe7893b9{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n",document.head.appendChild(S),{setters:[function(e){o=e._,i=e.u,u=e.a,c=e.b,s=e.r,l=e.p,f=e.c,d=e.E,p=e.M,v=e.h,y=e.o,m=e.d,h=e.j,b=e.w,g=e.e,w=e.t,_=e.f},function(e){x=e.default},function(){}],execute:function(){var r={class:"oauth-result-container"},S={key:0,class:"loading-box"},k={class:"message"},j={key:1,class:"secondary-auth-container"},O=Object.assign({name:"OAuth2Result"},{setup:function(n){var o=i(),O=u(),T=c(),P=s("正在处理认证信息..."),q=s(!1),C=s(""),I=s(""),E=s(""),G=s(""),A=s(""),L=s("phone"),N=s(!0);l("userName",E),l("last_id",G),l("isSecondary",s(!0)),l("contactType",L),l("hasContactInfo",N);var R=f((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===L.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===L.value}]})),z=function(){var e=a(t().m((function e(n){var r,a;return t().w((function(e){for(;;)switch(e.n){case 0:P.value="认证成功，正在跳转...",r=decodeURIComponent(A.value||"/"),n.clientParams&&((a=new URLSearchParams).set("type",n.clientParams.type),n.clientParams.wp&&a.set("wp",n.clientParams.wp),r+=(r.includes("?")?"&":"?")+a.toString()),window.location.href=r;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),F=function(){q.value=!1,P.value="已取消验证，正在返回登录页...";var e=new URLSearchParams;o.query.idp_id&&e.set("idp_id",o.query.idp_id),o.query.redirect_url&&e.set("redirect",o.query.redirect_url),"client"===o.query.type&&(e.set("type","client"),o.query.wp&&e.set("wp",o.query.wp));var t=e.toString()?"/login?".concat(e.toString()):"/login";O.push(t)};return d(a(t().m((function n(){var r,a,i,u,c,s,l,f,d,v,y,m,h,b,g,w,_,x,S,k,j,R;return t().w((function(t){for(;;)switch(t.n){case 0:if(t.p=0,r=o.query,a=r.auth_token,i=r.idp_id,u=r.redirect_url,c=r.login_type,s=r.auth_error,"is_test"!==c){t.n=1;break}return P.value="测试完成",p.success("测试完成，正在关闭窗口..."),setTimeout((function(){return window.close()}),2e3),t.a(2);case 1:if(!s){t.n=2;break}throw new Error(s);case 2:if(a){t.n=3;break}throw new Error("缺少有效的认证令牌");case 3:return localStorage.setItem("loginType",c),l={clientId:"client_portal",grantType:"implicit",redirect_uri:u,idpId:i,authWeb:{authWebToken:a}},P.value="验证登录信息...",t.n=4,T.LoginIn(l,c,i);case 4:if("object"!==e(f=t.v)||null===f||!f.isSecondary){t.n=5;break}P.value="需要进行二次认证...",L.value=f.contactType||"phone",N.value=f.hasContactInfo||!1,d=f.secondary&&Array.isArray(f.secondary)&&f.secondary.length>0?f.secondary[0].id:i,C.value=f.uniqKey,I.value=f.user_id,E.value=f.userName,G.value=d,A.value=u||"/",q.value=!0,t.n=7;break;case 5:if(!0!==f){t.n=6;break}P.value="认证成功，正在跳转...",A.value=u||"/",t.n=7;break;case 6:throw new Error("登录处理失败");case 7:t.n=9;break;case 8:t.p=8,R=t.v,console.error("处理错误:",R),v="认证失败，请稍后再试";try{if(null!==(y=R.response)&&void 0!==y&&y.data)v=R.response.data.error_description||R.response.data.message||R.response.data.error||v;else if(R.message){if((m=R.message).includes("msg=登录失败")&&(h=m.split("msg=登录失败:")[1]||m,m=h.trim()),m.includes("{"))try{b=m.indexOf("{"),g=m.substring(b),(w=JSON.parse(g))&&w.message&&(v=w.message)}catch(n){}"认证失败，请稍后再试"===v&&m.includes("message =")&&(_=/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/,(x=m.match(_))&&x[1]&&(v=x[1].trim())),"认证失败，请稍后再试"===v&&m.includes("reason =")&&(S=/reason\s*=\s*(\w+)/,(k=m.match(S))&&k[1]&&(j=k[1].replace(/_/g," ").toLowerCase(),v="认证失败: ".concat(j))),"认证失败，请稍后再试"===v&&(v=m.split("\n")[0]).length>100&&(v=v.substring(0,97)+"...")}}catch(n){console.error("处理错误消息时发生异常:",n)}P.value=v,p.error(v),setTimeout((function(){O.push({name:"Login"})}),2e3);case 9:return t.a(2)}}),n,null,[[0,8]])})))),function(e,t){var n=v("base-icon"),a=v("el-icon");return y(),m("div",r,[q.value?_("v-if",!0):(y(),m("div",S,[h(a,{class:"loading-icon",size:40},{default:b((function(){return[h(n,{name:"loading"})]})),_:1}),g("div",k,w(P.value),1)])),_(" 使用SecondaryAuth组件代替Sms组件 "),q.value?(y(),m("div",j,[h(x,{"auth-info":{uniqKey:C.value,contactType:L.value,hasContactInfo:N.value},"auth-id":G.value,"user-name":E.value,"last-id":G.value,"auth-methods":R.value,onVerificationSuccess:z,onCancel:F},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])])):_("v-if",!0)])}}});n("default",o(O,[["__scopeId","data-v-fe7893b9"],["__file","D:/asec-platform/frontend/portal/src/view/login/oauth2/oauth2_result.vue"]]))}}}))}();
