/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{u as e,a,r as s,b as l,c as t,y as n,G as u,N as o,h as r,o as i,d as v,j as m,w as c,F as d,i as y,f as p,e as g,J as f,m as h,k as b,t as S,Z as I,I as q,W as O,$ as w}from"./index.74d1ee23.js";import{J as N}from"./index-browser-esm.c2d3b5c9.js";const k={class:"router-history"},J=["tab"],x=Object.assign({name:"HistoryComponent"},{setup(x){const C=e(),j=a(),V=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),E=s([]),A=s(""),P=s(!1),T=l(),L=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),R=s(0),$=s(0),_=s(!1),F=s(!1),G=s(""),H=t((()=>N("$..defaultRouter[0]",T.userInfo)[0]||"dashboard")),U=()=>{E.value=[{name:H.value,meta:{title:"总览"},query:{},params:{}}],j.push({name:H.value}),P.value=!1,sessionStorage.setItem("historys",JSON.stringify(E.value))},W=()=>{let e;const a=E.value.findIndex((a=>(V(a)===G.value&&(e=a),V(a)===G.value))),s=E.value.findIndex((e=>V(e)===A.value));E.value.splice(0,a),a>s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(E.value))},X=()=>{let e;const a=E.value.findIndex((a=>(V(a)===G.value&&(e=a),V(a)===G.value))),s=E.value.findIndex((e=>V(e)===A.value));E.value.splice(a+1,E.value.length),a<s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(E.value))},Y=()=>{let e;E.value=E.value.filter((a=>(V(a)===G.value&&(e=a),V(a)===G.value))),j.push(e),sessionStorage.setItem("historys",JSON.stringify(E.value))},Z=e=>{if(!E.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const s in e.query)if(e.query[s]!==a.query[s])return!1;for(const s in e.params)if(e.params[s]!==a.params[s])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,E.value.push(a)}window.sessionStorage.setItem("activeValue",V(e))},z=s({});n((()=>E.value),(()=>{z.value={},E.value.forEach((e=>{z.value[V(e)]=e}))}));const B=e=>{const a=z.value[e];j.push({name:a.name,query:a.query,params:a.params})},D=e=>{const a=E.value.findIndex((a=>V(a)===e));V(C)===e&&(1===E.value.length?j.push({name:H.value}):a<E.value.length-1?j.push({name:E.value[a+1].name,query:E.value[a+1].query,params:E.value[a+1].params}):j.push({name:E.value[a-1].name,query:E.value[a-1].query,params:E.value[a-1].params})),E.value.splice(a,1)};n((()=>P.value),(()=>{P.value?document.body.addEventListener("click",(()=>{P.value=!1})):document.body.removeEventListener("click",(()=>{P.value=!1}))})),n((()=>C),((e,a)=>{"Login"!==e.name&&"Reload"!==e.name&&(E.value=E.value.filter((e=>!e.meta.closeTab)),Z(e),sessionStorage.setItem("historys",JSON.stringify(E.value)),A.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),n((()=>E.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(E.value))}),{deep:!0});return(()=>{o.on("closeThisPage",(()=>{D(L(C))})),o.on("closeAllPage",(()=>{U()})),o.on("mobile",(e=>{F.value=e})),o.on("collapse",(e=>{_.value=e}));const e=[{name:H.value,meta:{title:"总览"},query:{},params:{}}];E.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?A.value=window.sessionStorage.getItem("activeValue"):A.value=V(C),Z(C),"true"===window.sessionStorage.getItem("needCloseAll")&&(U(),window.sessionStorage.removeItem("needCloseAll"))})(),u((()=>{o.off("collapse"),o.off("mobile")})),(e,a)=>{const s=r("el-tab-pane"),l=r("el-tabs");return i(),v("div",k,[m(l,{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value=e),closable:!(1===E.value.length&&e.$route.name===H.value),type:"card",onContextmenu:a[1]||(a[1]=q((e=>(e=>{if(1===E.value.length&&C.name===H.value)return!1;let a="";if(a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a){let s;P.value=!0,s=_.value?54:220,F.value&&(s=0),R.value=e.clientX-s,$.value=e.clientY+10,G.value=a.substring(4)}})(e)),["prevent"])),onTabChange:B,onTabRemove:D},{default:c((()=>[(i(!0),v(d,null,y(E.value,(e=>(i(),p(s,{key:L(e),label:e.meta.title,name:L(e),tab:e,class:"gva-tab"},{label:c((()=>[g("span",{tab:e,style:f({color:A.value===L(e)?h(T).activeColor:"#333"})},[g("i",{class:"dot",style:f({backgroundColor:A.value===L(e)?h(T).activeColor:"#ddd"})},null,4),b(" "+S(h(I)(e.meta.title,e)),1)],12,J)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),O(g("ul",{style:f({left:R.value+"px",top:$.value+"px"}),class:"contextmenu"},[g("li",{onClick:U},"关闭所有"),g("li",{onClick:W},"关闭左侧"),g("li",{onClick:X},"关闭右侧"),g("li",{onClick:Y},"关闭其他")],4),[[w,P.value]])])}}});export{x as default};
