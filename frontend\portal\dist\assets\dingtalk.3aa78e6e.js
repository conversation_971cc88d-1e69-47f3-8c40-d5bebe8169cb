/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{a as t,r as e,b as i,v as a,o as n,d as o,e as s,k as d,B as l,_ as r}from"./index.4982c0f9.js";const c={style:{"text-align":"center"}},p={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},u={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},h={name:"Dingtalk",mounted(){this.loadThirdPartyScript()},methods:{loadThirdPartyScript(){const t=document.createElement("script");t.src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js",t.onload=()=>{this.doSomethingWithThirdPartyLibrary()},document.body.appendChild(t)}}},g=r(Object.assign(h,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(r){const h=t(),g=e(0),f=r;i();const y=async()=>{await(async()=>{const t={type:"dingtalk",data:{idpId:f.auth_id}},e=await l(t);if(200===e.status)return e.data.uniqKey})();const t=f.auth_info.dingtalkAppKey,e=window.location.host,i=`${window.location.protocol}//${e}`;setTimeout((()=>{window.DTFrameLogin({id:"self_defined_element",width:300,height:300},{redirect_uri:encodeURIComponent(i),client_id:t,scope:"openid",response_type:"code",state:f.auth_id,prompt:"consent"},(t=>{const{redirectUrl:e,authCode:i,state:a}=t;h.push({name:"Status",query:{code:i,state:a,auth_type:"dingtalk"},replace:!0})}),(t=>{t&&console.error("钉钉登录错误:",t)}))}),100)};return y(),a(f,(async(t,e)=>{g.value++,await y()})),(t,e)=>(n(),o("div",{key:g.value},[s("div",c,[s("span",p,[(n(),o("svg",u,e[0]||(e[0]=[s("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),e[1]||(e[1]=d(" 钉钉认证 "))])]),e[2]||(e[2]=s("div",{id:"self_defined_element",class:"self-defined-classname"},null,-1))]))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/dingtalk/dingtalk.vue"]]);export{g as default};
