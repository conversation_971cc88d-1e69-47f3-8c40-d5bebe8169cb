/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
System.register(["./index-legacy.11b10372.js","./header-legacy.b5f7e66b.js","./menu-legacy.82f3d709.js","./ASD-legacy.b6ffb1bc.js","./iconfont-legacy.37c53566.js"],(function(e,t){"use strict";var a,n,i,o,l,u,c,r,f,d,s=document.createElement("style");return s.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(s),{setters:[function(e){a=e._,n=e.h,i=e.o,o=e.d,l=e.f,u=e.j,c=e.e,r=e.g},function(e){f=e.default},function(e){d=e.default},function(){},function(){}],execute:function(){var t={class:"layout-page"},s={class:"layout-wrap"},h={id:"layoutMain",class:"layout-main"},y=Object.assign({name:"Client"},{setup:function(e){return function(e,a){var y=n("router-view");return i(),o("div",t,[l("公共顶部菜单-"),u(f),c("div",s,[l("公共侧边栏菜单"),u(d),c("div",h,[l("主流程路由渲染点"),(i(),r(y,{key:e.$route.fullPath}))])])])}}});e("default",a(y,[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]))}}}));
