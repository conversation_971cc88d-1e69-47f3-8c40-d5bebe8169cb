/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js"],(function(e,n){"use strict";var t,u,r,i,o,a,c,l,d,f,s;return{setters:[function(e){t=e.a0,u=e.h,r=e.o,i=e.d,o=e.j,a=e.w,c=e.T,l=e.f,d=e.a2,f=e.m,s=e.z}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[o(v,null,{default:a((function(e){var t=e.Component;return[o(c,{mode:"out-in",name:"el-fade-in-linear"},{default:a((function(){return[(r(),l(d,{include:f(n).keepAliveRouters},[(r(),l(s(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
