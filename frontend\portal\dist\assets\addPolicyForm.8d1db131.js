/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{r as e,B as l,h as a,o as i,d as t,j as o,w as n,k as d,f as u,g as s,F as r,i as c,e as p}from"./index.74d1ee23.js";import"./iconfont.2d75af05.js";const m={style:{width:"100%",float:"left"}},v={style:{"border-radius":"5px",overflow:"hidden","background-color":"#C0E9FC","padding-top":"10px",width:"100%",height:"auto"}},x={style:{width:"100%",float:"left"}},g={style:{"border-radius":"5px",overflow:"hidden","background-color":"#C0E9FC","padding-top":"10px",width:"100%",height:"auto","margin-bottom":"10px"}},f={style:{width:"100%",float:"left"}},b={style:{"border-radius":"5px",overflow:"hidden","background-color":"#C0E9FC","padding-top":"10px",width:"100%",height:"auto"}},h={style:{width:"100%",float:"left"}},_={style:{"border-radius":"5px",overflow:"hidden","background-color":"#C0E9FC","padding-top":"10px",width:"100%",height:"auto","margin-bottom":"10px"}},V={class:"dialog-footer",style:{"padding-left":"65%"}},y=Object.assign({name:"AddPolicyForm",props:["type","formLabelAlign"]},{emits:["submitForm"],setup(y,{emit:w}){const k=e("root"),U=l({source:"",name:"",description:"",uniqueIdentification:"",state:"1"}),C=e("1"),z=[{value:"xs",label:"销售部门"},{value:"yf",label:"研发部门"}],F=e("pc"),q=e(!0),E=e(!1),j=e(!1),A=e(!0),P=e(!1),O=w,B=()=>{console.log(U),O("submitForm",!1)};return(e,l)=>{const y=a("base-radio"),w=a("base-radio-group"),O=a("base-form-item"),D=a("base-input"),T=a("base-option"),I=a("base-select"),L=a("el-switch"),G=a("base-card"),H=a("el-tab-pane"),J=a("el-tabs"),K=a("base-form"),M=a("base-button");return i(),t("div",null,[o(K,{"label-position":"right","label-width":"100px",model:U,style:{"max-width":"650px"}},{default:n((()=>[o(O,{label:"状态：",prop:"state"},{default:n((()=>[o(w,{modelValue:C.value,"onUpdate:modelValue":l[0]||(l[0]=e=>C.value=e),class:"ml-4"},{default:n((()=>[o(y,{label:"1",size:"large"},{default:n((()=>l[17]||(l[17]=[d("启用")]))),_:1,__:[17]}),o(y,{label:"2",size:"large"},{default:n((()=>l[18]||(l[18]=[d("禁用")]))),_:1,__:[18]})])),_:1},8,["modelValue"])])),_:1}),o(O,{class:"custom-label",label:"基础信息"}),"user"!==k.value?(i(),u(O,{key:0,label:"名称：",prop:"name",rules:[{required:!0,message:"名称不能为空",trigger:["blur"]}]},{default:n((()=>[o(D,{modelValue:U.name,"onUpdate:modelValue":l[1]||(l[1]=e=>U.name=e)},null,8,["modelValue"])])),_:1})):s("",!0),o(O,{label:"描述：",prop:"description"},{default:n((()=>[o(D,{modelValue:U.description,"onUpdate:modelValue":l[2]||(l[2]=e=>U.description=e)},null,8,["modelValue"])])),_:1}),o(O,{label:"用户范围：",rules:[{required:!0,message:"请选择组织",trigger:["blur"]}]},{default:n((()=>[o(I,{modelValue:U.source,"onUpdate:modelValue":l[3]||(l[3]=e=>U.source=e),placeholder:"请选择",style:{width:"100%"}},{default:n((()=>[(i(),t(r,null,c(z,(e=>o(T,{key:e.value,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"]))),64))])),_:1},8,["modelValue"])])),_:1}),o(O,{label:"认证策略",class:"custom-label"}),o(J,{style:{"margin-left":"100px"},modelValue:F.value,"onUpdate:modelValue":l[14]||(l[14]=e=>F.value=e)},{default:n((()=>[o(H,{label:"PC端",name:"pc"},{default:n((()=>[p("div",m,[l[25]||(l[25]=p("p",{style:{"font-weight":"700","font-size":"14px","margin-bottom":"10px","margin-top":"10px"}},"首次认证",-1)),p("div",v,[p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:q.value,"onUpdate:modelValue":l[4]||(l[4]=e=>q.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[19]||(l[19]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-users-copy"})])],-1)),l[20]||(l[20]=p("span",null,"本地账户",-1))])),_:1,__:[19,20]})]),p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:E.value,"onUpdate:modelValue":l[5]||(l[5]=e=>E.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[21]||(l[21]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-teshuADyuzhanghaoshenqingliucheng"})])],-1)),l[22]||(l[22]=p("span",null,"ldap/ad认证",-1))])),_:1,__:[21,22]})]),p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:j.value,"onUpdate:modelValue":l[6]||(l[6]=e=>j.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[23]||(l[23]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-icon_weixin-logo-copy"})])],-1)),l[24]||(l[24]=p("span",null,"微信认证",-1))])),_:1,__:[23,24]})])])]),p("div",x,[l[30]||(l[30]=p("p",{style:{"font-weight":"700","font-size":"14px","margin-top":"20px","margin-bottom":"10px"}},"二次认证",-1)),p("div",g,[p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:A.value,"onUpdate:modelValue":l[7]||(l[7]=e=>A.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关",width:"50px"},null,8,["modelValue"]),l[26]||(l[26]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-duanxin-copy"})])],-1)),l[27]||(l[27]=p("span",null,"短信认证",-1))])),_:1,__:[26,27]})]),p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:P.value,"onUpdate:modelValue":l[8]||(l[8]=e=>P.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[28]||(l[28]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-yanzhengma1-copy"})])],-1)),l[29]||(l[29]=p("span",null,"OTP验证码",-1))])),_:1,__:[28,29]})])])])])),_:1}),o(H,{label:"移动端",name:"mobile"},{default:n((()=>[p("div",f,[l[37]||(l[37]=p("p",{style:{"font-weight":"700","font-size":"14px","margin-bottom":"10px","margin-top":"10px"}},"首次认证",-1)),p("div",b,[p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:q.value,"onUpdate:modelValue":l[9]||(l[9]=e=>q.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[31]||(l[31]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-users-copy"})])],-1)),l[32]||(l[32]=p("span",null,"本地账户",-1))])),_:1,__:[31,32]})]),p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:E.value,"onUpdate:modelValue":l[10]||(l[10]=e=>E.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[33]||(l[33]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-teshuADyuzhanghaoshenqingliucheng"})])],-1)),l[34]||(l[34]=p("span",null,"ldap/ad认证",-1))])),_:1,__:[33,34]})]),p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:j.value,"onUpdate:modelValue":l[11]||(l[11]=e=>j.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[35]||(l[35]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-icon_weixin-logo-copy"})])],-1)),l[36]||(l[36]=p("span",null,"微信认证",-1))])),_:1,__:[35,36]})])])]),p("div",h,[l[42]||(l[42]=p("p",{style:{"font-weight":"700","font-size":"14px","margin-top":"20px","margin-bottom":"10px"}},"二次认证",-1)),p("div",_,[p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:A.value,"onUpdate:modelValue":l[12]||(l[12]=e=>A.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关",width:"50px"},null,8,["modelValue"]),l[38]||(l[38]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-duanxin-copy"})])],-1)),l[39]||(l[39]=p("span",null,"短信认证",-1))])),_:1,__:[38,39]})]),p("div",null,[o(G,{class:"box-card"},{default:n((()=>[o(L,{modelValue:P.value,"onUpdate:modelValue":l[13]||(l[13]=e=>P.value=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"]),l[40]||(l[40]=p("div",null,[p("svg",{class:"icon svg-icon","aria-hidden":"true"},[p("use",{"xlink:href":"#el-icon-yanzhengma1-copy"})])],-1)),l[41]||(l[41]=p("span",null,"OTP验证码",-1))])),_:1,__:[40,41]})])])])])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["model"]),p("span",V,[o(M,{onClick:l[15]||(l[15]=e=>B())},{default:n((()=>l[43]||(l[43]=[d("取消")]))),_:1,__:[43]}),o(M,{type:"primary",onClick:l[16]||(l[16]=e=>B()),color:"#256EBF"},{default:n((()=>l[44]||(l[44]=[d("确定")]))),_:1,__:[44]})])])}}});export{y as default};
