<template>
  <el-tabs type="card" class="demo-tabs">
    <el-tab-pane>
      <template #label>
        <span class="custom-tabs-label">
          <span>调查分析</span>
        </span>
      </template>
      <div
          style="height: 190px;padding: 10px;background-color: #FFFFFF;border-radius: 5px;border: 1px rgba(242, 242, 242, 1) solid"
      >
        <p style="font-weight: 700;font-size: 16px;font-style: normal;color: rgba(0, 0, 0, 0.701960784313725)">
          取证调查</p>
        <span
            style="padding-right: 5px;font-style: normal;font-weight: 400;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
        >搜索数据事件通过下面的过滤条件组合</span>
        <base-select style="font-size: 13px;width: 150px;height: 26px" v-model="value" class="m-2 el-select-tj"
                   placeholder="选择已保存条件"
        >
          <base-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              class="date-option"
          />
        </base-select>
        <div style="padding-top: 10px">
          <base-select
              v-model="dateBuilder.type"
              size="small"
              class="survey-select"
              placeholder="请选择"
              style="height: 26px"
          >
            <base-option
                v-for="item in conditionType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="survey-option"
            />
          </base-select>
          <div style="color: darkgrey;font-size: 17px;width: 40px;float: right">
            <el-icon @click="addAppAddress">
              <CirclePlusFilled/>
            </el-icon>
            <el-icon v-if="queryBuilder.length > 1" @click="removeAppAddress(scope.$index)">
              <RemoveFilled/>
            </el-icon>
          </div>
          <div class="block">
            <el-date-picker
                v-model="dateBuilder.date"
                type="datetimerange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="defaultTime1"
                style="height: 26px;"
            />
          </div>

        </div>
        <div style="padding-top: 10px;padding-bottom: 10px">
          <base-select
              style="padding-right: 20px"
              v-model="dateBuilder.type"
              class="survey-select"
              size="small"
              placeholder="请选择"
          >
            <base-option
                v-for="item in conditionType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="survey-option"
            />
          </base-select>
          <base-select
              style="padding-right: 20px"
              v-model="dateBuilder.operation"
              class="survey-select"
              size="small"
              placeholder="请选择"

          >
            <base-option
                v-for="item in operation"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="survey-option"
            />
          </base-select>
          <base-select
              v-model="dateBuilder.value"
              multiple
              collapse-tags-tooltip
              placeholder="Select"
              class="survey-multi-select"
              style="width: calc(100% - 550px);height: 26px"
          >
            <base-option
                v-for="item in values"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                style="height: 26px"
            />
          </base-select>
          <div style="color: darkgrey;font-size: 17px;width: 40px;float: right">
            <el-icon @click="addAppAddress">
              <CirclePlusFilled/>
            </el-icon>
            <el-icon v-if="queryBuilder.length > 1" @click="removeAppAddress(scope.$index)">
              <RemoveFilled/>
            </el-icon>
          </div>
        </div>
        <div style="padding-bottom: 10px">
          <base-select
              style="padding-right: 20px"
              v-model="dateBuilder.type"
              size="small"
              class="survey-select"
              placeholder="请选择"
          >
            <base-option
                v-for="item in conditionType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="survey-option"
            />
          </base-select>
          <base-select
              style="padding-right: 20px"
              v-model="dateBuilder.operation"
              size="small"
              class="survey-select"
              placeholder="请选择"
          >
            <base-option
                v-for="item in operation"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="survey-option"
            />
          </base-select>
          <base-select
              v-model="dateBuilder.value"
              multiple
              collapse-tags-tooltip
              placeholder="Select"
              class="survey-multi-select"
              style="width: calc(100% - 550px);height: 26px"
          >
            <base-option
                v-for="item in values"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </base-select>
          <div style="color: darkgrey;font-size: 17px;width: 40px;float: right">
            <el-icon @click="addAppAddress">
              <CirclePlusFilled/>
            </el-icon>
            <el-icon v-if="queryBuilder.length > 1" @click="removeAppAddress(scope.$index)">
              <RemoveFilled/>
            </el-icon>
          </div>
        </div>
        <div style="width: 100%">
          <base-button style="height: 26px;float: right" type="primary" color="#256EBF">开始调查</base-button>
          <base-button style="height: 26px;float: right;margin-right: 10px" color="#256EBF" type="primary">保存调查条件
          </base-button>
        </div>
      </div>
      <div style="background-color: #FFFFFF;border-radius: 5px;margin-top: 5px;border: 1px rgba(242, 242, 242, 1) solid">
        <div style="padding: 20px">
          <span style="font-size: 16px;color: rgba(0, 0, 0, 0.701960784313725);font-weight: 700"
          >调查结果：50页，5000条调查记录</span>
        </div>
        <div>
          <div style="padding: 10px 10px">
            <base-button icon @click="alarmExport" style="border: 0px;font-size: 12px">
              <template #icon>
                <ExportOutlined class="alarmExport"/>
              </template>
              导出
            </base-button>
            <base-button icon style="font-size: 12px;border: 0px">
              <template #icon>
                <el-icon size="16px">
                  <RefreshRight/>
                </el-icon>
              </template>
              刷新
            </base-button>

          </div>
          <el-table
              ref="multipleTableRef"
              :data="alarmList"
              name="alarmTable"
              class="alarmTable"
              style="width: 100%;margin-top: 5px;min-width: 1200px"
              stripe
              row-class-name="alarm-table-row-style"
              header-row-class-name="alarm-table-header-style"
          >
            <el-table-column label="风险评分" width="180">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>风险评分</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                <span
                    style="font-weight: 700;font-style: normal;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                >
                  <el-icon style="color: red">
                  <WarningFilled/>
                </el-icon>
                  {{ scope.row.score }}
                </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="日期时间" width="180">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>日期时间</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.date }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="事件类型">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>事件类型</span>
                </div>
              </template>
              <template #default="scope">
                <div>
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.type }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="文件名">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>文件名</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.fileName }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="文件路径">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>文件路径</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="color: rgba(0, 0, 0, 0.701960784313725);font-weight: 400;font-size: 12px;font-style: normal">
                {{ scope.row.filePath }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="用户名" show-overflow-tooltip>
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>用户名</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
              <span style="
                    color: rgba(0, 0, 0, 0.701960784313725);
                    font-size: 12px;
                    font-weight: 400;
                    font-style: normal;
                    "
              >
                {{ scope.row.user }}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="operate" label="操作">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>操作</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <el-link type="primary"
                           :underline="false"
                           style="
                       color: rgba(2, 167, 240, 0.996078431372549);
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700"
                           @click="details(scope.row)"
                  >
                    查看详情
                  </el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
              v-model:currentPage="currentPage4"
              v-model:page-size="pageSize4"
              :page-sizes="[100, 200, 300, 400]"
              :small="small"
              :disabled="disabled"
              :background="background"
              layout="total, sizes, prev, pager, next, jumper"
              :total="10000"
              style="float: right"
              class="risk-pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="告警规则">
      <template #label>
        <span class="custom-tabs-label">
          <span>历史查询</span>
        </span>
      </template>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: 'SurveyData',
}
</script>
<script setup>
import { reactive, ref } from 'vue'

const value = ref('')

const options = [{
  value: '1',
  label: '条件一',
}]

const queryBuilder = reactive([, {
  type: '2',
  operation: '1',
  value: ['1', '2', '3', '4'],
}, {
  type: '3',
  operation: '2',
  value: ['5'],
}])

const dateBuilder = reactive({
  type: '1',
  date: ([
    new Date(2000, 10, 10, 10, 10),
    new Date(2000, 10, 11, 10, 10),
  ]),
  operation: '1',
  value: ['1', '2'],
})

const conditionType = [{
  value: '1',
  label: '时间范围',
}, {
  value: '2',
  label: '风险等级',
}, {
  value: '3',
  label: '用户',
}]

const operation = [{
  value: '1',
  label: '包含',
}, {
  value: '2',
  label: '等于',
}]

const values = [{
  value: '1',
  label: '严重事件',
}, {
  value: '2',
  label: '高危事件',
}, {
  value: '3',
  label: '中危事件',
}, {
  value: '4',
  label: '低危事件',
}, {
  value: '5',
  label: 'Anco',
}]

const defaultTime1 = new Date(2000, 1, 1, 12, 0, 0) // '12:00:00'

const addAppAddress = () => {
  console.log(111)
  queryBuilder.push({
    type: '1',
    date: ([
      new Date(2000, 10, 10, 10, 10),
      new Date(2000, 10, 11, 10, 10),
    ]),
  })
}

const removeAppAddress = (index) => {
  queryBuilder.splice(index, 1)
}

const alarmList = ref([{
  score: '14',
  date: '2015-10-02',
  type: 'U盘拷贝',
  fileName: '客户信息.xls',
  filePath: 'D:\\\\test\\test\\custom',
  user: 'Anc',
}, {
  score: '14',
  date: '2015-10-02',
  type: '网盘上传',
  fileName: '客户信息.xls',
  filePath: 'D:\\\\test\\test\\custom',
  user: 'Anc',
}, {
  score: '14',
  date: '2015-10-02',
  type: '重命名文件',
  fileName: '客户信息.xls',
  filePath: 'D:\\\\test\\test\\custom',
  user: 'Anc',
}, {
  score: '14',
  date: '2015-10-02',
  type: 'U盘拷贝',
  fileName: '客户信息.xls',
  filePath: 'D:\\\\test\\test\\custom',
  user: 'Anc',
}, {
  score: '14',
  date: '2015-10-02',
  type: 'U盘拷贝',
  fileName: '客户信息.xls',
  filePath: 'D:\\\\test\\test\\custom',
  user: 'Anc',
}, {
  score: '14',
  date: '2015-10-02',
  type: 'U盘拷贝',
  fileName: '客户信息.xls',
  filePath: 'D:\\\\test\\test\\custom',
  user: 'Anc',
}, {
  score: '14',
  date: '2015-10-02',
  type: 'U盘拷贝',
  fileName: '客户信息.xls',
  filePath: 'D:\\\\test\\test\\custom',
  user: 'Anc',
}])

// 分页
const currentPage4 = ref(4)
const pageSize4 = ref(100)
const small = ref(true)
const background = ref(true)
const disabled = ref(false)
const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
}
</script>
<style scoped>
.block {
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  flex: 1;
  float: right;
  width: calc(100% - 260px);
}

.block:last-child {
  border-right: none;
}
</style>
<style>
.el-select-tj .el-input__inner {
  height: 20px !important;
}

.el-select-tj .el-input__suffix {
  height: 20px !important;
}

.alarm-table-header-style .cell {
  font-size: 12px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.701960784313725);
}
</style>

<style lang="scss">
.el-select-tj {
  input {
    font-size: 13px;
  }
}

.date-option {
  font-size: 13px;
  color: #D7D7D7;
}

.date-option:hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}

.date-option.hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}

.survey-select {
  height: 20px;
  width: 210px !important;

  .el-input {
    height: 26px;
    width: 210px;
  }

  .el-input__inner {
    font-size: 12px;
    color: #AAAAAA;
  }
}

.survey-option {
  font-size: 12px;
}

.survey-option:hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}

.survey-option.hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}

.block {
  input {
    font-size: 12px !important;
  }
}

.survey-select {
  height: 26px;
}

.survey-multi-select {
  div {
    height: 26px;
  }

  input {
    height: 26px;
    line-height: 26px;
  }

  span {
    height: 20px;
  }
}

.risk-pagination {
  float: right;

  .el-pager {
    li {
      background-color: #FFFFFF !important;
    }

    .is-active {
      background-color: #4E8DDA !important;
    }
  }

  .btn-prev {
    background-color: #FFFFFF !important;
  }

  .btn-next {
    background-color: #FFFFFF !important;
  }
}

.alarmTable th.is-leaf {
  background: #FFFFFF !important;
}

.alarmTable th, .el-table tr {
  background: #FFFFFF;
}

.demo-tabs {

  .el-tabs__header {
    height: 35px !important;

    * {
      height: 35px !important;
    }
  }

  .el-tabs__nav {
    border-bottom: 1px solid var(--el-border-color-light) !important;
  }
}

.demo-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}

.demo-tabs .custom-tabs-label span {
  vertical-align: middle;
  margin-left: 4px;
}

.demo-tabs .el-tabs__item {
  font-size: 12px;
}

.demo-tabs .el-tabs__nav {
  background-color: #FFFFFF;
}

.demo-tabs .is-active {
  background-color: rgba(37, 110, 191, 1);
  color: #FFFFFF;
  font-size: 12px;
}
</style>
