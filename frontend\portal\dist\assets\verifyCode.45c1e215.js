/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{r as e,c as a,u as t,a as s,b as n,t as i,l as o,i as l,w as c,e as u,d as r,_ as d,m as y,n as p,M as f,j as m,o as _,k as v}from"./index.bfaf04e1.js";const h={class:"verify-code"},g={style:{top:"10px","margin-bottom":"25px","text-align":"center"}},b={class:"title"},k={key:0,class:"message-text"},C={key:1,class:"message-text"},x={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},I={style:{"text-align":"center"}},T=d(Object.assign({name:"VerifyCode"},{props:{auth_info:{type:Object,default:function(){return{}}},auth_id:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}},secondaryType:{type:String,default:"sms"}},emits:["verification-success","back","cancel"],setup(d,{emit:T}){const w=e(""),N=y("userName");y("last_id"),y("isSecondary");const S=d;console.log("verifyCode组件接收到的属性:",{secondaryType:S.secondaryType,authInfo:S.auth_info,canVerify:"email"===S.secondaryType?!1!==S.auth_info.hasEmail:!1!==S.auth_info.notPhone});const V=T,j=a((()=>void 0!==S.auth_info.hasContactInfo?S.auth_info.hasContactInfo:(S.secondaryType,!1!==S.auth_info.hasContactInfo))),q=e(60);let z;const K=()=>{clearInterval(z)},O=async()=>{if(!j.value)return;const e={uniq_key:S.auth_info.uniqKey,idp_id:S.auth_id};try{const a=await p(e);200===a.status&&-1!==a.data.code?(q.value=60,z=setInterval((()=>{q.value--,0===q.value&&K()}),1e3)):(f({showClose:!0,message:a.data.msg,type:"error"}),q.value=0)}catch(a){f({showClose:!0,message:"发送验证码失败",type:"error"}),q.value=0}};O();const E=t(),L=async()=>{const e={uniq_key:S.auth_info.uniqKey,auth_code:w.value,user_name:S.userName||N.value,idp_id:S.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"},a=await E.LoginIn(e,"accessory");-1!==a.code&&V("verification-success",a)},M=()=>{V("cancel")};return(e,a)=>{const t=m("base-button"),y=m("base-input");return _(),s("div",h,[n("div",g,[n("span",b,i("email"===d.secondaryType?"邮件认证":"短信认证"),1)]),n("div",null,[j.value?(_(),s("div",k,"验证码已发送至您账号("+i(d.userName||o(N))+")关联的"+i("email"===d.secondaryType?"邮箱":"手机")+"，请注意查收",1)):(_(),s("div",C,"您的账号("+i(d.userName||o(N))+")未关联"+i("email"===d.secondaryType?"邮箱":"手机号码")+"，请联系管理员！",1)),j.value?(_(),s("div",x,[l(y,{modelValue:w.value,"onUpdate:modelValue":a[0]||(a[0]=e=>w.value=e),placeholder:"email"===d.secondaryType?"邮箱验证码":"短信验证码",class:"input-with-select"},{append:c((()=>[l(t,{type:"info",disabled:q.value>0,onClick:O},{default:c((()=>[v("重新发送 "+i(q.value>0?`(${q.value}秒)`:""),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue","placeholder"])])):u("",!0),n("div",I,[j.value?(_(),r(t,{key:0,type:"primary",size:"large",disabled:!w.value,onClick:L},{default:c((()=>a[1]||(a[1]=[v("确 定 ")]))),_:1,__:[1]},8,["disabled"])):u("",!0),l(t,{type:"info",size:"large",onClick:M},{default:c((()=>a[2]||(a[2]=[v("取 消 ")]))),_:1,__:[2]})])])])}}}),[["__scopeId","data-v-2c12ced5"]]);export{T as default};
