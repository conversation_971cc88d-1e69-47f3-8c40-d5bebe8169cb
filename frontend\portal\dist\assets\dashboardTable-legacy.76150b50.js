/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js","./date-legacy.431857fb.js"],(function(e,t){"use strict";var a,o,l,i,m,c,d,f,n,s,r,g=document.createElement("style");return g.textContent='@charset "UTF-8";.commit-table[data-v-df172550]{background-color:#fff;height:400px}.commit-table-title[data-v-df172550]{font-weight:600;margin-bottom:12px}.commit-table .log-item[data-v-df172550]{display:flex;justify-content:space-between;margin-top:14px}.commit-table .log-item .key-box[data-v-df172550]{justify-content:center}.commit-table .log-item .key[data-v-df172550]{display:inline-flex;justify-content:center;align-items:center;width:20px;height:20px;border-radius:50%;background:#F0F2F5;text-align:center;color:rgba(0,0,0,.65)}.commit-table .log-item .key.top[data-v-df172550]{background:#314659;color:#fff}.commit-table .log-item .message[data-v-df172550]{color:rgba(0,0,0,.65)}.commit-table .log-item .form[data-v-df172550]{color:rgba(0,0,0,.65);margin-left:12px}.commit-table .log-item .flex[data-v-df172550]{line-height:20px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.commit-table .log-item .flex-1[data-v-df172550]{flex:1}.commit-table .log-item .flex-2[data-v-df172550]{flex:2}.commit-table .log-item .flex-3[data-v-df172550]{flex:3}.commit-table .log-item .flex-4[data-v-df172550]{flex:4}.commit-table .log-item .flex-5[data-v-df172550]{flex:5}\n',document.head.appendChild(g),{setters:[function(e){a=e.q,o=e._,l=e.r,i=e.o,m=e.d,c=e.e,d=e.F,f=e.i,n=e.E,s=e.t},function(e){r=e.f}],execute:function(){var t=a.create(),g={class:"commit-table"},u={class:"log"},b={class:"flex-1 flex key-box"},x={class:"flex-5 flex message"},v={class:"flex-3 flex form"},p=Object.assign({name:"DashboardTable"},{setup:function(e){var a,o=l(!0),p=l([]);return(a=0,t({url:"https://api.github.com/repos/flipped-aurora/gin-vue-admin/commits?page="+a,method:"get"})).then((function(e){var t=e.data;o.value=!1,t.forEach((function(e,t){e.commit.message&&t<10&&p.value.push({from:r(e.commit.author.date,"yyyy-MM-dd"),title:e.commit.author.name,showDayAndMonth:!0,message:e.commit.message})}))})),function(e,t){return i(),m("div",g,[t[0]||(t[0]=c("div",{class:"commit-table-title"}," 更新日志 ",-1)),c("div",u,[(i(!0),m(d,null,f(p.value,(function(e,t){return i(),m("div",{key:t,class:"log-item"},[c("div",b,[c("span",{class:n(["key",t<3&&"top"])},s(t+1),3)]),c("div",x,s(e.message),1),c("div",v,s(e.from),1)])})),128))])])}}});e("default",o(p,[["__scopeId","data-v-df172550"]]))}}}));
