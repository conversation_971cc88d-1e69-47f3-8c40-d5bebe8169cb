<template>
  <div class="agents">
    <div style="background-color: #FFFFFF;padding: 22px 20px 60px 20px;">
      <div class="header">
        <base-button style="width: 100px" :icon="Plus" @click="add()">添加组件</base-button>
        <base-button style="width: 77px" :icon="RefreshRight" @click="getTableDate">刷新</base-button>
        <!--      <el-input-->
        <!--          v-model="search"-->
        <!--          class="w-50 m-2 organize-search"-->
        <!--          placeholder="Search"-->
        <!--          :suffix-icon="Search"-->
        <!--          style="width: 15%;float: right"-->
        <!--      />-->
        <div style="float: right">
          <el-popover popper-class="custom-popover" :show-arrow="false" :width="20" trigger="click">
            <template #reference>
              <base-button :icon="Filter">筛选</base-button>
            </template>
            <div
                style="color: #252631;font-weight:400;border-bottom: 1px #EBEBEB solid;text-align: center;height: 38px;line-height: 38px"
            >
              选择显示项
            </div>
            <div style="padding-left: 17px">
              <base-checkbox style="margin-top: 10px;width: 100%" v-model="isMACHidden">MAC</base-checkbox>
              <base-checkbox style="margin-top: 10px;width: 100%" v-model="isSystemTypeHidden">系统类型</base-checkbox>
              <base-checkbox style="margin-top: 10px;width: 100%" v-model="isVersionsHidden">版本</base-checkbox>
              <base-checkbox style="margin-top: 10px;width: 100%" v-model="isCPUHidden">CPU</base-checkbox>
              <base-checkbox style="margin-top: 10px;margin-bottom: 10px;width: 100%" v-model="isRAMHidden">内存
              </base-checkbox>
            </div>
          </el-popover>
        </div>
        <!--      <el-select-->
        <!--          v-model="status"-->
        <!--          class="m-2"-->
        <!--          style="float: right;margin-right: 5px"-->
        <!--          placeholder="Select"-->
        <!--          size="small"-->
        <!--      >-->
        <!--        <el-option-->
        <!--            v-for="item in statusOptions"-->
        <!--            :key="item.value"-->
        <!--            :label="item.label"-->
        <!--            :value="item.value"-->
        <!--        />-->
        <!--      </base-select>-->
      </div>
      <CustomTable
          :table-data="tableData"
          v-bind="tableConfig"
          ref="customTable"
      >
        <template #app_name="scope">
          <div style="text-align: center">
            <span style="color: #252631" v-if="scope.row.app_name">{{ scope.row.app_name }}</span>
            <span style="color: #252631" v-else>{{ scope.row.name }}</span>
          </div>
        </template>
        <template #display_app_type="scope">
          <div style="text-align: center">
            <span style="color: #252631" v-if="scope.row.display_app_type === 3">gateway</span>
            <span style="color: #252631" v-if="scope.row.display_app_type === 2">Connector</span>
          </div>
        </template>
        <template #app_status="scope">
          <div style="text-align: center">
            <span v-if="!scope.row.installed">---</span>
            <el-icon :size="16" v-else-if="scope.row.online" style="color: #52c41a">
              <SuccessFilled/>
            </el-icon>
            <el-icon v-else :size="16">
              <CircleCloseFilled color="#D23030"/>
            </el-icon>
          </div>
        </template>
        <template #operate="scope">
          <div style="text-align: center">
            <el-link :underline="false" style="font-size: 14px;color: #2972C8" @click="edit(scope.row)">
              编辑
            </el-link>
            <base-divider direction="vertical"/>
            <el-link
                :underline="false"
                style="font-size: 14px;color: #2972C8"
                @click="delete(scope.row.id)"
            >删除
            </el-link>
            <base-divider v-if="scope.row.command_windows||scope.row.command_linux" direction="vertical"/>
            <el-link
                v-if="scope.row.command_windows||scope.row.command_linux"
                :underline="false"
                style="font-size: 14px;color: #2972C8"
                @click="copy(scope.row.platform === 'windows' ? scope.row.command_windows : scope.row.command_linux)"
            >复制安装命令
            </el-link>
          </div>
        </template>
      </CustomTable>
      <el-drawer
          v-model="drawer"
          :title="drawerTitle"
          direction="rtl"
          :show-close="false"
          size="40%"
      >
        <template #header="{ close, titleId }">
          <div style="width: 15px;max-width: 20px;float: left">
            <base-button link @click="close">
              <el-icon>
                <Close/>
              </el-icon>
            </base-button>
          </div>
          <span :id="titleId" class="titleClass">{{ drawerTitle }}</span>
        </template>
        <CustomFrom
            ref="customForm"
            v-bind="formOptions"
            :formOptions="formOptions.formValues"
            :cancel="cancel"
            :submitForm="submitForm"
            :isFooter="false"
        />
        <template #footer>
          <base-button
              color="#256EBF"
              type="primary"
              @click="submitForm"
          >确定
          </base-button>
          <base-button style="margin-left: 10px;margin-right: 90px" @click="cancel">取消</base-button>
        </template>
      </el-drawer>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Agents',
}
</script>
<script setup>
import { nextTick, provide, reactive, ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Plus,
  RefreshRight,
  CircleCloseFilled,
  Filter,
} from '@element-plus/icons-vue'
import CustomTable from '@/components/customTable.vue'
import CustomFrom from '@/components/customFrom/customFrom.vue'
import { createInstallCmd, getAgentsList, updateInstallCmd } from '@/api/agents'
import { agentsFormItemOptions } from '@/view/system/agentsFormItemOptions'
import useClipboard from 'vue-clipboard3'

const { toClipboard } = useClipboard()

const customForm = ref()
const customTable = ref()
// const search = ref('')
const drawerTitle = ref('')
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)
const drawer = ref(false)

let formOptions = {
  formItems: [],
  formValues: reactive({}),
}

// const statusOptions = ref([{
//   value: '0',
//   label: '全部状态',
// }, {
//   value: '1',
//   label: '未处理',
// }, {
//   value: '2',
//   label: '已处理',
// }])
// const status = ref('0')

const isMACHidden = ref(false)
const isSystemTypeHidden = ref(false)
const isVersionsHidden = ref(false)
const isCPUHidden = ref(false)
const isRAMHidden = ref(false)

const propList = [
  {
    prop: 'app_name',
    label: '名称',
    slotName: 'app_name',
  },
  {
    prop: 'platform',
    label: '系统类型',
    slotName: 'platform',
    isHidden: isSystemTypeHidden,
  },
  {
    prop: 'display_app_type',
    label: '组件类型',
    slotName: 'display_app_type',
  },
  {
    prop: 'app_version',
    label: '版本',
    slotName: 'app_version',
    isHidden: isVersionsHidden,
  },
  {
    prop: 'desc',
    label: '描述',
    slotName: 'desc',
  },
  {
    prop: 'app_status',
    label: '状态',
    slotName: 'app_status',
  },
  {
    prop: 'operate',
    label: '操作',
    slotName: 'operate',
  },
]
const tableConfig = {
  propList,
  isIndexColumn: true,
  isOperationColumn: false,
}

const getTableDate = async() => {
  const query = {
    limit: pageSize.value,
    offset: (currentPage.value - 1) * pageSize.value,
  }
  const res = await getAgentsList(query)
  console.log('getAgentsList')
  console.log(res)
  if (res.data.code === 0) {
    tableData.value = res.data.data.rows
    total.value = res.data.data.total_rows
  }
}

const init = async() => {
  await getTableDate()
}

init()
const operation = ref('add')
const add = () => {
  operation.value = 'add'
  drawerTitle.value = '添加组件'
  formOptions.formItems = agentsFormItemOptions(true)
  drawer.value = true
}

const edit = (row) => {
  console.log('handleEdit')
  console.log(row)
  operation.value = 'edit'
  drawerTitle.value = '编辑组件'
  formOptions.formItems = agentsFormItemOptions(false)
  formOptions.formValues.id = row.id
  formOptions.formValues.name = row.app_name || row.name
  formOptions.formValues.desc = row.desc
  formOptions.formValues.type = row.display_app_type
  formOptions.formValues.platform = row.app_plat === 'windows' ? '1' : '2'
  formOptions.formValues.installCommand = row.platform === 'windows' ? row.command_windows : row.command_linux
  drawer.value = true
}

const initFormLabelAlign = () => {
  const keys = Object.keys(formOptions.formValues)
  let obj = {}
  keys.forEach((item) => {
    obj[item] = ''
  })
  Object.assign(formOptions.formValues, obj)
}

const cancel = () => {
  initFormLabelAlign()
  drawer.value = false
}

const submitForm = async(formEl) => {
  console.log('agents')
  console.log(formOptions.formValues)
  console.log(customForm.value)
  console.log(formEl)
  await customForm.value.ruleFormRef.validate(async(valid, fields) => {
    if (!valid) {
      ElMessage({
        showClose: true,
        message: '字段校验失败，请检查！',
        type: 'error',
      })
      return ''
    }
    console.log(formOptions.formValues)
    const agent = {
      name: formOptions.formValues.name,
      desc: formOptions.formValues.desc,
      type: Number(formOptions.formValues.type),
      platform: formOptions.formValues.platform === 1 ? 'windows' : 'linux',
    }
    let res = ''
    if (operation.value === 'edit') {
      res = await updateInstallCmd(agent)
    } else {
      res = await createInstallCmd(agent)
    }
    if (res.status === 200 && res.data.code === 0) {
      ElMessage.success({
        message: '生成命令成功',
      })
      initFormLabelAlign()
      await getTableDate()
      drawer.value = false
      return
    }
    ElMessage.error({
      message: '生成命令失败，请重试',
    })
  })
}

const copy = async(value) => {
  console.log(value)
  if (!value) {
    ElMessage.error({
      message: '命令未生成，请保存后再复制',
    })
    return
  }
  try {
    await toClipboard(value.toString())
    ElMessage.success({
      message: '复制成功',
    })
  } catch (e) {
    ElMessage.error({
      message: '复制失败请重试',
    })
  }
}

const filterField = () => {
  console.log('filterField')
  isDescHidden.value = !isDescHidden.value
  console.log(isDescHidden.value)
  console.log(propList)
  console.log(tableConfig)
  // nextTick(() => {
  //   customTable.value.doLayout()
  // })
}
provide('currentPage', currentPage)
provide('pageSize', pageSize)
provide('total', total)
provide('getTableData', getTableDate)
</script>
<style scoped>
.titleClass {
  font-size: 18px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #252631;
  margin-left: 15px;
}

</style>
<style lang="scss">
.el-drawer__header {
  margin-bottom: 5px !important;
  border-bottom: 1px solid #EBEBEB;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.custom-popover {
  min-width: 130px;
  width: 130px !important;
  box-shadow: 1px 4px 15px 0px rgba(0, 0, 0, 0.11) !important;
  padding: 0px !important;
}
</style>
