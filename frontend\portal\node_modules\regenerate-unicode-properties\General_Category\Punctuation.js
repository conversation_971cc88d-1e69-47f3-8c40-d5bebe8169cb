const set = require('regenerate')(0x5F, 0x7B, 0x7D, 0xA1, 0xA7, 0xAB, 0xBB, 0xBF, 0x37E, 0x387, 0x5BE, 0x5C0, 0x5C3, 0x5C6, 0x61B, 0x6D4, 0x85E, 0x970, 0x9FD, 0xA76, 0xAF0, 0xC77, 0xC84, 0xDF4, 0xE4F, 0xF14, 0xF85, 0x10FB, 0x1400, 0x166E, 0x1CD3, 0x2D70, 0x3030, 0x303D, 0x30A0, 0x30FB, 0xA673, 0xA67E, 0xA8FC, 0xA95F, 0xABEB, 0xFE63, 0xFE68, 0xFF3F, 0xFF5B, 0xFF5D, 0x1039F, 0x103D0, 0x1056F, 0x10857, 0x1091F, 0x1093F, 0x10A7F, 0x10D6E, 0x10EAD, 0x111CD, 0x111DB, 0x112A9, 0x1145D, 0x114C6, 0x116B9, 0x1183B, 0x119E2, 0x11BE1, 0x11FFF, 0x16AF5, 0x16B44, 0x16FE2, 0x1BC9F, 0x1E5FF);
set.addRange(0x21, 0x23).addRange(0x25, 0x2A).addRange(0x2C, 0x2F).addRange(0x3A, 0x3B).addRange(0x3F, 0x40).addRange(0x5B, 0x5D).addRange(0xB6, 0xB7).addRange(0x55A, 0x55F).addRange(0x589, 0x58A).addRange(0x5F3, 0x5F4).addRange(0x609, 0x60A).addRange(0x60C, 0x60D).addRange(0x61D, 0x61F).addRange(0x66A, 0x66D).addRange(0x700, 0x70D).addRange(0x7F7, 0x7F9).addRange(0x830, 0x83E).addRange(0x964, 0x965).addRange(0xE5A, 0xE5B).addRange(0xF04, 0xF12).addRange(0xF3A, 0xF3D).addRange(0xFD0, 0xFD4).addRange(0xFD9, 0xFDA).addRange(0x104A, 0x104F).addRange(0x1360, 0x1368).addRange(0x169B, 0x169C).addRange(0x16EB, 0x16ED).addRange(0x1735, 0x1736).addRange(0x17D4, 0x17D6).addRange(0x17D8, 0x17DA).addRange(0x1800, 0x180A).addRange(0x1944, 0x1945).addRange(0x1A1E, 0x1A1F).addRange(0x1AA0, 0x1AA6).addRange(0x1AA8, 0x1AAD).addRange(0x1B4E, 0x1B4F).addRange(0x1B5A, 0x1B60).addRange(0x1B7D, 0x1B7F).addRange(0x1BFC, 0x1BFF).addRange(0x1C3B, 0x1C3F).addRange(0x1C7E, 0x1C7F).addRange(0x1CC0, 0x1CC7).addRange(0x2010, 0x2027).addRange(0x2030, 0x2043).addRange(0x2045, 0x2051).addRange(0x2053, 0x205E).addRange(0x207D, 0x207E).addRange(0x208D, 0x208E).addRange(0x2308, 0x230B).addRange(0x2329, 0x232A).addRange(0x2768, 0x2775);
set.addRange(0x27C5, 0x27C6).addRange(0x27E6, 0x27EF).addRange(0x2983, 0x2998).addRange(0x29D8, 0x29DB).addRange(0x29FC, 0x29FD).addRange(0x2CF9, 0x2CFC).addRange(0x2CFE, 0x2CFF).addRange(0x2E00, 0x2E2E).addRange(0x2E30, 0x2E4F).addRange(0x2E52, 0x2E5D).addRange(0x3001, 0x3003).addRange(0x3008, 0x3011).addRange(0x3014, 0x301F).addRange(0xA4FE, 0xA4FF).addRange(0xA60D, 0xA60F).addRange(0xA6F2, 0xA6F7).addRange(0xA874, 0xA877).addRange(0xA8CE, 0xA8CF).addRange(0xA8F8, 0xA8FA).addRange(0xA92E, 0xA92F).addRange(0xA9C1, 0xA9CD).addRange(0xA9DE, 0xA9DF).addRange(0xAA5C, 0xAA5F).addRange(0xAADE, 0xAADF).addRange(0xAAF0, 0xAAF1).addRange(0xFD3E, 0xFD3F).addRange(0xFE10, 0xFE19).addRange(0xFE30, 0xFE52).addRange(0xFE54, 0xFE61).addRange(0xFE6A, 0xFE6B).addRange(0xFF01, 0xFF03).addRange(0xFF05, 0xFF0A).addRange(0xFF0C, 0xFF0F).addRange(0xFF1A, 0xFF1B).addRange(0xFF1F, 0xFF20).addRange(0xFF3B, 0xFF3D).addRange(0xFF5F, 0xFF65).addRange(0x10100, 0x10102).addRange(0x10A50, 0x10A58).addRange(0x10AF0, 0x10AF6).addRange(0x10B39, 0x10B3F).addRange(0x10B99, 0x10B9C).addRange(0x10F55, 0x10F59).addRange(0x10F86, 0x10F89).addRange(0x11047, 0x1104D).addRange(0x110BB, 0x110BC).addRange(0x110BE, 0x110C1).addRange(0x11140, 0x11143).addRange(0x11174, 0x11175).addRange(0x111C5, 0x111C8).addRange(0x111DD, 0x111DF);
set.addRange(0x11238, 0x1123D).addRange(0x113D4, 0x113D5).addRange(0x113D7, 0x113D8).addRange(0x1144B, 0x1144F).addRange(0x1145A, 0x1145B).addRange(0x115C1, 0x115D7).addRange(0x11641, 0x11643).addRange(0x11660, 0x1166C).addRange(0x1173C, 0x1173E).addRange(0x11944, 0x11946).addRange(0x11A3F, 0x11A46).addRange(0x11A9A, 0x11A9C).addRange(0x11A9E, 0x11AA2).addRange(0x11B00, 0x11B09).addRange(0x11C41, 0x11C45).addRange(0x11C70, 0x11C71).addRange(0x11EF7, 0x11EF8).addRange(0x11F43, 0x11F4F).addRange(0x12470, 0x12474).addRange(0x12FF1, 0x12FF2).addRange(0x16A6E, 0x16A6F).addRange(0x16B37, 0x16B3B).addRange(0x16D6D, 0x16D6F).addRange(0x16E97, 0x16E9A).addRange(0x1DA87, 0x1DA8B).addRange(0x1E95E, 0x1E95F);
exports.characters = set;
