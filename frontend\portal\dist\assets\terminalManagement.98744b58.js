/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{x as e,_ as a,r as o,p as l,h as s,o as i,d as t,e as n,j as p,w as r,k as c,t as d,J as u,O as m}from"./index.74d1ee23.js";import"./iconfont.2d75af05.js";import{_ as f}from"./customTable.09ee0d92.js";const h={class:"terminal"},x={style:{"background-color":"#FFFFFF",padding:"17px 20px 60px 20px","border-radius":"4px","min-height":"calc(100vh - 145px)"}},_={class:"header"},g={style:{height:"40px"}},v={style:{"font-size":"13px",float:"left","margin-right":"7px"}},w={class:"icon","aria-hidden":"true"},b=["xlink:href"],y={style:{"font-size":"12px",color:"#252631","text-overflow":"ellipsis",overflow:"hidden",width:"130px",display:"inline-block"}},k={style:{height:"40px"}},z={style:{"font-size":"12px",color:"#252631","text-overflow":"ellipsis",overflow:"hidden",width:"130px",display:"inline-block"}},j={style:{height:"40px"}},C={style:{"font-size":"12px",color:"#252631","text-overflow":"ellipsis",overflow:"hidden",width:"120px",display:"inline-block"}},N={style:{height:"40px"}},F={style:{color:"#252631","font-size":"12px"}},D=a(Object.assign({name:"TerminalManagement"},{setup(a){const D=o(""),O=o(1),S=o(100),T=o(0),V={propList:[{prop:"app_name",label:"终端名",slotName:"app_name",isCenter:""},{prop:"os_info",label:"终端类型",slotName:"os_info"},{prop:"mac_info",label:"MAC地址",slotName:"mac_info"},{prop:"app_ips",label:"IP地址",slotName:"app_ips"},{prop:"login_user",label:"登陆用户",slotName:"login_user"},{prop:"update_time",label:"最后上线时间",slotName:"update_time"},{prop:"online",label:"在线状态",slotName:"online"}],isSelectColumn:!1,isOperationColumn:!1},I=o([]),L=async()=>{const a={limit:S.value,offset:(O.value-1)*S.value,search:D.value},o=await(async a=>e({url:"/console/v1/agents",method:"post",data:a}))(a);console.log("terminalList"),console.log(o),console.log("end"),0===o.data.code&&(I.value=o.data.data.rows,T.value=o.data.data.total_rows)};L();const M=e=>{switch(e){case"windows":return"#icon-windows";case"linux":return"#icon-linux-5";case"mac":case"ios":return"#icon-mac";default:return""}};return o([]),l("currentPage",O),l("pageSize",S),l("total",T),l("getTableData",L),(e,a)=>{const o=s("base-button"),l=s("base-input"),O=s("base-avatar");return i(),t("div",h,[n("div",x,[n("div",_,[p(o,{color:"#2972C8",plain:"",class:"iconfont icon-shuaxin",onClick:L},{default:r((()=>a[1]||(a[1]=[c("刷新")]))),_:1,__:[1]}),p(l,{modelValue:D.value,"onUpdate:modelValue":a[0]||(a[0]=e=>D.value=e),class:"w-50 m-2 organize-search",placeholder:"输入mac或用户名","suffix-icon":e.Search,style:{width:"15%",float:"right"},onChange:L},null,8,["modelValue","suffix-icon"])]),p(f,m({"table-data":I.value},V),{app_name:r((e=>[n("div",g,[n("span",v,[(i(),t("svg",w,[n("use",{"xlink:href":M(e.row.app_plat)},null,8,b)]))]),n("span",y,d(e.row.app_name),1)])])),mac_info:r((e=>[n("div",k,[n("span",z,d(e.row.mac_info.join("  ")),1)])])),app_ips:r((e=>[n("div",j,[n("span",C,d(e.row.app_ips.join("  ")),1)])])),online:r((e=>[n("div",N,[p(O,{size:8,style:u([{background:e.row.online?"#6DD230":"#252631"},{opacity:e.row.online?"1":"0.3"},{"margin-right":"6px"}])},null,8,["style"]),n("span",F,d(e.row.online?"在线":"离线"),1)])])),_:1},16,["table-data"])])])}}}),[["__scopeId","data-v-925153e2"]]);export{D as default};
