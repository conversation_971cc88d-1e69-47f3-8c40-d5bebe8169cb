/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,e,o="function"==typeof Symbol?Symbol:{},u=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function i(t,o,u,a){var i=o&&o.prototype instanceof l?o:l,f=Object.create(i.prototype);return r(f,"_invoke",function(t,r,o){var u,a,i,l=0,f=o||[],s=!1,y={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(t,r){return u=t,a=0,i=n,y.n=r,c}};function d(t,r){for(a=t,i=r,e=0;!s&&l&&!o&&e<f.length;e++){var o,u=f[e],d=y.p,h=u[2];t>3?(o=h===r)&&(i=u[(a=u[4])?5:(a=3,3)],u[4]=u[5]=n):u[0]<=d&&((o=t<2&&d<u[1])?(a=0,y.v=r,y.n=u[1]):d<h&&(o=t<3||u[0]>r||r>h)&&(u[4]=t,u[5]=r,y.n=h,a=0))}if(o||t>1)return c;throw s=!0,r}return function(o,f,h){if(l>1)throw TypeError("Generator is already running");for(s&&1===f&&d(f,h),a=f,i=h;(e=a<2?n:i)||!s;){u||(a?a<3?(a>1&&(y.n=-1),d(a,i)):y.n=i:y.v=i);try{if(l=2,u){if(a||(o="next"),e=u[o]){if(!(e=e.call(u,i)))throw TypeError("iterator result is not an object");if(!e.done)return e;i=e.value,a<2&&(a=0)}else 1===a&&(e=u.return)&&e.call(u),a<2&&(i=TypeError("The iterator does not provide a '"+o+"' method"),a=1);u=n}else if((e=(s=y.n<0)?i:t.call(r,y))!==c)break}catch(e){u=n,a=1,i=e}finally{l=1}}return{value:e,done:s}}}(t,u,a),!0),f}var c={};function l(){}function f(){}function s(){}e=Object.getPrototypeOf;var y=[][u]?e(e([][u]())):(r(e={},u,(function(){return this})),e),d=s.prototype=l.prototype=Object.create(y);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,r(t,a,"GeneratorFunction")),t.prototype=Object.create(d),t}return f.prototype=s,r(d,"constructor",s),r(s,"constructor",f),f.displayName="GeneratorFunction",r(s,a,"GeneratorFunction"),r(d),r(d,a,"Generator"),r(d,u,(function(){return this})),r(d,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:i,m:h}})()}function r(t,n,e,o){var u=Object.defineProperty;try{u({},"",{})}catch(t){u=0}r=function(t,n,e,o){if(n)u?u(t,n,{value:e,enumerable:!o,configurable:!o,writable:!o}):t[n]=e;else{var a=function(n,e){r(t,n,(function(t){return this._invoke(n,e,t)}))};a("next",0),a("throw",1),a("return",2)}},r(t,n,e,o)}function n(t,r,n,e,o,u,a){try{var i=t[u](a),c=i.value}catch(t){return void n(t)}i.done?r(c):Promise.resolve(c).then(e,o)}function e(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return o(t,r);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,r){(null==r||r>t.length)&&(r=t.length);for(var n=0,e=Array(r);n<r;n++)e[n]=t[n];return e}System.register(["./authority-legacy.db22ecf3.js","./warningBar-legacy.4145d360.js","./index-legacy.dbc04544.js"],(function(r,o){"use strict";var u,a,i,c,l,f,s,y,d,h,v,p,m,b,g;return{setters:[function(t){u=t.s},function(t){a=t.W},function(t){i=t.r,c=t.h,l=t.o,f=t.d,s=t.e,y=t.j,d=t.w,h=t.k,v=t.F,p=t.i,m=t.f,b=t.t,g=t.M}],execute:function(){var o={class:"clearfix sticky-button",style:{margin:"18px"}},w={class:"tree-content"};r("default",Object.assign({name:"Datas"},{props:{row:{default:function(){return{}},type:Object},authority:{default:function(){return[]},type:Array}},emits:["changeRow"],setup:function(r,_){var I=_.expose,j=_.emit,A=r,O=i([]),k=i(!1),x=function(t){t&&t.forEach((function(t){var r={};r.authorityId=t.authorityId,r.authorityName=t.authorityName,O.value.push(r),t.children&&t.children.length&&x(t.children)}))},S=i([]);x(A.authority),A.row.dataAuthorityId&&A.row.dataAuthorityId.forEach((function(t){var r=O.value&&O.value.filter((function(r){return r.authorityId===t.authorityId}))&&O.value.filter((function(r){return r.authorityId===t.authorityId}))[0];S.value.push(r)}));var C=j,E=function(){S.value=e(O.value),C("changeRow","dataAuthorityId",S.value),k.value=!0},T=function(){S.value=O.value.filter((function(t){return t.authorityId===A.row.authorityId})),C("changeRow","dataAuthorityId",S.value),k.value=!0},G=function(){var t=[];P(A.row,t),S.value=O.value.filter((function(r){return t.indexOf(r.authorityId)>-1})),C("changeRow","dataAuthorityId",S.value),k.value=!0},P=function(t,r){r.push(t.authorityId),t.children&&t.children.forEach((function(t){P(t,r)}))},N=function(){var r,e=(r=t().m((function r(){return t().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,u(A.row);case 1:0===t.v.code&&g({type:"success",message:"资源设置成功"});case 2:return t.a(2)}}),r)})),function(){var t=this,e=arguments;return new Promise((function(o,u){var a=r.apply(t,e);function i(t){n(a,o,u,i,c,"next",t)}function c(t){n(a,o,u,i,c,"throw",t)}i(void 0)}))});return function(){return e.apply(this,arguments)}}(),R=function(){C("changeRow","dataAuthorityId",S.value),k.value=!0};return I({enterAndNext:function(){N()},needConfirm:k}),function(t,r){var n=c("base-button"),e=c("base-checkbox"),u=c("el-checkbox-group");return l(),f("div",null,[s("div",o,[y(n,{class:"fl-right",size:"small",type:"primary",onClick:N},{default:d((function(){return r[1]||(r[1]=[h("确 定")])})),_:1,__:[1]}),y(n,{class:"fl-left",size:"small",type:"primary",onClick:E},{default:d((function(){return r[2]||(r[2]=[h("全选")])})),_:1,__:[2]}),y(n,{class:"fl-left",size:"small",type:"primary",onClick:T},{default:d((function(){return r[3]||(r[3]=[h("本角色")])})),_:1,__:[3]}),y(n,{class:"fl-left",size:"small",type:"primary",onClick:G},{default:d((function(){return r[4]||(r[4]=[h("本角色及子角色")])})),_:1,__:[4]})]),s("div",w,[y(u,{modelValue:S.value,"onUpdate:modelValue":r[0]||(r[0]=function(t){return S.value=t}),onChange:R},{default:d((function(){return[(l(!0),f(v,null,p(O.value,(function(t,r){return l(),m(e,{key:r,label:t},{default:d((function(){return[h(b(t.authorityName),1)]})),_:2},1032,["label"])})),128))]})),_:1},8,["modelValue"])]),y(a,{title:"此功能仅用于创建角色和角色的many2many关系表，具体使用还须自己结合表实现业务，详情参考示例代码（客户示例）"})])}}}))}}}))}();
