/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{_ as e,I as a,r as l,E as o,J as t,h as n,o as s,f as i,w as d,j as r,C as u,e as c,H as v,T as m,d as p,k as g,g as w,M as f}from"./index.2320e6b9.js";import{_ as h}from"./ASD.492c8837.js";import{g as b}from"./browser.2b540f5e.js";const y={style:{background:"'#273444'"}},x={class:"downloadWin"},_={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},k={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-bottom":"42px","margin-top":"60px",display:"none"}},F={key:1,class:"download-complete"},T=e(Object.assign({name:"downloadWin"},{setup(e){a((e=>({"4220daf8":e.activeBackground,"53d1bf38":e.normalText})));const T=l(!1),E=l(!0),z=l(!1),L=l("1"),j=l({});j.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};const C=l(!1),S=l(0),B=l(!1);let R=0;const M=()=>{const e=document.body.clientWidth;e<1e3||e>=1e3&&e<1200?(z.value=!1,E.value=!1,T.value=!0):(z.value=!1,E.value=!0,T.value=!1)};M();const O=l(!1);o((()=>{t.emit("collapse",T.value),t.emit("mobile",z.value),t.on("showLoading",(()=>{O.value=!0})),t.on("closeLoading",(()=>{O.value=!1})),window.onresize=()=>(M(),t.emit("collapse",T.value),void t.emit("mobile",z.value))}));const U=l("#1f2a36"),W=l(!1),D=()=>{T.value=!T.value,E.value=!T.value,W.value=!T.value,t.emit("collapse",T.value)},$=e=>100===e?"下载完成":`${e}%`,q=async(e,a)=>{try{const l=await H(e);I(l,a)}catch(l){if(R<3&&"网络连接超时"===l.message)return R++,q(e,a);throw new Error(`安装包下载失败，请检查网络连接或联系管理员。错误: ${l.message}`)}},H=e=>new Promise(((a,l)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.timeout=3e5;let t=Date.now();o.onprogress=e=>{if(e.lengthComputable){const a=e.loaded/e.total*100;S.value=Math.round(a)}else{const a=(Date.now()-t)/1e3,l=60*(e.loaded/a),o=e.loaded/l*100;S.value=Math.min(99,Math.round(o))}},o.onload=()=>{200===o.status?a(o.response):l(new Error(`HTTP 错误: ${o.status}`))},o.onerror=()=>{l(new Error("网络错误"))},o.ontimeout=()=>{l(new Error("网络连接超时"))},o.send()})),I=(e,a)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,a);else{const l=document.createElement("a"),o=document.querySelector("body");l.href=window.URL.createObjectURL(e),l.download=a,l.style.display="none",o.appendChild(l),l.click(),o.removeChild(l),window.URL.revokeObjectURL(l.href)}};return(e,a)=>{const l=n("base-row"),o=n("base-icon"),t=n("el-menu-item"),M=n("el-menu"),O=n("el-scrollbar"),H=n("Expand"),I=n("el-icon"),P=n("Fold"),A=n("base-aside"),G=n("el-link"),J=n("el-progress"),X=n("base-main"),K=n("base-container");return s(),i(K,{class:"layout-cont"},{default:d((()=>[r(K,{class:u([E.value?"openside":"hideside",z.value?"mobile":""])},{default:d((()=>[r(l,{class:u([W.value?"shadowBg":""]),onClick:a[0]||(a[0]=e=>(W.value=!W.value,E.value=!!T.value,void D()))},null,8,["class"]),r(A,{class:"main-cont main-left gva-aside"},{default:d((()=>[c("div",{class:u(["tilte",[E.value?"openlogoimg":"hidelogoimg"]]),style:v({background:U.value})},a[2]||(a[2]=[c("img",{alt:"",class:"logoimg",src:h},null,-1)]),6),c("div",y,[r(O,{style:{height:"calc(100vh - 110px)"}},{default:d((()=>[r(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:d((()=>[r(M,{collapse:T.value,"collapse-transition":!1,"default-active":L.value,"background-color":j.value.background,"active-text-color":j.value.activeText,class:"el-menu-vertical","unique-opened":""},{default:d((()=>[r(t,{index:"1"},{default:d((()=>[r(o,{name:"xiazai",size:"16px"}),a[3]||(a[3]=c("span",null,"客户端下载",-1))])),_:1,__:[3]})])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})]),c("div",{class:"footer",style:v({background:U.value})},[c("div",{class:"menu-total",onClick:D},[T.value?(s(),i(I,{key:0,color:"#FFFFFF",size:"14px"},{default:d((()=>[r(H)])),_:1})):(s(),i(I,{key:1,color:"#FFFFFF",size:"14px"},{default:d((()=>[r(P)])),_:1}))])],4)])),_:1}),r(X,{class:"main-cont main-right client"},{default:d((()=>[c("div",x,[c("div",{style:{"margin-bottom":"5%",float:"left","margin-right":"5%",width:"205px",height:"209px",background:"#F1F8FF",position:"relative"},onClick:a[1]||(a[1]=e=>(async e=>{if("windows"===e){C.value=!0,S.value=0,B.value=!1,R=0;try{const a=await b({platform:e});if(0!==a.data.code)throw new Error(a.data.msg);{const e=window.location.port,l=new URL(a.data.data.download_url);let o;e?l.toString().includes("asec-deploy")?o=a.data.data.download_url:(l.port=e,o=l.toString()):(l.port="",o=l.toString());const t=e?a.data.data.latest_filename.replace(/@(\d+)/,`@${e}`):a.data.data.latest_filename;await q(o,t),B.value=!0,f({type:"success",message:"下载完成"})}}catch(a){f({type:"error",message:a.message||"下载失败，请联系管理员"})}finally{C.value=!1,setTimeout((()=>{B.value=!1}),3e3)}}})("windows"))},[(s(),p("svg",_,a[4]||(a[4]=[c("use",{"xlink:href":"#icon-windows"},null,-1)]))),(s(),p("svg",k,a[5]||(a[5]=[c("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),a[8]||(a[8]=c("br",null,null,-1)),r(G,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:d((()=>a[6]||(a[6]=[g(" Windows客户端 ")]))),_:1,__:[6]}),r(G,{class:"window-hidden",underline:!1,style:{"margin-top":"42px",display:"none"}},{default:d((()=>a[7]||(a[7]=[g(" 点击下载Windows客户端 ")]))),_:1,__:[7]}),C.value?(s(),i(J,{key:0,percentage:S.value,format:$,"stroke-width":10,style:{"margin-top":"20px"}},null,8,["percentage"])):w("",!0),B.value?(s(),p("div",F,"下载完成")):w("",!0)])])])),_:1})])),_:1},8,["class"])])),_:1})}}}),[["__scopeId","data-v-07719df4"]]);export{T as default};
