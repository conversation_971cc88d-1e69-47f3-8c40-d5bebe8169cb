/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{u as e,a as r,b as a,r as t,H as s,p as i,L as c,M as n}from"./index.74d1ee23.js";import"./iconfont.2d75af05.js";const o=Object.assign({name:"WxOAuthCallback"},{setup(o){const u=e(),l=r(),y=a(),{code:d,state:p,auth_type:f,redirect_url:h}=u.query,m=t(Array.isArray(p)?p[0]:p),_=t("");return s((async()=>{const e=c.service({fullscreen:!0,text:"登录中，请稍候..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(e,"qiyewx_oauth",m.value)?await l.push({name:"verify",query:{redirect_url:h}}):n.error("登录失败，请重试")}catch(r){console.error("登录过程出错:",r),n.error("登录过程出错，请重试")}finally{e.close()}})),i("userName",_),(e,r)=>null}});export{o as default};
