/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var r,a,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",l=n.toStringTag||"@@toStringTag";function u(e,n,o,l){var u=n&&n.prototype instanceof i?n:i,c=Object.create(u.prototype);return t(c,"_invoke",function(e,t,n){var o,l,u,i=0,c=n||[],p=!1,d={p:0,n:0,v:r,a:f,f:f.bind(r,4),d:function(e,t){return o=e,l=0,u=r,d.n=t,s}};function f(e,t){for(l=e,u=t,a=0;!p&&i&&!n&&a<c.length;a++){var n,o=c[a],f=d.p,m=o[2];e>3?(n=m===t)&&(u=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=r):o[0]<=f&&((n=e<2&&f<o[1])?(l=0,d.v=t,d.n=o[1]):f<m&&(n=e<3||o[0]>t||t>m)&&(o[4]=e,o[5]=t,d.n=m,l=0))}if(n||e>1)return s;throw p=!0,t}return function(n,c,m){if(i>1)throw TypeError("Generator is already running");for(p&&1===c&&f(c,m),l=c,u=m;(a=l<2?r:u)||!p;){o||(l?l<3?(l>1&&(d.n=-1),f(l,u)):d.n=u:d.v=u);try{if(i=2,o){if(l||(n="next"),a=o[n]){if(!(a=a.call(o,u)))throw TypeError("iterator result is not an object");if(!a.done)return a;u=a.value,l<2&&(l=0)}else 1===l&&(a=o.return)&&a.call(o),l<2&&(u=TypeError("The iterator does not provide a '"+n+"' method"),l=1);o=r}else if((a=(p=d.n<0)?u:e.call(t,d))!==s)break}catch(a){o=r,l=1,u=a}finally{i=1}}return{value:a,done:p}}}(e,o,l),!0),c}var s={};function i(){}function c(){}function p(){}a=Object.getPrototypeOf;var d=[][o]?a(a([][o]())):(t(a={},o,(function(){return this})),a),f=p.prototype=i.prototype=Object.create(d);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,t(e,l,"GeneratorFunction")),e.prototype=Object.create(f),e}return c.prototype=p,t(f,"constructor",p),t(p,"constructor",c),c.displayName="GeneratorFunction",t(p,l,"GeneratorFunction"),t(f),t(f,l,"Generator"),t(f,o,(function(){return this})),t(f,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:u,m:m}})()}function t(e,r,a,n){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,r,a,n){if(r)o?o(e,r,{value:a,enumerable:!n,configurable:!n,writable:!n}):e[r]=a;else{var l=function(r,a){t(e,r,(function(e){return this._invoke(r,a,e)}))};l("next",0),l("throw",1),l("return",2)}},t(e,r,a,n)}function r(e,t,r,a,n,o,l){try{var u=e[o](l),s=u.value}catch(e){return void r(e)}u.done?t(s):Promise.resolve(s).then(a,n)}function a(e){return function(){var t=this,a=arguments;return new Promise((function(n,o){var l=e.apply(t,a);function u(e){r(l,n,o,u,s,"next",e)}function s(e){r(l,n,o,u,s,"throw",e)}u(void 0)}))}}System.register(["./customFrom-legacy.fd23a917.js","./index-legacy.dbc04544.js","./customTable-legacy.8e1d28b3.js","./directoryTree-legacy.8e961781.js","./resource-legacy.d4de6f0a.js","./agents-legacy.d4bdf688.js","./iconfont-legacy.37c53566.js"],(function(t,r){"use strict";var n,o,l,u,s,i,c,p,d,f,m,g,v,_,b,y,h,w,V,x,k,j,S,O,T,P,A,F,N,z,C,q,G=document.createElement("style");return G.textContent='@charset "UTF-8";.common-layout[data-v-756b5444]{min-height:calc(100vh - 200px)}.menu-label[data-v-756b5444]{width:81%;float:left;font-size:12px;padding-top:8px;color:rgba(51,51,51,.7);padding-left:10px}.organize-but[data-v-756b5444]{height:28px;width:28px;padding:6px;border-width:0px;position:absolute;font-size:12px;color:rgba(51,51,51,.7)}.organize-search[data-v-756b5444]{width:200px;float:right;height:30px}.el-table__body tr.current-row>td{background-color:#cee9fd!important}.el-table--enable-row-hover .el-table__body tr:hover>td{background-color:#cee9fd}.app-table-style .el-table__cell .cell{font-size:12px}.tree-panel{width:200px;overflow:auto;position:absolute;left:0}.tree-panel .el-tree .el-tree__empty-text{left:38%;top:45%}.tree-panel .el-tree .el-tree-node__children{overflow:initial}.tree-panel .el-tree .is-current>.el-tree-node__content{background-color:#fff}.tree-panel .el-tree .el-tree-node:focus .el-tree-node__content:hover{background-color:rgba(0,156,255,.1)}.tree-panel .el-tree .el-tree-node__content:hover{background-color:rgba(0,156,255,.2)}\n',document.head.appendChild(G),{setters:[function(e){n=e._},function(e){o=e._,l=e.r,u=e.p,s=e.B,i=e.h,c=e.o,p=e.d,d=e.j,f=e.w,m=e.e,g=e.k,v=e.O,_=e.F,b=e.i,y=e.t,h=e.f,w=e.g,V=e.M,x=e.P},function(e){k=e._},function(e){j=e.D},function(e){S=e.b,O=e.a,T=e.c,P=e.u,A=e.d,F=e.e,N=e.f,z=e.h,C=e.i},function(e){q=e.g},function(){}],execute:function(){var r=function(e,t){return console.log("options"),console.log(e),console.log("appFormItemOptions"),console.log(t),[{field:"app_name",label:"应用名称:",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"app_desc",label:"描述:",type:"input",placeholder:"组织描述"},{field:"app_status",label:"状态:",type:"radio",options:[{label:"启用",value:"1"},{label:"禁用",value:"0"}]},{field:"group_id",label:"所属分组:",type:"select",placeholder:"所属分组",options:e,optionsLabe:"group_name",optionsValue:"id",optionsKey:"id",rules:[{required:!0,message:"所属分组不能为空",trigger:"blur"}]},{field:"app_sites",label:"服务器地址:",type:"table",options:[{label:"tcp",value:"tcp"},{label:"udp",value:"udp"},{label:"icmp",value:"icmp"}],rules:[{required:!0,message:"服务器地址不能为空",trigger:"blur"}],subRules:{protocol:[{required:!0,message:"协议不能为空",trigger:"blur"}],server_addr:[{required:!0,message:"服务器地址不能为空",trigger:"blur"}],port:[{required:!0,message:"端口不能为空",trigger:"blur"}]}},{field:"sdp",label:"所属SDP:",type:"select",placeholder:"所属SDP",options:t,optionsLabe:"app_name",optionsValue:"appliance_id",optionsKey:"appliance_id",rules:[{required:!0,message:"SDP不能为空",trigger:"blur"}]},{field:"web_entry",label:"Web入口:",type:"input"},{field:"app_icon",label:"应用图标:",type:"imgRadio",localIcon:"/src/assets/noBody.png"}]},G={class:"application"},I={style:{height:"35px"}},D={style:{height:"95%",display:"block",width:"200px","overflow-y":"scroll"}},E={class:"header"},L={style:{"text-align":"center"}},R={style:{"text-align":"center"}},B={style:{"text-align":"center"}},U=Object.assign({name:"ApplicationManagement"},{setup:function(t){var o=l(""),U=l(!1),K=l("group"),M=l("新增应用"),H=l(""),W=l(!1),J=l([]),Q={label:"group_name",children:"zones",isLeaf:"leaf",isRoot:"root"},X={propList:[{prop:"app_name",label:"应用名称",slotName:"app_name"},{prop:"app_desc",label:"描述",slotName:"app_desc"},{prop:"group_name",label:"所属分组",slotName:"group_name"},{prop:"app_sites",label:"服务器地址",slotName:"app_sites"},{prop:"app_status",label:"状态",slotName:"app_status"}],isSelectColumn:!0,isOperationColumn:!0},Y=l([]),Z=l(1),$=l(100),ee=l(0);u("treeRef",o),u("currentPage",Z),u("pageSize",$),u("total",ee);var te=function(){var t=a(e().m((function t(){var r,a;return e().w((function(e){for(;;)switch(e.n){case 0:return r={offset:(Z.value-1)*$.value,limit:$.value,search:H.value},o.value&&(r.group_id=Number(o.value)),e.n=1,S(r);case 1:a=e.v,console.log("getAppList"),console.log(a),0===a.data.code&&(Y.value=a.data.data.rows,ee.value=a.data.data.total_rows);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();te(),u("getTableData",te);var re=function(){var t=a(e().m((function t(){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,O("corp_id");case 1:200===(r=e.v).status&&0===r.data.code&&(J.value=r.data.data);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();re();var ae=l([]),ne=function(){var t=a(e().m((function t(r,a){var n;return e().w((function(e){for(;;)switch(e.n){case 0:if(0!==r.level){e.n=2;break}return e.n=1,O("corp_id");case 1:if(200!==(n=e.v).status||0!==n.data.code){e.n=2;break}return ae.value=n.data.data,e.a(2,a(n.data.data));case 2:return e.a(2,a([]))}}),t)})));return function(e,r){return t.apply(this,arguments)}}(),oe={formItems:[],formValues:s({})},le=s({}),ue=function(){var t=a(e().m((function t(){var r,a;return e().w((function(e){for(;;)switch(e.n){case 0:return r={limit:$.value,offset:(Z.value-1)*$.value},e.n=1,q(r);case 1:if(a=e.v,console.log("getAgentsList"),console.log(a),0!==a.data.code){e.n=2;break}return e.a(2,a.data.data.rows);case 2:return e.a(2,[])}}),t)})));return function(){return t.apply(this,arguments)}}(),se=l(""),ie=function(){var t=a(e().m((function t(a){var n,o,u;return e().w((function(e){for(;;)switch(e.n){case 0:if("group"!==a){e.n=1;break}M.value="新增应用分组",se.value="group",oe.formItems=[{field:"name",label:"应用组织名称：",type:"input",placeholder:"请输入应用组织名称",rules:[{required:!0,message:"应用组织名称不能为空",trigger:"blur"}]},{field:"description",label:"应用组织描述：",type:"input",placeholder:"应用组织描述"}],e.n=3;break;case 1:return se.value="app",M.value="新增应用",K.value="app",o=l,e.n=2,ue();case 2:u=e.v,n=o(u),oe.formItems=r(ae,n),oe.formValues.app_sites=[{protocol:"",address:"",port:""}],oe.formValues.app_status="1",oe.formValues.app_icon={type:"1"};case 3:U.value=!0;case 4:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),ce=function(){pe(),U.value=!1},pe=function(){var e=Object.keys(oe.formValues),t={};e.forEach((function(e){t[e]=""})),Object.assign(oe.formValues,t)},de=function(){var t=a(e().m((function t(a,n){var o,u,s,i;return e().w((function(e){for(;;)switch(e.n){case 0:return console.log("handleEdit"),console.log(a,n),M.value="编辑应用",se.value="updateApp",s=l,e.n=1,ue();case 1:return i=e.v,o=s(i),e.n=2,T({id:n.id});case 2:u=e.v,console.log("application"),console.log(u),oe.formItems=r(ae,o),t=u.data.data,console.log("setFormValues"),console.log(t),oe.formValues.id=t.id,oe.formValues.app_name=t.app_name,oe.formValues.app_desc=t.app_desc,oe.formValues.app_icon=t.app_icon,oe.formValues.app_sites=[{protocol:"",serverAddress:"",port:""}],t.app_sites&&(oe.formValues.app_sites=t.app_sites),oe.formValues.app_status=t.app_status.toString(),oe.formValues.corp_id=t.corp_id,oe.formValues.group_id=t.group_id.toString(),oe.formValues.web_url=t.web_url,oe.formValues.sdp=t.bind_se.map((function(e){return e.appliance_id}))[0],oe.formValues.app_icon={type:"1"},U.value=!0;case 3:return e.a(2)}var t}),t)})));return function(e,r){return t.apply(this,arguments)}}(),fe=l(1);u("cascaderKey",fe);var me=function(){var t=a(e().m((function t(r){return e().w((function(t){for(;;)switch(t.n){case 0:return console.log("submitForm"),console.log(oe.formValues),t.n=1,r.validate(function(){var t=a(e().m((function t(r,a){var n,o,u,s,i,c,p,d,f;return e().w((function(e){for(;;)switch(e.n){case 0:if(r){e.n=1;break}return V({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),e.a(2,"");case 1:if(n="",o="","app"!==se.value&&"updateApp"!==se.value){e.n=3;break}return oe.formValues.app_sites[0].port=Number(oe.formValues.app_sites[0].port),u=[],p=l,e.n=2,ue();case 2:for(i in d=e.v,(s=p(d)).value)oe.formValues.sdp.includes(s.value[i].appliance_id)&&u.push({app_type:s.value[i].display_app_type,connector_id:2===s.value[i].display_app_type?s.value[i].appliance_id:"",se_id:2!==s.value[i].display_app_type?s.value[i].appliance_id:""});o={app_name:oe.formValues.app_name,app_desc:oe.formValues.app_desc,app_status:Number(oe.formValues.app_status),group_id:Number(oe.formValues.group_id),corp_id:1,app_sites:oe.formValues.app_sites,se_app:u,web_entry:oe.formValues.web_entry},e.n=4;break;case 3:n={group_name:oe.formValues.name};case 4:c="",f=se.value,e.n="group"===f?5:"updateGroup"===f?7:"app"===f?9:"updateApp"===f?11:13;break;case 5:return e.n=6,N(n);case 6:return c=e.v,console.log(c),e.a(3,13);case 7:return n.id=oe.formValues.id,e.n=8,F(n);case 8:return c=e.v,e.a(3,13);case 9:return console.log("createApp"),e.n=10,A(o);case 10:return c=e.v,console.log(c),e.a(3,13);case 11:return o.id=oe.formValues.id,console.log("updateApp"),console.log(oe.formValues.id),console.log(o),e.n=12,P(o);case 12:return c=e.v,console.log(c),e.a(3,13);case 13:return c.data.code<0?V({showClose:!0,message:c.data.msg,type:"error"}):V({type:"success",message:"添加应用成功"}),pe(),e.n=14,te();case 14:++fe.value,U.value=!1;case 15:return e.a(2)}}),t)})));return function(e,r){return t.apply(this,arguments)}}());case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),ge=function(){oe.formValues.app_sites.push({protocol:"",serverAddress:"",port:""})},ve=function(e){oe.formValues.app_sites.splice(e,1)},_e=function(){var t=a(e().m((function t(r,a){return e().w((function(e){for(;;)switch(e.n){case 0:console.log("edit"),console.log(a),console.log(a.id),M.value="修改分组",se.value="updateGroup",oe.formItems=[{field:"name",label:"应用组织名称：",type:"input",placeholder:"请输入应用组织名称",rules:[{required:!0,message:"应用组织名称不能为空",trigger:"blur"}]},{field:"description",label:"应用组织描述：",type:"input",placeholder:"应用组织描述"}],oe.formValues.id=a.id,oe.formValues.name=a.group_name,U.value=!0;case 1:return e.a(2)}}),t)})));return function(e,r){return t.apply(this,arguments)}}();return u("open",(function(t){x.confirm("删除分组以后将无法恢复，确认删除分组？","删除应用分组",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(a(e().m((function r(){var a;return e().w((function(e){for(;;)switch(e.n){case 0:return console.log(t),e.n=1,z({id:Number(t)});case 1:a=e.v,console.log(a),0===a.data.code?(V({type:"success",message:"删除成功"}),++fe.value):V({type:"error",message:"删除失败"});case 2:return e.a(2)}}),r)})))).catch((function(){V({type:"info",message:"取消删除"})}))})),u("handleEdit",de),u("handleDelete",(function(t,r){console.log("handleDelete"),console.log(r),x.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个应用吗？</strong><br><strong>删除后用户将无法访问该应用，请谨慎操作。</strong>',"删除应用",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(a(e().m((function t(){var a;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,C({id:r.id});case 1:if(a=e.v,console.log(a),0!==a.data.code){e.n=3;break}return V({type:"success",message:"删除成功"}),e.n=2,te();case 2:e.n=4;break;case 3:V({type:"error",message:"删除失败"});case 4:pe();case 5:return e.a(2)}}),t)})))).catch((function(){pe(),V({type:"info",message:"取消删除"})}))})),function(e,t){var r=i("base-button"),a=i("base-aside"),o=i("base-input"),l=i("SuccessFilled"),u=i("el-icon"),s=i("Remove"),V=i("base-main"),x=i("base-container"),S=i("el-dialog");return c(),p("div",G,[d(x,{style:{height:"100%"}},{default:f((function(){return[d(a,{width:"200px",style:{"min-height":"calc(100vh - 200px)"}},{default:f((function(){return[m("div",I,[t[4]||(t[4]=m("span",{class:"menu-label"},"应用分组",-1)),d(r,{class:"organize-but",icon:e.FolderAdd,onClick:t[0]||(t[0]=function(e){return ie("group")})},null,8,["icon"])]),m("div",D,[d(j,{class:"tree-panel","load-operate":W.value,"load-node":ne,"tree-props":Q,edit:_e},null,8,["load-operate"])])]})),_:1}),d(V,null,{default:f((function(){return[m("div",E,[d(r,{icon:e.Plus,onClick:t[1]||(t[1]=function(e){return ie("app")})},{default:f((function(){return t[5]||(t[5]=[g("新增应用")])})),_:1,__:[5]},8,["icon"]),d(r,{icon:e.RefreshRight,onClick:te},{default:f((function(){return t[6]||(t[6]=[g("刷新")])})),_:1,__:[6]},8,["icon"]),d(o,{modelValue:H.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return H.value=e}),class:"w-50 m-2 organize-search",placeholder:"Search","suffix-icon":e.Search,onChange:te},null,8,["modelValue","suffix-icon"])]),d(k,v({"table-data":Y.value},X),{app_sites:f((function(e){return[m("div",L,[(c(!0),p(_,null,b(e.row.app_sites,(function(e,r){return c(),p("span",{key:e.id,style:{color:"rgba(0, 0, 0, 0.701960784313725)","font-size":"12px","font-weight":"400","font-style":"normal"}},[g(y(e.protocol?e.protocol+"://"+e.address+":"+e.port:""),1),t[7]||(t[7]=m("br",null,null,-1))])})),128))])]})),group_name:f((function(e){return[m("div",R,[m("span",null,y(e.row.group_name||"默认分组"),1)])]})),app_status:f((function(e){return[m("div",B,[e.row.app_status?(c(),h(u,{key:0,style:{color:"#52c41a"}},{default:f((function(){return[d(l)]})),_:1})):(c(),h(u,{key:1},{default:f((function(){return[d(s)]})),_:1}))])]})),_:1},16,["table-data"])]})),_:1})]})),_:1}),U.value?(c(),h(S,{key:0,modelValue:U.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return U.value=e}),title:M.value,width:"30%","custom-class":"custom-dialog"},{default:f((function(){return[d(n,v(oe,{"form-options":le,cancel:ce,"submit-form":me,"add-app-address":ge,"remove-app-address":ve}),null,16,["form-options"])]})),_:1},8,["modelValue","title"])):w("",!0)])}}});t("default",o(U,[["__scopeId","data-v-756b5444"]]))}}}))}();
