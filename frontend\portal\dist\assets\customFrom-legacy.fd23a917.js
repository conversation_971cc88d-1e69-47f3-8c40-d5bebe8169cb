/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||n(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=n(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,u=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return u=e.done,e},e:function(e){c=!0,l=e},f:function(){try{u||null==r.return||r.return()}finally{if(c)throw l}}}}function n(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",l=n.toStringTag||"@@toStringTag";function u(n,r,o,l){var u=r&&r.prototype instanceof i?r:i,s=Object.create(u.prototype);return a(s,"_invoke",function(n,r,o){var a,l,u,i=0,s=o||[],f=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return a=t,l=0,u=e,d.n=n,c}};function p(n,r){for(l=n,u=r,t=0;!f&&i&&!o&&t<s.length;t++){var o,a=s[t],p=d.p,m=a[2];n>3?(o=m===r)&&(u=a[(l=a[4])?5:(l=3,3)],a[4]=a[5]=e):a[0]<=p&&((o=n<2&&p<a[1])?(l=0,d.v=r,d.n=a[1]):p<m&&(o=n<3||a[0]>r||r>m)&&(a[4]=n,a[5]=r,d.n=m,l=0))}if(o||n>1)return c;throw f=!0,r}return function(o,s,m){if(i>1)throw TypeError("Generator is already running");for(f&&1===s&&p(s,m),l=s,u=m;(t=l<2?e:u)||!f;){a||(l?l<3?(l>1&&(d.n=-1),p(l,u)):d.n=u:d.v=u);try{if(i=2,a){if(l||(o="next"),t=a[o]){if(!(t=t.call(a,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,l<2&&(l=0)}else 1===l&&(t=a.return)&&t.call(a),l<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),l=1);a=e}else if((t=(f=d.n<0)?u:n.call(r,d))!==c)break}catch(t){a=e,l=1,u=t}finally{i=1}}return{value:t,done:f}}}(n,o,l),!0),s}var c={};function i(){}function s(){}function f(){}t=Object.getPrototypeOf;var d=[][r]?t(t([][r]())):(a(t={},r,(function(){return this})),t),p=f.prototype=i.prototype=Object.create(d);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,a(e,l,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=f,a(p,"constructor",f),a(f,"constructor",s),s.displayName="GeneratorFunction",a(f,l,"GeneratorFunction"),a(p),a(p,l,"Generator"),a(p,r,(function(){return this})),a(p,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:u,m:m}})()}function a(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}a=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var l=function(t,n){a(e,t,(function(e){return this._invoke(t,n,e)}))};l("next",0),l("throw",1),l("return",2)}},a(e,t,n,r)}function l(e,t,n,r,o,a,l){try{var u=e[a](l),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,o)}function u(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function u(e){l(a,r,o,u,c,"next",e)}function c(e){l(a,r,o,u,c,"throw",e)}u(void 0)}))}}System.register(["./index-legacy.dbc04544.js"],(function(n,r){"use strict";var a,l,c,i,s,f,d,p,m,v,y,h,g,b,k,x,V,_,w,C,E,S,T,A,U=document.createElement("style");return U.textContent='@charset "UTF-8";.custom-tree-node{flex:1;display:flex;align-items:center;justify-content:space-between;font-size:14px;padding-right:8px}.el-tree-node__content{width:100%!important}.custom-transfer{border:1px #E4E4E4 solid;border-radius:4px;padding:0!important}.custom-transfer .el-radio-group{border:none;--el-border-radius-base: 0 !important}.custom-transfer .tree-label{height:25px;font-size:12px;margin:0}.custom-transfer .tree-label *{height:25px;font-size:12px}.custom-transfer .tree-label span{padding:6px 10px}.custom-transfer .tree-label .el-radio-button__inner{border:none;border-right:1px #dcdfe6 solid!important}.custom-transfer .el-input{height:25px}.custom-transfer .el-input__wrapper{border-radius:0!important;box-shadow:1px 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset!important}.custom-transfer .el-scrollbar{height:350px}.custom-transfer .el-memu-user{height:25px;width:100%}.custom-transfer .el-memu-user li *{width:50px;font-size:12px!important}.custom-transfer .el-pagination{padding:0!important}.custom-transfer .el-pagination ul li{margin:0 2px!important}.custom-transfer .el-pagination ul li.is-active{color:#fff!important;border-color:#4e8dda!important;background:#4E8DDA!important}.inputDisabled{width:345px;float:left}.custom-div:empty:before{content:attr(placeholder);color:#b1b3b8;margin-left:10px}\n',document.head.appendChild(U),{setters:[function(e){a=e.r,l=e.l,c=e.S,i=e.y,s=e.h,f=e.o,d=e.f,p=e.w,m=e.j,v=e.e,y=e.m,h=e.R,g=e.d,b=e.F,k=e.i,x=e.k,V=e.t,e.A,_=e.U,w=e.p,C=e.O,E=e.J,S=e.E,T=e.g,A=e.M}],execute:function(){var r={style:{"border-bottom":"1px #E4E4E4 solid","border-top":"1px #E4E4E4 solid"}},U={style:{width:"100%",height:"25px","border-bottom":"1px #E4E4E4 solid"}},O={class:"custom-tree-node"},j=["onClick"],P=Object.assign({name:"CustomTransfer"},{setup:function(n,_){var w=_.expose,C=a(),E=a(!1),S=a(""),T=l("groupCheck"),A=l("userCheck"),P=l("appGroupCheck"),z=l("appCheck"),F=l("getTreeData"),L=l("defaultProps"),R=l("treeData"),D=l("treeType"),M=l("treeTotal"),I=l("treeCurrentPage"),N=l("treePageSize"),G=l("dataType"),$=function(){var e=u(o().m((function e(t){return o().w((function(e){for(;;)switch(e.n){case 0:if(console.log(t),t.parent.parent){e.n=1;break}return e.a(2,t.parent);case 1:return e.n=2,$(t.parent);case 2:return e.a(2,e.v)}}),e)})));return function(t){return e.apply(this,arguments)}}(),q=l("checkedData"),K=l("treeSearch"),H=l("treeLabel"),Y=function(){var e=u(o().m((function e(n){var r,a,l,u,c;return o().w((function(e){for(;;)switch(e.n){case 0:console.log("add check"),console.log(n),r=t(n.childNodes),e.p=1,r.s();case 2:if((a=r.n()).done){e.n=5;break}if(!(l=a.value).checked){e.n=3;break}console.log("chec"),console.log(l),u={id:l.data.id,name:l.data.name,type:D.value,dataType:G.value},"group"===D.value&&"user"===G.value&&T.value.push(u),"user"===D.value&&"user"===G.value&&A.value.push(u),"group"===D.value&&"resource"===G.value&&P.value.push(u),"user"===D.value&&"resource"===G.value&&z.value.push(u),q.value.push(u),e.n=4;break;case 3:if(console.log(l),!(l.childNodes.length>0)){e.n=4;break}return e.n=4,Y(l);case 4:e.n=2;break;case 5:e.n=7;break;case 6:e.p=6,c=e.v,r.e(c);case 7:return e.p=7,r.f(),e.f(7);case 8:return e.a(2)}}),e,null,[[1,6,7,8]])})));return function(t){return e.apply(this,arguments)}}(),B=function(){var t=u(o().m((function t(n){var r,a,l,u,c,i;return o().w((function(t){for(;;)switch(t.n){case 0:return r=C.value.getNode(n),console.log(C.value.getCheckedKeys().length),console.log(R.value),C.value.getCheckedKeys().length===R.value.length?E.value=!0:E.value=!1,q.value.length=0,"group"===D.value&&"user"===G.value&&(T.value.length=0,(a=q.value).push.apply(a,e(A.value))),"user"===D.value&&"user"===G.value&&(A.value.length=0,(l=q.value).push.apply(l,e(T.value))),"group"===D.value&&"resource"===G.value&&(P.value.length=0,(u=q.value).push.apply(u,e(z.value))),"user"===D.value&&"resource"===G.value&&(z.value.length=0,(c=q.value).push.apply(c,e(P.value))),t.n=1,$(r);case 1:return i=t.v,console.log("父节点"),console.log(i),t.n=2,Y(i);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),J=function(){var e=u(o().m((function e(){var t,n,r,a;return o().w((function(e){for(;;)switch(e.n){case 0:if(console.log("setCheckedKeys"),console.log(G.value),console.log(D.value),"user"!==G.value){e.n=5;break}t=[],r=D.value,e.n="user"===r?1:"group"===r?3:5;break;case 1:return console.log(A.value),t=A.value.map((function(e){return e.id})),e.n=2,C.value.setCheckedKeys(t,!1);case 2:return e.a(3,5);case 3:return console.log(T.value),t=T.value.map((function(e){return e.id})),e.n=4,C.value.setCheckedKeys(t,!1);case 4:return e.a(3,5);case 5:if("resource"!==G.value){e.n=10;break}n=[],a=D.value,e.n="user"===a?6:"group"===a?8:10;break;case 6:return console.log(z.value),n=z.value.map((function(e){return e.id})),e.n=7,C.value.setCheckedKeys(n,!1);case 7:return e.a(3,10);case 8:return console.log(P.value),n=P.value.map((function(e){return e.id})),console.log(n),console.log(n.length),e.n=9,C.value.setCheckedKeys(n,!1);case 9:return e.a(3,10);case 10:console.log("修改全选状态"),console.log(C.value.getCheckedKeys()),C.value.getCheckedKeys().length===R.value.length?E.value=!0:E.value=!1;case 11:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),X=function(){F()};c((function(){console.log("nextTick")})),i(R,(function(t,n){var r,o;(console.log("watch"),console.log(T.value),console.log(A.value),console.log(P.value),console.log(z.value),console.log(C),"user"===G.value)?(r=q.value).push.apply(r,e(T.value).concat(e(A.value))):(o=q.value).push.apply(o,e(P.value).concat(e(z.value)));"group"===D.value&&"user"===G.value&&(S.value="组织名称"),"user"===D.value&&"user"===G.value&&(S.value="用户名"),"group"===D.value&&"resource"===G.value&&(S.value="资源分组名称"),"user"===D.value&&"resource"===G.value&&(S.value="资源名称"),J()}),{immediate:!0,deep:!0});var Q=function(e){E.value?(console.log(R.value),C.value.setCheckedNodes(R.value,!1)):C.value.setCheckedNodes([]),B(R.value[0])},W=function(){console.log("removeAll"),"user"===G.value&&(T.value.length=0,A.value.length=0),"resource"===G.value&&(P.value.length=0,z.value.length=0),q.value.length=0,J()};return w({groupCheck:T,userCheck:A,appGroupCheck:P,appCheck:z}),function(t,n){var o=s("el-radio-button"),a=s("base-radio-group"),l=s("base-checkbox"),u=s("base-input"),c=s("el-tree"),i=s("el-scrollbar"),_=s("el-pagination"),w=s("base-col"),F=s("el-link"),$=s("base-row");return f(),d($,{class:"custom-transfer"},{default:p((function(){return[m(w,{span:12,style:{"border-right":"1px #E4E4E4 solid"}},{default:p((function(){return[v("div",null,[m(a,{modelValue:y(D),"onUpdate:modelValue":n[0]||(n[0]=function(e){return h(D)?D.value=e:null}),size:"large"},{default:p((function(){return[(f(!0),g(b,null,k(y(H),(function(e){return f(),d(o,{key:e.value,class:"tree-label",label:e.value,onChange:X},{default:p((function(){return[x(V(e.text),1)]})),_:2},1032,["label"])})),128))]})),_:1},8,["modelValue"])]),v("div",r,[m(l,{style:{margin:"2px 5px 5px 22px"},modelValue:E.value,"onUpdate:modelValue":n[1]||(n[1]=function(e){return E.value=e}),onChange:Q},null,8,["modelValue"]),m(u,{style:{width:"calc(100% - 41px)",height:"23px"},modelValue:y(K),"onUpdate:modelValue":n[2]||(n[2]=function(e){return h(K)?K.value=e:null}),placeholder:S.value,onChange:X},null,8,["modelValue","placeholder"])]),m(i,{height:"350px",wrapRef:"scroll"},{default:p((function(){return[m(c,{data:y(R),ref_key:"treeRef",ref:C,"expand-on-click-node":!1,"check-on-click-node":"",props:y(L),"show-checkbox":"","node-key":"id",onCheck:B},null,8,["data","props"])]})),_:1}),m(_,{small:"",currentPage:y(I),"onUpdate:currentPage":n[3]||(n[3]=function(e){return h(I)?I.value=e:null}),"page-size":y(N),"onUpdate:pageSize":n[4]||(n[4]=function(e){return h(N)?N.value=e:null}),layout:"prev, pager, next",total:y(M),"onUpdate:total":n[5]||(n[5]=function(e){return h(M)?M.value=e:null}),onCurrentChange:X},null,8,["currentPage","page-size","total"])]})),_:1}),m(w,{span:12},{default:p((function(){return[m(i,{wrapRef:"scroll",style:{height:"400px"}},{default:p((function(){return[v("div",U,[m(F,{underline:!1,style:{margin:"5px",float:"right"},onClick:W},{default:p((function(){return n[6]||(n[6]=[x("清空")])})),_:1,__:[6]})]),v("div",null,[m(c,{data:y(q),props:y(L)},{default:p((function(t){var n=t.node,r=t.data;return[v("span",O,[v("span",null,V(n.label),1),v("span",null,[v("a",{onClick:function(t){return function(t,n){console.log("remove");var r=t.parent,o=r.data.children||r.data;console.log(n);var a=o.findIndex((function(e){return e.id===n.id}));o.splice(a,1),q.value=e(q.value),console.log(D.value),console.log(G.value),"group"===n.type&&"user"===n.dataType&&T.value.splice(T.value.indexOf(n),1),"user"===n.type&&"user"===n.dataType&&A.value.splice(A.value.indexOf(n),1),"group"===n.type&&"resource"===n.dataType&&P.value.splice(P.value.indexOf(n),1),"user"===n.type&&"resource"===n.dataType&&z.value.splice(z.value.indexOf(n),1),J()}(n,r)}},"删除",8,j)])])]})),_:1},8,["data","props"])])]})),_:1})]})),_:1})]})),_:1})}}}),z={exports:{}};
/*!
       * clipboard.js v2.0.11
       * https://clipboardjs.com/
       *
       * Licensed MIT © Zeno Rocha
       */
!function(e){var t;t=function(){return function(){var e={686:function(e,t,n){n.d(t,{default:function(){return V}});var r=n(279),o=n.n(r),a=n(370),l=n.n(a),u=n(817),c=n.n(u);function i(e){try{return document.execCommand(e)}catch(t){return!1}}var s=function(e){var t=c()(e);return i("cut"),t},f=function(e,t){var n=function(e){var t="rtl"===document.documentElement.getAttribute("dir"),n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[t?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(r,"px"),n.setAttribute("readonly",""),n.value=e,n}(e);t.container.appendChild(n);var r=c()(n);return i("copy"),n.remove(),r},d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},n="";return"string"==typeof e?n=f(e,t):e instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null==e?void 0:e.type)?n=f(e.value,t):(n=c()(e),i("copy")),n};function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}var m=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.action,n=void 0===t?"copy":t,r=e.container,o=e.target,a=e.text;if("copy"!==n&&"cut"!==n)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==p(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===n&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===n&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return a?d(a,{container:r}):o?"cut"===n?s(o):d(o,{container:r}):void 0};function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function y(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function h(e,t){return h=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},h(e,t)}function g(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r,o,a=b(e);if(t){var l=b(this).constructor;n=Reflect.construct(a,arguments,l)}else n=a.apply(this,arguments);return r=this,!(o=n)||"object"!==v(o)&&"function"!=typeof o?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r):o}}function b(e){return b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},b(e)}function k(e,t){var n="data-clipboard-".concat(e);if(t.hasAttribute(n))return t.getAttribute(n)}var x=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&h(e,t)}(a,e);var t,n,r,o=g(a);function a(e,t){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(n=o.call(this)).resolveOptions(t),n.listenClick(e),n}return t=a,n=[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText,this.container="object"===v(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var t=this;this.listener=l()(e,"click",(function(e){return t.onClick(e)}))}},{key:"onClick",value:function(e){var t=e.delegateTarget||e.currentTarget,n=this.action(t)||"copy",r=m({action:n,container:this.container,target:this.target(t),text:this.text(t)});this.emit(r?"success":"error",{action:n,text:r,trigger:t,clearSelection:function(){t&&t.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(e){return k("action",e)}},{key:"defaultTarget",value:function(e){var t=k("target",e);if(t)return document.querySelector(t)}},{key:"defaultText",value:function(e){return k("text",e)}},{key:"destroy",value:function(){this.listener.destroy()}}],r=[{key:"copy",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return d(e,t)}},{key:"cut",value:function(e){return s(e)}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],t="string"==typeof e?[e]:e,n=!!document.queryCommandSupported;return t.forEach((function(e){n=n&&!!document.queryCommandSupported(e)})),n}}],n&&y(t.prototype,n),r&&y(t,r),a}(o()),V=x},828:function(e){if("undefined"!=typeof Element&&!Element.prototype.matches){var t=Element.prototype;t.matches=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector}e.exports=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}},438:function(e,t,n){var r=n(828);function o(e,t,n,r,o){var l=a.apply(this,arguments);return e.addEventListener(n,l,o),{destroy:function(){e.removeEventListener(n,l,o)}}}function a(e,t,n,o){return function(n){n.delegateTarget=r(n.target,t),n.delegateTarget&&o.call(e,n)}}e.exports=function(e,t,n,r,a){return"function"==typeof e.addEventListener?o.apply(null,arguments):"function"==typeof n?o.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return o(e,t,n,r,a)})))}},879:function(e,t){t.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},t.nodeList=function(e){var n=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===n||"[object HTMLCollection]"===n)&&"length"in e&&(0===e.length||t.node(e[0]))},t.string=function(e){return"string"==typeof e||e instanceof String},t.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},370:function(e,t,n){var r=n(879),o=n(438);e.exports=function(e,t,n){if(!e&&!t&&!n)throw new Error("Missing required arguments");if(!r.string(t))throw new TypeError("Second argument must be a String");if(!r.fn(n))throw new TypeError("Third argument must be a Function");if(r.node(e))return function(e,t,n){return e.addEventListener(t,n),{destroy:function(){e.removeEventListener(t,n)}}}(e,t,n);if(r.nodeList(e))return function(e,t,n){return Array.prototype.forEach.call(e,(function(e){e.addEventListener(t,n)})),{destroy:function(){Array.prototype.forEach.call(e,(function(e){e.removeEventListener(t,n)}))}}}(e,t,n);if(r.string(e))return function(e,t,n){return o(document.body,e,t,n)}(e,t,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},817:function(e){e.exports=function(e){var t;if("SELECT"===e.nodeName)e.focus(),t=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var n=e.hasAttribute("readonly");n||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),n||e.removeAttribute("readonly"),t=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var r=window.getSelection(),o=document.createRange();o.selectNodeContents(e),r.removeAllRanges(),r.addRange(o),t=r.toString()}return t}},279:function(e){function t(){}t.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function o(){r.off(e,o),t.apply(n,arguments)}return o._=t,this.on(e,o,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,o=n.length;r<o;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],o=[];if(r&&t)for(var a=0,l=r.length;a<l;a++)r[a].fn!==t&&r[a].fn._!==t&&o.push(r[a]);return o.length?n[e]=o:delete n[e],this}},e.exports=t,e.exports.TinyEmitter=t}},t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}return n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n(686)}().default},e.exports=t()}(z);var F=_(z.exports),L=n("u",(function(e){var t=void 0===(null==e?void 0:e.appendToBody)||e.appendToBody;return{toClipboard:function(e,n){return new Promise((function(r,o){var a=document.createElement("button"),l=new F(a,{text:function(){return e},action:function(){return"copy"},container:void 0!==n?n:document.body});l.on("success",(function(e){l.destroy(),r(e)})),l.on("error",(function(e){l.destroy(),o(e)})),t&&document.body.appendChild(a),a.click(),t&&document.body.removeChild(a)}))}}})),R={style:{color:"darkgrey","font-size":"20px"}},D=["onClick"],M=["placeholder"],I={style:{float:"right","margin-right":"5px"}},N={style:{width:"100%"}},G=["src"],$=["src"],q={key:0,class:"dialog-footer"},K={class:"dialog-footer"},H={name:"CustomFrom",props:{formValues:{type:Object,required:!0},rules:{type:Object,required:!1},formItems:{type:Array,default:function(){return[]}},isFooter:{type:Boolean,required:!1,default:!0},itemStyle:{type:Object,default:function(){return{}}},colLayout:{type:Object,default:function(){return{xl:24,lg:24,md:12,sm:24}}},cancel:{type:Function,required:!0},submitForm:{type:Function,required:!0},addAppAddress:{type:Function,required:!1},removeAppAddress:{type:Function,required:!1}}};n("_",Object.assign(H,{setup:function(t,n){var r=n.expose,c=a("allDay"),i=a(),y=t,h=l("getTreeData"),_=a(!1),U=a(),O=l("dataType"),j=a(),z=l("treeType"),F=l("treeLabel"),H=l("checkedData"),Y=function(){console.log(i.value),y.formValues.groupCheck=i.value.groupCheck,y.formValues.userCheck=i.value.userCheck,y.formValues.appGroupCheck=i.value.appGroupCheck,y.formValues.appCheck=i.value.appCheck,y.formValues.user=[].concat(e(i.value.groupCheck),[i.value.userCheck]),y.formValues.resource=[].concat(e(i.value.appGroupCheck),[i.value.appCheck]),_.value=!1},B=L().toClipboard,J=function(){var e=u(o().m((function e(t){return o().w((function(e){for(;;)switch(e.n){case 0:if(t){e.n=1;break}return A.error({message:"命令未生成，请保存后再复制"}),e.a(2);case 1:return e.p=1,e.n=2,B(t.toString());case 2:A.success({message:"复制成功"}),e.n=4;break;case 3:e.p=3,e.v,A.error({message:"复制失败请重试"});case 4:return e.a(2)}}),e,null,[[1,3]])})));return function(t){return e.apply(this,arguments)}}();return w("checkedData",H),r({ruleFormRef:j}),function(e,n){var r=s("base-input"),o=s("base-button"),a=s("base-option"),l=s("base-select"),u=s("el-tree-select"),y=s("base-checkbox"),w=s("base-radio"),A=s("base-radio-group"),L=s("el-date-picker"),B=s("el-time-picker"),X=s("base-form-item"),Q=s("el-table-column"),W=s("CirclePlusFilled"),Z=s("el-icon"),ee=s("RemoveFilled"),te=s("el-table"),ne=s("el-tag"),re=s("ArrowDown"),oe=s("base-icon"),ae=s("el-upload"),le=s("base-col"),ue=s("base-row"),ce=s("base-form"),ie=s("el-dialog");return f(),g(b,null,[m(ce,{ref_key:"ruleFormRef",ref:j,"label-position":"left","label-width":"100px",style:{"max-width":"550px","margin-left":"30px"},model:t.formValues},{default:p((function(){return[m(ue,null,{default:p((function(){return[(f(!0),g(b,null,k(t.formItems,(function(i){return f(),d(le,C({ref_for:!0},t.colLayout),{default:p((function(){return[i.isHidden?T("",!0):(f(),d(X,{key:0,label:i.rules?i.label:"  "+i.label,rules:i.rules,style:E(i.rules?"margin-right: 35px":"margin-left:1px !important;margin-right: 35px"),prop:i.field},{default:p((function(){return["input"===i.type||"password"===i.type?(f(),g(b,{key:0},[m(r,{type:i.type,placeholder:i.placeholder,disabled:i.disabled,modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e},class:S({inputDisabled:i.disabled}),autocomplete:"new-passwprd"},null,8,["type","placeholder","disabled","modelValue","onUpdate:modelValue","class"]),i.disabled?(f(),d(o,{key:0,style:{"margin-left":"7px",float:"right"},onClick:function(e){return J(t.formValues["".concat(i.field)])}},{default:p((function(){return n[5]||(n[5]=[x("复制 ")])})),_:2,__:[5]},1032,["onClick"])):T("",!0)],64)):T("",!0),"date"===i.type?(f(),d(l,{key:1,modelValue:c.value,"onUpdate:modelValue":n[0]||(n[0]=function(e){return c.value=e}),class:"m-2",placeholder:"Select",size:"small"},{default:p((function(){return[m(a,{label:"全天",value:"allDay"},{default:p((function(){return n[6]||(n[6]=[x("全天")])})),_:1,__:[6]}),m(a,{label:"其它",value:"other"},{default:p((function(){return n[7]||(n[7]=[x("其它")])})),_:1,__:[7]})]})),_:1},8,["modelValue"])):T("",!0),"treeSelect"===i.type?(f(),d(u,{key:2,props:{value:"id",label:"name",children:"subGroups"},modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e},data:i.options.value,"check-strictly":"","render-after-expand":!1},null,8,["modelValue","onUpdate:modelValue","data"])):T("",!0),"select"===i.type?(f(),d(l,{key:3,placeholder:i.placeholder,style:{width:"100%"},modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e}},{default:p((function(){return[(f(!0),g(b,null,k(i.options.value,(function(e){return f(),d(a,{key:e["".concat(i.optionsKey)],label:e["".concat(i.optionsLabe)],value:e["".concat(i.optionsValue)]},null,8,["label","value"])})),128))]})),_:2},1032,["placeholder","modelValue","onUpdate:modelValue"])):T("",!0),"multiSelect"===i.type?(f(),d(l,{key:4,placeholder:i.placeholder,multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"},modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e}},{default:p((function(){return[(f(!0),g(b,null,k(i.options.value,(function(e){return f(),d(a,{key:e["".concat(i.optionsKey)]||e.id,label:e["".concat(i.optionsLabe)]||e.name,value:e["".concat(i.optionsValue)]||e.name},null,8,["label","value"])})),128))]})),_:2},1032,["placeholder","modelValue","onUpdate:modelValue"])):T("",!0),"checkbox"===i.type?(f(),d(y,{key:5,modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e},size:"large"},null,8,["modelValue","onUpdate:modelValue"])):T("",!0),"radio"===i.type?(f(),d(A,{key:6,"text-color":"#252631",modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e},class:"ml-4"},{default:p((function(){return[(f(!0),g(b,null,k(i.options,(function(e){return f(),d(w,{size:"large",label:e.value,key:e.value},{default:p((function(){return[x(V(e.label),1)]})),_:2},1032,["label"])})),128))]})),_:2},1032,["modelValue","onUpdate:modelValue"])):T("",!0),"datepicker"===i.type||"timepicker"===i.type?(f(),d(A,{key:7,modelValue:t.formValues["".concat(i.dateType)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.dateType)]=e},class:"ml-4"},{default:p((function(){return["timepicker"===i.type?(f(),d(w,{key:0,label:"0",size:"large"},{default:p((function(){return n[8]||(n[8]=[x("全天")])})),_:1,__:[8]})):(f(),d(w,{key:1,label:"0",size:"large"},{default:p((function(){return n[9]||(n[9]=[x("永不过期")])})),_:1,__:[9]})),m(w,{label:"1",size:"large"},{default:p((function(){return n[10]||(n[10]=[x("自定义")])})),_:1,__:[10]}),"1"===t.formValues["".concat(i.dateType)]&&"datepicker"===i.type?(f(),d(L,{key:2,modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e},format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",type:"date",placeholder:"请选择日期",style:{width:"44%"}},null,8,["modelValue","onUpdate:modelValue"])):"1"===t.formValues["".concat(i.dateType)]&&"timepicker"===i.type?(f(),d(B,{key:3,modelValue:t.formValues["".concat(i.field)],"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)]=e},"value-format":"hh:mm:ss","is-range":"",format:"hh:mm:ss","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"44%"}},null,8,["modelValue","onUpdate:modelValue"])):T("",!0)]})),_:2},1032,["modelValue","onUpdate:modelValue"])):T("",!0),"table"===i.type?(f(),d(te,{key:8,data:t.formValues["".concat(i.field)],style:{width:"100%"}},{default:p((function(){return[m(Q,{align:"center"},{header:p((function(){return n[11]||(n[11]=[v("span",{style:{color:"red"}},"*",-1),v("span",null," 协议",-1)])})),default:p((function(e){return[m(X,{prop:"".concat(i.field,"[").concat(e.$index,"].protocol"),rules:i.subRules.protocol},{default:p((function(){return[m(l,{modelValue:t.formValues["".concat(i.field)][e.$index].protocol,"onUpdate:modelValue":function(n){return t.formValues["".concat(i.field)][e.$index].protocol=n},size:"small",placeholder:"请选择"},{default:p((function(){return[(f(!0),g(b,null,k(i.options,(function(e){return f(),d(a,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:2},1032,["modelValue","onUpdate:modelValue"])]})),_:2},1032,["prop","rules"])]})),_:2},1024),m(Q,{align:"center"},{header:p((function(){return n[12]||(n[12]=[v("span",{style:{color:"red"}},"*",-1),v("span",null," 服务器地址",-1)])})),default:p((function(e){return[m(X,{prop:"".concat(i.field,"[").concat(e.$index,"].address"),rules:i.subRules.address},{default:p((function(){return[m(r,{modelValue:t.formValues["".concat(i.field)][e.$index].address,"onUpdate:modelValue":function(n){return t.formValues["".concat(i.field)][e.$index].address=n},size:"small",placeholder:"请输入地址"},null,8,["modelValue","onUpdate:modelValue"])]})),_:2},1032,["prop","rules"])]})),_:2},1024),m(Q,{align:"center"},{header:p((function(){return n[13]||(n[13]=[v("span",{style:{color:"red"}},"*",-1),v("span",null," 端口",-1)])})),default:p((function(e){return[m(X,{prop:"".concat(i.field,"[").concat(e.$index,"].port"),rules:i.subRules.port},{default:p((function(){return[m(r,{modelValue:t.formValues["".concat(i.field)][e.$index].port,"onUpdate:modelValue":function(n){return t.formValues["".concat(i.field)][e.$index].port=n},onBlur:function(n){return t.formValues["".concat(i.field)][e.$index].port=Number(t.formValues["".concat(i.field)][e.$index].port)},size:"small",placeholder:"请输入端口"},null,8,["modelValue","onUpdate:modelValue","onBlur"])]})),_:2},1032,["prop","rules"])]})),_:2},1024),m(Q,{align:"center"},{header:p((function(){return n[14]||(n[14]=[v("span",null,"操作",-1)])})),default:p((function(e){return[v("div",R,[m(Z,{onClick:t.addAppAddress},{default:p((function(){return[m(W)]})),_:1},8,["onClick"]),t.formValues["".concat(i.field)].length>1?(f(),d(Z,{key:0,onClick:function(n){return t.removeAppAddress(e.$index)}},{default:p((function(){return[m(ee)]})),_:2},1032,["onClick"])):T("",!0)])]})),_:2},1024)]})),_:2},1032,["data"])):T("",!0),"dialog"===i.type?(f(),g("div",{key:9,style:{width:"100%",border:"1px #DCDFE6 double","border-radius":"4px"},onClick:function(e){return t=i.treeType,n=i.title,r=i.dataType,console.log("open1"),H.value.length=0,z.value=t,U.value=n,O.value=r,F.value="user"===r?[{text:"组织",value:"group"},{text:"用户",value:"user"}]:[{text:"分组",value:"group"},{text:"资源",value:"user"}],h(),void(_.value=!0);var t,n,r}},[v("div",{class:"custom-div",style:{width:"calc(100% - 21px)",float:"left"},placeholder:i.placeholder,contenteditable:"false"},[(f(!0),g(b,null,k(t.formValues["".concat(i.groupCheck)],(function(e){return f(),d(ne,{key:e.id||e.user_group_id,type:"info",effect:"light"},{default:p((function(){return[x(V(e.name||e.user_group_name),1)]})),_:2},1024)})),128)),(f(!0),g(b,null,k(t.formValues["".concat(i.userCheck)],(function(e){return f(),d(ne,{key:e.id||e.user_id,type:"info"},{default:p((function(){return[x(V(e.name||e.user_name),1)]})),_:2},1024)})),128)),(f(!0),g(b,null,k(t.formValues["".concat(i.appGroupCheck)],(function(e){return f(),d(ne,{key:e.id||e.app_group_id,type:"info"},{default:p((function(){return[x(V(e.name||e.app_group_name),1)]})),_:2},1024)})),128)),(f(!0),g(b,null,k(t.formValues["".concat(i.appCheck)],(function(e){return f(),d(ne,{key:e.id||e.app_id,type:"info"},{default:p((function(){return[x(V(e.name||e.app_name),1)]})),_:2},1024)})),128))],8,M),v("span",I,[m(Z,null,{default:p((function(){return[m(re)]})),_:1})])],8,D)):T("",!0),"imgRadio"===i.type?(f(),g(b,{key:10},[v("span",N,[m(A,{modelValue:t.formValues["".concat(i.field)].type,"onUpdate:modelValue":function(e){return t.formValues["".concat(i.field)].type=e}},{default:p((function(){return[m(w,{label:"1",size:"large"},{default:p((function(){return n[15]||(n[15]=[x("图标库选择")])})),_:1,__:[15]}),m(w,{label:"2",size:"large"},{default:p((function(){return n[16]||(n[16]=[x("自定义上传")])})),_:1,__:[16]})]})),_:2},1032,["modelValue","onUpdate:modelValue"])]),v("span",null,["1"===t.formValues["".concat(i.field)].type?(f(),g("img",{key:0,style:{height:"70px"},src:i.localIcon,class:"avatar",onClick:n[1]||(n[1]=function(){return e.iconSelect&&e.iconSelect.apply(e,arguments)})},null,8,G)):T("",!0),"2"===t.formValues["".concat(i.field)].type?(f(),d(ae,{key:1,style:{height:"70px",width:"70px"},class:"avatar-uploader",action:"127.0.0.1:3000","show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload},{default:p((function(){return[t.formValues["".concat(i.field)].iconUrl?(f(),g("img",{key:0,style:{height:"70px",width:"70px"},src:t.formValues["".concat(i.field)].iconUrl,class:"avatar"},null,8,$)):(f(),d(Z,{key:1,class:"avatar-uploader-icon",style:{height:"70px",width:"70px"}},{default:p((function(){return[m(oe,{name:"plus"})]})),_:1}))]})),_:2},1032,["on-success","before-upload"])):T("",!0)])],64)):T("",!0)]})),_:2},1032,["label","rules","style","prop"]))]})),_:2},1040)})),256))]})),_:1}),t.isFooter?(f(),g("span",q,[m(o,{onClick:t.cancel},{default:p((function(){return n[17]||(n[17]=[x("取消")])})),_:1,__:[17]},8,["onClick"]),m(o,{color:"#256EBF",style:{"margin-left":"10px"},type:"primary",onClick:n[2]||(n[2]=function(e){return t.submitForm(j.value)})},{default:p((function(){return n[18]||(n[18]=[x("确定")])})),_:1,__:[18]})])):T("",!0)]})),_:1},8,["model"]),m(ie,{modelValue:_.value,"onUpdate:modelValue":n[4]||(n[4]=function(e){return _.value=e}),title:U.value,width:"600px",draggable:""},{footer:p((function(){return[v("span",K,[m(o,{onClick:n[3]||(n[3]=function(e){return _.value=!1})},{default:p((function(){return n[19]||(n[19]=[x("取消")])})),_:1,__:[19]}),m(o,{type:"primary",onClick:Y},{default:p((function(){return n[20]||(n[20]=[x(" 确定 ")])})),_:1,__:[20]})])]})),default:p((function(){return[m(P,{ref_key:"treeCheck",ref:i},null,512)]})),_:1},8,["modelValue","title"])],64)}}}))}}}))}();
