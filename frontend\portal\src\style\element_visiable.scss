/* 全局滚动条样式 */

::-webkit-scrollbar-track-piece {
    background-color: #f8f8f8;
}

::-webkit-scrollbar {
    width: 9px;
    height: 9px;
}

::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    background-clip: padding-box;
    min-height: 28px;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #bbb;
}

/* 保留一些有用的 CSS 变量 */
:root {
    --primary-color: #4D70FF;
    --menu-item-height: 56px;
}

/* 保留一些通用的布局样式 */
.gva-search-box {
    padding: 24px;
    padding-bottom: 2px;
    background-color: #fff;
    border-radius: 2px;
    margin-bottom: 12px;
}

.gva-form-box {
    padding: 24px;
    background-color: #fff;
    border-radius: 2px;
}

.gva-table-box {
    padding: 24px;
    background-color: #fff;
    border-radius: 2px;
}

/* 通用分页样式 */
.gva-pagination {
    display: flex;
    justify-content: flex-end;

    .btn-prev, .btn-next, .number, .btn-quicknext {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 32px;
        height: 32px;
    }

    .btn-prev {
        padding-right: 6px;
    }

    .btn-next {
        padding-left: 6px;
    }

    .active, .is-active {
        background: var(--primary-color, #4D70FF);
        border-radius: 2px;
        color: #ffffff !important;
    }
}