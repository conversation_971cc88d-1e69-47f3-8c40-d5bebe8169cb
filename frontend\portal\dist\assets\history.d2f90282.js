/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{u as e,a,r as s,b as l,c as t,v as n,D as u,J as o,h as r,o as i,d as v,j as m,w as c,F as d,i as p,f as y,e as g,H as f,m as h,k as b,t as S,P as I,G as q,N as O,Q as w}from"./index.2320e6b9.js";import{J as N}from"./index-browser-esm.c2d3b5c9.js";const k={class:"router-history"},J=["tab"],x=Object.assign({name:"HistoryComponent"},{setup(x){const C=e(),j=a(),V=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),E=s([]),P=s(""),A=s(!1),T=l(),L=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),R=s(0),H=s(0),$=s(!1),_=s(!1),D=s(""),F=t((()=>N("$..defaultRouter[0]",T.userInfo)[0]||"dashboard")),G=()=>{E.value=[{name:F.value,meta:{title:"总览"},query:{},params:{}}],j.push({name:F.value}),A.value=!1,sessionStorage.setItem("historys",JSON.stringify(E.value))},Q=()=>{let e;const a=E.value.findIndex((a=>(V(a)===D.value&&(e=a),V(a)===D.value))),s=E.value.findIndex((e=>V(e)===P.value));E.value.splice(0,a),a>s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(E.value))},U=()=>{let e;const a=E.value.findIndex((a=>(V(a)===D.value&&(e=a),V(a)===D.value))),s=E.value.findIndex((e=>V(e)===P.value));E.value.splice(a+1,E.value.length),a<s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(E.value))},X=()=>{let e;E.value=E.value.filter((a=>(V(a)===D.value&&(e=a),V(a)===D.value))),j.push(e),sessionStorage.setItem("historys",JSON.stringify(E.value))},Y=e=>{if(!E.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const s in e.query)if(e.query[s]!==a.query[s])return!1;for(const s in e.params)if(e.params[s]!==a.params[s])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,E.value.push(a)}window.sessionStorage.setItem("activeValue",V(e))},z=s({});n((()=>E.value),(()=>{z.value={},E.value.forEach((e=>{z.value[V(e)]=e}))}));const B=e=>{const a=z.value[e];j.push({name:a.name,query:a.query,params:a.params})},K=e=>{const a=E.value.findIndex((a=>V(a)===e));V(C)===e&&(1===E.value.length?j.push({name:F.value}):a<E.value.length-1?j.push({name:E.value[a+1].name,query:E.value[a+1].query,params:E.value[a+1].params}):j.push({name:E.value[a-1].name,query:E.value[a-1].query,params:E.value[a-1].params})),E.value.splice(a,1)};n((()=>A.value),(()=>{A.value?document.body.addEventListener("click",(()=>{A.value=!1})):document.body.removeEventListener("click",(()=>{A.value=!1}))})),n((()=>C),((e,a)=>{"Login"!==e.name&&"Reload"!==e.name&&(E.value=E.value.filter((e=>!e.meta.closeTab)),Y(e),sessionStorage.setItem("historys",JSON.stringify(E.value)),P.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),n((()=>E.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(E.value))}),{deep:!0});return(()=>{o.on("closeThisPage",(()=>{K(L(C))})),o.on("closeAllPage",(()=>{G()})),o.on("mobile",(e=>{_.value=e})),o.on("collapse",(e=>{$.value=e}));const e=[{name:F.value,meta:{title:"总览"},query:{},params:{}}];E.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?P.value=window.sessionStorage.getItem("activeValue"):P.value=V(C),Y(C),"true"===window.sessionStorage.getItem("needCloseAll")&&(G(),window.sessionStorage.removeItem("needCloseAll"))})(),u((()=>{o.off("collapse"),o.off("mobile")})),(e,a)=>{const s=r("el-tab-pane"),l=r("el-tabs");return i(),v("div",k,[m(l,{modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),closable:!(1===E.value.length&&e.$route.name===F.value),type:"card",onContextmenu:a[1]||(a[1]=q((e=>(e=>{if(1===E.value.length&&C.name===F.value)return!1;let a="";if(a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a){let s;A.value=!0,s=$.value?54:220,_.value&&(s=0),R.value=e.clientX-s,H.value=e.clientY+10,D.value=a.substring(4)}})(e)),["prevent"])),onTabChange:B,onTabRemove:K},{default:c((()=>[(i(!0),v(d,null,p(E.value,(e=>(i(),y(s,{key:L(e),label:e.meta.title,name:L(e),tab:e,class:"gva-tab"},{label:c((()=>[g("span",{tab:e,style:f({color:P.value===L(e)?h(T).activeColor:"#333"})},[g("i",{class:"dot",style:f({backgroundColor:P.value===L(e)?h(T).activeColor:"#ddd"})},null,4),b(" "+S(h(I)(e.meta.title,e)),1)],12,J)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),O(g("ul",{style:f({left:R.value+"px",top:H.value+"px"}),class:"contextmenu"},[g("li",{onClick:G},"关闭所有"),g("li",{onClick:Q},"关闭左侧"),g("li",{onClick:U},"关闭右侧"),g("li",{onClick:X},"关闭其他")],4),[[w,A.value]])])}}});export{x as default};
