/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{a as e,s as l}from"./system.2850f217.js";import{x as a,B as u,r as d,h as o,o as t,d as m,j as s,w as n,k as i,F as V,g as p,e as v,i as r,f as c,M as b}from"./index.74d1ee23.js";const f={class:"system"},_={class:"gva-btn-list"},y=Object.assign({name:"Config"},{setup(y){const U=u([]),h=d({system:{"iplimit-count":0,"iplimit-time":0},jwt:{},mysql:{},pgsql:{},excel:{},autocode:{},redis:{},qiniu:{},"tencent-cos":{},"aliyun-oss":{},"hua-wei-obs":{},captcha:{},zap:{},local:{},email:{},timer:{detail:{}}}),q=async()=>{const l=await e();0===l.code&&(h.value=l.data.config)};q();const w=()=>{},g=async()=>{0===(await l({config:h.value})).code&&(b({type:"success",message:"配置文件设置成功"}),await q())},k=async()=>{var e;0===(await a({url:"/email/emailTest",method:"post",data:e})).code?(b({type:"success",message:"邮件发送成功"}),await q()):b({type:"error",message:"邮件发送失败"})};return(e,l)=>{const a=o("base-option"),u=o("base-select"),d=o("base-form-item"),b=o("base-input"),y=o("base-checkbox"),q=o("el-input-number"),x=o("el-collapse-item"),z=o("base-button"),M=o("el-collapse"),j=o("base-form");return t(),m("div",f,[s(j,{ref:"form",model:h.value,"label-width":"240px"},{default:n((()=>[s(M,{modelValue:U,"onUpdate:modelValue":l[88]||(l[88]=e=>U=e)},{default:n((()=>[s(x,{title:"系统配置",name:"1"},{default:n((()=>[s(d,{label:"环境值"},{default:n((()=>[s(u,{modelValue:h.value.system.env,"onUpdate:modelValue":l[0]||(l[0]=e=>h.value.system.env=e),style:{width:"100%"}},{default:n((()=>[s(a,{value:"public"}),s(a,{value:"develop"})])),_:1},8,["modelValue"])])),_:1}),s(d,{label:"端口值"},{default:n((()=>[s(b,{modelValue:h.value.system.addr,"onUpdate:modelValue":l[1]||(l[1]=e=>h.value.system.addr=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"数据库类型"},{default:n((()=>[s(u,{modelValue:h.value.system["db-type"],"onUpdate:modelValue":l[2]||(l[2]=e=>h.value.system["db-type"]=e),style:{width:"100%"}},{default:n((()=>[s(a,{value:"mysql"}),s(a,{value:"pgsql"})])),_:1},8,["modelValue"])])),_:1}),s(d,{label:"Oss类型"},{default:n((()=>[s(u,{modelValue:h.value.system["oss-type"],"onUpdate:modelValue":l[3]||(l[3]=e=>h.value.system["oss-type"]=e),style:{width:"100%"}},{default:n((()=>[s(a,{value:"local"}),s(a,{value:"qiniu"}),s(a,{value:"tencent-cos"}),s(a,{value:"aliyun-oss"}),s(a,{value:"huawei-obs"})])),_:1},8,["modelValue"])])),_:1}),s(d,{label:"多点登录拦截"},{default:n((()=>[s(y,{modelValue:h.value.system["use-multipoint"],"onUpdate:modelValue":l[4]||(l[4]=e=>h.value.system["use-multipoint"]=e)},{default:n((()=>l[89]||(l[89]=[i("开启")]))),_:1,__:[89]},8,["modelValue"])])),_:1}),s(d,{label:"开启redis"},{default:n((()=>[s(y,{modelValue:h.value.system["use-redis"],"onUpdate:modelValue":l[5]||(l[5]=e=>h.value.system["use-redis"]=e)},{default:n((()=>l[90]||(l[90]=[i("开启")]))),_:1,__:[90]},8,["modelValue"])])),_:1}),s(d,{label:"限流次数"},{default:n((()=>[s(q,{modelValue:h.value.system["iplimit-count"],"onUpdate:modelValue":l[6]||(l[6]=e=>h.value.system["iplimit-count"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"限流时间"},{default:n((()=>[s(q,{modelValue:h.value.system["iplimit-time"],"onUpdate:modelValue":l[7]||(l[7]=e=>h.value.system["iplimit-time"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1})])),_:1}),s(x,{title:"jwt签名",name:"2"},{default:n((()=>[s(d,{label:"jwt签名"},{default:n((()=>[s(b,{modelValue:h.value.jwt["signing-key"],"onUpdate:modelValue":l[8]||(l[8]=e=>h.value.jwt["signing-key"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"有效期（秒）"},{default:n((()=>[s(b,{modelValue:h.value.jwt["expires-time"],"onUpdate:modelValue":l[9]||(l[9]=e=>h.value.jwt["expires-time"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"缓冲期（秒）"},{default:n((()=>[s(b,{modelValue:h.value.jwt["buffer-time"],"onUpdate:modelValue":l[10]||(l[10]=e=>h.value.jwt["buffer-time"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"签发者"},{default:n((()=>[s(b,{modelValue:h.value.jwt.issuer,"onUpdate:modelValue":l[11]||(l[11]=e=>h.value.jwt.issuer=e)},null,8,["modelValue"])])),_:1})])),_:1}),s(x,{title:"Zap日志配置",name:"3"},{default:n((()=>[s(d,{label:"级别"},{default:n((()=>[s(b,{modelValue:h.value.zap.level,"onUpdate:modelValue":l[12]||(l[12]=e=>h.value.zap.level=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"输出"},{default:n((()=>[s(b,{modelValue:h.value.zap.format,"onUpdate:modelValue":l[13]||(l[13]=e=>h.value.zap.format=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"日志前缀"},{default:n((()=>[s(b,{modelValue:h.value.zap.prefix,"onUpdate:modelValue":l[14]||(l[14]=e=>h.value.zap.prefix=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"日志文件夹"},{default:n((()=>[s(b,{modelValue:h.value.zap.director,"onUpdate:modelValue":l[15]||(l[15]=e=>h.value.zap.director=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"编码级"},{default:n((()=>[s(b,{modelValue:h.value.zap["encode-level"],"onUpdate:modelValue":l[16]||(l[16]=e=>h.value.zap["encode-level"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"栈名"},{default:n((()=>[s(b,{modelValue:h.value.zap["stacktrace-key"],"onUpdate:modelValue":l[17]||(l[17]=e=>h.value.zap["stacktrace-key"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"日志留存时间(默认以天为单位)"},{default:n((()=>[s(b,{modelValue:h.value.zap["max-age"],"onUpdate:modelValue":l[18]||(l[18]=e=>h.value.zap["max-age"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"显示行"},{default:n((()=>[s(y,{modelValue:h.value.zap["show-line"],"onUpdate:modelValue":l[19]||(l[19]=e=>h.value.zap["show-line"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"输出控制台"},{default:n((()=>[s(y,{modelValue:h.value.zap["log-in-console"],"onUpdate:modelValue":l[20]||(l[20]=e=>h.value.zap["log-in-console"]=e)},null,8,["modelValue"])])),_:1})])),_:1}),s(x,{title:"Redis admin数据库配置",name:"4"},{default:n((()=>[s(d,{label:"库"},{default:n((()=>[s(b,{modelValue:h.value.redis.db,"onUpdate:modelValue":l[21]||(l[21]=e=>h.value.redis.db=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"地址"},{default:n((()=>[s(b,{modelValue:h.value.redis.addr,"onUpdate:modelValue":l[22]||(l[22]=e=>h.value.redis.addr=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"密码"},{default:n((()=>[s(b,{modelValue:h.value.redis.password,"onUpdate:modelValue":l[23]||(l[23]=e=>h.value.redis.password=e)},null,8,["modelValue"])])),_:1})])),_:1}),s(x,{title:"邮箱配置",name:"5"},{default:n((()=>[s(d,{label:"接收者邮箱"},{default:n((()=>[s(b,{modelValue:h.value.email.to,"onUpdate:modelValue":l[24]||(l[24]=e=>h.value.email.to=e),placeholder:"可多个，以逗号分隔"},null,8,["modelValue"])])),_:1}),s(d,{label:"端口"},{default:n((()=>[s(b,{modelValue:h.value.email.port,"onUpdate:modelValue":l[25]||(l[25]=e=>h.value.email.port=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"发送者邮箱"},{default:n((()=>[s(b,{modelValue:h.value.email.from,"onUpdate:modelValue":l[26]||(l[26]=e=>h.value.email.from=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"host"},{default:n((()=>[s(b,{modelValue:h.value.email.host,"onUpdate:modelValue":l[27]||(l[27]=e=>h.value.email.host=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"是否为ssl"},{default:n((()=>[s(y,{modelValue:h.value.email["is-ssl"],"onUpdate:modelValue":l[28]||(l[28]=e=>h.value.email["is-ssl"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"secret"},{default:n((()=>[s(b,{modelValue:h.value.email.secret,"onUpdate:modelValue":l[29]||(l[29]=e=>h.value.email.secret=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"测试邮件"},{default:n((()=>[s(z,{onClick:k},{default:n((()=>l[91]||(l[91]=[i("测试邮件")]))),_:1,__:[91]})])),_:1})])),_:1}),s(x,{title:"验证码配置",name:"7"},{default:n((()=>[s(d,{label:"字符长度"},{default:n((()=>[s(b,{modelValue:h.value.captcha["key-long"],"onUpdate:modelValue":l[30]||(l[30]=e=>h.value.captcha["key-long"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"平台宽度"},{default:n((()=>[s(b,{modelValue:h.value.captcha["img-width"],"onUpdate:modelValue":l[31]||(l[31]=e=>h.value.captcha["img-width"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"图片高度"},{default:n((()=>[s(b,{modelValue:h.value.captcha["img-height"],"onUpdate:modelValue":l[32]||(l[32]=e=>h.value.captcha["img-height"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1})])),_:1}),s(x,{title:"数据库配置",name:"9"},{default:n((()=>["mysql"===h.value.system["db-type"]?(t(),m(V,{key:0},[s(d,{label:"用户名"},{default:n((()=>[s(b,{modelValue:h.value.mysql.username,"onUpdate:modelValue":l[33]||(l[33]=e=>h.value.mysql.username=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"密码"},{default:n((()=>[s(b,{modelValue:h.value.mysql.password,"onUpdate:modelValue":l[34]||(l[34]=e=>h.value.mysql.password=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"地址"},{default:n((()=>[s(b,{modelValue:h.value.mysql.path,"onUpdate:modelValue":l[35]||(l[35]=e=>h.value.mysql.path=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"数据库"},{default:n((()=>[s(b,{modelValue:h.value.mysql["db-name"],"onUpdate:modelValue":l[36]||(l[36]=e=>h.value.mysql["db-name"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"maxIdleConns"},{default:n((()=>[s(b,{modelValue:h.value.mysql["max-idle-conns"],"onUpdate:modelValue":l[37]||(l[37]=e=>h.value.mysql["max-idle-conns"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"maxOpenConns"},{default:n((()=>[s(b,{modelValue:h.value.mysql["max-open-conns"],"onUpdate:modelValue":l[38]||(l[38]=e=>h.value.mysql["max-open-conns"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"日志模式"},{default:n((()=>[s(y,{modelValue:h.value.mysql["log-mode"],"onUpdate:modelValue":l[39]||(l[39]=e=>h.value.mysql["log-mode"]=e)},null,8,["modelValue"])])),_:1})],64)):p("",!0),"pgsql"===h.value.system.dbType?(t(),m(V,{key:1},[s(d,{label:"用户名"},{default:n((()=>[s(b,{modelValue:h.value.pgsql.username,"onUpdate:modelValue":l[40]||(l[40]=e=>h.value.pgsql.username=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"密码"},{default:n((()=>[s(b,{modelValue:h.value.pgsql.password,"onUpdate:modelValue":l[41]||(l[41]=e=>h.value.pgsql.password=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"地址"},{default:n((()=>[s(b,{modelValue:h.value.pgsql.path,"onUpdate:modelValue":l[42]||(l[42]=e=>h.value.pgsql.path=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"数据库"},{default:n((()=>[s(b,{modelValue:h.value.pgsql.dbname,"onUpdate:modelValue":l[43]||(l[43]=e=>h.value.pgsql.dbname=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"maxIdleConns"},{default:n((()=>[s(b,{modelValue:h.value.pgsql["max-idle-conns"],"onUpdate:modelValue":l[44]||(l[44]=e=>h.value.pgsql["max-idle-conns"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"maxOpenConns"},{default:n((()=>[s(b,{modelValue:h.value.pgsql["max-open-conns"],"onUpdate:modelValue":l[45]||(l[45]=e=>h.value.pgsql["max-open-conns"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),s(d,{label:"日志模式"},{default:n((()=>[s(y,{modelValue:h.value.pgsql["log-mode"],"onUpdate:modelValue":l[46]||(l[46]=e=>h.value.pgsql["log-mode"]=e)},null,8,["modelValue"])])),_:1})],64)):p("",!0)])),_:1}),s(x,{title:"oss配置",name:"10"},{default:n((()=>["local"===h.value.system["oss-type"]?(t(),m(V,{key:0},[l[92]||(l[92]=v("h2",null,"本地文件配置",-1)),s(d,{label:"本地文件访问路径"},{default:n((()=>[s(b,{modelValue:h.value.local.path,"onUpdate:modelValue":l[47]||(l[47]=e=>h.value.local.path=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"本地文件存储路径"},{default:n((()=>[s(b,{modelValue:h.value.local["store-path"],"onUpdate:modelValue":l[48]||(l[48]=e=>h.value.local["store-path"]=e)},null,8,["modelValue"])])),_:1})],64)):p("",!0),"qiniu"===h.value.system["oss-type"]?(t(),m(V,{key:1},[l[95]||(l[95]=v("h2",null,"qiniu上传配置",-1)),s(d,{label:"存储区域"},{default:n((()=>[s(b,{modelValue:h.value.qiniu.zone,"onUpdate:modelValue":l[49]||(l[49]=e=>h.value.qiniu.zone=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"空间名称"},{default:n((()=>[s(b,{modelValue:h.value.qiniu.bucket,"onUpdate:modelValue":l[50]||(l[50]=e=>h.value.qiniu.bucket=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"CDN加速域名"},{default:n((()=>[s(b,{modelValue:h.value.qiniu["img-path"],"onUpdate:modelValue":l[51]||(l[51]=e=>h.value.qiniu["img-path"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"是否使用https"},{default:n((()=>[s(y,{modelValue:h.value.qiniu["use-https"],"onUpdate:modelValue":l[52]||(l[52]=e=>h.value.qiniu["use-https"]=e)},{default:n((()=>l[93]||(l[93]=[i("开启")]))),_:1,__:[93]},8,["modelValue"])])),_:1}),s(d,{label:"accessKey"},{default:n((()=>[s(b,{modelValue:h.value.qiniu["access-key"],"onUpdate:modelValue":l[53]||(l[53]=e=>h.value.qiniu["access-key"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"secretKey"},{default:n((()=>[s(b,{modelValue:h.value.qiniu["secret-key"],"onUpdate:modelValue":l[54]||(l[54]=e=>h.value.qiniu["secret-key"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"上传是否使用CDN上传加速"},{default:n((()=>[s(y,{modelValue:h.value.qiniu["use-cdn-domains"],"onUpdate:modelValue":l[55]||(l[55]=e=>h.value.qiniu["use-cdn-domains"]=e)},{default:n((()=>l[94]||(l[94]=[i("开启")]))),_:1,__:[94]},8,["modelValue"])])),_:1})],64)):p("",!0),"tencent-cos"===h.value.system["oss-type"]?(t(),m(V,{key:2},[l[96]||(l[96]=v("h2",null,"腾讯云COS上传配置",-1)),s(d,{label:"存储桶名称"},{default:n((()=>[s(b,{modelValue:h.value["tencent-cos"].bucket,"onUpdate:modelValue":l[56]||(l[56]=e=>h.value["tencent-cos"].bucket=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"所属地域"},{default:n((()=>[s(b,{modelValue:h.value["tencent-cos"].region,"onUpdate:modelValue":l[57]||(l[57]=e=>h.value["tencent-cos"].region=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"secretID"},{default:n((()=>[s(b,{modelValue:h.value["tencent-cos"]["secret-id"],"onUpdate:modelValue":l[58]||(l[58]=e=>h.value["tencent-cos"]["secret-id"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"secretKey"},{default:n((()=>[s(b,{modelValue:h.value["tencent-cos"]["secret-key"],"onUpdate:modelValue":l[59]||(l[59]=e=>h.value["tencent-cos"]["secret-key"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"路径前缀"},{default:n((()=>[s(b,{modelValue:h.value["tencent-cos"]["path-prefix"],"onUpdate:modelValue":l[60]||(l[60]=e=>h.value["tencent-cos"]["path-prefix"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"访问域名"},{default:n((()=>[s(b,{modelValue:h.value["tencent-cos"]["base-url"],"onUpdate:modelValue":l[61]||(l[61]=e=>h.value["tencent-cos"]["base-url"]=e)},null,8,["modelValue"])])),_:1})],64)):p("",!0),"aliyun-oss"===h.value.system["oss-type"]?(t(),m(V,{key:3},[l[97]||(l[97]=v("h2",null,"阿里云OSS上传配置",-1)),s(d,{label:"区域"},{default:n((()=>[s(b,{modelValue:h.value["aliyun-oss"].endpoint,"onUpdate:modelValue":l[62]||(l[62]=e=>h.value["aliyun-oss"].endpoint=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"accessKeyId"},{default:n((()=>[s(b,{modelValue:h.value["aliyun-oss"]["access-key-id"],"onUpdate:modelValue":l[63]||(l[63]=e=>h.value["aliyun-oss"]["access-key-id"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"accessKeySecret"},{default:n((()=>[s(b,{modelValue:h.value["aliyun-oss"]["access-key-secret"],"onUpdate:modelValue":l[64]||(l[64]=e=>h.value["aliyun-oss"]["access-key-secret"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"存储桶名称"},{default:n((()=>[s(b,{modelValue:h.value["aliyun-oss"]["bucket-name"],"onUpdate:modelValue":l[65]||(l[65]=e=>h.value["aliyun-oss"]["bucket-name"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"访问域名"},{default:n((()=>[s(b,{modelValue:h.value["aliyun-oss"]["bucket-url"],"onUpdate:modelValue":l[66]||(l[66]=e=>h.value["aliyun-oss"]["bucket-url"]=e)},null,8,["modelValue"])])),_:1})],64)):p("",!0),"huawei-obs"===h.value.system["oss-type"]?(t(),m(V,{key:4},[l[98]||(l[98]=v("h2",null,"华为云Obs上传配置",-1)),s(d,{label:"路径"},{default:n((()=>[s(b,{modelValue:h.value["hua-wei-obs"].path,"onUpdate:modelValue":l[67]||(l[67]=e=>h.value["hua-wei-obs"].path=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"存储桶名称"},{default:n((()=>[s(b,{modelValue:h.value["hua-wei-obs"].bucket,"onUpdate:modelValue":l[68]||(l[68]=e=>h.value["hua-wei-obs"].bucket=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"区域"},{default:n((()=>[s(b,{modelValue:h.value["hua-wei-obs"].endpoint,"onUpdate:modelValue":l[69]||(l[69]=e=>h.value["hua-wei-obs"].endpoint=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"accessKey"},{default:n((()=>[s(b,{modelValue:h.value["hua-wei-obs"]["access-key"],"onUpdate:modelValue":l[70]||(l[70]=e=>h.value["hua-wei-obs"]["access-key"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"secretKey"},{default:n((()=>[s(b,{modelValue:h.value["hua-wei-obs"]["secret-key"],"onUpdate:modelValue":l[71]||(l[71]=e=>h.value["hua-wei-obs"]["secret-key"]=e)},null,8,["modelValue"])])),_:1})],64)):p("",!0)])),_:1}),s(x,{title:"Excel上传配置",name:"11"},{default:n((()=>[s(d,{label:"合成目标地址"},{default:n((()=>[s(b,{modelValue:h.value.excel.dir,"onUpdate:modelValue":l[72]||(l[72]=e=>h.value.excel.dir=e)},null,8,["modelValue"])])),_:1})])),_:1}),s(x,{title:"自动化代码配置",name:"12"},{default:n((()=>[s(d,{label:"是否自动重启(linux)"},{default:n((()=>[s(y,{modelValue:h.value.autocode["transfer-restart"],"onUpdate:modelValue":l[73]||(l[73]=e=>h.value.autocode["transfer-restart"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"root(项目根路径)"},{default:n((()=>[s(b,{modelValue:h.value.autocode.root,"onUpdate:modelValue":l[74]||(l[74]=e=>h.value.autocode.root=e),disabled:""},null,8,["modelValue"])])),_:1}),s(d,{label:"Server(后端代码地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["transfer-restart"],"onUpdate:modelValue":l[75]||(l[75]=e=>h.value.autocode["transfer-restart"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"SApi(后端api文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["server-api"],"onUpdate:modelValue":l[76]||(l[76]=e=>h.value.autocode["server-api"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"SInitialize(后端Initialize文件夹)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["server-initialize"],"onUpdate:modelValue":l[77]||(l[77]=e=>h.value.autocode["server-initialize"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"SModel(后端Model文件地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["server-model"],"onUpdate:modelValue":l[78]||(l[78]=e=>h.value.autocode["server-model"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"SRequest(后端Request文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["server-request"],"onUpdate:modelValue":l[79]||(l[79]=e=>h.value.autocode["server-request"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"SRouter(后端Router文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["server-router"],"onUpdate:modelValue":l[80]||(l[80]=e=>h.value.autocode["server-router"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"SService(后端Service文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["server-service"],"onUpdate:modelValue":l[81]||(l[81]=e=>h.value.autocode["server-service"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"Web(前端文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode.web,"onUpdate:modelValue":l[82]||(l[82]=e=>h.value.autocode.web=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"WApi(后端WApi文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["web-api"],"onUpdate:modelValue":l[83]||(l[83]=e=>h.value.autocode["web-api"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"WForm(后端WForm文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["web-form"],"onUpdate:modelValue":l[84]||(l[84]=e=>h.value.autocode["web-form"]=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"WTable(后端WTable文件夹地址)"},{default:n((()=>[s(b,{modelValue:h.value.autocode["web-table"],"onUpdate:modelValue":l[85]||(l[85]=e=>h.value.autocode["web-table"]=e)},null,8,["modelValue"])])),_:1})])),_:1}),s(x,{title:"Timer(定时任务)",name:"13"},{default:n((()=>[s(d,{label:"Start（是否启用）"},{default:n((()=>[s(y,{modelValue:h.value.timer.start,"onUpdate:modelValue":l[86]||(l[86]=e=>h.value.timer.start=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"Spec(CRON表达式)"},{default:n((()=>[s(b,{modelValue:h.value.timer.spec,"onUpdate:modelValue":l[87]||(l[87]=e=>h.value.timer.spec=e)},null,8,["modelValue"])])),_:1}),(t(!0),m(V,null,r(h.value.timer.detail,((e,l)=>(t(),m(V,null,[(t(!0),m(V,null,r(e,((a,u)=>(t(),m("div",{key:u},[(t(),c(d,{key:l+u,label:u},{default:n((()=>[s(b,{modelValue:e[u],"onUpdate:modelValue":l=>e[u]=l},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["label"]))])))),128))],64)))),256))])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["model"]),v("div",_,[s(z,{type:"primary",size:"small",onClick:g},{default:n((()=>l[99]||(l[99]=[i("立即更新")]))),_:1,__:[99]}),s(z,{type:"primary",size:"small",onClick:w},{default:n((()=>l[100]||(l[100]=[i("重启服务（开发中）")]))),_:1,__:[100]})])])}}});export{y as default};
