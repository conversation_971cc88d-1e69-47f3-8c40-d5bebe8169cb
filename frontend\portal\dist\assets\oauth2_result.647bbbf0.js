/*! 
 Build based on gin-vue-admin 
 Time : 1749553755000 */
import{_ as e,u as a,a as s,b as t,r as n,p as r,c as o,E as i,M as l,h as c,o as u,d,j as p,w as m,e as v,t as y,g as h}from"./index.1aedb960.js";import g from"./secondaryAuth.d9168e16.js";import"./verifyCode.672c2c28.js";const f={class:"oauth-result-container"},w={key:0,class:"loading-box"},_={class:"message"},b={key:1,class:"secondary-auth-container"},q=e(Object.assign({name:"OAuth2Result"},{setup(e){const q=a(),S=s(),I=t(),T=n("正在处理认证信息..."),C=n(!1),j=n(""),P=n(""),x=n(""),L=n(""),k=n(""),A=n("phone"),E=n(!0);r("userName",x),r("last_id",L),r("isSecondary",n(!0)),r("contactType",A),r("hasContactInfo",E);const O=o((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===A.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===A.value}])),R=async e=>{T.value="认证成功，正在跳转...";let a=decodeURIComponent(k.value||"/");if(e.clientParams){const s=new URLSearchParams;s.set("type",e.clientParams.type),e.clientParams.wp&&s.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+s.toString()}window.location.href=a},N=()=>{C.value=!1,T.value="已取消验证，正在返回登录页...";const e=new URLSearchParams;q.query.idp_id&&e.set("idp_id",q.query.idp_id),q.query.redirect_url&&e.set("redirect",q.query.redirect_url),"client"===q.query.type&&(e.set("type","client"),q.query.wp&&e.set("wp",q.query.wp));const a=e.toString()?`/login?${e.toString()}`:"/login";S.push(a)};return i((async()=>{var e;try{const{auth_token:e,idp_id:a,redirect_url:s,login_type:t,auth_error:n}=q.query;if("is_test"===t)return T.value="测试完成",l.success("测试完成，正在关闭窗口..."),void setTimeout((()=>window.close()),2e3);if(n)throw new Error(n);if(!e)throw new Error("缺少有效的认证令牌");localStorage.setItem("loginType",t);const r={clientId:"client_portal",grantType:"implicit",redirect_uri:s,idpId:a,authWeb:{authWebToken:e}};T.value="验证登录信息...";const o=await I.LoginIn(r,t,a);if("object"==typeof o&&null!==o&&o.isSecondary){T.value="需要进行二次认证...",A.value=o.contactType||"phone",E.value=o.hasContactInfo||!1;const e=o.secondary&&Array.isArray(o.secondary)&&o.secondary.length>0?o.secondary[0].id:a;j.value=o.uniqKey,P.value=o.user_id,x.value=o.userName,L.value=e,k.value=s||"/",C.value=!0}else{if(!0!==o)throw new Error("登录处理失败");T.value="认证成功，正在跳转...",k.value=s||"/"}}catch(a){console.error("处理错误:",a);let t="认证失败，请稍后再试";try{if(null==(e=a.response)?void 0:e.data)t=a.response.data.error_description||a.response.data.message||a.response.data.error||t;else if(a.message){let e=a.message;if(e.includes("msg=登录失败")){const a=e.split("msg=登录失败:")[1]||e;e=a.trim()}if(e.includes("{"))try{const a=e.indexOf("{"),s=e.substring(a),n=JSON.parse(s);n&&n.message&&(t=n.message)}catch(s){}if("认证失败，请稍后再试"===t&&e.includes("message =")){const a=/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/,s=e.match(a);s&&s[1]&&(t=s[1].trim())}if("认证失败，请稍后再试"===t&&e.includes("reason =")){const a=/reason\s*=\s*(\w+)/,s=e.match(a);if(s&&s[1]){t=`认证失败: ${s[1].replace(/_/g," ").toLowerCase()}`}}"认证失败，请稍后再试"===t&&(t=e.split("\n")[0],t.length>100&&(t=t.substring(0,97)+"..."))}}catch(s){console.error("处理错误消息时发生异常:",s)}T.value=t,l.error(t),setTimeout((()=>{S.push({name:"Login"})}),2e3)}})),(e,a)=>{const s=c("base-icon"),t=c("el-icon");return u(),d("div",f,[C.value?h("",!0):(u(),d("div",w,[p(t,{class:"loading-icon",size:40},{default:m((()=>[p(s,{name:"loading"})])),_:1}),v("div",_,y(T.value),1)])),C.value?(u(),d("div",b,[p(g,{"auth-info":{uniqKey:j.value,contactType:A.value,hasContactInfo:E.value},"auth-id":L.value,"user-name":x.value,"last-id":L.value,"auth-methods":O.value,onVerificationSuccess:R,onCancel:N},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])])):h("",!0)])}}}),[["__scopeId","data-v-7cf917e2"]]);export{q as default};
