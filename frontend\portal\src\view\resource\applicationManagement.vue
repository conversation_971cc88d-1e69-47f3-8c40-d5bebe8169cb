<template>
  <div class="application">
    <base-container style="height: 100%">
      <base-aside width="200px" style="min-height: calc(100vh - 200px)">
        <div style="height: 35px">
          <span class="menu-label">应用分组</span>
          <base-button class="organize-but" :icon="FolderAdd" @click="add('group')"/>
        </div>
        <div style="height: 95%;display: block;width: 200px;overflow-y: scroll">
          <DirectoryTree
              class="tree-panel"
              :load-operate="loadOperate"
              :load-node="loadNode"
              :tree-props="treeProps"
              :edit="edit"
          />
        </div>
        <!--        <CustomTree @append="append"/>-->
      </base-aside>
      <!--      用户表格-->
      <base-main>
        <div class="header">
          <base-button :icon="Plus" @click="add('app')">新增应用</base-button>
          <base-button :icon="RefreshRight" @click="getAppList">刷新</base-button>
          <base-input
              v-model="searchApp"
              class="w-50 m-2 organize-search"
              placeholder="Search"
              :suffix-icon="Search"
              @change="getAppList"
          />
        </div>
        <CustomTable
            :table-data="tableData"
            v-bind="tableConfig"
        >
          <template #app_sites="scope">
            <div style="text-align: center">
          <span
              v-for="(item,index) in scope.row.app_sites"
              :key="item.id"
              style="
                    color: rgba(0, 0, 0, 0.701960784313725);
                    font-size: 12px;
                    font-weight: 400;
                    font-style: normal;
                    "
          >
                {{ item.protocol ? item.protocol + '://' + item.address + ':' + item.port : '' }}<br>
              </span>
            </div>
          </template>
          <template #group_name="scope">
            <div style="text-align: center">
              <span>{{ scope.row.group_name || '默认分组' }}</span>
            </div>
          </template>
          <template #app_status="scope">
            <div style="text-align: center">
              <el-icon v-if="scope.row.app_status" style="color: #52c41a">
                <SuccessFilled/>
              </el-icon>
              <el-icon v-else>
                <Remove/>
              </el-icon>
            </div>
          </template>
        </CustomTable>
      </base-main>
    </base-container>
    <!--弹窗-->
    <el-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="title"
        width="30%"
        custom-class="custom-dialog"
    >
      <CustomFrom
          v-bind="formOptions"
          :form-options="formValues"
          :cancel="cancel"
          :submit-form="submitForm"
          :add-app-address="addAppAddress"
          :remove-app-address="removeAppAddress"
      />
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ApplicationManagement',
}
</script>
<script setup>
import {
  FolderAdd,
  Plus,
  RefreshRight,
  Search,
} from '@element-plus/icons-vue'
import CustomFrom from '@/components/customFrom/customFrom.vue'
import { provide, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CustomTable from '@/components/customTable.vue'
import DirectoryTree from '@/components/directoryTree/directoryTree.vue'
import { groupFormItemOptions } from './groupFormOptions'
import { appFormItemOptions } from '@/view/resource/appFormOptions'
import {
  getResourceGroup,
  createGroup,
  updateGroup,
  delGroup,
  createApp,
  updateApp,
  delApp,
  getApplication, getApplicationDetails,
} from '@/api/resource'
import { getAgentsList } from '@/api/agents'

const treeRef = ref('')
const dialogVisible = ref(false)
const type = ref('group')
const title = ref('新增应用')
const searchApp = ref('')
const loadOperate = ref(false)
const group = ref([])
const treeProps = {
  label: 'group_name',
  children: 'zones',
  isLeaf: 'leaf',
  isRoot: 'root',
}
const propList = [
  {
    prop: 'app_name',
    label: '应用名称',
    slotName: 'app_name',
  },
  {
    prop: 'app_desc',
    label: '描述',
    slotName: 'app_desc',
  },
  {
    prop: 'group_name',
    label: '所属分组',
    slotName: 'group_name',
  },
  {
    prop: 'app_sites',
    label: '服务器地址',
    slotName: 'app_sites',
  },
  {
    prop: 'app_status',
    label: '状态',
    slotName: 'app_status',
  },
]
const tableConfig = {
  propList,
  isSelectColumn: true,
  isOperationColumn: true,
}
const tableData = ref([])
// 分页
const currentPage = ref(1)
const pageSize = ref(100)
const total = ref(0)

provide('treeRef', treeRef)
provide('currentPage', currentPage)
provide('pageSize', pageSize)
provide('total', total)

const getAppList = async() => {
  const query = {
    offset: (currentPage.value - 1) * pageSize.value,
    limit: pageSize.value,
    search: searchApp.value,
  }
  if (treeRef.value) query.group_id = Number(treeRef.value)
  const res = await getApplication(query)
  console.log('getAppList')
  console.log(res)
  if (res.data.code === 0) {
    tableData.value = res.data.data.rows
    total.value = res.data.data.total_rows
    // total.value = res.data
  }
}
getAppList()
provide('getTableData', getAppList)

const getGroupList = async() => {
  const res = await getResourceGroup('corp_id')
  if (res.status === 200 && res.data.code === 0) {
    group.value = res.data.data
  }
}

getGroupList()

const options = ref([])
const loadNode = async(node, resolve) => {
  if (node.level === 0) {
    const res = await getResourceGroup('corp_id')
    if (res.status === 200 && res.data.code === 0) {
      options.value = res.data.data
      return resolve(res.data.data)
    }
  }
  return resolve([])
}

const formOptions = {
  formItems: [],
  formValues: reactive({}),
}

const formValues = reactive({})

// const handleClose = (done) => {
//   ElMessageBox.confirm('是否取消新增?')
//       .then(() => {
//         done()
//       })
//       .catch(() => {
//         // catch error
//       })
// }

const getSDPOptions = async() => {
  const query = {
    limit: pageSize.value,
    offset: (currentPage.value - 1) * pageSize.value,
  }
  const res = await getAgentsList(query)
  console.log('getAgentsList')
  console.log(res)
  if (res.data.code === 0) {
    return res.data.data.rows
  }
  return []
}

const operation = ref('')
const add = async(lx) => {
  if (lx === 'group') {
    title.value = '新增应用分组'
    operation.value = 'group'
    formOptions.formItems = groupFormItemOptions()
  } else {
    operation.value = 'app'
    title.value = '新增应用'
    type.value = 'app'
    const sdpOptions = ref(await getSDPOptions())
    formOptions.formItems = appFormItemOptions(options, sdpOptions)
    formOptions.formValues.app_sites = [{
      protocol: '',
      address: '',
      port: '',
    }]
    formOptions.formValues.app_status = '1'
    formOptions.formValues.app_icon = {
      type: '1',
    }
  }
  dialogVisible.value = true
}

const cancel = () => {
  initFormLabelAlign()
  dialogVisible.value = false
}

const initFormLabelAlign = () => {
  const keys = Object.keys(formOptions.formValues)
  const obj = {}
  keys.forEach((item) => {
    obj[item] = ''
  })
  Object.assign(formOptions.formValues, obj)
}

const setFormValues = (data) => {
  console.log('setFormValues')
  console.log(data)
  formOptions.formValues.id = data.id
  formOptions.formValues.app_name = data.app_name
  formOptions.formValues.app_desc = data.app_desc
  formOptions.formValues.app_icon = data.app_icon
  formOptions.formValues.app_sites = [{
    protocol: '',
    serverAddress: '',
    port: '',
  }]
  if (data.app_sites) {
    formOptions.formValues.app_sites = data.app_sites
  }
  formOptions.formValues.app_status = data.app_status.toString()
  formOptions.formValues.corp_id = data.corp_id
  formOptions.formValues.group_id = data.group_id.toString()
  formOptions.formValues.web_url = data.web_url
  formOptions.formValues.sdp = data.bind_se.map(i => i.appliance_id)[0]
  formOptions.formValues.app_icon = {
    type: '1',
  }
}

const handleEdit = async(index, row) => {
  console.log('handleEdit')
  console.log(index, row)
  title.value = '编辑应用'
  operation.value = 'updateApp'
  const sdpOptions = ref(await getSDPOptions())
  const application = await getApplicationDetails({ id: row.id })
  console.log('application')
  console.log(application)
  formOptions.formItems = appFormItemOptions(options, sdpOptions)
  setFormValues(application.data.data)
  dialogVisible.value = true
}

const cascaderKey = ref(1)
provide('cascaderKey', cascaderKey)
const submitForm = async(formEl) => {
  console.log('submitForm')
  console.log(formOptions.formValues)
  await formEl.validate(async(valid, fields) => {
    if (!valid) {
      ElMessage({
        showClose: true,
        message: '字段校验失败，请检查！',
        type: 'error',
      })
      return ''
    }
    let group = ''
    let app = ''
    if (operation.value === 'app' || operation.value === 'updateApp') {
      formOptions.formValues.app_sites[0].port = Number(formOptions.formValues.app_sites[0].port)
      let se_app = []
      const sdpOptions = ref(await getSDPOptions())
      for (const index in sdpOptions.value) {
        if (formOptions.formValues.sdp.includes(sdpOptions.value[index].appliance_id)) {
          se_app.push({
            app_type: sdpOptions.value[index].display_app_type,
            connector_id: sdpOptions.value[index].display_app_type === 2 ? sdpOptions.value[index].appliance_id : '',
            se_id: sdpOptions.value[index].display_app_type !== 2 ? sdpOptions.value[index].appliance_id : '',
          })
        }
      }
      app = {
        app_name: formOptions.formValues.app_name,
        app_desc: formOptions.formValues.app_desc,
        app_status: Number(formOptions.formValues.app_status),
        group_id: Number(formOptions.formValues.group_id),
        corp_id: 1,
        app_sites: formOptions.formValues.app_sites,
        se_app: se_app,
        web_entry: formOptions.formValues.web_entry,
      }
    } else {
      group = {
        group_name: formOptions.formValues.name,
      }
    }
    let res = ''
    switch (operation.value) {
      case 'group':
        res = await createGroup(group)
        console.log(res)
        break
      case 'updateGroup':
        group.id = formOptions.formValues.id
        res = await updateGroup(group)
        break
      case 'app':
        console.log('createApp')
        res = await createApp(app)
        console.log(res)
        break
      case 'updateApp':
        app.id = formOptions.formValues.id
        console.log('updateApp')
        console.log(formOptions.formValues.id)
        console.log(app)
        res = await updateApp(app)
        console.log(res)
        break
    }
    if (res.data.code < 0) {
      ElMessage({
        showClose: true,
        message: res.data.msg,
        type: 'error',
      })
    } else {
      ElMessage({
        type: 'success',
        message: '添加应用成功',
      })
    }
    initFormLabelAlign()
    await getAppList()
    ++cascaderKey.value
    dialogVisible.value = false
  })
}

const addAppAddress = () => {
  formOptions.formValues.app_sites.push({
    protocol: '',
    serverAddress: '',
    port: '',
  })
}

const removeAppAddress = (index) => {
  formOptions.formValues.app_sites.splice(index, 1)
}

const edit = async(node, data) => {
  console.log('edit')
  console.log(data)
  console.log(data.id)
  title.value = '修改分组'
  operation.value = 'updateGroup'
  formOptions.formItems = groupFormItemOptions()
  formOptions.formValues.id = data.id
  formOptions.formValues.name = data.group_name
  dialogVisible.value = true
}

const open = (id) => {
  ElMessageBox.confirm(
      '删除分组以后将无法恢复，确认删除分组？',
      '删除应用分组',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
  )
      .then(async() => {
        console.log(id)
        const res = await delGroup({ id: Number(id) })
        console.log(res)
        if (res.data.code === 0) {
          ElMessage({
            type: 'success',
            message: '删除成功',
          })
          ++cascaderKey.value
        } else {
          ElMessage({
            type: 'error',
            message: '删除失败',
          })
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
}

const handleDelete = (index, row) => {
  console.log('handleDelete')
  console.log(row)
  ElMessageBox.confirm(
      '<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个应用吗？</strong><br><strong>删除后用户将无法访问该应用，请谨慎操作。</strong>',
      '删除应用',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
  ).then(async() => {
    const res = await delApp({ id: row.id })
    console.log(res)
    if (res.data.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      await getAppList()
    } else {
      ElMessage({
        type: 'error',
        message: '删除失败',
      })
    }
    initFormLabelAlign()
  })
      .catch(() => {
        initFormLabelAlign()
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
}

provide('open', open)
provide('handleEdit', handleEdit)
provide('handleDelete', handleDelete)
</script>
<style scoped>
.common-layout {
  min-height: calc(100vh - 200px);
}

.menu-label {
  width: 81%;
  float: left;
  font-size: 12px;
  padding-top: 8px;
  color: rgba(51, 51, 51, 0.701960784313725);
  padding-left: 10px;
}

.organize-but {
  height: 28px;
  width: 28px;
  padding: 6px 6px;
  border-width: 0px;
  position: absolute;
  font-size: 12px;
  color: rgba(51, 51, 51, 0.701960784313725);
}

.organize-search {
  width: 200px;
  float: right;
  height: 30px;
}
</style>
<style>
/* 用来设置当前页面element全局table 选中某行时的背景色*/
.el-table__body tr.current-row > td {
  background-color: rgba(206, 233, 253, 1) !important;
  /* color: #f19944; */ /* 设置文字颜色，可以选择不设置 */
}

/* 用来设置当前页面element全局table 鼠标移入某行时的背景色*/
.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: rgba(206, 233, 253, 1);
  /* color: #f19944; */ /* 设置文字颜色，可以选择不设置 */
}

.app-table-style .el-table__cell .cell {
  /*color: #256EBF !important;*/
  font-size: 12px;
}

</style>

<style lang="scss">
.tree-panel {
  width: 200px;
  overflow: auto;
  position: absolute;
  left: 0;

  .el-tree {
    //width: fit-content;
    //min-width: -webkit-fill-available;
    .el-tree__empty-text {
      left: 38%;
      top: 45%;
    }

    .el-tree-node__children {
      overflow: initial;
    }

    .is-current > .el-tree-node__content {
      background-color: #FFFFFF;
    }

    .el-tree-node {
      &:focus {
        .el-tree-node__content {
          &:hover {
            background-color: rgba(0, 156, 255, 0.1);
          }
        }
      }
    }

    .el-tree-node__content {
      &:hover {
        background-color: rgba(0, 156, 255, 0.2);
      }
    }
  }
}
</style>
