/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{f as a,h as e,r as t}from"./autoCode.a94c47f9.js";import{_ as l,a as i,r as n,h as s,o,d as r,e as c,j as d,w as u,k as p,t as m,m as f,f as g,P as y,M as _}from"./index.74d1ee23.js";import{f as b}from"./format.8e96a47e.js";import"./date.23f5a973.js";import"./dictionary.aa4d2fe8.js";import"./sysDictionary.2a470765.js";const w={class:"gva-table-box"},v={class:"gva-btn-list"},h={class:"gva-pagination"},k=l(Object.assign({name:"AutoCodeAdmin"},{setup(l){const k=i(),C=n(1),x=n(0),z=n(10),T=n([]),B=a=>{z.value=a,N()},j=a=>{C.value=a,N()},N=async()=>{const e=await a({page:C.value,pageSize:z.value});0===e.code&&(T.value=e.data.list,x.value=e.data.total,C.value=e.data.page,z.value=e.data.pageSize)};N();const D=async(a,e)=>{e?y.confirm("此操作将删除自动创建的文件和api（会删除表！！！）, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{y.confirm("此操作将删除自动创建的文件和api（会删除表！！！）, 请继续确认！！！","会删除表",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{y.confirm("此操作将删除自动创建的文件和api（会删除表！！！）, 请继续确认！！！","会删除表",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await t({id:Number(a.ID),deleteTable:e})).code&&(_.success("回滚成功"),N())}))}))})):y.confirm("此操作将删除自动创建的文件和api, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await t({id:Number(a.ID),deleteTable:e})).code&&(_.success("回滚成功"),N())}))},I=a=>{a?k.push({name:"autoCodeEdit",params:{id:a.ID}}):k.push({name:"autoCode"})};return(a,t)=>{const l=s("base-button"),i=s("el-table-column"),n=s("el-tag"),k=s("el-table"),A=s("el-pagination");return o(),r("div",null,[c("div",w,[c("div",v,[d(l,{size:"small",type:"primary",icon:"plus",onClick:t[0]||(t[0]=a=>I(null))},{default:u((()=>t[1]||(t[1]=[p("新增")]))),_:1,__:[1]})]),d(k,{data:T.value},{default:u((()=>[d(i,{type:"selection",width:"55"}),d(i,{align:"left",label:"id",width:"60",prop:"ID"}),d(i,{align:"left",label:"日期",width:"180"},{default:u((a=>[p(m(f(b)(a.row.CreatedAt)),1)])),_:1}),d(i,{align:"left",label:"结构体名","min-width":"150",prop:"structName"}),d(i,{align:"left",label:"结构体描述","min-width":"150",prop:"structCNName"}),d(i,{align:"left",label:"表名称","min-width":"150",prop:"tableName"}),d(i,{align:"left",label:"回滚标记","min-width":"150",prop:"flag"},{default:u((a=>[a.row.flag?(o(),g(n,{key:0,type:"danger",size:"small",effect:"dark"},{default:u((()=>t[2]||(t[2]=[p(" 已回滚 ")]))),_:1,__:[2]})):(o(),g(n,{key:1,size:"small",type:"success",effect:"dark"},{default:u((()=>t[3]||(t[3]=[p(" 未回滚 ")]))),_:1,__:[3]}))])),_:1}),d(i,{align:"left",label:"操作","min-width":"240"},{default:u((a=>[c("div",null,[d(l,{size:"small",type:"primary",link:"",disabled:1===a.row.flag,onClick:e=>D(a.row,!0)},{default:u((()=>t[4]||(t[4]=[p("回滚(删表)")]))),_:2,__:[4]},1032,["disabled","onClick"]),d(l,{size:"small",type:"primary",link:"",disabled:1===a.row.flag,onClick:e=>D(a.row,!1)},{default:u((()=>t[5]||(t[5]=[p("回滚(不删表)")]))),_:2,__:[5]},1032,["disabled","onClick"]),d(l,{size:"small",type:"primary",link:"",onClick:e=>I(a.row)},{default:u((()=>t[6]||(t[6]=[p("复用")]))),_:2,__:[6]},1032,["onClick"]),d(l,{size:"small",type:"primary",link:"",onClick:t=>(async a=>{y.confirm("此操作将删除本历史, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await e({id:Number(a.ID)})).code&&(_.success("删除成功"),N())}))})(a.row)},{default:u((()=>t[7]||(t[7]=[p("删除")]))),_:2,__:[7]},1032,["onClick"])])])),_:1})])),_:1},8,["data"]),c("div",h,[d(A,{"current-page":C.value,"page-size":z.value,"page-sizes":[10,30,50,100],total:x.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:j,onSizeChange:B},null,8,["current-page","page-size","total"])])])])}}}),[["__scopeId","data-v-3907acab"]]);export{k as default};
