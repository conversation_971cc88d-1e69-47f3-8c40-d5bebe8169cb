/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
System.register(["./index-legacy.04f34b53.js"],(function(e,n){"use strict";var t,u,r,i,o,l,a,c,f,d,s;return{setters:[function(e){t=e.R,u=e.h,r=e.o,i=e.d,o=e.j,l=e.w,a=e.T,c=e.f,f=e.V,d=e.m,s=e.x}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[o(v,null,{default:l((function(e){var t=e.Component;return[o(a,{mode:"out-in",name:"el-fade-in-linear"},{default:l((function(){return[(r(),c(f,{include:d(n).keepAliveRouters},[(r(),c(s(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
