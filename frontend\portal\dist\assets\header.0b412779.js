/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as e,h as t,o as i,d as o,e as s,f as a,j as n,w as r,t as d,k as l}from"./index.4982c0f9.js";import{_ as c}from"./ASD.492c8837.js";const u=""+new URL("avator.bd83723a.png",import.meta.url).href,h={class:"layout-header"},g={id:"u-header-menu",class:"right-wrapper"},m={id:"u-avator",ref:"countMenu"},w={class:"user-info"},p={class:"user-name"};const f=e({name:"ClientHeader",data:()=>({drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duanyc",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1}),computed:{isAccess:()=>!1},watch:{userId(e,t){console.log("用户id变动",e,t),console.debug("用户id变动")}},mounted(){},beforeDestroy(){},methods:{minimizeWnd(){agentApi.minimizeWnd()},maximizeWndOrNot(){this.isMaxWindow?(agentApi.normalnizeWnd(),this.isMaxWindow=!1):(agentApi.maximizeWnd(),this.isMaxWindow=!0)},dropdownVisiHandle(){},closeWnd(){if(TestQtModule("UIPlatform_Window","HideWnd"))EventBus.$emit("closeAssui"),this.$nextTick((()=>{agentApi.hideWend()}));else try{this.$ipcSend("UIPlatform_Window","TerminateWnd")}catch(e){this.$message.error("操作失败，因小助手版本低。请重启小助手或电脑以升级。")}},async setHandle(e){if("changeLange"===e){const e=this.$i18n.locale;setLang("zh"===e?"en":"zh")}else"changeMode"===e&&this.changeMode()},userMenuHandle(e,t={}){switch(this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="注销后会取消自动身份认证功能，您确定要注销吗？",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0}"lougOut"!==e&&"switchNetwork"!==e&&(this.drawer=!0)},async logoutHandle(){if(this.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),1===this.logoutType){try{let e;this.isSsoAuth()&&(e=await ssoLogout(_.get(this.clientInfo,"accessStatus.lastAuthType")));const t=await proxyApi.cutoffDevice({device_id:_.get(this.clientInfo,"detail.DeviceID",0),remark:"LogOut"});if(0!==parseInt(_.get(t,"errcode")))return _.get(t,"errmsg")||this.$message.error("注销失败！可能是因为网络不可用，或者服务器繁忙。"),void loading.destory();commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),await agentApi.logOut({IsCredibleDevice:_.get(this.clientInfo,"detail.IsTrustDev","0")}),this.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,this.isDot1xMode&&this.setClientInfo(_.merge({},this.clientInfo,{basic:{IsOnline:0}})),this.setAuthInfo({...this.authInfo,basic:{}}),this.setClientInfo({...this.clientInfo,accessStatus:{}});const i=(new Date).getTime();this.$router.push({name:"message",params:{forceTo:!0},query:{t:i}}),_.isString(e)&&""!==e&&(console.log("logoutUrl:".logoutUrl),agentApi.windowOpenUrl(e))}catch(e){console.error("退出登录错误",e)}loading.destory()}},getCountMenuWidth(){const e=this.isZtpUser?44:0,t=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0);this.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(t)+e})},hdEventHandle(e){if("router"===e.type)this.userMenuHandle(e.val)},closeDrawer(){this.deviceInnerBack=!1},changeVisible(e){this.drawer=e}}},[["render",function(e,f,I,v,y,_){const W=t("el-dropdown-item"),k=t("el-dropdown-menu"),C=t("el-dropdown"),x=t("FullScreen"),S=t("el-icon"),b=t("Minus"),D=t("Close");return i(),o("div",h,[f[3]||(f[3]=s("div",{class:"header-logo"},[a("如果图片加载失败就隐藏"),s("img",{src:c,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),f[4]||(f[4]=s("div",{id:"u-electron-drag"},null,-1)),s("ul",g,[s("li",m,[n(C,{id:"ui-headNav-header-div-account_info",placement:"bottom-start",onCommand:_.userMenuHandle,onVisibleChange:_.dropdownVisiHandle},{default:r((()=>[s("div",w,[f[0]||(f[0]=s("div",{class:"user-face"},[s("img",{src:u,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),s("span",p,d(y.username),1)]),n(k,{slot:"dropdown",class:"header-count-menu"},{default:r((()=>[n(W,{id:"ui-headNav-header-li-cancel_account",command:"lougOut"},{default:r((()=>f[1]||(f[1]=[s("i",{class:"iconfont icon-zhuxiao"},null,-1),l("注销 ")]))),_:1,__:[1]})])),_:1})])),_:1},8,["onCommand","onVisibleChange"])],512),f[2]||(f[2]=s("div",{class:"user-divider"},null,-1)),n(S,{class:"window-operate",onClick:_.maximizeWndOrNot},{default:r((()=>[n(x)])),_:1},8,["onClick"]),n(S,{class:"window-operate",onClick:_.minimizeWnd},{default:r((()=>[n(b)])),_:1},8,["onClick"]),n(S,{class:"window-operate",style:{"margin-right":"16px"},onClick:_.closeWnd},{default:r((()=>[n(D)])),_:1},8,["onClick"])])])}],["__scopeId","data-v-30488b75"],["__file","D:/asec-platform/frontend/portal/src/view/client/header.vue"]]);export{f as default};
