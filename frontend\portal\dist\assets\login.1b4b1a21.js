/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as o,o as t,g as e}from"./index.4982c0f9.js";import i from"./index.4332345e.js";import"./localLogin.6a42ce4f.js";import"./wechat.23adad4b.js";import"./feishu.e6ba81d5.js";import"./dingtalk.3aa78e6e.js";import"./oauth2.bbc060fc.js";import"./iconfont.2d75af05.js";import"./sms.829ddc94.js";import"./secondaryAuth.95bf0c1b.js";import"./verifyCode.0c196ff0.js";const s=o(Object.assign({name:"ClientNewLogin"},{setup:o=>(o,s)=>(t(),e(i))}),[["__file","D:/asec-platform/frontend/portal/src/view/client/login.vue"]]);export{s as default};
