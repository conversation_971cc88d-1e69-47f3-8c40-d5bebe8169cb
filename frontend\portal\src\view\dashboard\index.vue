<template xmlns="http://www.w3.org/1999/html">
  <div class="page">
    <div class="gva-card-box" style="padding-bottom: 0px">
      <div style="width: 100%;margin-bottom: 5px">
        <base-row :gutter="12" style="margin-right:-2px;padding-bottom: 0px">
          <base-col :span="6" style="background-color: whitesmoke;overflow: hidden;padding-right:2px">
            <base-card shadow="never" class="alarm">
              <div style="margin-bottom: 20px">
                <span style="color:#252631;font-size: 16px;font-weight: 500">业务总数</span>
              </div>
              <div>
                <div style="float: left;">
                  <span
                      style="margin-right: 13px;float: left;width: 44px;height: 44px;background: #8DAEF1;text-align: center;line-height: 44px;border-radius: 100px;display: block"
                  >
                    <div class="iconfont icon-yewuzongshu"></div>
                  </span>
                  <span style="display: block;margin-top: 5px;float: left;color: #252631;font-size: 26px"
                  >{{ counts.ApplicationCount.total_count }}</span>
                </div>
                <div style="float: right;">
                  <span>
                    <i class="iconfont icon-lianjie"></i>
                    <span style="font-size: 14px;color: #252631;opacity: 0.5">可用数：</span>
                    <span style="font-size: 14px;color: #252631;">{{ counts.ApplicationCount.online_count }}</span>
                  </span>
                  <br>
                  <br>
                  <span>
                    <i class="iconfont icon-duankai"></i>
                    <span style="font-size: 14px;color: #252631;opacity: 0.5">不可用：</span>
                    <span> {{ counts.ApplicationCount.total_count - counts.ApplicationCount.online_count }}</span>
                  </span>
                </div>
              </div>
            </base-card>
          </base-col>
          <base-col :span="6" style="background-color: whitesmoke;overflow: hidden;padding-right:2px">
            <base-card shadow="never" class="alarm">
              <div style="margin-bottom: 20px">
                <span style="color:#252631;font-size: 16px;font-weight: 500">用户总数</span>
              </div>
              <div>
                <div style="width: 120px;float: left;">
                  <span
                      style="margin-right: 13px;float: left;width: 44px;height: 44px;background: #71BDDF;text-align: center;line-height: 44px;border-radius: 100px;display: block"
                  >
                    <div class="iconfont icon-yonghuzongshu"></div>
                  </span>
                  <span style="display: block;margin-top: 5px;color: #252631;font-size: 26px"
                  >24</span>
                </div>
                <div style="float: right;">
                  <span>
                    <base-avatar :size="8"
                               style="background:#6DD230;opacity: 1;margin-right: 8px"
                    ></base-avatar>
                    <span style="font-size: 14px;color: #252631;opacity: 0.5">在线用户：</span>
                    <span style="font-size: 14px;color: #252631;">15</span>
                  </span>
                  <br>
                  <br>
                  <span>
                    <base-avatar :size="8"
                               style="background:#252631;opacity: 0.3;margin-right: 8px"
                    ></base-avatar>
                    <span style="font-size: 14px;color: #252631;opacity: 0.5">离线用户：</span>
                    <span> 35</span>
                  </span>
                </div>
              </div>
            </base-card>
          </base-col>
          <base-col :span="6" style="background-color: whitesmoke;overflow: hidden;padding-right:2px">
            <base-card shadow="never" class="alarm">
              <div style="margin-bottom: 20px">
                <span style="color:#252631;font-size: 16px;font-weight: 500">累计访问数</span>
              </div>
              <div>
                <div style="width: 140px;float: left">
                  <span
                      style="margin-right: 13px;float: left;width: 44px;height: 44px;background: #8AB05D;text-align: center;line-height: 44px;border-radius: 100px;display: block"
                  >
                    <div class="iconfont icon-leijifangwen"></div>
                  </span>
                  <span style="display: block;margin-top: 5px;color: #252631;font-size: 26px"
                  >{{ counts.AccessCount.total_count }}</span>
                </div>
                <div style="float: right;padding-top: 6px;padding-bottom: 11px">
                  <span>
                    <span style="font-size: 14px;color: #252631;opacity: 0.5">今日访问：</span>
                    <span style="font-size: 14px;color: #252631;">{{
                        counts.AccessCount.today_count
                      }}</span>
                  </span>
                  <br>
                  <br>
                  <span>
                  </span>
                </div>
              </div>
            </base-card>
          </base-col>
          <base-col :span="6" style="background-color: whitesmoke;overflow: hidden;padding-right:2px">
            <base-card shadow="never" class="alarm device-alarm">
              <div style="margin-bottom: 20px">
                <span style="color:#252631;font-size: 16px;font-weight: 500">接入设备</span>
              </div>
              <div>
                <base-row>
                  <base-col :span="8" v-for="item in counts.AgentCount">
                    <div class="device">
                      <svg class="icon" aria-hidden="true">
                        <use v-if="item.plat_type === 'windows'" xlink:href="#icon-windows"></use>
                        <use v-if="item.plat_type === 'mac'" xlink:href="#icon-pingguo"></use>
                        <use v-if="item.plat_type === 'linux'" xlink:href="#icon-linux"></use>
                      </svg>
                      <br>
                      <span style="font-size: 20px;">{{ item.count }}</span>
                    </div>
                  </base-col>
                </base-row>
              </div>
            </base-card>
          </base-col>
        </base-row>
      </div>
    </div>
    <div class="gva-card-box" style="height: 420px;padding-right: 7px">
      <base-row>
        <base-col :span="12" style="width: calc(50% - 4px);max-width: calc(50% - 4px)">
          <!-- 用户访问趋势 -->
          <div class="access-trends" style="float: left;width: 100%;background-color: #FFFFFF;border-radius: 4px">
            <div style="height: 20px;line-height: 20px;padding: 10px 19px 10px 19px;border-bottom: #EBEBEB 1px solid">
              <span style="font-size: 18px;font-weight: 500;font-family: HarmonyOS_Medium">应用访问趋势</span>
              <span style="margin-left: 15px;font-size: 10px;color: #252631;opacity: 0.5;float: right">最近{{
                  day
                }}天</span>
              <el-link :underline="false" style="float: right;font-size: 12px;color: #2972C8">查看全部</el-link>
            </div>
            <span style="float: right;font-size: 8px;color: #252631;opacity: 0.5;margin-top: 6px;margin-right: 19px"
            >{{ lineTitle }}</span>
            <div v-if="vray>0" style="padding-left: 10px;padding-bottom: 20px;height: 133px" ref="container"></div>
            <el-table
                :key="tableKey"
                height="210px"
                ref="tableRef"
                :data="trendData"
                highlight-current-row
                style="width: calc(100% - 22px);margin: 10px 11px 0px"
                @row-click="handleCurrentChange"
            >
              <el-table-column property="app_name" label="访问应用">
                <template #default="scope">
                  <base-avatar
                      style="height: 20px;width:20px;font-size: 10px;margin-right: 7px"
                      :style="{background:getColos(scope.$index)}"
                  > {{
                      scope.row.app_name.slice(0, 1)
                    }}
                  </base-avatar>
                  <span>{{ scope.row.app_name }}</span>
                </template>
              </el-table-column>
              <el-table-column property="total" label="访问次数">
                <template #default="scope">
                  <el-progress
                      :percentage="Math.round(scope.row.total/trendData.reduce((pre,cur)=>{return pre+cur.total},0)*100)"
                  >
                    <base-button text>{{ scope.row.total }}</base-button>
                  </el-progress>
                </template>
              </el-table-column>
              <el-table-column property="data" label="访问趋势">
                <template #header>
                  <div style="text-align: center;font-size: 12px;font-family: HarmonyOS_Medium">访问趋势</div>
                </template>
                <template #default="scope">
                  <component
                      :is="tinyLineComponent"
                      v-if="scope.row.data.length>0"
                      :data="scope.row.data.map((item=>item.count))"
                  >
                  </component>
                </template>
              </el-table-column>
              <el-table-column prop="operate" label="操作" width="50">
                <template #default="scope">
                  <el-link :underline="false" style="font-size: 12px;color: #2972C8">详情</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </base-col>
        <base-col :span="12" style="width: calc(50% + 4px);max-width: calc(50% + 4px)">
          <!-- 数据外发趋势 -->
          <div class="access-trends"
               style="margin-left: 7px;float: left;width: calc(100% - 7px);background-color: #FFFFFF;border-radius: 4px"
          >
            <div style="height: 20px;line-height: 20px;padding: 10px 19px 10px 19px;border-bottom: #EBEBEB 1px solid">
              <span style="font-size: 18px;font-weight: 500">数据外发趋势</span>
              <span style="margin-left: 15px;font-size: 10px;color: #252631;opacity: 0.5;float: right">最近{{
                  day
                }}天</span>
            </div>
            <span style="float: right;font-size: 8px;color: #252631;opacity: 0.5;margin-top: 6px;margin-right: 19px"
            >qq</span>
            <div style="padding-left: 10px;padding-bottom: 20px;height: 133px" ref="qcContainer"></div>
            <el-table
                height="210px"
                ref="tableRef"
                :data="qcData"
                highlight-current-row
                style="width: calc(100% - 22px);margin: 10px 11px 0px"
            >
              <el-table-column property="name" label="外发通道">
                <template #default="scope">
                  <base-avatar
                      style="height: 20px;width:20px;font-size: 10px;margin-right: 7px"
                      :style="{background:getColos(scope.$index)}"
                  > {{
                      scope.row.name.slice(0, 1)
                    }}
                  </base-avatar>
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column property="count" label="时间数量">
                <template #default="scope">
                  <el-progress
                      :percentage="Math.round(scope.row.count/qcData.reduce((pre,cur)=>{return pre+cur.count},0)*100)"
                  >
                    <base-button text>{{ scope.row.count }}</base-button>
                  </el-progress>
                </template>
              </el-table-column>
              <el-table-column property="fileSize" label="文件大小">
              </el-table-column>
              <el-table-column prop="operate" label="操作" width="50">
                <template #default="scope">
                  <el-link :underline="false" style="font-size: 12px;color: #2972C8">详情</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </base-col>
      </base-row>
    </div>
    <!-- 关注用户 -->
    <div class="footer"
         style="margin: 0px 10px 0px 10px;padding:0px;height: 100%;width: calc(100% - 30px)"
    >
      <base-row :gutter="20">
        <base-col :span="8" style="overflow: hidden;">
          <div class="grid-content">
            <div class="header">
              <span style="font-size: 18px;font-weight: 500;color: #252631">关注用户</span>
              <span style="float: right;margin-left: 15px;font-size: 10px;color: #252631;opacity: 0.5"
              >最近{{ day }}天</span>
              <el-link :underline="false" style="float: right;font-size: 12px;color: #2972C8">查看所有关注用户</el-link>
            </div>
            <div class="collect">
              <div style="width: calc(50% - 10px);float: left;text-align: center;">
                <div style="margin-top: 11px;font-size: 16px;font-weight: 500;color: #252631;margin-bottom: 14px">
                  离职中
                </div>
                <div style="height: 42px">
                  <svg style="font-size: 30px;margin-right: 10px" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-fengxian-hong"></use>
                  </svg>
                  <span style="font-size: 30px;font-weight: 600;color: #252631">6</span>
                </div>
                <div>
                  <span style="font-size: 10px;color: #252631;opacity: 0.5">严重风险用户</span>
                  <el-link :underline="false" style="color: #2972C8;font-size: 10px;margin-left: 5px">查看</el-link>
                </div>
              </div>
              <base-divider style="float: left;height: 40px;margin-top: 42px" direction="vertical"/>
              <div style="width: calc(50% - 10px);float: right;text-align: center;">
                <div style="margin-top: 11px;font-size: 16px;font-weight: 500;color: #252631;margin-bottom: 14px">
                  离职倾向
                </div>
                <div style="height: 42px">
                  <svg style="font-size: 30px;margin-right: 10px" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-fengxian-hong"></use>
                  </svg>
                  <span style="font-size: 30px;font-weight: 600;color: #252631">6</span>
                </div>
                <div>
                  <span style="font-size: 10px;color: #252631;opacity: 0.5">严重风险用户</span>
                  <el-link :underline="false" style="color: #2972C8;font-size: 10px;margin-left: 5px">查看</el-link>
                </div>
              </div>
            </div>
            <div class="content">
              <el-table :data="followUsersData" height="230px" style="width: 100%">
                <el-table-column prop="type" label="关注用户类型" width="180">
                  <template #default="scope">
                    <span style="font-size: 12px;color: #252631">{{ scope.row.type }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="count" label="用户数量" width="180">
                  <template #default="scope">
                    <el-link :underline="false" style="color: #2972C8;font-size: 12px">{{ scope.row.count }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column prop="eventLevel" label="严重事件">
                  <template #default="scope">
                    <svg style="font-size: 14px;margin-right: 10px" class="icon" aria-hidden="true">
                      <use xlink:href="#icon-fengxian-hong"></use>
                    </svg>
                    <el-link :underline="false" style="color: #2972C8;font-size: 12px">{{ scope.row.eventLevel }}
                    </el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </base-col>
        <base-col :span="16">
          <div class="grid-content">
            <div class="header">
              <span style="font-size: 18px;font-weight: 500;color: #252631">风险用户排行</span>
              <span style="float: right;margin-left: 15px;font-size: 10px;color: #252631;opacity: 0.5"
              >最近{{ day }}天</span>
              <el-link :underline="false" style="float: right;font-size: 12px;color: #2972C8">查看所有用户</el-link>
            </div>
            <div class="content">
              <el-table :data="riskUsersData" height="333px" style="width: 100%;margin-top: 10px">
                <el-table-column prop="user" label="用户">
                  <template #default="scope">
                    <span>{{ scope.row.user }}</span>
                    <br>
                    <el-tag style="margin-right: 5px" v-for="item in scope.row.type" class="ml-2" type="info">{{
                        item
                      }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="severity" label="严重事件">
                  <template #header>
                    <svg style="font-size: 14px;margin-right: 10px" class="icon" aria-hidden="true">
                      <use xlink:href="#icon-fengxian-hong"></use>
                    </svg>
                    <span style="font-family: HarmonyOS_Medium">严重事件</span>
                  </template>
                  <template #default="scope">
                    <svg style="font-size: 14px;margin-right: 10px" class="icon" aria-hidden="true">
                      <use xlink:href="#icon-fengxian-hong"></use>
                    </svg>
                    <el-link :underline="false" style="margin-right: 5px;color: #2972C8;font-size: 12px">
                      {{ scope.row.severity.count }}
                    </el-link>
                    <span style="font-size: 10px;color: #252631;opacity: 0.5">{{ scope.row.severity.size }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="highRisk" label="高危事件">
                  <template #header>
                    <svg style="font-size: 14px;margin-right: 10px" class="icon" aria-hidden="true">
                      <use xlink:href="#icon-fengxian-cheng"></use>
                    </svg>
                    <span style="font-family: HarmonyOS_Medium">高危事件</span>
                  </template>
                  <template #default="scope">
                    <svg style="font-size: 14px;margin-right: 10px" class="icon" aria-hidden="true">
                      <use xlink:href="#icon-fengxian-cheng"></use>
                    </svg>
                    <el-link :underline="false" style="margin-right: 5px;color: #2972C8;font-size: 12px">
                      {{ scope.row.highRisk.count }}
                    </el-link>
                    <span style="font-size: 10px;color: #252631;opacity: 0.5">{{ scope.row.highRisk.size }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="riskDetails" label="风险概述">
                  <template #default="scope">
                    <el-tag style="margin-right: 15px" v-for="item in scope.row.riskDetails" class="ml-2" type="info">
                      {{ item }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </base-col>
      </base-row>
    </div>
  </div>
</template>

<script setup>
import '../../assets/ali/iconfont.css'
import '@/assets/ali/iconfont'
import { computed, getCurrentInstance, inject, watch } from 'vue'
import { Line } from '@antv/g2plot'
import { onMounted, ref } from 'vue'
import { getDashboardCounts, getDashboardTrend } from '@/api/dashboard'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import TinyLine from './tinyLine/tinyLine.vue'

const colos = ['#71BDDF', '#8DAEF1', '#8BB05D']
const tableRef = ref()
const lineTitle = ref()
const vray = ref(0)
const container = ref(null)
const qcContainer = ref(null)
const instance = getCurrentInstance()
const day = inject('day')
const data = ref([])
const tableKey = ref(0)
let line
const counts = ref({
  ApplicationCount: {
    online_count: 0,
    total_count: 0,
  },
  AccessCount: {
    today_count: 0,
    total_count: 0,
  },
  AgentCount: [{
    plat_type: 'windows',
    count: 0,
  }, {
    plat_type: 'mac',
    count: 0,
  }, {
    plat_type: 'linux',
    count: 0,
  }],
})

const getColos = (index) => {
  if (index < 3) {
    return colos[index]
  }
  return colos[index - 3 * Math.floor(index / 3)]
}

const trendData = ref([])

const dataLoading = async() => {
  console.log('dataLoading')
  // 获取count数据
  const query = {
    days: Number(day.value),
    top: 10,
  }
  console.log(query)
  const count = await getDashboardCounts(query)
  if (count.data.code !== 0) {
    ElMessage({
      showClose: true,
      message: count.data.msg,
      type: 'error',
    })
  }
  console.log(count)
  counts.value = count.data.data
  const trend = await getDashboardTrend(query)
  trendData.value = trend.data.data
  tableRef.value.setCurrentRow(trendData.value[0])
  lineTitle.value = trendData.value[0].app_name
  console.log(trend)
  // line.value.render()
  ++vray.value
  initChart(trendData.value[0])
}

dataLoading()

watch(day, async(newData, oldData) => {
  console.log('wach1')
  const query = {
    days: Number(day.value),
    top: 10,
  }
  const trend = await getDashboardTrend(query)
  trendData.value = trend.data.data
  tableRef.value.setCurrentRow(trendData.value[0])
  console.log(trend)
  ++tableKey.value
  handleCurrentChange(trendData.value[0])
})

const initChart = (row) => {
  data.value = row.data
  line = new Line(container.value, {
    data: data.value,
    xField: 'date',
    yField: 'count',
    step: 5,
    yAxis: {
      tickCount: 3,
      label: {
        style: {
          fill: '#ADADAD',
          fillOpacity: 0.7,
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#F8F8F8',
          },
        },
      },
    },
    xAxis: {
      tickCount: 7,
      label: {
        style: {
          fill: '#ADADAD',
          fillOpacity: 0.7,
        },
      },
      line: {
        style: {
          stroke: '#F8F8F8',
        },
      },
    },
    color: '#2972C8',
    lineStyle: {
      lineWidth: 1,
    },
    point: {
      size: 3,
      shape: 'circle',
      style: {
        fill: '#FFFFFF',
        stroke: '#2972C8',
        lineWidth: 1,
      },
    },
    tooltip: { showMarkers: false },
    theme: {
      styleSheet: {
        brandColor: '#2972C8',
      },
    },
    interactions: [{ type: 'marker-active' }],
  })
  line.render()
}

const initQcChart = (row) => {
  const data = [
    {
      date: '2022-12-13',
      count: 200,
    },
    {
      date: '2022-12-14',
      count: 195,
    },
    {
      date: '2022-12-15',
      count: 250,
    },
    {
      date: '2022-12-16',
      count: 300,
    },
    {
      date: '2022-12-17',
      count: 250,
    },
    {
      date: '2022-12-18',
      count: 100,
    },
    {
      date: '2022-12-19',
      count: 50,
    },
    {
      date: '2022-12-20',
      count: 70,
    },
    {
      date: '2022-12-21',
      count: 120,
    },
    {
      date: '2022-12-22',
      count: 189,
    },
    {
      date: '2022-12-23',
      count: 300,
    },
    {
      date: '2022-12-24',
      count: 0,
    },
  ]
  line = new Line(qcContainer.value, {
    data: data,
    xField: 'date',
    yField: 'count',
    yAxis: {
      tickCount: 3,
      label: {
        style: {
          fill: '#ADADAD',
          fillOpacity: 0.7,
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#F8F8F8',
          },
        },
      },
    },
    xAxis: {
      tickCount: 7,
      label: {
        style: {
          fill: '#ADADAD',
          fillOpacity: 0.7,
        },
      },
      line: {
        style: {
          stroke: '#F8F8F8',
        },
      },
    },
    color: '#2972C8',
    lineStyle: {
      lineWidth: 1,
    },
    point: {
      size: 3,
      shape: 'circle',
      style: {
        fill: '#FFFFFF',
        stroke: '#2972C8',
        lineWidth: 1,
      },
    },
    tooltip: { showMarkers: false },
    theme: {
      styleSheet: {
        brandColor: '#2972C8',
      },
    },
    interactions: [{ type: 'marker-active' }],
  })
  line.render()
}

const qcData = [
  { name: 'qq', icon: '', count: 87, fileSize: '60KB' },
  { name: '邮箱', icon: '', count: 69, fileSize: '60KB' },
  { name: '微信', icon: '', count: 50, fileSize: '60KB' },
  { name: '飞书', icon: '', count: 32, fileSize: '60KB' },
]

const followUsersData = [
  { type: '新员工', count: 62, eventLevel: 48 },
  { type: '外包员工', count: 97, eventLevel: 37 },
  { type: '供应商', count: 13, eventLevel: 12 },
  { type: '供应商', count: 8, eventLevel: 5 },
]

const riskUsersData = [
  {
    user: '<EMAIL>',
    type: ['离职倾向', '新入职'],
    severity: { count: 32, size: '50KB' },
    highRisk: { count: 67, size: '50KB' },
    riskDetails: ['百度网盘上传', 'Github上传', '微信发送文件', '源代码'],
  },
  {
    user: '<EMAIL>',
    type: ['离职倾向', '新入职'],
    severity: { count: 25, size: '50KB' },
    highRisk: { count: 20, size: '50KB' },
    riskDetails: ['微信发送文件', '客户资料'],
  },
  {
    user: '<EMAIL>',
    type: ['离职倾向', '新入职'],
    severity: { count: 13, size: '50KB' },
    highRisk: { count: 23, size: '50KB' },
    riskDetails: ['百度网盘上传', 'Github上传', '微信发送文件'],
  },
  {
    user: '<EMAIL>',
    type: ['离职倾向', '新入职'],
    severity: { count: 4, size: '50KB' },
    highRisk: { count: 8, size: '50KB' },
    riskDetails: ['百度网盘上传', 'Github上传', '微信发送文件', '源代码'],
  },
]

const handleCurrentChange = (row) => {
  tableRef.value.setCurrentRow(row)
  // line.destroy()
  // initChart(row)
  console.log('handleCurrentChange')
  lineTitle.value = row.app_name
  line.changeData(row.data)
}

const tinyLineComponent = computed(() => {
  return TinyLine
})

// onMounted(dataLoading)
onMounted(() => {
  dataLoading()
  initQcChart()
})

const tableData = [
  {
    type: '离职倾向',
    source: '智能识别',
    count: '5',
    riskEvent: '20',
  },
  {
    type: '新入职',
    count: '4',
    riskEvent: '20',
  },
  {
    type: '供应商',
    count: '3',
    riskEvent: '20',
  },
  {
    type: '财务部门',
    count: '2',
    riskEvent: '20',
  },
  {
    type: '核心技术骨干',
    count: '5',
    riskEvent: '20',
  },
]

const value = ref('')

const options = [
  {
    value: '1',
    label: '严重风险',
  },
  {
    value: '2',
    label: '中度风险',
  },
  {
    value: '3',
    label: '低度风险',
  },
]

const route = useRoute()
const query = route.query
const keycloak = inject('$keycloak')
const clineData = {
  action: 0,
  msg: {
    token: keycloak.token,
    refreshToken: keycloak.refreshToken,
    realm: keycloak.realm,
  },
  platform: document.location.hostname
}
if (query.type === 'client') {
  const port = query.wp || 50001
  const websocket = ref({})
  const wsUrl = ref(`ws://127.0.0.1:${port}`)

  const initWebSocket = () => {
    websocket.value = new WebSocket(wsUrl.value)
    websocket.value.onopen = () => {
      console.log('socket连接成功')
      sendMessage(JSON.stringify(clineData))
    }
    websocket.value.onmessage = (e) => {
      console.log(e)
      closeWebSocket()
    }
    websocket.value.onerror = (e) => {
      console.log('socket连接错误')
      console.log(e)
      window.location.href = `asecagent://?web=${JSON.stringify(clineData)}`
    }
  }
  // 发送消息
  const sendMessage = (msg) => {
    console.log(msg, '0')
    websocket.value.send(msg)
  }
  // 关闭链接（在页面销毁时可销毁链接）
  const closeWebSocket = () => {
    console.log('socket断开链接')
    websocket.value.close()
  }
  console.log(`asecagent://?web=${JSON.stringify(clineData)}`)
  initWebSocket()
}
</script>
<script>
export default {
  name: 'Dashboard',
}
</script>

<style lang="scss" scoped>
@mixin flex-center {
  display: flex;
  align-items: center;
}

.page {
  background: #f0f2f5;
  padding: 0;

  .gva-card-box {
    padding: 10px 10px;

    .el-row {
      padding: 0px;
    }

    & + .gva-card-box {
      padding-top: 0px;
      padding-bottom: 0px;
    }
  }

  .gva-card {
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 2px;
    height: auto;
    padding: 26px 30px;
    overflow: hidden;
    box-shadow: 0 0 7px 1px rgba(0, 0, 0, 0.03);
  }

  .gva-top-card {
    height: 260px;
    @include flex-center;
    justify-content: space-between;
    color: #777;

    &-left {
      height: 100%;
      display: flex;
      flex-direction: column;

      &-title {
        font-size: 22px;
        color: #343844;
      }

      &-dot {
        font-size: 16px;
        color: #6B7687;
        margin-top: 24px;
      }

      &-rows {
        // margin-top: 15px;
        margin-top: 18px;
        color: #6B7687;
        width: 600px;
        align-items: center;
      }

      &-item {
        + .gva-top-card-left-item {
          margin-top: 24px;
        }

        margin-top: 14px;
      }
    }

    &-right {
      height: 600px;
      width: 600px;
      margin-top: 28px;
    }
  }

  ::v-deep(.estate div) {
    height: 20px;
    color: #333333;
  }

  ::v-deep(.estate input) {
    height: 20px;
    color: #333333;
    font-size: 12px;
  }

  ::v-deep(.el-card__header) {
    padding: 0;
    border-bottom: none;
  }

  .card-header {
    padding-bottom: 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .quick-entrance-title {
    height: 30px;
    font-size: 22px;
    color: #333;
    width: 100%;
    border-bottom: 1px solid #eee;
  }

  .quick-entrance-items {
    @include flex-center;
    justify-content: center;
    text-align: center;
    color: #333;

    .quick-entrance-item {
      padding: 16px 28px;
      margin-top: -16px;
      margin-bottom: -16px;
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        box-shadow: 0px 0px 7px 0px rgba(217, 217, 217, 0.55);
      }

      cursor: pointer;
      height: auto;
      text-align: center;
      // align-items: center;
      &-icon {
        width: 50px;
        height: 50px !important;
        border-radius: 8px;
        @include flex-center;
        justify-content: center;
        margin: 0 auto;

        i {
          font-size: 24px;
        }
      }

      p {
        margin-top: 10px;
      }
    }
  }

  .echart-box {
    padding: 14px;
  }
}

.dashboard-icon {
  font-size: 20px;
  color: rgb(85, 160, 248);
  width: 30px;
  height: 30px;
  margin-right: 10px;
  @include flex-center;
}

.flex-center {
  @include flex-center;
}

//小屏幕不显示右侧，将登录框居中
@media (max-width: 750px) {
  .gva-card {
    padding: 20px 10px !important;

    .gva-top-card {
      height: auto;

      &-left {
        &-title {
          font-size: 20px !important;
        }

        &-rows {
          margin-top: 15px;
          align-items: center;
        }
      }

      &-right {
        display: none;
      }
    }

    .gva-middle-card {
      &-item {
        line-height: 20px;
      }
    }

    .dashboard-icon {
      font-size: 18px;
    }
  }
}

.alarm {
  padding: 16px 25px 25px 19px;
  border: 0px;
}
</style>
<style>
.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background-color: #256DBF;
  color: #FFFFFF;
  font-size: 12px !important;
  font-weight: 700;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  color: #080808;
  font-size: 12px !important;
  font-weight: 700;
  background-color: #F2F2F2;
}

.admin-box .el-table th.is-leaf {
  background: #FAFAFA;
  font-size: 12px;
  color: #252631;
  height: 40px;
}

.admin-box .el-table th .cell {
  font-size: 12px;
  font-family: HarmonyOS_Medium;
  /*font-weight: 700;*/
}

.el-table th, .el-table tr {
  background: #FFFFFF;
}
</style>
<style lang="scss">
.alarm {
  .el-card__body {
    padding: 0 !important;

    .icon-lianjie {
      font-size: 10px;
      color: #70D335;
      margin-right: 6px;
    }

    .icon-duankai {
      font-size: 10px;
      color: #D33434;
      margin-right: 6px;
    }

    .icon-yewuzongshu {
      font-size: 22px;
      color: #FFFFFF;
    }

    .icon-yonghuzongshu {
      font-size: 22px;
      color: #FFFFFF;
    }

    .icon-leijifangwen {
      font-size: 22px;
      color: #FFFFFF;
    }

    .el-row {
      padding: 0px !important;

      .device {
        text-align: center;

        .icon {
          margin-bottom: 15px;
          font-size: 22px;
        }
      }
    }
  }
}

.access-trends {
  .el-table__header-wrapper {
    height: 42px !important;

    .el-table__header {
      height: 42px !important;

      thead {
        height: 42px !important;

        tr {
          height: 42px !important;

          th {
            height: 42px !important;
            padding: 0px !important;

            .cell {
              font-size: 12px;
              color: #252631;
            }
          }
        }
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      .el-table__row {
        height: 42px !important;

        .el-table__cell {
          height: 42px !important;
          padding: 0px !important;

          .el-progress {
            flex-direction: row-reverse;

            .el-progress__text {
              min-width: 25px;

              button {
                padding: 8px 0px 8px 0px !important;
              }
            }
          }
        }
      }
    }
  }
}

.el-progress-bar__inner {
  background: #2972C8;
}

.device-alarm {
  padding: 16px 25px 15px 19px !important;
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.grid-content {
  height: 390px;
  background: #FFFFFF;
  border-radius: 4px;

  .header {
    border-bottom: 1px solid #EBEBEB;
    height: 46px;
    line-height: 46px;
    padding: 0px 19px 0px 19px;
  }

  .collect {
    height: 113px;
  }

  .content {
    padding: 0px 10px;
  }
}

.footer {
  .el-row {
    padding-top: 0px;

    .is-guttered {
      padding-right: 0px !important;

      .el-table__cell {
        padding: 0px;
        height: 42px;
      }
    }
  }

}
</style>
