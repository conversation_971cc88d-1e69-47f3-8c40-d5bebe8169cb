/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import e from"./menuItem.1131fe90.js";import t from"./asyncSubmenu.a28c0585.js";import{c as o,h as n,o as r,f as s,w as u,d as l,F as a,i,g as f,z as m}from"./index.74d1ee23.js";const c=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(c){const d=c,h=o((()=>d.routerInfo.children&&d.routerInfo.children.filter((e=>!e.hidden)).length?t:e));return(e,t)=>{const o=n("AsideComponent");return c.routerInfo.hidden?f("",!0):(r(),s(m(h.value),{key:0,"is-collapse":c.isCollapse,theme:c.theme,"router-info":c.routerInfo},{default:u((()=>[c.routerInfo.children&&c.routerInfo.children.length?(r(!0),l(a,{key:0},i(c.routerInfo.children,(e=>(r(),s(o,{key:e.name,"is-collapse":!1,"router-info":e,theme:c.theme},null,8,["router-info","theme"])))),128)):f("",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}});export{c as default};
