<template>
  <div>
    <div style="width: 55%;float: left;background-color: #FFFFFF;border: 1px #DCDCDC solid">
      <div style="height: 36px;line-height: 36px;background-color: #FFFFFF;border-bottom: 1px #DCDCDC solid">
        <span
            style="margin-left: 10px;font-size: 16px;font-weight: 700;color: rgba(0, 0, 0, 0.701960784313725)"
        >风险用户排行</span>
        <span style="margin-right: 10px;color: #AAAAAA;font-size: 12px;float: right">最近90天</span>
      </div>
      <div>
        <el-table class="overview-table" :data="tableData" stripe height="430" style="width: 100%; text-align: center">
          <el-table-column prop="type" width="180">
            <template #header>
              <div style="text-align: center">
                <span style="font-size: 12px;font-weight: 700">用户</span>
              </div>
            </template>
            <template #default="scope">
              <span style="font-size: 12px;font-weight: 700">{{ scope.row.user }} </span>
            </template>
          </el-table-column>
          <el-table-column prop="count" width="150">
            <template #header>
              <div style="text-align: center">
                <el-icon style="color: #D9001B;">
                  <WarningFilled/>
                </el-icon>
                <span style="font-size: 12px;font-weight: 700">
                  严重事件</span>
              </div>
            </template>
            <template #default="scope">
              <div style="margin-left: 35px;height: 30px;float: left;margin-top: 5px">
                <el-icon style="color: #D9001B; margin-right: 5px;">
                  <WarningFilled/>
                </el-icon>
              </div>
              <div style="float: left;height: 20px">
                <div style="height: 15px">
                  <span style="font-size: 12px;margin-left: 15px">{{ scope.row.seriousEvent.count }}</span>
                </div>
                <span style="font-size: 12px;color: #C0C0C0">{{ scope.row.seriousEvent.size }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="riskEvent" label="高危事件" width="150">
            <template #header>
              <div style="text-align: center">
                <el-icon style="color: #F59A23;margin-right: 5px">
                  <WarningFilled/>
                </el-icon>
                <span style="font-size: 12px;font-weight: 700">
                  高危事件</span>
              </div>
            </template>
            <template #default="scope">
              <div style="margin-left: 35px;height: 30px;float: left;margin-top: 5px">
                <el-icon style="color: #F59A23; margin-right: 5px;">
                  <WarningFilled/>
                </el-icon>
              </div>
              <div style="float: left;height: 20px">
                <div style="height: 15px">
                  <span style="font-size: 12px;margin-left: 15px">{{ scope.row.seriousEvent.count }}</span>
                </div>
                <span style="font-size: 12px;color: #C0C0C0">{{ scope.row.seriousEvent.size }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="风险概述" width="">
            <template #header>
              <div style="text-align: center">
                <span style="font-size: 12px;font-weight: 700">风险概述</span>
              </div>
            </template>
            <template #default="scope">
              <el-tag
                  style="margin-right: 5px"
                  key="i"
                  v-for="item in scope.row.overview"
                  class="mx-1"
                  type="info"
                  color="#AAAAAA"
                  effect="dark"
              >
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <el-link
            style="margin-right: 10px;color: #44BFF2;font-size: 12px;font-weight:700;float: right;margin-top: 5px;margin-bottom: 5px"
            type="primary"
        >查看所有风险用户
        </el-link>
      </div>
    </div>
    <div style="float: left;width: 43%;margin-left: 10px;background-color: #FFFFFF;border: 1px #DCDCDC solid">
      <div
          style="width: 100%;background-color: #FFFFFF;height: 36px;line-height: 36px;border-bottom: 1px #DCDCDC solid"
      >
        <span style="margin-left: 10px;font-size: 16px;font-weight: 700;color: rgba(0, 0, 0, 0.701960784313725)"
        >关注用户</span>
        <span style="margin-right: 10px;font-size: 12px;color: #AAAAAA;float: right">最近90天</span>
      </div>
      <div
          style="height: 160px;width: calc(100% - 20px);margin: 10px 10px;background-color: #F6F6F6;border-radius: 5px"
      >
        <div style="float: left;margin: 0px 10px;padding: 10px 50px;width: 30%">
          <div>
            <span style="font-size: 16px;font-weight: 700;color: rgba(0, 0, 0, 0.701960784313725)">离职中</span>
          </div>
          <br>
          <p style="margin-right: 10px;float:left;width: 20px;height:20px;background: #D9001B;border-radius: 100px"></p>
          <span style="font-size: 20px;color: rgba(0, 0, 0, 0.701960784313725)">5</span>
          <br>
          <div style="margin-top: 10px">
            <span style="font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">严重风险用户</span>
          </div>
          <br>
          <el-link
              style="font-size: 12px;font-weight:700;color:rgba(2, 167, 240, 0.996078431372549);"
              :underline="false"
              type="primary"
          >查看用户
          </el-link>
        </div>
        <base-divider
            style="margin-top: 30px;border-left:2px #838383 var(--el-border-style);height: 50%"
            direction="vertical"
        />
        <div style="float: right;width: 43%;padding: 10px">
          <div>
            <span style="font-size: 16px;font-weight: 700;color: rgba(0, 0, 0, 0.701960784313725)">离职倾向</span>
          </div>
          <br>
          <p style="margin-right: 10px;float:left;width: 20px;height:20px;background: #D9001B;border-radius: 100px"></p>
          <span style="font-size: 20px;color: rgba(0, 0, 0, 0.701960784313725)">5</span>
          <br>
          <div style="margin-top: 10px">
            <span style="font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)">严重风险事件</span>
          </div>
          <br>
          <el-link
              style="font-size: 12px;font-weight:700;color:rgba(2, 167, 240, 0.996078431372549);"
              :underline="false"
              type="primary"
          >查看用户
          </el-link>
        </div>
        <el-link
            style="margin-right: 10px;font-weight: 700;float: right;font-size: 12px;color: rgba(2, 167, 240, 0.996078431372549)"
            :underline="false" type="primary"
        >查看其他32个
        </el-link>
      </div>
      <div>
        <el-table
            :data="concernTableData"
            stripe
            height="250"
            style="width: 100%; text-align: center"
            class="type-table"
        >
          <el-table-column prop="type" width="180">
            <template #header>
              <div style="text-align: center">
                <span style="font-size: 12px;font-weight: 700">关注用户类型</span>
              </div>
            </template>
            <template #default="scope">
              <div>
                <span style="font-size: 12px;font-weight: 700">{{ scope.row.type }} </span>
                <span v-if="scope.row.source" style="font-size: 12px;color:#7DD0F0;font-weight: 700"
                >({{ scope.row.source }})</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="count" width="180">
            <template #header>
              <div style="text-align: center">
                <span style="font-size: 12px;font-weight: 700">用户数量</span>
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center;">
                <span style="font-size: 12px">{{ scope.row.count }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="riskEvent">
            <template #header>
              <div style="text-align: center">
                <span style="font-size: 12px;font-weight: 700">严重事件</span>
              </div>
            </template>
            <template #default="scope">
              <div style="text-align: center">
                <el-icon style="color: #D9001B;margin-right: 5px;">
                  <WarningFilled/>
                </el-icon>
                <span style="font-size: 12px">{{ scope.row.riskEvent }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-link
            style="margin-right: 10px;color: #44BFF2;font-size: 12px;font-weight:700;float: right;margin-top: 5px;margin-bottom: 5px"
            type="primary"
        >查看所有关注用户
        </el-link>
      </div>
    </div>
    <div style=";background-color: #FFFFFF;float: left;width: calc(100% - 20px);margin-top: 10px">
      <div style="line-height: 25px;width: calc(100% - 22px);padding: 10px 10px;border: #f0f2f5 1px solid">
        <span style="font-size: 16px;font-weight: 700">数据外发风险</span>
        <span style="float: right;margin-left: 15px;margin-right: 20px;color: #C6C6C6;font-size: 12px">最近90天</span>
        <base-select
            size="small"
            v-model="value"
            style="color: #333333;height: 25px;float: right;width: 85px"
            class="m-2 estate"
            placeholder="严重风险"
        >
          <base-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              style="color: #333333"
              class="estate-option"
          />
        </base-select>
      </div>
      <div style="height: 310px;">
        <base-row :gutter="20" style="margin: 0px 0px;padding: 0px 0px">
          <base-col :span="2" style=";border: #DEDEDE 1px solid;padding: 0px 0px">
            <div style="width: 100%;height: 208px;float: left;font-size: 12px">
              <div style="font-size: 12px;background-color: #CEE9FE;line-height: 35px;text-align: center;height: 35px">
                源代码(7)
              </div>
              <div style="font-size: 12px;text-align: center;height: 35px;line-height: 35px">设计文档(5)</div>
              <div style="font-size: 12px;background-color: #f0f2f5;line-height: 35px;text-align: center;height: 35px">
                财务数据(2)
              </div>
              <div style="font-size: 12px;text-align: center;line-height: 35px;height: 35px">客户数据(3)</div>
              <div style="font-size: 12px;background-color: #f0f2f5;text-align: center;line-height: 35px;height: 35px">
                产品规划(3)
              </div>
            </div>
          </base-col>
          <base-col :span="8" style="border: #DEDEDE 1px solid;padding: 0px 0px">
            <div style="width: 98%;border: #f0f2f5 1px solid;float: left;height: 295px;padding: 5px 5px">
              <FolderOpenFilled/>
              <span style="color: #535353;font-size: 12px;font-weight: 700"> 共享文件 50%</span>
              <div style="margin-top: calc(295px - 30px)">
                <span style="color: #999999;font-size: 12px">2个共享服务器</span>
              </div>
            </div>
          </base-col>
          <base-col :span="7" style="border: #DEDEDE 1px solid;padding: 0px 0px">
            <div style="float: left;width: 100%">
              <div style="border: #f0f2f5 1px solid;background-color: #CEE9FE">
                <WechatFilled style="padding: 5px"/>
                <span style="color: #535353;font-size: 12px;font-weight: 700"> IM 文件传输 15%</span>
                <div style="margin-top: 160px;margin-bottom: 5px">
                  <span style="color: #999999;font-size: 12px;padding-left: 5px">2个APP</span>
                </div>
              </div>
              <div style="padding-top: 5px;border: #f0f2f5 1px solid;">
                <UsbOutlined style="padding-left: 5px"/>
                <span style="color: #535353;font-size: 12px;font-weight: 700"> U盘拷贝 60%</span>
                <div style="margin-top: 61px;padding-left: 5px">
                  <span style="color: #999999;font-size: 12px">2个服务</span>
                </div>
              </div>
            </div>
          </base-col>
          <base-col :span="7" style="border: #DEDEDE 1px solid;padding: 0px 0px">
            <div style="float: right;width: 100%">
              <div style="border: #f0f2f5 1px solid;">
                <MailFilled style="padding: 5px"/>
                <span style="color: #535353;font-size: 12px;font-weight: 700">邮箱发送 10%</span>
                <div style="margin-top: 160px;margin-bottom: 5px">
                  <span style="padding-left: 5px;color: #999999;font-size: 12px">3个邮箱服务器</span>
                </div>
              </div>
              <div style="background-color: #F2F2F2;padding-top: 5px;border: #f0f2f5 1px solid;">
                <CloudServerOutlined style="padding-left: 5px"/>
                <span style="color: #535353;font-size: 12px;font-weight: 700"> 网盘上传 10%</span>
                <div style="margin-top: 61px">
                  <span style="padding-left: 5px;color: #999999;font-size: 12px">3个App</span>
                </div>
              </div>
            </div>
          </base-col>
        </base-row>
        <!--        <div style="border: #DEDEDE 1px solid;width: 8%;height: 140px;float: left">-->
        <!--          <div style="background-color: #CEE9FE;line-height: 25px;text-align: center;height: 25px">源代码(7)</div>-->
        <!--          <div style="text-align: center;height: 25px;line-height: 25px">设计文档(5)</div>-->
        <!--          <div style="background-color: #f0f2f5;line-height: 25px;text-align: center;height: 25px">财务数据(2)</div>-->
        <!--          <div style="text-align: center;line-height: 25px;height: 25px">客户数据(3)</div>-->
        <!--          <div style="background-color: #f0f2f5;text-align: center;line-height: 25px;height: 25px">产品规划(3)</div>-->
        <!--        </div>-->
        <!--        <div style="border: #f0f2f5 1px solid;width: 31%;float: left;height: 130px;padding: 5px 5px">-->
        <!--          <FolderOpenFilled/>-->
        <!--          <span> 共享文件 50%</span>-->
        <!--          <div style="margin-top: calc(180px - 80px)">-->
        <!--            <span>2个共享服务器</span>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--        <div style="width: 30%;float: left;">-->
        <!--          <div style="border: #f0f2f5 1px solid;background-color: #CEE9FE">-->
        <!--            <FolderOpenFilled style="padding: 5px"/>-->
        <!--            <span> IM 文件传输 15%</span>-->
        <!--            <div style="margin-top: 35px;margin-bottom: 5px">-->
        <!--              <span style="padding-left: 5px">2个APP</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--          <div style="padding-top: 5px;border: #f0f2f5 1px solid;">-->
        <!--            <FolderOpenFilled style="padding-left: 5px"/>-->
        <!--            <span> U盘拷贝 60%</span>-->
        <!--            <div style="margin-top: 20px;padding-left: 5px">-->
        <!--              <span>2个服务</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--        <div style="width: calc(100% - 69.8%);float: right">-->
        <!--          <div style="border: #f0f2f5 1px solid;">-->
        <!--            <FolderOpenFilled style="padding: 5px"/>-->
        <!--            <span>邮箱发送 10%</span>-->
        <!--            <div style="margin-top: 35px;margin-bottom: 5px">-->
        <!--              <span style="padding-left: 5px">3个邮箱服务器</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--          <div style="padding-top: 5px;border: #f0f2f5 1px solid;">-->
        <!--            <FolderOpenFilled style="padding-left: 5px"/>-->
        <!--            <span> 网盘上传 10%</span>-->
        <!--            <div style="margin-top: 20px">-->
        <!--              <span style="padding-left: 5px">3个App</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
      </div>
    </div>

  </div>
</template>

<script>
export default {
  name: 'DataSecurityOverview',
}
</script>
<script setup>
import { ref } from 'vue'

const tableData = [{
  user: '<EMAIL>',
  seriousEvent: {
    count: 20,
    size: '50KB',
  },
  highRiskEvent: {
    count: 20,
    size: '50KB',
  },
  overview: ['百度网盘上传', 'Github上传', '微信发送文件', '源代码'],
}, {
  user: '<EMAIL>',
  seriousEvent: {
    count: 20,
    size: '50KB',
  },
  highRiskEvent: {
    count: 20,
    size: '50KB',
  },
  overview: ['微信发送文件', '客户资料'],
}, {
  user: '<EMAIL>',
  seriousEvent: {
    count: 20,
    size: '50KB',
  },
  highRiskEvent: {
    count: 20,
    size: '50KB',
  },
  overview: ['微信发送文件', '客户资料'],
}, {
  user: '<EMAIL>',
  seriousEvent: {
    count: 20,
    size: '50KB',
  },
  highRiskEvent: {
    count: 20,
    size: '50KB',
  },
  overview: ['微信发送文件', '客户资料'],
}]

const concernTableData = [
  {
    type: '离职倾向',
    source: '智能识别',
    count: '5',
    riskEvent: '20',
  },
  {
    type: '新入职',
    count: '4',
    riskEvent: '20',
  },
  {
    type: '供应商',
    count: '3',
    riskEvent: '20',
  },
  {
    type: '财务部门',
    count: '2',
    riskEvent: '20',
  },
]

const value = ref('1')

const options = [{
  value: '1',
  label: '严重风险',
}, {
  value: '2',
  label: '高风险',
}, {
  value: '3',
  label: '中风险',
}, {
  value: '4',
  label: '低风险',
}]
</script>
<style lang="scss" scoped>
::v-deep(.estate div) {
  height: 25px;
  color: #333333;
}

::v-deep(.estate input) {
  height: 25px;
  color: #333333;
  font-size: 12px;
}
</style>

<style>
.overview-table > th, .el-table tr {
  background-color: #FFFFFF;
}

.admin-box .el-table th.is-leaf {
  background-color: #FFFFFF;
}

.type-table > div .cell {
  min-height: 30px !important;
  line-height: 30px !important;
  height: 30px !important;
}

.estate-option:hover {
  background-color: #1E90FF;
}

.estate-option.hover {
  background: #1E90FF;
}
</style>
