/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{u as e,r,H as a,f as t,g as s,L as i,M as c,p as n}from"./index.bfaf04e1.js";import"./iconfont.2d75af05.js";const o=Object.assign({name:"WxOAuthCallback"},{setup(o){const u=t(),l=s(),y=e(),{code:d,state:p,auth_type:f,redirect_url:h}=u.query,m=r(Array.isArray(p)?p[0]:p),_=r("");return a((async()=>{const e=i.service({fullscreen:!0,text:"登录中，请稍候..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(e,"qiyewx_oauth",m.value)?await l.push({name:"verify",query:{redirect_url:h}}):c.error("登录失败，请重试")}catch(r){console.error("登录过程出错:",r),c.error("登录过程出错，请重试")}finally{e.close()}})),n("userName",_),(e,r)=>null}});export{o as default};
