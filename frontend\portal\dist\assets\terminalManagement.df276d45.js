/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{x as a,_ as e,r as o,p as l,j as s,o as i,a as t,b as n,i as p,w as r,k as c,t as d,J as u,O as m}from"./index.bfaf04e1.js";import"./iconfont.2d75af05.js";import{_ as f}from"./customTable.dd835374.js";const h={class:"terminal"},x={style:{"background-color":"#FFFFFF",padding:"17px 20px 60px 20px","border-radius":"4px","min-height":"calc(100vh - 145px)"}},_={class:"header"},g={style:{height:"40px"}},v={style:{"font-size":"13px",float:"left","margin-right":"7px"}},w={class:"icon","aria-hidden":"true"},b=["xlink:href"],y={style:{"font-size":"12px",color:"#252631","text-overflow":"ellipsis",overflow:"hidden",width:"130px",display:"inline-block"}},k={style:{height:"40px"}},z={style:{"font-size":"12px",color:"#252631","text-overflow":"ellipsis",overflow:"hidden",width:"130px",display:"inline-block"}},j={style:{height:"40px"}},C={style:{"font-size":"12px",color:"#252631","text-overflow":"ellipsis",overflow:"hidden",width:"120px",display:"inline-block"}},N={style:{height:"40px"}},F={style:{color:"#252631","font-size":"12px"}},D=e(Object.assign({name:"TerminalManagement"},{setup(e){const D=o(""),O=o(1),S=o(100),T=o(0),V={propList:[{prop:"app_name",label:"终端名",slotName:"app_name",isCenter:""},{prop:"os_info",label:"终端类型",slotName:"os_info"},{prop:"mac_info",label:"MAC地址",slotName:"mac_info"},{prop:"app_ips",label:"IP地址",slotName:"app_ips"},{prop:"login_user",label:"登陆用户",slotName:"login_user"},{prop:"update_time",label:"最后上线时间",slotName:"update_time"},{prop:"online",label:"在线状态",slotName:"online"}],isSelectColumn:!1,isOperationColumn:!1},I=o([]),L=async()=>{const e={limit:S.value,offset:(O.value-1)*S.value,search:D.value},o=await(async e=>a({url:"/console/v1/agents",method:"post",data:e}))(e);console.log("terminalList"),console.log(o),console.log("end"),0===o.data.code&&(I.value=o.data.data.rows,T.value=o.data.data.total_rows)};L();const M=a=>{switch(a){case"windows":return"#icon-windows";case"linux":return"#icon-linux-5";case"mac":case"ios":return"#icon-mac";default:return""}};return o([]),l("currentPage",O),l("pageSize",S),l("total",T),l("getTableData",L),(a,e)=>{const o=s("base-button"),l=s("base-input"),O=s("base-avatar");return i(),t("div",h,[n("div",x,[n("div",_,[p(o,{color:"#2972C8",plain:"",class:"iconfont icon-shuaxin",onClick:L},{default:r((()=>e[1]||(e[1]=[c("刷新")]))),_:1,__:[1]}),p(l,{modelValue:D.value,"onUpdate:modelValue":e[0]||(e[0]=a=>D.value=a),class:"w-50 m-2 organize-search",placeholder:"输入mac或用户名","suffix-icon":a.Search,style:{width:"15%",float:"right"},onChange:L},null,8,["modelValue","suffix-icon"])]),p(f,m({"table-data":I.value},V),{app_name:r((a=>[n("div",g,[n("span",v,[(i(),t("svg",w,[n("use",{"xlink:href":M(a.row.app_plat)},null,8,b)]))]),n("span",y,d(a.row.app_name),1)])])),mac_info:r((a=>[n("div",k,[n("span",z,d(a.row.mac_info.join("  ")),1)])])),app_ips:r((a=>[n("div",j,[n("span",C,d(a.row.app_ips.join("  ")),1)])])),online:r((a=>[n("div",N,[p(O,{size:8,style:u([{background:a.row.online?"#6DD230":"#252631"},{opacity:a.row.online?"1":"0.3"},{"margin-right":"6px"}])},null,8,["style"]),n("span",F,d(a.row.online?"在线":"离线"),1)])])),_:1},16,["table-data"])])])}}}),[["__scopeId","data-v-925153e2"]]);export{D as default};
