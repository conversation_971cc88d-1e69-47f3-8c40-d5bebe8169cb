const set = require('regenerate')(0xAA, 0xB5, 0xBA, 0x345, 0x37F, 0x386, 0x38C, 0x10C7, 0x10CD, 0x1F59, 0x1F5B, 0x1F5D, 0x1FBE, 0x2071, 0x207F, 0x2102, 0x2107, 0x2115, 0x2124, 0x2126, 0x2128, 0x2139, 0x214E, 0x2D27, 0x2D2D, 0xA7D3, 0x10780, 0x1D4A2, 0x1D4BB, 0x1D546);
set.addRange(0x41, 0x5A).addRange(0x61, 0x7A).addRange(0xC0, 0xD6).addRange(0xD8, 0xF6).addRange(0xF8, 0x1BA).addRange(0x1BC, 0x1BF).addRange(0x1C4, 0x293).addRange(0x295, 0x2B8).addRange(0x2C0, 0x2C1).addRange(0x2E0, 0x2E4).addRange(0x370, 0x373).addRange(0x376, 0x377).addRange(0x37A, 0x37D).addRange(0x388, 0x38A).addRange(0x38E, 0x3A1).addRange(0x3A3, 0x3F5).addRange(0x3F7, 0x481).addRange(0x48A, 0x52F).addRange(0x531, 0x556).addRange(0x560, 0x588).addRange(0x10A0, 0x10C5).addRange(0x10D0, 0x10FA).addRange(0x10FC, 0x10FF).addRange(0x13A0, 0x13F5).addRange(0x13F8, 0x13FD).addRange(0x1C80, 0x1C8A).addRange(0x1C90, 0x1CBA).addRange(0x1CBD, 0x1CBF).addRange(0x1D00, 0x1DBF).addRange(0x1E00, 0x1F15).addRange(0x1F18, 0x1F1D).addRange(0x1F20, 0x1F45).addRange(0x1F48, 0x1F4D).addRange(0x1F50, 0x1F57).addRange(0x1F5F, 0x1F7D).addRange(0x1F80, 0x1FB4).addRange(0x1FB6, 0x1FBC).addRange(0x1FC2, 0x1FC4).addRange(0x1FC6, 0x1FCC).addRange(0x1FD0, 0x1FD3).addRange(0x1FD6, 0x1FDB).addRange(0x1FE0, 0x1FEC).addRange(0x1FF2, 0x1FF4).addRange(0x1FF6, 0x1FFC).addRange(0x2090, 0x209C).addRange(0x210A, 0x2113).addRange(0x2119, 0x211D).addRange(0x212A, 0x212D).addRange(0x212F, 0x2134).addRange(0x213C, 0x213F).addRange(0x2145, 0x2149);
set.addRange(0x2160, 0x217F).addRange(0x2183, 0x2184).addRange(0x24B6, 0x24E9).addRange(0x2C00, 0x2CE4).addRange(0x2CEB, 0x2CEE).addRange(0x2CF2, 0x2CF3).addRange(0x2D00, 0x2D25).addRange(0xA640, 0xA66D).addRange(0xA680, 0xA69D).addRange(0xA722, 0xA787).addRange(0xA78B, 0xA78E).addRange(0xA790, 0xA7CD).addRange(0xA7D0, 0xA7D1).addRange(0xA7D5, 0xA7DC).addRange(0xA7F2, 0xA7F6).addRange(0xA7F8, 0xA7FA).addRange(0xAB30, 0xAB5A).addRange(0xAB5C, 0xAB69).addRange(0xAB70, 0xABBF).addRange(0xFB00, 0xFB06).addRange(0xFB13, 0xFB17).addRange(0xFF21, 0xFF3A).addRange(0xFF41, 0xFF5A).addRange(0x10400, 0x1044F).addRange(0x104B0, 0x104D3).addRange(0x104D8, 0x104FB).addRange(0x10570, 0x1057A).addRange(0x1057C, 0x1058A).addRange(0x1058C, 0x10592).addRange(0x10594, 0x10595).addRange(0x10597, 0x105A1).addRange(0x105A3, 0x105B1).addRange(0x105B3, 0x105B9).addRange(0x105BB, 0x105BC).addRange(0x10783, 0x10785).addRange(0x10787, 0x107B0).addRange(0x107B2, 0x107BA).addRange(0x10C80, 0x10CB2).addRange(0x10CC0, 0x10CF2).addRange(0x10D50, 0x10D65).addRange(0x10D70, 0x10D85).addRange(0x118A0, 0x118DF).addRange(0x16E40, 0x16E7F).addRange(0x1D400, 0x1D454).addRange(0x1D456, 0x1D49C).addRange(0x1D49E, 0x1D49F).addRange(0x1D4A5, 0x1D4A6).addRange(0x1D4A9, 0x1D4AC).addRange(0x1D4AE, 0x1D4B9).addRange(0x1D4BD, 0x1D4C3).addRange(0x1D4C5, 0x1D505);
set.addRange(0x1D507, 0x1D50A).addRange(0x1D50D, 0x1D514).addRange(0x1D516, 0x1D51C).addRange(0x1D51E, 0x1D539).addRange(0x1D53B, 0x1D53E).addRange(0x1D540, 0x1D544).addRange(0x1D54A, 0x1D550).addRange(0x1D552, 0x1D6A5).addRange(0x1D6A8, 0x1D6C0).addRange(0x1D6C2, 0x1D6DA).addRange(0x1D6DC, 0x1D6FA).addRange(0x1D6FC, 0x1D714).addRange(0x1D716, 0x1D734).addRange(0x1D736, 0x1D74E).addRange(0x1D750, 0x1D76E).addRange(0x1D770, 0x1D788).addRange(0x1D78A, 0x1D7A8).addRange(0x1D7AA, 0x1D7C2).addRange(0x1D7C4, 0x1D7CB).addRange(0x1DF00, 0x1DF09).addRange(0x1DF0B, 0x1DF1E).addRange(0x1DF25, 0x1DF2A).addRange(0x1E030, 0x1E06D).addRange(0x1E900, 0x1E943).addRange(0x1F130, 0x1F149).addRange(0x1F150, 0x1F169).addRange(0x1F170, 0x1F189);
exports.characters = set;
