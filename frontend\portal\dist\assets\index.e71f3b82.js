/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{a0 as e,h as a,o as s,d as n,j as t,w as u,T as o,f as d,a2 as i,m as l,z as r}from"./index.74d1ee23.js";const m=Object.assign({name:"SuperAdmin"},{setup(m){const c=e();return(e,m)=>{const f=a("router-view");return s(),n("div",null,[t(f,null,{default:u((({Component:e})=>[t(o,{mode:"out-in",name:"el-fade-in-linear"},{default:u((()=>[(s(),d(i,{include:l(c).keepAliveRouters},[(s(),d(r(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{m as default};
