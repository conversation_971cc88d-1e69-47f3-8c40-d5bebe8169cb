/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
@charset "UTF-8";.common-layout[data-v-3071d543]{min-height:calc(100vh - 200px)}.menu-label[data-v-3071d543]{width:81%;float:left;font-size:14px;padding-top:8px;font-weight:700;color:rgba(51,51,51,.7);padding-left:10px}.organize-but[data-v-3071d543]{height:28px;width:28px;padding:6px;border-width:0px;position:absolute;font-size:12px;color:rgba(51,51,51,.7)}.organize-search[data-v-3071d543]{width:200px;float:right;height:30px}/*!* 用来设置当前页面element全局table 鼠标移入某行时的背景色*!*/.app-table-style .el-table__cell .cell{font-size:12px}.app-table-style td{background-color:#fff}.table-header th.is-leaf{background-color:#fff!important}.risk-pagination{float:right}.risk-pagination .el-pager li,.risk-pagination .btn-prev,.risk-pagination .btn-next{background-color:#fff!important}
