/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{u as e,a,r as s,b as t,c as l,p as i,o as n,d as o,e as r,f as c,g as u,L as d}from"./index.2320e6b9.js";import y from"./secondaryAuth.e4383672.js";import"./verifyCode.c72175ce.js";const A=""+new URL("login_building.7b8b4335.png",import.meta.url).href,h={class:"login-page"},v={class:"content"},p={class:"right-panel"},g={key:0,class:"auth-status"},m={class:"status-icon"},w={class:"icon","aria-hidden":"true",style:{height:"48px",width:"48px",color:"#0082ef"}},f={key:1,class:"auth-waiting"},U={class:"waiting-icon"},q={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},C=Object.assign({name:"Status"},{setup(C){const K=e(),S=a(),b=s({});let{code:x,state:I}=K.query;const{auth_type:P,redirect_url:B,type:E,wp:Q}=K.query,N=window.location.host,O=window.location.protocol,T=t(),D=s(null),F=s(!1),J=s([]),R=s(!1),W=s(""),Y=s(""),Z=s(Array.isArray(I)?I[0]:I),k=s(""),H=s("phone"),j=s(!0),G=l((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===H.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===H.value}])),L=async e=>{var a;D.value=d.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=B||"/";if(e.clientParams){const s=new URLSearchParams;s.set("type",e.clientParams.type),e.clientParams.wp&&s.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+s.toString()}window.location.href=a}finally{null==(a=D.value)||a.close()}},M=()=>{F.value=!1,R.value=!1;const e=new URLSearchParams;e.set("idp_id",Array.isArray(I)?I[0]:I),B&&e.set("redirect",encodeURIComponent(B)),"client"===E&&(e.set("type","client"),Q&&e.set("wp",Q));const a=`/login?${e.toString()}`;S.push(a)};return(async()=>{var e;D.value=d.service({fullscreen:!0,text:"登录中，请稍候..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:`${O}//${N}/#/status`,idpId:Array.isArray(I)?I[0]:I,authWeb:{authWebCode:Array.isArray(x)?x[0]:x}},a=await T.LoginIn(e,P,Z.value);if(-1!==a.code)a.isSecondary&&(R.value=a.isSecondary,J.value=a.secondary,Y.value=a.secondary[0].id,W.value=a.uniqKey,k.value=a.userName,H.value=a.contactType,j.value=a.hasContactInfo||!1,b.value.uniqKey=a.uniqKey,b.value.name=a.secondary[0].name,b.value.notPhone=a.notPhone,F.value=!0);else{let e=`${O}//${N}/#/login?idp_id=${Array.isArray(I)?I[0]:I}`;B&&(e+=`&redirect=${B}`),"client"===E&&(e+="&type=client",Q&&(e+=`&wp=${Q}`)),location.href=e}}catch(a){console.error("登录处理失败:",a);let e=`${O}//${N}/#/login?idp_id=${Array.isArray(I)?I[0]:I}`;"client"===E&&(e+="&type=client",Q&&(e+=`&wp=${Q}`)),location.href=e}finally{null==(e=D.value)||e.close()}})(),l((()=>J.value.filter((e=>e.id!==Y.value)))),i("userName",k),i("isSecondary",R),i("contactType",H),i("hasContactInfo",j),i("uniqKey",W),i("last_id",Z),(e,a)=>(n(),o("div",h,[a[8]||(a[8]=r("div",{class:"header"},[r("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAATCAYAAACKsM07AAAAAXNSR0IArs4c6QAAAq5JREFUSEudk11IU2EYx//PcZtmZlREiNgHlNsZqEHmWUYXUhdFIHUh0U3eFNGneGZRXe2uiO2MLKPoqrqRLvqEIvoES3fMGw13Vkr0odCNgmVsuZ3zxFk5t7NNWS8ceJ/n/J//733eD8I8wy2rhxncCcCRRzZDRG3hgHQtnw3l9W/hIrFK/QKgcr5FABjXyqNr4WtK5NLlBYhedQ+Y76UXMfDQjAlozjAj2qsFpPsFAVyy+oTAO1NFjBEt6Kk2Y1EOfQCQnJuDgacRxTOnTSPl7MDZoa4jg0cJEOZM6HJEkU6asUsOXSIgOf8HMHSObxgJbvtk7SInwN0eOs+EMxbxbk3xPDZz1d7+zUWs9wBUPKshxoVw0HN2QYC75Y6Dq9Z8BXhVujhuW1Q1erFubDa3yTdQGp+2l83GuhGrGFakwQUBoty3D6DuHAcWB2B+OQcD00UC7R/2Sy/TBVlbJMrqc4C35zOaN894rQU9TQsAQiqAhv8CAKyT4f4YaIykzsZq5Dz9Zgnptmo2KP8jBGCDUaET3SZgaYYHUVALSHIWYP2JkWK7faKbAFd6AQM9P9loGws2Rq2LEWX1KsBHLPnJaHm08rOvKWbmU6sUZfUAwDetJiwIzRF/w6NcW+aU+5oF0IPsf9SqKdItK+AtwI2ZYvptF0pWDPnrfuUC1HYMLo4bsQmAU+/hr456NUXamgK42gdqiBJD2Sb8QlO27IDvlc05VbqRGPYMjU0oJtbvgrEsq3O21UaC9e+TWyTKoSsAjllFBJwKKx6/6O3vAhtHC7xZXZriOU5mmzNGbJzAmbfBdBP0Gq3sWVj8ses7wCsLATBoyiGUVJLoVQ+C+UaOg/qmKdJqd3tvA5Ngvo3CB9EhEuXQOwD1luoEmM7FE8s77Y7J62BuLdw9WTHwB+of7onYTsUzAAAAAElFTkSuQmCC",alt:"公司logo",class:"logo"}),r("span",{class:"header-text"},"ASec安全平台")],-1)),r("div",v,[a[7]||(a[7]=r("div",{class:"left-panel"},[r("img",{src:A,alt:"宣传图",class:"image"})],-1)),r("div",p,[F.value?(n(),o("div",f,[r("div",U,[(n(),o("svg",q,a[4]||(a[4]=[r("use",{"xlink:href":"#icon-auth-verify_code"},null,-1)])))]),a[5]||(a[5]=r("h4",{class:"waiting-title"},"需要进行安全验证",-1)),a[6]||(a[6]=r("p",{class:"waiting-message"},"请完成二次身份验证以确保账户安全",-1))])):(n(),o("div",g,[r("div",m,[(n(),o("svg",w,a[0]||(a[0]=[r("use",{"xlink:href":"#icon-auth-qiyewx"},null,-1)])))]),a[1]||(a[1]=r("h3",{class:"status-title"},"正在登录",-1)),a[2]||(a[2]=r("p",{class:"status-message"},"正在处理信息...",-1)),a[3]||(a[3]=r("div",{class:"loading-dots"},[r("span"),r("span"),r("span")],-1))]))])]),F.value?(n(),c(y,{key:0,"auth-info":{uniqKey:W.value,contactType:H.value,hasContactInfo:j.value},"auth-id":Y.value,"user-name":k.value,"last-id":Z.value,"auth-methods":G.value,onVerificationSuccess:L,onCancel:M},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):u("",!0)]))}});export{C as default};
