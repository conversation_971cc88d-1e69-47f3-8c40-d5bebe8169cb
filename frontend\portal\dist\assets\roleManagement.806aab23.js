/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{r as a,B as e,h as t,o as s,d as l,e as o,j as n,w as i,k as r,t as d,f as u,F as c,i as p,P as m,M as g}from"./index.74d1ee23.js";const f={class:"role"},b={class:"header"},v={style:{"text-align":"center"}},y={style:{"font-size":"12px"}},h={style:{"text-align":"center"}},x={style:{"font-size":"12px"}},w={style:{"text-align":"center"}},_={style:{"font-size":"12px"}},z={style:{"text-align":"center"}},k={style:{"text-align":"center"}},V={class:"dialog-footer"},C=Object.assign({name:"RoleManagement"},{setup(C){const S=a(""),U=[{empno:"0001",name:"销售人员",description:"销售",member:["test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa"],state:1},{empno:"0002",name:"ST组织",description:"ST",member:["aaa","bbb"],state:1},{empno:"0003",name:"测试",description:"test",member:["ccc","ddd"],state:1}],T=e({name:"",description:"",member:""}),P=[{value:"zs",label:"张三"},{value:"ls",label:"李四",disabled:!0},{value:"ww",label:"王五"},{value:"zl",label:"赵六"}],R=a("add"),j=a("新增角色"),B=a(!1),$=a([]),F=a=>{console.log(a),$.value=a},M=a=>{m.confirm("是否取消新增?").then((()=>{a()})).catch((()=>{}))},q=()=>{console.log(T),R.value="add",j.value="新增角色",B.value=!1},E=a(4),H=a(100),L=a(!0),O=a(!0),A=a(!1),D=a=>{console.log(`${a} items per page`)},G=a=>{console.log(`current page: ${a}`)};return(a,e)=>{const C=t("base-button"),$=t("base-input"),I=t("el-table-column"),J=t("SuccessFilled"),K=t("el-icon"),N=t("Remove"),Q=t("el-link"),W=t("el-table"),X=t("el-pagination"),Y=t("base-form-item"),Z=t("base-option"),aa=t("base-select"),ea=t("base-form"),ta=t("el-dialog");return s(),l("div",f,[o("div",b,[n(C,{icon:a.Plus,onClick:e[0]||(e[0]=a=>(console.log(1),j.value="新增角色",R.value="add",void(B.value=!0)))},{default:i((()=>e[10]||(e[10]=[r("新增角色")]))),_:1,__:[10]},8,["icon"]),n(C,{icon:a.RefreshRight},{default:i((()=>e[11]||(e[11]=[r("刷新")]))),_:1,__:[11]},8,["icon"]),n($,{modelValue:S.value,"onUpdate:modelValue":e[1]||(e[1]=a=>S.value=a),class:"w-50 m-2 organize-search",placeholder:"Search","suffix-icon":a.Search,style:{width:"15%",float:"right"}},null,8,["modelValue","suffix-icon"])]),n(W,{ref:"multipleTableRef",data:U,name:"roleTable",stripe:"",style:{width:"100%","margin-top":"5px","min-width":"1200px"},"highlight-current-row":"","class-name":"table-row-style","row-class-name":"app-table-style",onSelectionChange:F},{default:i((()=>[n(I,{type:"selection",width:"55"}),n(I,{prop:"name",label:"角色名称",width:"180"},{header:i((()=>e[12]||(e[12]=[o("div",{style:{"text-align":"center"}},[o("span",{style:{"font-size":"12px","font-weight":"700"}},"角色名称")],-1)]))),default:i((a=>[o("div",v,[o("span",y,d(a.row.name),1)])])),_:1}),n(I,{prop:"description",label:"描述",width:"180"},{header:i((()=>e[13]||(e[13]=[o("div",{style:{"text-align":"center"}},[o("span",{style:{"font-size":"12px","font-weight":"700"}},"描述")],-1)]))),default:i((a=>[o("div",h,[o("span",x,d(a.row.description),1)])])),_:1}),n(I,{prop:"member",label:"角色成员","show-overflow-tooltip":""},{header:i((()=>e[14]||(e[14]=[o("div",{style:{"text-align":"center"}},[o("span",{style:{"font-size":"12px","font-weight":"700"}},"角色成员")],-1)]))),default:i((a=>[o("div",w,[o("span",_,d(a.row.member),1)])])),_:1}),n(I,{prop:"state",label:"状态"},{header:i((()=>e[15]||(e[15]=[o("div",{style:{"text-align":"center"}},[o("span",{style:{"font-size":"12px","font-weight":"700"}},"状态")],-1)]))),default:i((a=>[o("div",z,[1===a.row.state?(s(),u(K,{key:0,style:{color:"#52c41a"}},{default:i((()=>[n(J)])),_:1})):(s(),u(K,{key:1},{default:i((()=>[n(N)])),_:1}))])])),_:1}),n(I,{prop:"operate",label:"操作"},{header:i((()=>e[16]||(e[16]=[o("div",{style:{"text-align":"center"}},[o("span",{style:{"font-size":"12px","font-weight":"700"}},"操作")],-1)]))),default:i((a=>[o("div",k,[n(Q,{type:"primary",underline:!1,style:{color:"rgba(2, 167, 240, 0.996078431372549)","margin-right":"10px","font-size":"12px","font-style":"normal","font-weight":"700"},onClick:e=>{return t=a.$index,s=a.row,console.log(t,s),j.value="编辑角色",R.value="edit",T.name=s.name,T.description="描述",T.member="zs",void(B.value=!0);var t,s}},{default:i((()=>e[17]||(e[17]=[r(" 编辑 ")]))),_:2,__:[17]},1032,["onClick"]),n(Q,{type:"primary",underline:!1,style:{color:"rgba(2, 167, 240, 0.996078431372549)","font-size":"12px","font-style":"normal","font-weight":"700"},onClick:e=>{return t=a.$index,s=a.row,console.log(t),console.log(s),void m.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个角色吗？</strong><br><strong>删除后关联的用户将无法访问此角色关联的应用，请谨慎操作。</strong>',"删除用户",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((()=>{"0001"===s.empno?g({type:"success",message:"删除成功"}):g({type:"error",message:"删除失败"})})).catch((()=>{g({type:"info",message:"取消删除"})}));var t,s}},{default:i((()=>e[18]||(e[18]=[r(" 删除 ")]))),_:2,__:[18]},1032,["onClick"])])])),_:1})])),_:1},512),n(X,{currentPage:E.value,"onUpdate:currentPage":e[2]||(e[2]=a=>E.value=a),"page-size":H.value,"onUpdate:pageSize":e[3]||(e[3]=a=>H.value=a),"page-sizes":[100,200,300,400],small:L.value,disabled:A.value,background:O.value,layout:"total, sizes, prev, pager, next, jumper",total:1e4,style:{float:"right"},class:"risk-pagination",onSizeChange:D,onCurrentChange:G},null,8,["currentPage","page-size","small","disabled","background"]),n(ta,{modelValue:B.value,"onUpdate:modelValue":e[9]||(e[9]=a=>B.value=a),title:j.value,width:"30%","custom-class":"custom-dialog","before-close":M},{footer:i((()=>[o("span",V,[n(C,{onClick:e[7]||(e[7]=a=>q())},{default:i((()=>e[19]||(e[19]=[r("取消")]))),_:1,__:[19]}),n(C,{color:"#256EBF",type:"primary",onClick:e[8]||(e[8]=a=>q())},{default:i((()=>e[20]||(e[20]=[r("确定")]))),_:1,__:[20]})])])),default:i((()=>[n(ea,{"label-position":"right","label-width":"100px",model:T,style:{"max-width":"500px"}},{default:i((()=>[n(Y,{label:"角色名：",prop:"name",rules:[{required:!0,message:"角色不能为空",trigger:["blur"]}]},{default:i((()=>[n($,{modelValue:T.name,"onUpdate:modelValue":e[4]||(e[4]=a=>T.name=a),style:{width:"calc(100% - 20px)"}},null,8,["modelValue"])])),_:1}),n(Y,{label:"描述：",prop:"description"},{default:i((()=>[n($,{modelValue:T.description,"onUpdate:modelValue":e[5]||(e[5]=a=>T.description=a),style:{width:"calc(100% - 20px)"}},null,8,["modelValue"])])),_:1}),n(Y,{label:"角色成员：",rules:[{required:!0,message:"请选择成员",trigger:["blur"]}]},{default:i((()=>[n(aa,{modelValue:T.member,"onUpdate:modelValue":e[6]||(e[6]=a=>T.member=a),style:{width:"calc(100% - 20px)"},placeholder:"请选择"},{default:i((()=>[(s(),l(c,null,p(P,(a=>n(Z,{key:a.value,label:a.label,value:a.value,disabled:a.disabled},null,8,["label","value","disabled"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}});export{C as default};
