/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js"],(function(t,e){"use strict";var n;return{setters:[function(t){n=t.x}],execute:function(){t("g",(function(t){return n({url:"/api/getApiList",method:"post",data:t})})),t("c",(function(t){return n({url:"/api/createApi",method:"post",data:t})})),t("a",(function(t){return n({url:"/api/getApiById",method:"post",data:t})})),t("u",(function(t){return n({url:"/api/updateApi",method:"post",data:t})})),t("e",(function(t){return n({url:"/api/getAllApis",method:"post",data:t})})),t("b",(function(t){return n({url:"/api/deleteApi",method:"post",data:t})})),t("d",(function(t){return n({url:"/api/deleteApisByIds",method:"delete",data:t})}))}}}));
