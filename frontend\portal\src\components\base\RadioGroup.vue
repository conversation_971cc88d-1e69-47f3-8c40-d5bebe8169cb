<template>
  <div class="base-radio-group" role="radiogroup">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'BaseRadioGroup',
  props: {
    modelValue: {
      type: [String, Number, Boolean],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'default',
      validator: (value) => ['large', 'default', 'small'].includes(value)
    },
    textColor: {
      type: String,
      default: ''
    },
    fill: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'change'],
  watch: {
    modelValue(val) {
      this.$emit('change', val)
    }
  },
  provide() {
    return {
      radioGroup: this
    }
  }
}
</script>

<style scoped>
.base-radio-group {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 0;
}
</style>
