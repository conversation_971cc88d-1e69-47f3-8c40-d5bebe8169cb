/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{m as e,j as l,o as s,d as a,w as n,b as o,t,a as i,e as c}from"./index.bfaf04e1.js";import"./iconfont.2d75af05.js";const d={class:"custom-tree-node"},r=["onClick"],p={key:0},u={key:1},m={key:2},f=Object.assign({name:"PolicyTree"},{emits:["append"],setup(f,{emit:y}){const g={label:"name",children:"zones",isLeaf:"leaf",isRoot:"root"};let h=0;const k=e("checkedGroupId");let v=null,x=null;const j=(e,l)=>0===e.level?l([{id:1,name:"杭州分公司"},{id:2,name:"深圳分公司"},{id:3,name:"长沙总部"},{id:4,name:"本地用户"}]):l([]);return(e,f)=>{const y=l("el-tree");return s(),a(y,{icon:e.CirclePlusFilled,style:{"font-size":"12px",height:"95%",overflow:"auto","max-height":"calc(100vh - 235px)"},props:g,load:j,lazy:"","expand-on-click-node":!1,"node-key":"id",class:"policy-tree"},{default:n((({node:e,data:l})=>[o("span",d,[o("span",{onClick:e=>(e=>{if(console.log("handleNodeClick"),console.log(k.value),console.log(e),h++,v&&h>=2&&(x=e.id,h=0,x===v))return alert("双击"),console.log("处理双击逻辑"),x=null,void(v=null);v=e.id,k.value=e.id,setTimeout((()=>{v=null,h=0}),300)})(l)},[f[3]||(f[3]=o("span",{style:{"margin-right":"3px"}},[o("svg",{class:"icon svg-icon","aria-hidden":"true"},[o("use",{"xlink:href":"#el-icon-wenjianjia"})])],-1)),o("span",null,t(e.label),1),1===l.id?(s(),i("span",p,f[0]||(f[0]=[o("span",{class:"custom-tree-type"},"ldap",-1)]))):c("",!0),2===l.id?(s(),i("span",u,f[1]||(f[1]=[o("span",{class:"custom-tree-type"},"企业微信",-1)]))):c("",!0),l.id>2?(s(),i("span",m,f[2]||(f[2]=[o("span",{class:"custom-tree-type"},"local",-1)]))):c("",!0)],8,r)])])),_:1},8,["icon"])}}});export{f as default};
