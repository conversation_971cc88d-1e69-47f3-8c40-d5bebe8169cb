<template>
  <div class="common-layout organize">
    <base-container style="height: 100%">
      <base-aside width="200px" style="min-height: calc(100vh - 200px)">
        <div style="height: 35px">
          <span class="menu-label">用户目录</span>
          <base-button class="organize-but" :icon="FolderAdd" @click="addOrganize()"/>
        </div>
        <DirectoryTree
            :loadOperate=loadOperate
            :edit="edit"
            :append="append"
            :loadNode="loadNode"
            :treeProps="treeProps"
        />
      </base-aside>
      <!--      用户表格-->
      <base-main>
        <div class="header">
          <base-button :icon="Plus" @click="add('user')">新增</base-button>
          <base-button :icon="RefreshRight" @click="getTableData">刷新</base-button>
          <base-input
              v-model="searchUser"
              class="w-50 m-2 organize-search"
              placeholder="(名称)"
              :suffix-icon="Search"
              @change="getTableData"
          />
        </div>
        <CustomTable
            :table-data="tableData"
            v-bind="tableConfig"
        >
          <template #uniqueIdentification="scope">
            <div style="text-align: center">
              <span style="font-size: 12px">{{ JSONPath('$...uniqueIdentification[0]', scope.row)[0] }}</span>
            </div>
          </template>
          <template #phone="scope">
            <div style="text-align: center">
              <span style="font-size: 12px">{{ JSONPath('$...phone[0]', scope.row)[0] }}</span>
            </div>
          </template>
          <template #expiration="scope">
            <div style="text-align: center">
              <span style="font-size: 12px">{{ JSONPath('$..expiration[0]', scope.row)[0] }}</span>
            </div>
          </template>
          <template #enabled="scope">
            <div style="text-align: center">
              <el-icon v-if="scope.row.enabled" style="color: #52c41a">
                <SuccessFilled/>
              </el-icon>
              <el-icon v-else>
                <Remove/>
              </el-icon>
            </div>
          </template>
        </CustomTable>
      </base-main>
    </base-container>
    <!--弹窗-->
    <el-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="title"
        width="30%"
        custom-class="custom-dialog"
    >
      <CustomFrom
          ref="customForm"
          v-bind="formOptions"
          :formOptions="formValues"
          :cancel="cancel"
          :submitForm="submitForm"
      />
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Organize',
}
</script>
<script setup>
import { provide, reactive, ref } from 'vue'
import {
  FolderAdd,
  Plus,
  RefreshRight,
  Search,
} from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import '@/assets/ali/iconfont'
import { JSONPath } from 'jsonpath-plus'
import dayjs from 'dayjs'
import { useUserStore } from '@/pinia/modules/user'
import DirectoryTree from '@/components/directoryTree/directoryTree.vue'
import CustomTable from '@/components/customTable.vue'
import CustomFrom from '@/components/customFrom/customFrom.vue'
import { groupFormItemOptions } from './groupFormOptions'
import { userFormItemOptions } from './userFormOptions'

const userStore = useUserStore()

const searchUser = ref('')
const tableData = ref([])
// 分页
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(50)
const nameDisabled = ref(false)
const treeRef = ref('')
const cascaderKey = ref(1)
const title = ref('新增用户目录')
const type = ref('root')
const operation = ref('add')
const dialogVisible = ref(false)
const loadOperate = true
const treeProps = {
  label: 'name',
  children: 'zones',
  isLeaf: 'leaf',
  isRoot: 'root',
}
const formLabelAlign = reactive({
  id: '',
  source: '',
  name: '',
  description: '',
  uniqueIdentification: '',
  statu: '1',
  expirationRadio: '0',
  expiration: '',
})

let formOptions = {
  formItems: [],
  formValues: reactive({}),
}

const propList = [
  {
    prop: 'username',
    label: '名称',
    slotName: 'username',
  },
  {
    prop: 'uniqueIdentification',
    label: '工号',
    slotName: 'uniqueIdentification',
  },
  {
    prop: 'organize',
    label: '所属组织',
    slotName: 'organize',
  },
  {
    prop: 'phone',
    label: '手机号码',
    slotName: 'phone',
  },
  {
    prop: 'expiration',
    label: '过期时间',
    slotName: 'expiration',
  },
  {
    prop: 'enabled',
    label: '状态',
    slotName: 'enabled',
  },
]

const options = ref([])
const getOptions = async() => {
  const res = await userStore.GetOrganize('')
  if (res.data) {
    options.value = res.data
  }
}

const userOrigin = ref([])
const getUserOrigin = async() => {
  const res = await userStore.GetUserOrigin()
  if (res.status === 200 && res.data.code === 0) {
    userOrigin.value = res.data.data
  }
}

const getTableData = async() => {
  console.log('getTableData')
  console.log(treeRef.value)
  const query = {
    briefRepresentation: false,
    first: (currentPage.value - 1) * pageSize.value,
    max: pageSize.value,
    search: searchUser.value,
  }
  let userListtotal = {
    data: 0,
  }
  let userList = {}
  if (treeRef.value) {
    userList = await getGroupMembers()
    userListtotal.data = userList.data.length
  } else {
    userListtotal = await userStore.GetUserListCount(query)
    userList = await userStore.GetUserList(query)
  }

  if (userList.data) {
    tableData.value = userList.data
    total.value = userListtotal.data
  }
}

const rolesOptions = ref([])
const getRoles = async() => {
  const res = await userStore.GetRoles('')
  if (res.data) {
    rolesOptions.value = res.data
  }
}

const init = async() => {
  await getOptions()
  await getTableData()
  await getRoles()
  await getUserOrigin()
}

init()
const tableConfig = {
  propList,
  isSelectColumn: false,
  isOperationColumn: true,
}

const formValues = reactive({
  source: 'select',
})

provide('cascaderKey', cascaderKey)
provide('treeRef', treeRef)
provide('operation', operation)
provide('title', title)
provide('type', type)
provide('dialogVisible', dialogVisible)
provide('formLabelAlign', formLabelAlign)
provide('currentPage', currentPage)
provide('pageSize', pageSize)
provide('total', total)
provide('getTableData', getTableData)

// 弹窗
// const handleClose = (done) => {
//   ElMessageBox.confirm('是否取消新增?')
//       .then(() => {
//         initFormLabelAlign()
//         done()
//       })
//       .catch(() => {
//         // catch error
//       })
// }

const cancel = () => {
  initFormLabelAlign()
  dialogVisible.value = false
}

const initFormLabelAlign = () => {
  const keys = Object.keys(formOptions.formValues)
  let obj = {}
  keys.forEach((item) => {
    obj[item] = ''
  })
  Object.assign(formOptions.formValues, obj)
}
const customForm = ref()
const submitForm = async(formEl) => {
  console.log('organize')
  console.log(formOptions.formValues)
  await formEl.validate(async(valid, fields) => {
    if (!valid) {
      ElMessage({
        showClose: true,
        message: '字段校验失败，请检查！',
        type: 'error',
      })
      return ''
    }
    console.log(formLabelAlign.expiration)
    console.log(dayjs(formLabelAlign.expiration).format('YYYY-MM-DD HH:mm:ss'))
    // 子组件值
    let organize = ''
    let user = ''
    if (operation.value === 'addUser' || operation.value === 'updateUser') {
      user = {
        id: formOptions.formValues.id,
        username: formOptions.formValues.name,
        enabled: Number(formOptions.formValues.statu) ? 1 : 0,
        totp: false,
        emailVerified: false,
        email: formOptions.formValues.email,
        groups: [formOptions.formValues.source],
        realmRoles: [formOptions.formValues.roles],
        attributes: {
          defaultRouter: 'dashboard',
          nickName: formOptions.formValues.name,
          uniqueIdentification: [formOptions.formValues.uniqueIdentification],
          description: [formOptions.formValues.description || ''],
          phone: [formOptions.formValues.phone],
          expiration: [!Number(formOptions.formValues.dateType) ? '永久' : dayjs(formOptions.formValues.expiration).format('YYYY-MM-DD HH:mm:ss')],
        },
        credentials: [
          {
            type: 'password',
            temporary: false,
            value: formOptions.formValues.password,
          },
        ],
      }
    } else {
      organize = {
        id: formOptions.formValues.id,
        name: formOptions.formValues.name,
        attributes: {
          uniqueIdentification: [formOptions.formValues.uniqueIdentification],
          description: [formOptions.formValues.description || ''],
          source: [formOptions.formValues.source],
        },
      }
    }

    let res = ''
    switch (operation.value) {
      case 'add':
        res = await userStore.CreateOrganize(organize)
        break
      case 'update':
        res = await userStore.UpdateOrganize(organize)
        break
      case 'addSub':
        res = await userStore.AddSubgroup(organize)
        break
      case 'addUser':
        res = await userStore.CreateUser(user)
        break
      case 'updateUser':
        res = await userStore.UpdateUser(user)
        break
    }
    dialogVisible.value = false
    await getTableData()
    await getOptions()
    initFormLabelAlign()
    ++cascaderKey.value
    console.log(valid)
    console.log(fields)
  })
}

const add = (lx) => {
  console.log('add')
  title.value = '新增用户'
  type.value = lx
  operation.value = 'addUser'
  nameDisabled.value = false
  formOptions.formItems = userFormItemOptions(options, rolesOptions)
  console.log(formOptions.formItems)
  formOptions.formValues.statu = '1'
  formOptions.formValues.dateType = '0'
  dialogVisible.value = true
}

const addOrganize = () => {
  console.log('addOrganize')
  title.value = '新增目录'
  type.value = 'root'
  operation.value = 'add'
  formOptions.formItems = groupFormItemOptions(userOrigin, options, true)
  dialogVisible.value = true
}

const open = (id) => {
  ElMessageBox.confirm(
      '删除目录以后将无法恢复，确认删除目录？',
      '删除用户目录',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
  )
      .then(async() => {
        console.log(id)
        const res = await userStore.DelOrganize(id)
        console.log(res)
        if (res.status === 204) {
          ElMessage({
            type: 'success',
            message: '删除成功',
          })
          ++cascaderKey.value
          await getOptions()
        } else {
          ElMessage({
            type: 'error',
            message: '删除失败',
          })
          openW(id)
        }
        initFormLabelAlign()
      })
      .catch(() => {
        initFormLabelAlign()
        ElMessage({
          type: 'info',
          message: '取消删除',
        })
      })
}

provide('open', open)

const openW = (id) => {
  const meg = (id === 7 ? '删除当前用户目录需先手动清除当前用户目录所有数据再重试，包括角色、用户' : '当前用户目录已被认证策略引用（XXX,YYY）关联，请先前往该认证策略修改后再删除')
  ElMessageBox.alert(
      meg,
      '删除用户目录',
      {
        confirmButtonText: '确认',
      },
  )
}

const setFormValues = (data) => {
  console.log('setFormValues')
  console.log(data)
  formOptions.formValues.id = data.data.id
  formOptions.formValues.name = data.data.username || data.data.name
  formOptions.formValues.source = data.data.group || JSONPath('$..attributes.source[0]', data.data)[0]
  formOptions.formValues.description = JSONPath('$..description[0]', data.data)[0]
  formOptions.formValues.uniqueIdentification = JSONPath('$..uniqueIdentification[0]', data.data)[0]
  formOptions.formValues.password = '123456'
  formOptions.formValues.confirmPassword = '123456'
  formOptions.formValues.group = data.groupId
  formOptions.formValues.groupId = data.groupId
  formOptions.formValues.email = data.data.email
  formOptions.formValues.phone = JSONPath('$..phone[0]', data.data)[0]
  formOptions.formValues.statu = data.enabled ? '0' : '1'
  if (JSONPath('$..expiration[0]', data.data)[0] && JSONPath('$..expiration[0]', data.data)[0] === '永久') {
    formOptions.formValues.dateType = '0'
  } else {
    formOptions.formValues.dateType = '1'
    formOptions.formValues.expiration = JSONPath('$..expiration[0]', data.data)[0]
  }
}

// 表格
const handleEdit = async(index, row) => {
  console.log('handleEdit')
  console.log(row)
  title.value = '编辑用户'
  type.value = 'user'
  nameDisabled.value = true
  operation.value = 'updateUser'
  const userInfo = await userStore.GetUserInfo(row.id)
  console.log(userInfo)
  formOptions.formItems = userFormItemOptions(options, rolesOptions)
  setFormValues(userInfo)
  dialogVisible.value = true
}

const handleDelete = (index, row) => {
  console.log('handleDelete')
  console.log(row)
  ElMessageBox.confirm(
      '<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个用户吗？</strong><br><strong>删除后用户将无法登录和访问应用，请谨慎操作。</strong>',
      '删除用户',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
  ).then(async() => {
    const res = await userStore.DeleteUser(row.id)
    console.log(res)
    if (res.status === 204) {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      await getTableData()
    } else {
      ElMessage({
        type: 'error',
        message: '删除失败',
      })
    }
    initFormLabelAlign()
  }).catch(() => {
    initFormLabelAlign()
    ElMessage({
      type: 'info',
      message: '取消删除',
    })
  })
}

provide('handleEdit', handleEdit)
provide('handleDelete', handleDelete)

const getGroupMembers = async() => {
  const id = treeRef.value
  const query = {
    briefRepresentation: false,
    first: (currentPage.value - 1) * pageSize.value,
    max: pageSize.value,
    search: searchUser.value,
  }
  return await userStore.GetGroupMembers(id, query)
}

const append = async(data) => {
  console.log('append')
  console.log(data)
  operation.value = 'addSub'
  title.value = '新增子目录'
  type.value = 'sub'
  const organize = await userStore.GetOrganizeDetails(data.id)
  console.log('organize')
  console.log(organize)
  formOptions.formValues.id = data.id
  formOptions.formValues.source = JSONPath('$...source[0]', organize.data)[0]
  formOptions.formValues.group = organize.data.name
  formOptions.formItems = groupFormItemOptions(userOrigin, options, true)
  dialogVisible.value = true
}

const edit = async(node, data) => {
  console.log('edit')
  console.log(data)
  console.log(node)
  const organize = await userStore.GetOrganizeDetails(data.id)
  title.value = '修改子目录'
  type.value = 'root'
  operation.value = 'update'
  let isHidden = true
  if (node.level > 1) {
    organize.groupId = node.parent.data.id
    isHidden = true
  }
  setFormValues(organize)
  formOptions.formItems = groupFormItemOptions(userOrigin, options, isHidden)
  dialogVisible.value = true
}

let organize = ''
const loadNode = async(node, resolve) => {
  if (node.level === 0) {
    organize = await userStore.GetOrganize('')
    return resolve(organize.data)
  }
  if (node.data.subGroups.length > 0) return resolve(node.data.subGroups)
  return resolve([])
}
</script>
<style scoped>
.common-layout {
  min-height: calc(100vh - 200px);
}

.menu-label {
  width: 81%;
  float: left;
  font-size: 12px;
  padding-top: 8px;
  color: rgba(51, 51, 51, 0.701960784313725);
  padding-left: 10px;
}

.organize-but {
  height: 28px;
  width: 28px;
  padding: 6px 6px;
  border-width: 0px;
  position: absolute;
  font-size: 12px;
}

.organize-search {
  width: 200px;
  float: right;
  height: 30px;
}
</style>
