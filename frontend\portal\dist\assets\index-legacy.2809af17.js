/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,o="function"==typeof Symbol?Symbol:{},u=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function a(t,o,u,i){var a=o&&o.prototype instanceof l?o:l,f=Object.create(a.prototype);return n(f,"_invoke",function(t,n,o){var u,i,a,l=0,f=o||[],s=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return u=t,i=0,a=e,d.n=n,c}};function p(t,n){for(i=t,a=n,r=0;!s&&l&&!o&&r<f.length;r++){var o,u=f[r],p=d.p,b=u[2];t>3?(o=b===n)&&(a=u[(i=u[4])?5:(i=3,3)],u[4]=u[5]=e):u[0]<=p&&((o=t<2&&p<u[1])?(i=0,d.v=n,d.n=u[1]):p<b&&(o=t<3||u[0]>n||n>b)&&(u[4]=t,u[5]=n,d.n=b,i=0))}if(o||t>1)return c;throw s=!0,n}return function(o,f,b){if(l>1)throw TypeError("Generator is already running");for(s&&1===f&&p(f,b),i=f,a=b;(r=i<2?e:a)||!s;){u||(i?i<3?(i>1&&(d.n=-1),p(i,a)):d.n=a:d.v=a);try{if(l=2,u){if(i||(o="next"),r=u[o]){if(!(r=r.call(u,a)))throw TypeError("iterator result is not an object");if(!r.done)return r;a=r.value,i<2&&(i=0)}else 1===i&&(r=u.return)&&r.call(u),i<2&&(a=TypeError("The iterator does not provide a '"+o+"' method"),i=1);u=e}else if((r=(s=d.n<0)?a:t.call(n,d))!==c)break}catch(r){u=e,i=1,a=r}finally{l=1}}return{value:r,done:s}}}(t,u,i),!0),f}var c={};function l(){}function f(){}function s(){}r=Object.getPrototypeOf;var d=[][u]?r(r([][u]())):(n(r={},u,(function(){return this})),r),p=s.prototype=l.prototype=Object.create(d);function b(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,n(t,i,"GeneratorFunction")),t.prototype=Object.create(p),t}return f.prototype=s,n(p,"constructor",s),n(s,"constructor",f),f.displayName="GeneratorFunction",n(s,i,"GeneratorFunction"),n(p),n(p,i,"Generator"),n(p,u,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:a,m:b}})()}function n(t,e,r,o){var u=Object.defineProperty;try{u({},"",{})}catch(t){u=0}n=function(t,e,r,o){if(e)u?u(t,e,{value:r,enumerable:!o,configurable:!o,writable:!o}):t[e]=r;else{var i=function(e,r){n(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},n(t,e,r,o)}function e(t,n,e,r,o,u,i){try{var a=t[u](i),c=a.value}catch(t){return void e(t)}a.done?n(c):Promise.resolve(c).then(r,o)}function r(t){return function(){var n=this,r=arguments;return new Promise((function(o,u){var i=t.apply(n,r);function a(t){e(i,o,u,a,c,"next",t)}function c(t){e(i,o,u,a,c,"throw",t)}a(void 0)}))}}System.register(["./warningBar-legacy.4145d360.js","./index-legacy.dbc04544.js"],(function(n,e){"use strict";var o,u,i,a,c,l,f,s,d,p,b,v;return{setters:[function(t){o=t.W},function(t){u=t.x,i=t.r,a=t.B,c=t.h,l=t.o,f=t.d,s=t.j,d=t.e,p=t.w,b=t.k,v=t.M}],execute:function(){var e=function(t){return u({url:"/email/emailTest",method:"post",data:t})},m={class:"gva-form-box"};n("default",Object.assign({name:"Email"},{setup:function(n){var u=i(null),y=a({to:"",subject:"",body:""}),h=function(){var n=r(t().m((function n(){return t().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,e();case 1:0===t.v.code&&v.success("发送成功");case 2:return t.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}(),_=function(){var n=r(t().m((function n(){return t().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,e();case 1:0===t.v.code&&v.success("发送成功,请查收");case 2:return t.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();return function(t,n){var e=c("base-input"),r=c("base-form-item"),i=c("base-button"),a=c("base-form");return l(),f("div",null,[s(o,{title:"需要提前配置email配置文件，为防止不必要的垃圾邮件，在线体验功能不开放此功能体验。"}),d("div",m,[s(a,{ref_key:"emailForm",ref:u,"label-position":"right","label-width":"80px",model:y},{default:p((function(){return[s(r,{label:"目标邮箱"},{default:p((function(){return[s(e,{modelValue:y.to,"onUpdate:modelValue":n[0]||(n[0]=function(t){return y.to=t})},null,8,["modelValue"])]})),_:1}),s(r,{label:"邮件"},{default:p((function(){return[s(e,{modelValue:y.subject,"onUpdate:modelValue":n[1]||(n[1]=function(t){return y.subject=t})},null,8,["modelValue"])]})),_:1}),s(r,{label:"邮件内容"},{default:p((function(){return[s(e,{modelValue:y.body,"onUpdate:modelValue":n[2]||(n[2]=function(t){return y.body=t}),type:"textarea"},null,8,["modelValue"])]})),_:1}),s(r,null,{default:p((function(){return[s(i,{onClick:h},{default:p((function(){return n[3]||(n[3]=[b("发送测试邮件")])})),_:1,__:[3]}),s(i,{onClick:_},{default:p((function(){return n[4]||(n[4]=[b("发送邮件")])})),_:1,__:[4]})]})),_:1})]})),_:1},8,["model"])])])}}}))}}}))}();
