/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
System.register(["./index-legacy.9982a24a.js","./index-legacy.04f34b53.js","./localLogin-legacy.259f647b.js","./wechat-legacy.458a3570.js","./feishu-legacy.35c246a0.js","./dingtalk-legacy.432ac4eb.js","./oauth2-legacy.14078acc.js","./sms-legacy.d0ec82cf.js","./secondaryAuth-legacy.764b384e.js","./verifyCode-legacy.acb55512.js"],(function(n,e){"use strict";var c,t,a;return{setters:[function(n){c=n.default},function(n){t=n.o,a=n.f},function(){},function(){},function(){},function(){},function(){},function(){},function(){},function(){}],execute:function(){n("default",Object.assign({name:"ClientNewLogin"},{setup:function(n){return function(n,e){return t(),a(c)}}}))}}}));
