<template>
    <div class="secondary-auth-overlay">
        <div class="secondary-auth-container">
            <div v-if="showSelector" class="auth-selector">
                <h2 class="title">请选择二次认证方式</h2>
                <div class="auth-methods">
                    <base-card 
                        v-for="method in availableMethods" 
                        :key="method.type" 
                        class="auth-method-card"
                        @click="selectAuthMethod(method)"
                    >
                        <div class="auth-method-content">
                            <base-avatar>
                                <svg class="icon" aria-hidden="true">
                                    <use :xlink:href="'#icon-auth-' + method.icon" />
                                </svg>
                            </base-avatar>
                            <div class="auth-method-name">{{ method.name }}</div>
                        </div>
                    </base-card>
                </div>
                <div class="selector-footer">
                    <base-button 
                        type="info" 
                        @click="() => handleCancel()"
                    >取消</base-button>
                </div>
            </div>
            
            <!-- 加载统一的验证码组件，传入不同的模板类型 -->
            <verify-code
                v-if="!showSelector && selectedMethod"
                :auth_info="authInfo"
                :auth_id="authId"
                :user-name="userName"
                :last_id="lastId"
                :secondary-type="selectedMethod.type"
                @verification-success="handleVerificationSuccess"
                @back="showSelector = true"
                @cancel="handleCancel"
            ></verify-code>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SecondaryAuth'
}
</script>

<script setup>
import { ref, computed } from 'vue'
import VerifyCode from '@/view/login/secondaryAuth/verifyCode.vue'

const props = defineProps({
    authMethods: {
        type: Array,
        default: () => [{
            type: 'sms',
            name: '短信验证',
            icon: 'duanxin',
            available: true
        }, {
            type: 'email',
            name: '邮箱验证',
            icon: 'email',
            available: true
        }]
    },
    authInfo: {
        type: Object,
        required: true
    },
    authId: {
        type: String,
        required: true
    },
    userName: {
        type: String,
        default: ''
    },
    lastId: {
        type: String,
        default: ''
    }
})

// 是否显示选择器
const showSelector = ref(true)
// 选中的认证方式
const selectedMethod = ref(null)

// 可用的认证方式
const availableMethods = computed(() => {
    // console.log('认证方式列表:', props.authMethods)
    // console.log('过滤后的可用认证方式:', props.authMethods.filter(method => method.available))
    return props.authMethods.filter(method => method.available)
})

// 选择认证方式
const selectAuthMethod = (method) => {
    // console.log('选择认证方式:', method)
    selectedMethod.value = method
    showSelector.value = false
}

// 如果只有一种认证方式，自动选择
if (availableMethods.value.length === 1) {
    // console.log('只有一种认证方式，自动选择:', availableMethods.value[0])
    selectAuthMethod(availableMethods.value[0])
}

const emit = defineEmits(['verification-success', 'cancel'])

const handleCancel = () => {
    emit('cancel')
}

// 在验证成功时
const handleVerificationSuccess = (result) => {
    // 添加客户端参数到结果中
    if (route.query.type === 'client') {
        result.clientParams = {
            type: 'client',
            wp: route.query.wp || '50001'
        }
    }
    emit('verification-success', result)
}
</script>

<style lang="scss" scoped>
.secondary-auth-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.secondary-auth-container {
    background: #fff;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-width: 340px;
    max-width: 90%;
}

.auth-selector {
    .title {
        height: 60px;
        font-size: 24px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .auth-methods {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .auth-method-card {
        width: 120px;
        height: 120px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    }
    
    .auth-method-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
    
    .auth-method-name {
        margin-top: 10px;
        font-size: 14px;
    }
    
    .selector-footer {
        text-align: center;
        margin-top: 20px;
    }
}
</style>