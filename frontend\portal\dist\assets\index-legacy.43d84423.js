/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js","./index-browser-esm-legacy.6966c248.js"],(function(e,t){"use strict";var r,c,n,i,a,u=document.createElement("style");return u.textContent=".headerAvatar[data-v-7ac42d76]{display:flex;justify-content:center;align-items:center;margin-right:8px}.file[data-v-7ac42d76]{width:80px;height:80px;position:relative}\n",document.head.appendChild(u),{setters:[function(e){r=e.r,c=e.b,n=e.c,i=e._},function(e){a=e.J}],execute:function(){var u=""+new URL("noBody.745c3d16.png",t.meta.url).href,p=Object.assign({name:"CustomPic"},{props:{picType:{type:String,required:!1,default:"avatar"},picSrc:{type:String,required:!1,default:""}},setup:function(e){var t=e,i=r("/auth/");r(u);var p=c();return n((function(){return""===t.picSrc?""!==a("$..headerImg[0]",p.userInfo)[0]&&"http"===a("$..headerImg[0]",p.userInfo)[0].slice(0,4)?a("$..headerImg[0]",p.userInfo)[0]:i.value+a("$..headerImg[0]",p.userInfo)[0]:""!==t.picSrc&&"http"===t.picSrc.slice(0,4)?t.picSrc:i.value+t.picSrc})),n((function(){return t.picSrc&&"http"!==t.picSrc.slice(0,4)?i.value+t.picSrc:t.picSrc})),function(e,t){return null}}});e("C",i(p,[["__scopeId","data-v-7ac42d76"]]))}}}));
