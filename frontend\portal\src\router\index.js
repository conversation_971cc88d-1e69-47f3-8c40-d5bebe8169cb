import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'

const routes = [{
  path: '/',
  redirect: '/login',
}, {
  path: '/status',
  name: 'Status',
  component: () => import('@/view/login/wx/status.vue'),
}, {
  path: '/verify',
  name: 'verify',
  component: () => import('@/view/login/verify.vue'),
}, {
  path: '/login',
  name: 'Login',
  component: () => import('@/view/login/index.vue'),
}, {
  path: '/client',
  name: 'Client',
  component: () => import('@/view/client/index.vue'),
  children: [
    {
      path: '/client/login',
      name: 'ClientNewLogin',
      component: () => import('@/view/client/login.vue'),
    },
    {
      path: '/client/main',
      name: 'ClientMain',
      component: () => import('@/view/client/main.vue'),
    },
    {
      path: '/client/setting',
      name: 'ClientSetting',
      component: () => import('@/view/client/setting.vue'),
    },
  ]
}, {
  path: '/clientLogin',
  name: 'ClientLogin',
  component: () => import('@/view/login/clientLogin.vue'),
}, {
  path: '/downloadWin',
  name: 'downloadWin',
  component: () => import('@/view/login/downloadWin.vue'),
}, {
  path: '/wx_oauth_callback',
  name: 'WxOAuthCallback',
  component: () => import('@/view/login/wx/wx_oauth_callback.vue')
}, {
  path: '/oauth2_result',
  name: 'OAuth2Result',
  component: () => import('@/view/login/oauth2/oauth2_result.vue')
}, {
  path: '/oauth2_premises',
  name: 'OAuth2Premises',
  component: () => import('@/view/login/oauth2/oauth2_premises.vue')
}]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

router.beforeEach(async (to, from, next) => {
  
  const fullPath = window.location.href;
  const origin = window.location.origin;
  logger.log('Router beforeEach Current URL:', fullPath, 'origin:', origin);
  
  if (!fullPath.startsWith(origin + '/#/')) {
    console.log('Hash is not at the correct position');
    
    const hashIndex = fullPath.indexOf('#');
    let newUrl;
    
    if (hashIndex === -1) {
      // 如果没有哈希，将整个路径放在 #/ 后面
      newUrl = `${origin}/#${fullPath.substring(origin.length)}`;
    } else {
      // 如果有哈希，将哈希部分移到前面，其余部分跟在后面
      let beforeHash = fullPath.substring(origin.length, hashIndex);
      const afterHash = fullPath.substring(hashIndex);
      
      // 将开头的 '/?' 替换成 '&'
      beforeHash = beforeHash.replace(/^\/\?/, '&');
      
      console.log('beforeHash:', beforeHash);
      console.log('afterHash:', afterHash);
      
      newUrl = `${origin}/${afterHash}${beforeHash}`;
    }
    
    console.log('Final new URL:', newUrl);
    
    window.location.replace(newUrl);
    return;
  }
  
  logger.log('Proceeding with normal navigation');
  next();
});

export default router
