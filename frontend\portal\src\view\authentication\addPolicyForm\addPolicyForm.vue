<template>
  <div>
    <base-form
        label-position="right"
        label-width="100px"
        :model="formLabelAlign"
        style="max-width: 650px;"
    >
      <base-form-item
          label="状态："
          prop="state"
      >
        <base-radio-group v-model="radio" class="ml-4">
          <base-radio label="1" size="large">启用</base-radio>
          <base-radio label="2" size="large">禁用</base-radio>
        </base-radio-group>
      </base-form-item>
      <base-form-item class="custom-label" label="基础信息"></base-form-item>
      <base-form-item
          v-if="type !== 'user'"
          label="名称："
          prop="name"
          :rules="[
            {
              required:true,
              message:'名称不能为空',
              trigger: ['blur']
            }]"
      >
        <base-input v-model="formLabelAlign.name"/>
      </base-form-item>
      <base-form-item label="描述：" prop="description">
        <base-input v-model="formLabelAlign.description"/>
      </base-form-item>
      <base-form-item
          label="用户范围："
          :rules="[
            {
              required:true,
              message: '请选择组织',
              trigger: ['blur']
            }
          ]"
      >
        <base-select
            v-model="formLabelAlign.source"
            placeholder="请选择"
            style="width: 100%"
        >
          <base-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
          />
        </base-select>
      </base-form-item>
      <base-form-item
          label="认证策略"
          class="custom-label"
      >
      </base-form-item>
      <el-tabs style="margin-left: 100px;" v-model="terminal">
        <el-tab-pane label="PC端" name="pc">
          <div style="width: 100%;float: left">
            <p style="font-weight: 700;font-size: 14px;margin-bottom: 10px;margin-top: 10px">首次认证</p>
            <div
                style="border-radius: 5px;overflow:hidden;background-color: #C0E9FC;padding-top: 10px;width: 100%;height: auto"
            >
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="localState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-users-copy"></use>
                    </svg>
                  </div>
                  <span>本地账户</span>
                </base-card>
              </div>
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="ldapState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-teshuADyuzhanghaoshenqingliucheng"></use>
                    </svg>
                  </div>
                  <span>ldap/ad认证</span>
                </base-card>
              </div>
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="wxState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-icon_weixin-logo-copy"></use>
                    </svg>
                  </div>
                  <span>微信认证</span>
                </base-card>
              </div>
            </div>
          </div>
          <div style="width: 100%;float: left">
            <p style="font-weight: 700;font-size: 14px;margin-top: 20px;margin-bottom: 10px">二次认证</p>
            <div
                style="border-radius: 5px;overflow:hidden;background-color: #C0E9FC;padding-top: 10px;width: 100%;height: auto;margin-bottom: 10px"
            >
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="dxState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                      width="50px"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-duanxin-copy"></use>
                    </svg>
                  </div>
                  <span>短信认证</span>
                </base-card>
              </div>
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="otpState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-yanzhengma1-copy"></use>
                    </svg>
                  </div>
                  <span>OTP验证码</span>
                </base-card>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="移动端" name="mobile">
          <div style="width: 100%;float: left">
            <p style="font-weight: 700;font-size: 14px;margin-bottom: 10px;margin-top: 10px">首次认证</p>
            <div
                style="border-radius: 5px;overflow:hidden;background-color: #C0E9FC;padding-top: 10px;width: 100%;height: auto"
            >
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="localState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-users-copy"></use>
                    </svg>
                  </div>
                  <span>本地账户</span>
                </base-card>
              </div>
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="ldapState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-teshuADyuzhanghaoshenqingliucheng"></use>
                    </svg>
                  </div>
                  <span>ldap/ad认证</span>
                </base-card>
              </div>
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="wxState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-icon_weixin-logo-copy"></use>
                    </svg>
                  </div>
                  <span>微信认证</span>
                </base-card>
              </div>
            </div>
          </div>
          <div style="width: 100%;float: left">
            <p style="font-weight: 700;font-size: 14px;margin-top: 20px;margin-bottom: 10px">二次认证</p>
            <div
                style="border-radius: 5px;overflow:hidden;background-color: #C0E9FC;padding-top: 10px;width: 100%;height: auto;margin-bottom: 10px"
            >
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="dxState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                      width="50px"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-duanxin-copy"></use>
                    </svg>
                  </div>
                  <span>短信认证</span>
                </base-card>
              </div>
              <div>
                <base-card class="box-card">
                  <el-switch
                      v-model="otpState"
                      inline-prompt
                      active-text="开"
                      inactive-text="关"
                  />
                  <div>
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-yanzhengma1-copy"></use>
                    </svg>
                  </div>
                  <span>OTP验证码</span>
                </base-card>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </base-form>
    <span class="dialog-footer" style="padding-left: 65%">
      <base-button @click="submitForm()">取消</base-button>
      <base-button
          type="primary"
          @click="submitForm()"
          color="#256EBF"
      >确定</base-button>
    </span>
  </div>
</template>
<script>
export default {
  name: 'AddPolicyForm',
  props: ['type', 'formLabelAlign'],
}
</script>
<script setup>
import { reactive, ref } from 'vue'
import '../../../assets/ali/iconfont.css'
import '@/assets/ali/iconfont'

const type = ref('root')
const formLabelAlign = reactive({
  source: '',
  name: '',
  description: '',
  uniqueIdentification: '',
  state: '1',
})

const radio = ref('1')
const options = [
  {
    value: 'xs',
    label: '销售部门',
  },
  {
    value: 'yf',
    label: '研发部门',
  },
]

const terminal = ref('pc')
const localState = ref(true)
const ldapState = ref(false)
const wxState = ref(false)
const dxState = ref(true)
const otpState = ref(false)

const emits = defineEmits(['submitForm'])
const submitForm = () => {
  console.log(formLabelAlign)
  // 子组件值

  emits('submitForm', false)
}
</script>
<style lang="scss">
.box-card {
  width: 90px;
  float: left;
  margin-left: 10px;
  margin-bottom: 10px;
  text-align: center;

  .el-card__body {
    padding: 10px 10px !important;
  }

  span {
    font-size: 12px;
  }
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  //font-size: 30px;
}

.custom-label{
  div{
    font-size: 14px !important;
    font-weight: 700;
  }
}
</style>
