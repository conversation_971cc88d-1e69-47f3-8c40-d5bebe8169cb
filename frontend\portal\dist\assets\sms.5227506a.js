/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{r as e,u as a,a as t,b as s,t as n,l as i,i as l,w as o,e as u,d as r,m as d,n as c,M as p,j as _,o as m,k as f,_ as y}from"./index.bfaf04e1.js";const v={class:"sms"},h={key:0,style:{"margin-bottom":"20px"}},b={key:1,style:{"margin-bottom":"20px"}},g={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},k={style:{"text-align":"center"}},x=y(Object.assign({name:"Sms"},{props:{auth_info:{type:Object,default:function(){return{}}},auth_id:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}}},emits:["verification-success","back"],setup(y,{emit:x}){const w=e(""),I=d("userName");d("last_id");const N=d("isSecondary"),S=y,j=x,q=e(60);let C;const P=()=>{clearInterval(C)},V=async()=>{if(!S.auth_info.notPhone)return;const e={uniq_key:S.auth_info.uniqKey,idp_id:S.auth_id},a=await c(e);200===a.status&&-1!==a.data.code?(q.value=60,C=setInterval((()=>{q.value--,0===q.value&&P()}),1e3)):(p({showClose:!0,message:a.data.msg,type:"error"}),q.value=0)};V();const z=a(),K=async()=>{const e={uniq_key:S.auth_info.uniqKey,auth_code:w.value,user_name:S.userName||I.value,idp_id:S.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"},a=await z.LoginIn(e,"accessory");-1!==a.code&&j("verification-success",a)},O=()=>{j("back"),N&&(N.value=!1)};return(e,a)=>{const d=_("base-button"),c=_("base-input");return m(),t("div",v,[a[3]||(a[3]=s("div",{style:{top:"10px","margin-bottom":"25px","text-align":"center"}},[s("span",{class:"title"},"短信认证")],-1)),s("div",null,[y.auth_info.notPhone?(m(),t("div",h,"验证码已发送至您账号("+n(y.userName||i(I))+")关联的手机，请注意查收",1)):(m(),t("div",b,"您的账号("+n(y.userName||i(I))+")未关联手机号码，请联系管理员！",1)),y.auth_info.notPhone?(m(),t("div",g,[l(c,{modelValue:w.value,"onUpdate:modelValue":a[0]||(a[0]=e=>w.value=e),placeholder:"短信验证码",class:"input-with-select"},{append:o((()=>[l(d,{type:"info",disabled:q.value>0,onClick:V},{default:o((()=>[f("重新发送 "+n(q.value>0?`(${q.value}秒)`:""),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])):u("",!0),s("div",k,[y.auth_info.notPhone?(m(),r(d,{key:0,type:"primary",size:"large",disabled:!w.value,onClick:K},{default:o((()=>a[1]||(a[1]=[f("确 定 ")]))),_:1,__:[1]},8,["disabled"])):u("",!0),l(d,{type:"info",size:"large",onClick:O},{default:o((()=>a[2]||(a[2]=[f("取 消 ")]))),_:1,__:[2]})])])])}}}),[["__scopeId","data-v-403e93dc"]]);export{x as default};
