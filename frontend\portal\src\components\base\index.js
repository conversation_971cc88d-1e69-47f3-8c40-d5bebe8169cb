// 基础组件全局注册
import Button from './Button.vue'
import Input from './Input.vue'
import Form from './Form.vue'
import FormItem from './FormItem.vue'
import Container from './Container.vue'
import Aside from './Aside.vue'
import Main from './Main.vue'
import Row from './Row.vue'
import Col from './Col.vue'
import Divider from './Divider.vue'
import Avatar from './Avatar.vue'
import Carousel from './Carousel.vue'
import CarouselItem from './CarouselItem.vue'
import Card from './Card.vue'
import Timeline from './Timeline.vue'
import TimelineItem from './TimelineItem.vue'
import Select from './Select.vue'
import Option from './Option.vue'
import Checkbox from './Checkbox.vue'
import Radio from './Radio.vue'
import RadioGroup from './RadioGroup.vue'
import Icon from './Icon.vue'
import SvgIcon from './SvgIcon.vue'
import { Loading } from './Loading.js'
import { Message } from './Message.js'
import { MessageBox } from './MessageBox.js'

const components = {
  'base-button': Button,
  'base-input': Input,
  'base-form': Form,
  'base-form-item': FormItem,
  'base-container': Container,
  'base-aside': Aside,
  'base-main': Main,
  'base-row': Row,
  'base-col': Col,
  'base-divider': Divider,
  'base-avatar': Avatar,
  'base-carousel': Carousel,
  'base-carousel-item': CarouselItem,
  'base-card': Card,
  'base-timeline': Timeline,
  'base-timeline-item': TimelineItem,
  'base-select': Select,
  'base-option': Option,
  'base-checkbox': Checkbox,
  'base-radio': Radio,
  'base-radio-group': RadioGroup,
  'base-icon': Icon,
  'svg-icon': SvgIcon
}

export default {
  install(app) {
    Object.keys(components).forEach(key => {
      app.component(key, components[key])
    })

    // 注册全局属性
    app.config.globalProperties.$loading = Loading
    app.config.globalProperties.$message = Message
    app.config.globalProperties.$messageBox = MessageBox
  }
}

// 导出单个组件
export {
  Button,
  Input,
  Form,
  FormItem,
  Container,
  Aside,
  Main,
  Row,
  Col,
  Divider,
  Avatar,
  Carousel,
  CarouselItem,
  Card,
  Timeline,
  TimelineItem,
  Select,
  Option,
  Checkbox,
  Radio,
  RadioGroup,
  Icon,
  SvgIcon,
  Loading,
  Message,
  MessageBox
}
