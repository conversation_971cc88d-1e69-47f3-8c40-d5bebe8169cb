{"name": "asec-portal", "version": "2.5.3", "private": true, "scripts": {"serve": "pnpm vite --host --mode development", "build": "node --max-old-space-size=8192 ./node_modules/.bin/vite build --mode production", "build-local": "pnpm vite build --mode production", "build-dev": "pnpm vite build --mode development", "preview": "pnpm vite preview", "clean": "rimraf node_modules **/node_modules", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@vue/runtime-core": "^3.2.41", "axios": "^0.19.2", "core-js": "^3.6.5", "dayjs": "^1.11.13", "gdt-jsapi": "^1.9.51", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "json-bigint": "^1.0.0", "jsonpath-plus": "^7.2.0", "mitt": "^3.0.0", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.0.9", "qrcode": "^1.5.3", "qs": "^6.8.0", "screenfull": "^5.0.2", "vue": "^3.2.25", "vue-clipboard3": "^2.0.0", "vue-router": "^4.0.0-0", "vuejs-logger": "^1.5.5"}, "devDependencies": {"@vitejs/plugin-legacy": "^2.0.0", "@vitejs/plugin-vue": "^3.0.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.1.5", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "chalk": "^4.1.2", "dotenv": "^10.0.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "rimraf": "^5.0.5", "sass": "^1.54.0", "terser": "^5.4.0", "vite": "^3.1.0", "vite-plugin-banner": "^0.1.3", "vite-plugin-html": "^3.2.0", "vite-plugin-importer": "^0.2.5"}, "engines": {"node": ">=16", "pnpm": ">=8"}, "packageManager": "pnpm@10.7.1+sha512.2d92c86b7928dc8284f53494fb4201f983da65f0fb4f0d40baafa5cf628fa31dae3e5968f12466f17df7e97310e30f343a648baea1b9b350685dafafffdf5808"}