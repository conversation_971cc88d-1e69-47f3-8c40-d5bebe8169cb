/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,l,a="function"==typeof Symbol?Symbol:{},r=a.iterator||"@@iterator",u=a.toStringTag||"@@toStringTag";function o(t,a,r,u){var o=a&&a.prototype instanceof c?a:c,f=Object.create(o.prototype);return n(f,"_invoke",function(t,n,a){var r,u,o,c=0,f=a||[],d=!1,p={p:0,n:0,v:e,a:s,f:s.bind(e,4),d:function(t,n){return r=t,u=0,o=e,p.n=n,i}};function s(t,n){for(u=t,o=n,l=0;!d&&c&&!a&&l<f.length;l++){var a,r=f[l],s=p.p,m=r[2];t>3?(a=m===n)&&(o=r[(u=r[4])?5:(u=3,3)],r[4]=r[5]=e):r[0]<=s&&((a=t<2&&s<r[1])?(u=0,p.v=n,p.n=r[1]):s<m&&(a=t<3||r[0]>n||n>m)&&(r[4]=t,r[5]=n,p.n=m,u=0))}if(a||t>1)return i;throw d=!0,n}return function(a,f,m){if(c>1)throw TypeError("Generator is already running");for(d&&1===f&&s(f,m),u=f,o=m;(l=u<2?e:o)||!d;){r||(u?u<3?(u>1&&(p.n=-1),s(u,o)):p.n=o:p.v=o);try{if(c=2,r){if(u||(a="next"),l=r[a]){if(!(l=l.call(r,o)))throw TypeError("iterator result is not an object");if(!l.done)return l;o=l.value,u<2&&(u=0)}else 1===u&&(l=r.return)&&l.call(r),u<2&&(o=TypeError("The iterator does not provide a '"+a+"' method"),u=1);r=e}else if((l=(d=p.n<0)?o:t.call(n,p))!==i)break}catch(l){r=e,u=1,o=l}finally{c=1}}return{value:l,done:d}}}(t,r,u),!0),f}var i={};function c(){}function f(){}function d(){}l=Object.getPrototypeOf;var p=[][r]?l(l([][r]())):(n(l={},r,(function(){return this})),l),s=d.prototype=c.prototype=Object.create(p);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,n(e,u,"GeneratorFunction")),e.prototype=Object.create(s),e}return f.prototype=d,n(s,"constructor",d),n(d,"constructor",f),f.displayName="GeneratorFunction",n(d,u,"GeneratorFunction"),n(s),n(s,u,"Generator"),n(s,r,(function(){return this})),n(s,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:o,m:m}})()}function n(e,t,l,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}n=function(e,t,l,a){if(t)r?r(e,t,{value:l,enumerable:!a,configurable:!a,writable:!a}):e[t]=l;else{var u=function(t,l){n(e,t,(function(e){return this._invoke(t,l,e)}))};u("next",0),u("throw",1),u("return",2)}},n(e,t,l,a)}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function r(t,n,l){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var l=t[Symbol.toPrimitive];if(void 0!==l){var a=l.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:l,enumerable:!0,configurable:!0,writable:!0}):t[n]=l,t}function u(e,t,n,l,a,r,u){try{var o=e[r](u),i=o.value}catch(e){return void n(e)}o.done?t(i):Promise.resolve(i).then(l,a)}function o(e){return function(){var t=this,n=arguments;return new Promise((function(l,a){var r=e.apply(t,n);function o(e){u(r,l,a,o,i,"next",e)}function i(e){u(r,l,a,o,i,"throw",e)}o(void 0)}))}}System.register(["./index-legacy.dbc04544.js","./icon-legacy.64e17c4a.js","./warningBar-legacy.4145d360.js","./authorityBtn-legacy.a84008d4.js"],(function(e,n){"use strict";var l,r,u,i,c,f,d,p,s,m,v,b,y,h,w,g,_,V,k,I,x,j,D,O,U=document.createElement("style");return U.textContent='@charset "UTF-8";.warning[data-v-635be047]{color:#dc143c}.icon-column[data-v-635be047]{display:flex;align-items:center}.icon-column .el-icon[data-v-635be047]{margin-right:8px}\n',document.head.appendChild(U),{setters:[function(e){l=e._,r=e.B,u=e.r,i=e.h,c=e.o,f=e.d,d=e.e,p=e.j,s=e.w,m=e.k,v=e.t,b=e.f,y=e.z,h=e.g,w=e.a8,g=e.P,_=e.a9,V=e.M,k=e.aa,I=e.ab,x=e.ac},function(e){j=e.default},function(e){D=e.W},function(e){O=e.c}],execute:function(){var n={class:"gva-table-box"},U={class:"gva-btn-list"},P={key:0,class:"icon-column"},S={style:{display:"inline-flex"}},C={class:"dialog-footer"},z=Object.assign({name:"Menus"},{setup:function(e){var l=r({path:[{required:!0,message:"请输入菜单name",trigger:"blur"}],component:[{required:!0,message:"请输入文件路径",trigger:"blur"}],"meta.title":[{required:!0,message:"请输入菜单展示名称",trigger:"blur"}]}),z=u(1),T=u(0),B=u(999),E=u([]),q=u({}),A=function(){var e=o(t().m((function e(){var n;return t().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,w(a({page:z.value,pageSize:B.value},q.value));case 1:0===(n=e.v).code&&(E.value=n.data.list,T.value=n.data.total,z.value=n.data.page,B.value=n.data.pageSize);case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();A();var F=function(){N.value.component=N.value.component.replace(/\\/g,"/")},G=function(){var e=o(t().m((function e(n,l){var a;return t().w((function(e){for(;;)switch(e.n){case 0:if(0!==(a=n[l]).ID){e.n=1;break}return n.splice(l,1),e.a(2);case 1:return e.n=2,O({id:a.ID});case 2:if(0!==e.v.code){e.n=3;break}return n.splice(l,1),e.a(2);case 3:return e.a(2)}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),N=u({ID:0,path:"",name:"",hidden:"",parentId:"",component:"",meta:{title:"",icon:"",defaultMenu:!1,closeTab:!1,keepAlive:!1},parameters:[],menuBtn:[]}),M=function(){N.value.path=N.value.name},$=function(e){W(),e()},H=u(null),K=u(!1),W=function(){K.value=!1,H.value.resetFields(),N.value={ID:0,path:"",name:"",hidden:"",parentId:"",component:"",meta:{title:"",icon:"",defaultMenu:!1,keepAlive:""}}},J=u(!1),L=function(){W(),J.value=!1},Q=function(){var e=o(t().m((function e(){return t().w((function(e){for(;;)switch(e.n){case 0:H.value.validate(function(){var e=o(t().m((function e(n){var l;return t().w((function(e){for(;;)switch(e.n){case 0:if(!n){e.n=5;break}if(!Z.value){e.n=2;break}return e.n=1,k(N.value);case 1:l=e.v,e.n=4;break;case 2:return e.n=3,I(N.value);case 3:l=e.v;case 4:0===l.code&&(V({type:"success",message:Z.value?"编辑成功":"添加成功!"}),A()),W(),J.value=!1;case 5:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),R=u([{ID:"0",title:"根菜单"}]),X=function(){R.value=[{ID:"0",title:"根目录"}],Y(E.value,R.value,!1)},Y=function(e,t,n){e&&e.forEach((function(e){if(e.children&&e.children.length){var l={title:e.meta.title,ID:String(e.ID),disabled:n||e.ID===N.value.ID,children:[]};Y(e.children,l.children,n||e.ID===N.value.ID),t.push(l)}else{var a={title:e.meta.title,ID:String(e.ID),disabled:n||e.ID===N.value.ID};t.push(a)}}))},Z=u(!1),ee=u("新增菜单"),te=function(e){ee.value="新增菜单",N.value.parentId=String(e),Z.value=!1,X(),J.value=!0},ne=function(){var e=o(t().m((function e(n){var l;return t().w((function(e){for(;;)switch(e.n){case 0:return ee.value="编辑菜单",e.n=1,x({id:n});case 1:l=e.v,N.value=l.data.menu,Z.value=!0,X(),J.value=!0;case 2:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}();return function(e,a){var r=i("base-button"),u=i("el-table-column"),w=i("el-icon"),k=i("el-table"),I=i("base-input"),x=i("base-form-item"),O=i("base-checkbox"),T=i("base-option"),B=i("base-select"),q=i("el-cascader"),W=i("base-form"),X=i("el-dialog");return c(),f("div",null,[d("div",n,[d("div",U,[p(r,{size:"small",type:"primary",icon:"plus",onClick:a[0]||(a[0]=function(e){return te("0")})},{default:s((function(){return a[15]||(a[15]=[m("新增根菜单")])})),_:1,__:[15]})]),p(k,{data:E.value,"row-key":"ID"},{default:s((function(){return[p(u,{align:"left",label:"ID","min-width":"100",prop:"ID"}),p(u,{align:"left",label:"展示名称","min-width":"120",prop:"authorityName"},{default:s((function(e){return[d("span",null,v(e.row.meta.title),1)]})),_:1}),p(u,{align:"left",label:"图标","min-width":"140",prop:"authorityName"},{default:s((function(e){return[e.row.meta.icon?(c(),f("div",P,[p(w,null,{default:s((function(){return[(c(),b(y(e.row.meta.icon)))]})),_:2},1024),d("span",null,v(e.row.meta.icon),1)])):h("",!0)]})),_:1}),p(u,{align:"left",label:"路由Name","show-overflow-tooltip":"","min-width":"160",prop:"name"}),p(u,{align:"left",label:"路由Path","show-overflow-tooltip":"","min-width":"160",prop:"path"}),p(u,{align:"left",label:"是否隐藏","min-width":"100",prop:"hidden"},{default:s((function(e){return[d("span",null,v(e.row.hidden?"隐藏":"显示"),1)]})),_:1}),p(u,{align:"left",label:"父节点","min-width":"90",prop:"parentId"}),p(u,{align:"left",label:"排序","min-width":"70",prop:"sort"}),p(u,{align:"left",label:"文件路径","min-width":"360",prop:"component"}),p(u,{align:"left",fixed:"right",label:"操作",width:"300"},{default:s((function(e){return[p(r,{size:"small",type:"primary",link:"",icon:"plus",onClick:function(t){return te(e.row.ID)}},{default:s((function(){return a[16]||(a[16]=[m("添加子菜单")])})),_:2,__:[16]},1032,["onClick"]),p(r,{size:"small",type:"primary",link:"",icon:"edit",onClick:function(t){return ne(e.row.ID)}},{default:s((function(){return a[17]||(a[17]=[m("编辑")])})),_:2,__:[17]},1032,["onClick"]),p(r,{size:"small",type:"primary",link:"",icon:"delete",onClick:function(n){return l=e.row.ID,void g.confirm("此操作将永久删除所有角色下该菜单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(o(t().m((function e(){return t().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,_({ID:l});case 1:0===e.v.code&&(V({type:"success",message:"删除成功!"}),1===E.value.length&&z.value>1&&z.value--,A());case 2:return e.a(2)}}),e)})))).catch((function(){V({type:"info",message:"已取消删除"})}));var l}},{default:s((function(){return a[18]||(a[18]=[m("删除")])})),_:2,__:[18]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])]),p(X,{modelValue:J.value,"onUpdate:modelValue":a[14]||(a[14]=function(e){return J.value=e}),"before-close":$,title:ee.value},{footer:s((function(){return[d("div",C,[p(r,{size:"small",onClick:L},{default:s((function(){return a[27]||(a[27]=[m("取 消")])})),_:1,__:[27]}),p(r,{size:"small",type:"primary",onClick:Q},{default:s((function(){return a[28]||(a[28]=[m("确 定")])})),_:1,__:[28]})])]})),default:s((function(){return[p(D,{title:"新增菜单，需要在角色管理内配置权限才可使用"}),J.value?(c(),b(W,{key:0,ref_key:"menuForm",ref:H,inline:!0,model:N.value,rules:l,"label-position":"top","label-width":"85px"},{default:s((function(){return[p(x,{label:"路由Name",prop:"path",style:{width:"30%"}},{default:s((function(){return[p(I,{modelValue:N.value.name,"onUpdate:modelValue":a[1]||(a[1]=function(e){return N.value.name=e}),autocomplete:"off",placeholder:"唯一英文字符串",onChange:M},null,8,["modelValue"])]})),_:1}),p(x,{prop:"path",style:{width:"30%"}},{label:s((function(){return[d("div",S,[a[20]||(a[20]=m(" 路由Path ")),p(O,{modelValue:K.value,"onUpdate:modelValue":a[2]||(a[2]=function(e){return K.value=e}),style:{float:"right","margin-left":"20px"}},{default:s((function(){return a[19]||(a[19]=[m("添加参数")])})),_:1,__:[19]},8,["modelValue"])])]})),default:s((function(){return[p(I,{modelValue:N.value.path,"onUpdate:modelValue":a[3]||(a[3]=function(e){return N.value.path=e}),disabled:!K.value,autocomplete:"off",placeholder:"建议只在后方拼接参数"},null,8,["modelValue","disabled"])]})),_:1}),p(x,{label:"是否隐藏",style:{width:"30%"}},{default:s((function(){return[p(B,{modelValue:N.value.hidden,"onUpdate:modelValue":a[4]||(a[4]=function(e){return N.value.hidden=e}),placeholder:"是否在列表隐藏"},{default:s((function(){return[p(T,{value:!1,label:"否"}),p(T,{value:!0,label:"是"})]})),_:1},8,["modelValue"])]})),_:1}),p(x,{label:"父节点ID",style:{width:"30%"}},{default:s((function(){return[p(q,{modelValue:N.value.parentId,"onUpdate:modelValue":a[5]||(a[5]=function(e){return N.value.parentId=e}),style:{width:"100%"},disabled:!Z.value,options:R.value,props:{checkStrictly:!0,label:"title",value:"ID",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])]})),_:1}),p(x,{label:"文件路径",prop:"component",style:{width:"60%"}},{default:s((function(){return[p(I,{modelValue:N.value.component,"onUpdate:modelValue":a[6]||(a[6]=function(e){return N.value.component=e}),autocomplete:"off",placeholder:"页面:view/xxx/xx.vue 插件:plugin/xx/xx.vue",onBlur:F},null,8,["modelValue"]),a[22]||(a[22]=d("span",{style:{"font-size":"12px","margin-right":"12px"}},"如果菜单包含子菜单，请创建router-view二级路由页面或者",-1)),p(r,{style:{"margin-top":"4px"},size:"small",onClick:a[7]||(a[7]=function(e){return N.value.component="view/routerHolder.vue"})},{default:s((function(){return a[21]||(a[21]=[m("点我设置")])})),_:1,__:[21]})]})),_:1,__:[22]}),p(x,{label:"展示名称",prop:"meta.title",style:{width:"30%"}},{default:s((function(){return[p(I,{modelValue:N.value.meta.title,"onUpdate:modelValue":a[8]||(a[8]=function(e){return N.value.meta.title=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),p(x,{label:"图标",prop:"meta.icon",style:{width:"30%"}},{default:s((function(){return[p(j,{meta:N.value.meta,style:{width:"100%"}},null,8,["meta"])]})),_:1}),p(x,{label:"排序标记",prop:"sort",style:{width:"30%"}},{default:s((function(){return[p(I,{modelValue:N.value.sort,"onUpdate:modelValue":a[9]||(a[9]=function(e){return N.value.sort=e}),modelModifiers:{number:!0},autocomplete:"off"},null,8,["modelValue"])]})),_:1}),p(x,{label:"KeepAlive",prop:"meta.keepAlive",style:{width:"30%"}},{default:s((function(){return[p(B,{modelValue:N.value.meta.keepAlive,"onUpdate:modelValue":a[10]||(a[10]=function(e){return N.value.meta.keepAlive=e}),style:{width:"100%"},placeholder:"是否keepAlive缓存页面"},{default:s((function(){return[p(T,{value:!1,label:"否"}),p(T,{value:!0,label:"是"})]})),_:1},8,["modelValue"])]})),_:1}),p(x,{label:"CloseTab",prop:"meta.closeTab",style:{width:"30%"}},{default:s((function(){return[p(B,{modelValue:N.value.meta.closeTab,"onUpdate:modelValue":a[11]||(a[11]=function(e){return N.value.meta.closeTab=e}),style:{width:"100%"},placeholder:"是否自动关闭tab"},{default:s((function(){return[p(T,{value:!1,label:"否"}),p(T,{value:!0,label:"是"})]})),_:1},8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])):h("",!0),d("div",null,[p(r,{size:"small",type:"primary",icon:"edit",onClick:a[12]||(a[12]=function(e){return function(e){e.parameters||(e.parameters=[]),e.parameters.push({type:"query",key:"",value:""})}(N.value)})},{default:s((function(){return a[23]||(a[23]=[m("新增菜单参数")])})),_:1,__:[23]}),p(k,{data:N.value.parameters,style:{width:"100%"}},{default:s((function(){return[p(u,{align:"left",prop:"type",label:"参数类型",width:"180"},{default:s((function(e){return[p(B,{modelValue:e.row.type,"onUpdate:modelValue":function(t){return e.row.type=t},placeholder:"请选择"},{default:s((function(){return[p(T,{key:"query",value:"query",label:"query"}),p(T,{key:"params",value:"params",label:"params"})]})),_:2},1032,["modelValue","onUpdate:modelValue"])]})),_:1}),p(u,{align:"left",prop:"key",label:"参数key",width:"180"},{default:s((function(e){return[d("div",null,[p(I,{modelValue:e.row.key,"onUpdate:modelValue":function(t){return e.row.key=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),p(u,{align:"left",prop:"value",label:"参数值"},{default:s((function(e){return[d("div",null,[p(I,{modelValue:e.row.value,"onUpdate:modelValue":function(t){return e.row.value=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),p(u,{align:"left"},{default:s((function(e){return[d("div",null,[p(r,{type:"danger",size:"small",icon:"delete",onClick:function(t){return n=N.value.parameters,l=e.$index,void n.splice(l,1);var n,l}},{default:s((function(){return a[24]||(a[24]=[m("删除")])})),_:2,__:[24]},1032,["onClick"])])]})),_:1})]})),_:1},8,["data"]),p(r,{style:{"margin-top":"12px"},size:"small",type:"primary",icon:"edit",onClick:a[13]||(a[13]=function(e){return function(e){e.menuBtn||(e.menuBtn=[]),e.menuBtn.push({name:"",desc:""})}(N.value)})},{default:s((function(){return a[25]||(a[25]=[m("新增可控按钮")])})),_:1,__:[25]}),p(k,{data:N.value.menuBtn,style:{width:"100%"}},{default:s((function(){return[p(u,{align:"left",prop:"name",label:"按钮名称",width:"180"},{default:s((function(e){return[d("div",null,[p(I,{modelValue:e.row.name,"onUpdate:modelValue":function(t){return e.row.name=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),p(u,{align:"left",prop:"name",label:"备注",width:"180"},{default:s((function(e){return[d("div",null,[p(I,{modelValue:e.row.desc,"onUpdate:modelValue":function(t){return e.row.desc=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),p(u,{align:"left"},{default:s((function(e){return[d("div",null,[p(r,{type:"danger",size:"small",icon:"delete",onClick:function(t){return G(N.value.menuBtn,e.$index)}},{default:s((function(){return a[26]||(a[26]=[m("删除")])})),_:2,__:[26]},1032,["onClick"])])]})),_:1})]})),_:1},8,["data"])])]})),_:1},8,["modelValue","title"])])}}});e("default",l(z,[["__scopeId","data-v-635be047"]]))}}}))}();
