/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{_ as e,o as n,d as r,e as s}from"./index.74d1ee23.js";const i=""+new URL("notFound.4e921f05.png",import.meta.url).href;const t=e({name:"Error"},[["render",function(e,t,l,o,a,d){return n(),r("div",null,t[0]||(t[0]=[s("div",{class:"big"},[s("div",{class:"inner"},[s("img",{src:i}),s("p",null,"未知错误"),s("p",{style:{"font-size":"18px","line-height":"40px"}},"常见问题为当前此角色无访问权限，如果确定要使用，请联系管理员进行分配"),s("p",null,"↓")])],-1)]))}]]);export{t as default};
