
<template>
  <base-form
    ref="loginForm"
    :model="loginFormData"
    :rules="rules"
    :validate-on-rule-change="false"
    @keyup.enter="submitForm"
  >
    <base-form-item prop="user_name">
      <span>账号</span>
      <base-input
        v-model="loginFormData.user_name"
        size="large"
        placeholder="请输入用户名"
        suffix-icon="user"
      />
    </base-form-item>
    <base-form-item prop="password">
      <span>密码</span>
      <base-input
        v-model="loginFormData.password"
        show-password
        size="large"
        type="password"
        placeholder="请输入密码"
      />
    </base-form-item>
    <base-form-item>
      <base-button
        type="primary"
        size="large"
        class="login_submit_button"
        @click="submitForm"
      >登 录</base-button>
    </base-form-item>
  </base-form>
</template>

<script>
export default {
  name: 'LocalLogin',
}
</script>

<script setup>
import { reactive, ref, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/pinia/modules/user'
import JSEncrypt from 'jsencrypt'
import auth from '@/directive/auth'
import _ from 'lodash'

// 获取id用于登录接口参数
const props = defineProps({
  auth_id: {
    type: String,
    default: function() {
      return ''
    },
  },
  auth_info: {
    type: Object,
    default: function() {
      return []
    },
  }
})
// 验证函数
const checkUsername = (rule, value, callback) => {
  if (value.length < 5) {
    return callback(new Error('请输入正确的用户名'))
  } else {
    callback()
  }
}
const checkPassword = (rule, value, callback) => {
  if (value.length < 6) {
    return callback(new Error('请输入正确的密码'))
  } else {
    callback()
  }
}

// 登录相关操作
const loginForm = ref(null)
const loginFormData = reactive({
  user_name: '',
  password: '',
  idp_id: props.auth_id,
  redirect_uri: 'hello world',
  grant_type: 'implicit',
  client_id: 'client_portal'
})
const rules = reactive({
  user_name: [{ required: true, trigger: 'change', message: '用户名不能为空' }],
  password: [{ required: true, trigger: 'change', message: '密码不能为空' }],
})

const userStore = useUserStore()
const secondary = inject('secondary')
const isSecondary = inject('isSecondary')
const uniqKey = inject('uniqKey')
const userName = inject('userName') 
const contactType = inject('contactType')
const hasContactInfo = inject('hasContactInfo')
const login = async() => {
  console.log({ idp_id: props.auth_id })
  loginFormData.idp_id = props.auth_id
  const crypt = new JSEncrypt()
  crypt.setPublicKey(`-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA52nU2J3CmT/UsKy2oKYp
g7GyY/wn6T/cymNFrHFGjwpdzYQ0W+wZS75JNPOVvUPYu5zLFsr3FnfddXrBpxo7
ctNYaPAO9maCqo8WfmE5lA04av4trueA0Qd31OVVeBOfxvSkZxMevOneioxFqVh5
yO9meOc01oKzpQ6m8qLYh3Ru4/GUus9XABkV1ue7Ll1Owxj4h0ovXTZN2rVpyrNU
vr+OZeaKA+aMqv2t4woehMuj9hDU9t79mjmVCEJVTPjf051cBFpQawAPUzmMIDWU
Ez3OalPwD03+pHubn80+x+FN94wNK2VV5KtXxwx2g7ZfHGWfY3AwPaJ/uh7cDg/z
WQIDAQAB
-----END PUBLIC KEY-----`)
  const loginData = _.cloneDeep(loginFormData)

  loginData.password = crypt.encrypt(loginFormData.password)
  loginData.user_name = crypt.encrypt(loginFormData.user_name)

  if (props.auth_info.authType === 'msad' || props.auth_info.authType === 'ldap') {
    loginData.ad_pwd = loginData.password
    loginData.ad_username = loginData.user_name
    delete loginData.password
    delete loginData.user_name
  }
  const res = await userStore.LoginIn(loginData, props.auth_info.authType, props.auth_id)
  if (res.isSecondary) {
    isSecondary.value = res.isSecondary
    secondary.value = res.secondary
    uniqKey.value = res.uniqKey
    userName.value = loginFormData.user_name
    contactType.value = res.contactType
    hasContactInfo.value = res.hasContactInfo || false
  }
}

const submitForm = () => {
  loginForm.value.validate(async(v) => {
    if (v) {
      const flag = await login()
    } else {
      ElMessage({
        type: 'error',
        message: '用户名密码不能为空',
        showClose: true,
      })
      return false
    }
  })
}

</script>

<style lang="css">
@import "@/style/index.css";
</style>
