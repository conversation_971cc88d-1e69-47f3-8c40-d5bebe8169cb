<template>
  <div class="log">
    <el-tabs v-model="activeName" class="log-tabs">
      <el-tab-pane label="应用访问日志" name="app">
        <el-header>
          <!--          <base-button color="#2972C8" plain class="iconfont icon-daochu">导出</base-button>-->
          <base-button color="#2972C8" plain class="iconfont icon-shuaxin" @click="getTableData">刷新</base-button>
          <base-input
              v-model="searchUser"
              class="w-50 m-2 organize-search"
              placeholder="用户名、应用名、终端"
              :suffix-icon="Search"
              style="width: 15%;float: right"
              @change="getTableData"
          />
          <div style="float: right;margin-right: 12px">
            <el-popover popper-class="custom-popover" :show-arrow="false" :width="20" trigger="click">
              <template #reference>
                <base-button :icon="Filter" style="border-radius: 4px">筛选</base-button>
              </template>
              <div
                  style="color: #252631;font-weight:400;border-bottom: 1px #EBEBEB solid;text-align: center;height: 38px;line-height: 38px"
              >
                选择显示项
              </div>
              <div style="padding-left: 17px">
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isMACHidden">MAC</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isSystemTypeHidden">系统类型</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isVersionsHidden">版本</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isCPUHidden">CPU</base-checkbox>
                <base-checkbox style="margin-top: 10px;margin-bottom: 10px;width: 100%" v-model="isRAMHidden">内存
                </base-checkbox>
              </div>
            </el-popover>
          </div>
          <base-select v-if="activeName ==='login'" v-model="operationType" class="m-2"
                     style="float: right;margin-right: 12px;height: 32px;width: 105px" placeholder="操作类型"
          >
            <base-option key="login" label="用户登陆" value="LOGIN"/>
            <base-option key="logout" label="用户注销" value="LOGOUT"/>
          </base-select>
          <base-select v-if="activeName ==='operation'" v-model="operationType" class="m-2"
                     style="float: right;margin-right: 12px;height: 32px;width: 105px" placeholder="操作类型"
          >
            <base-option key="login" label="用户登陆" value="LOGIN"/>
            <base-option key="logout" label="用户注销" value="LOGOUT"/>
          </base-select>
          <el-date-picker
              v-model="dateValue"
              type="datetimerange"
              :shortcuts="shortcuts"
              range-separator="~"
              style="float: right;margin-right: 12px"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="getTableData"
          />
        </el-header>
        <base-main>
          <CustomTable
              :table-data="tableData"
              v-bind="appTableConfig"
          >
            <template #AccessTime="scope">
              <div style="height: 40px">
                <span
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >{{ dayjs(scope.row.AccessTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </div>
            </template>
            <template #DstPort="scope">
              <div style="height: 40px">
                <el-link style="font-size: 12px;color: #2972C8;font-family: HarmonyOS_Medium" :underline="false">
                  {{ scope.row.DstPort }}
                </el-link>
              </div>
            </template>
            <template #StrategyName="scope">
              <div style="height: 40px">
                <el-link style="font-size: 12px;color: #2972C8;font-family: HarmonyOS_Medium" :underline="false">
                  {{ scope.row.StrategyName }}
                </el-link>
              </div>
            </template>
            <template #Status="scope">
              <div style="height: 40px">
                <el-link :underline="false"
                         style="font-size: 12px;font-family: HarmonyOS_Medium"
                         :style="{color:scope.row.Status>1?'#D23030':'#2972C8'}"
                >{{ scope.row.Status === 1 ? '放通' : '拦截' }}
                </el-link>
              </div>
            </template>
          </CustomTable>
        </base-main>
      </el-tab-pane>
      <el-tab-pane label="管理员操作日志" name="operation">
        <el-header>
          <base-button color="#2972C8" plain class="iconfont icon-daochu">导出</base-button>
          <base-button color="#2972C8" plain class="iconfont icon-shuaxin" @click="getTableData">刷新</base-button>
          <base-input
              v-model="searchUser"
              class="w-50 m-2 organize-search"
              placeholder="用户名、应用名、终端"
              :suffix-icon="Search"
              style="width: 15%;float: right"
              @change="getTableData"
          />
          <div style="float: right;margin-right: 12px">
            <el-popover popper-class="custom-popover" :show-arrow="false" :width="20" trigger="click">
              <template #reference>
                <base-button :icon="Filter" style="border-radius: 4px">筛选</base-button>
              </template>
              <div
                  style="color: #252631;font-weight:400;border-bottom: 1px #EBEBEB solid;text-align: center;height: 38px;line-height: 38px"
              >
                选择显示项
              </div>
              <div style="padding-left: 17px">
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isMACHidden">MAC</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isSystemTypeHidden">系统类型</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isVersionsHidden">版本</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isCPUHidden">CPU</base-checkbox>
                <base-checkbox style="margin-top: 10px;margin-bottom: 10px;width: 100%" v-model="isRAMHidden">内存
                </base-checkbox>
              </div>
            </el-popover>
          </div>
          <base-select v-if="activeName!=='app'" v-model="operationType" class="m-2"
                     style="float: right;margin-right: 12px;height: 32px;width: 105px" placeholder="操作类型"
          >
            <base-option
                v-for="item in typeOptions"
                :key="item.resource_type"
                :label="item.name"
                :value="item.resource_type"
                :disabled="item.disabled"
            />
          </base-select>
          <el-date-picker
              v-model="dateValue"
              type="datetimerange"
              :shortcuts="shortcuts"
              range-separator="~"
              style="float: right;margin-right: 12px"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="getTableData"
          />
        </el-header>
        <base-main>
          <CustomTable
              :table-data="tableData"
              v-bind="operationTableConfig"
          >
            <template #operate_time="scope">
              <div style="height: 40px">
                <span
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >{{ dayjs(scope.row.operate_time).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </div>
            </template>
            <template #error="scope">
              <div style="height: 40px">
                <span
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >{{ scope.row.error ? '失败' : '成功' }}</span>
              </div>
            </template>
            <template #ip="scope">
              <div style="height: 40px">
                <el-link style="font-size: 12px;color: #2972C8;font-family: HarmonyOS_Medium" :underline="false">
                  {{ scope.row.ip }}
                </el-link>
              </div>
            </template>
            <template #operation_type="scope">
              <div style="height: 40px">
                <span
                    v-if="scope.row.operation_type === 'DELETE'"
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >删除 {{ getTypeName(scope.row.resource_type) }}
                  </span>
                <span
                    v-if="scope.row.operation_type === 'UPDATE'"
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >修改 {{ getTypeName(scope.row.resource_type) }}</span>
                <span
                    v-if="scope.row.operation_type === 'CREATE'"
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >新增 {{ getTypeName(scope.row.resource_type) }}</span>
              </div>
            </template>
          </CustomTable>
        </base-main>
      </el-tab-pane>
      <el-tab-pane label="用户登陆日志" name="login">
        <el-header>
          <base-button color="#2972C8" plain class="iconfont icon-daochu">导出</base-button>
          <base-button color="#2972C8" plain class="iconfont icon-shuaxin" @click="getTableData">刷新</base-button>
          <base-input
              v-model="searchUser"
              class="w-50 m-2 organize-search"
              placeholder="用户名、应用名、终端"
              :suffix-icon="Search"
              style="width: 15%;float: right"
              @change="getTableData"
          />
          <div style="float: right;margin-right: 12px">
            <el-popover popper-class="custom-popover" :show-arrow="false" :width="20" trigger="click">
              <template #reference>
                <base-button :icon="Filter" style="border-radius: 4px">筛选</base-button>
              </template>
              <div
                  style="color: #252631;font-weight:400;border-bottom: 1px #EBEBEB solid;text-align: center;height: 38px;line-height: 38px"
              >
                选择显示项
              </div>
              <div style="padding-left: 17px">
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isMACHidden">MAC</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isSystemTypeHidden">系统类型</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isVersionsHidden">版本</base-checkbox>
                <base-checkbox style="margin-top: 10px;width: 100%" v-model="isCPUHidden">CPU</base-checkbox>
                <base-checkbox style="margin-top: 10px;margin-bottom: 10px;width: 100%" v-model="isRAMHidden">内存
                </base-checkbox>
              </div>
            </el-popover>
          </div>
          <base-select v-if="activeName!=='app'" v-model="operationAuth" class="m-2"
                     style="float: right;margin-right: 12px;height: 32px;width: 105px" placeholder="操作类型"
          >
            <base-option key="login" label="用户登陆" value="LOGIN"/>
            <base-option key="logout" label="用户注销" value="LOGOUT"/>
          </base-select>
          <el-date-picker
              v-model="dateValue"
              type="datetimerange"
              :shortcuts="shortcuts"
              range-separator="~"
              style="float: right;margin-right: 12px"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="getTableData"
          />
        </el-header>
        <base-main>
          <CustomTable
              :table-data="tableData"
              v-bind="loginTableConfig"
          >
            <template #ip="scope">
              <div style="height: 40px">
                <el-link style="font-size: 12px;color: #2972C8;font-family: HarmonyOS_Medium" :underline="false">
                  {{ scope.row.ip }}
                </el-link>
              </div>
            </template>
            <template #operate_time="scope">
              <div style="height: 40px">
                <span
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >{{ dayjs(scope.row.operate_time).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </div>
            </template>
            <template #auth_type="scope">
              <div style="height: 40px">
                <span
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >本地认证</span>
              </div>
            </template>
            <template #username="scope">
              <div style="height: 40px">
                <span
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >{{ scope.row.username ? scope.row.username : JSON.parse(scope.row.details_json).username }}</span>
              </div>
            </template>
            <template #type="scope">
              <div style="height: 40px">
                <span
                    v-if="scope.row.type === 'LOGIN_ERROR'"
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >认证错误</span>
                <span
                    v-if="scope.row.type === 'LOGIN'"
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >登陆</span>
                <span
                    v-if="scope.row.type === 'LOGOUT'"
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >注销</span>
              </div>
            </template>
            <template #error="scope">
              <div style="height: 40px">
                <span
                    style="font-size: 12px;font-family: HarmonyOS_Medium"
                >{{ scope.row.error ? '失败' : '成功' }}</span>
              </div>
            </template>
          </CustomTable>
        </base-main>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'Log',
}
</script>
<script setup>
import { provide, ref, watch } from 'vue'
import {
  Filter,
  Search,
} from '@element-plus/icons-vue'
import CustomTable from '@/components/customTable.vue'
import { getAccessLog, getAuthLog, getOperateLog, getOperateType } from '@/api/log'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const activeName = ref('app')
const searchUser = ref('')
const dateValue = ref()
const operationType = ref()
const operationAuth = ref()
const typeOptions = ref([])
const tableData = ref([])
// 分页
const currentPage = ref(1)
const pageSize = ref(100)
const total = ref(0)
const shortcuts = [
  {
    text: '上一个星期',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '上个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '过去3个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

const appPropList = [
  {
    prop: 'AccessTime',
    label: '访问时间',
    slotName: 'AccessTime',
    isCenter: '',
  },
  {
    prop: 'AccessUsername',
    label: '用户名',
    slotName: 'AccessUsername',
  },
  {
    prop: 'client_name',
    label: '终端',
    slotName: 'client_name',
  },
  {
    prop: 'AppName',
    label: '应用名',
    slotName: 'AppName',
  },
  {
    prop: 'DstIp',
    label: '目的IP',
    slotName: 'DstIp',
  },
  {
    prop: 'DstPort',
    label: '端口',
    slotName: 'DstPort',
  },
  {
    prop: 'StrategyName',
    label: '命中策略',
    slotName: 'StrategyName',
  },
  {
    prop: 'Status',
    label: '拦截',
    slotName: 'Status',
  },
]
const operationPropList = [
  {
    prop: 'operate_time',
    label: '操作时间',
    slotName: 'operate_time',
    isCenter: '',
  },
  {
    prop: 'operation_type',
    label: '操作类型',
    slotName: 'operation_type',
  },
  {
    prop: 'error',
    label: '操作结果',
    slotName: 'error',
  },
  {
    prop: 'username',
    label: '用户名',
    slotName: 'username',
  },
  {
    prop: 'ip',
    label: '源IP',
    slotName: 'ip',
  },
]
const loginPropList = [
  {
    prop: 'operate_time',
    label: '时间',
    slotName: 'operate_time',
    isCenter: '',
  },
  {
    prop: 'type',
    label: '登陆类型',
    slotName: 'type',
  },
  {
    prop: 'auth_type',
    label: '认证类型',
    slotName: 'auth_type',
  },
  {
    prop: 'error',
    label: '登陆结果',
    slotName: 'error',
  },
  {
    prop: 'username',
    label: '用户名',
    slotName: 'username',
  },
  {
    prop: 'ip',
    label: '源IP',
    slotName: 'ip',
  },
]

const appTableConfig = {
  propList: appPropList,
  isSelectColumn: true,
  isIndexColumn: true,
  isOperationColumn: false,
}

const operationTableConfig = {
  propList: operationPropList,
  isSelectColumn: true,
  isIndexColumn: true,
  isOperationColumn: false,
}

const loginTableConfig = {
  propList: loginPropList,
  isSelectColumn: true,
  isIndexColumn: true,
  isOperationColumn: false,
}

const getType = async() => {
  console.log('getType')
  const res = await getOperateType()
  console.log('getType')
  if (res.data.code !== 0) {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
  }
  console.log(res)
  typeOptions.value = res.data.data
}

const getTypeName = (resource_type) => {
  console.log('getTypeName')
  console.log(typeOptions.value)
  console.log(resource_type)
  const type = typeOptions.value.find(item => item.resource_type === resource_type)
  return type === undefined ? '' : type.name
}

const getTableData = async() => {
  console.log('getTableData')
  console.log(dateValue.value)
  const query = {
    start_time: dateValue.value ? dateValue.value[0] : '',
    end_time: dateValue.value ? dateValue.value[1] : '',
    limit: pageSize.value,
    offset: (currentPage.value - 1) * pageSize.value,
    search: searchUser.value,
    sort: '',
  }
  if (activeName.value === 'operation') {
    query.resource_type = operationType.value
  }
  if (activeName.value === 'login') {
    query.type = operationAuth.value
  }
  console.log(query)
  let res
  console.log(activeName.value)
  if (activeName.value === 'app') {
    res = await getAccessLog(query)
  }
  if (activeName.value === 'operation') {
    res = await getOperateLog(query)
  }
  if (activeName.value === 'login') {
    res = await getAuthLog(query)
  }
  if (res.data.code !== 0) {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
  }
  tableData.value = res.data.data.rows
  total.value = res.data.data.total_rows
  console.log(activeName.value)
  console.log(res)
}

getTableData()

watch(activeName, async(newData, oldData) => {
  console.log('watch')
  currentPage.value = 1
  console.log(activeName.value)
  operationAuth.value = ''
  operationType.value = ''
  dateValue.value = []
  searchUser.value = ''
  if (activeName.value === 'operation') {
    getType()
  }
  getTableData()
})

watch(operationType, async(newData, oldData) => {
  console.log('watch')
  getTableData()
})

watch(operationAuth, async(newData, oldData) => {
  console.log('watch')
  getTableData()
})
provide('currentPage', currentPage)
provide('pageSize', pageSize)
provide('total', total)
provide('getTableData', getTableData)
</script>
<style lang="scss">
.log {
  background: #FFFFFF;
  margin: 56px 12px 0px;
  padding: 0px;
  min-height: calc(100vh - 68px) !important;

  .log-tabs {
    .el-tabs__header {
      * {
        height: 58px;
        line-height: 58px;
      }

      .el-tabs__active-bar {
        border-bottom: 1px #2972C8 solid;
        background: no-repeat;
      }
    }

    .el-tabs__nav {
      margin-left: 13px !important;
    }

    .el-tabs__item.is-active {
      color: #2972C8;
    }

    .el-tabs__item:hover {
      color: #2972C8;
    }

    .el-tabs__nav-wrap::after {
      height: 1px !important;
      background-color: #EBEBEB !important;
    }

    .el-tabs__content {
      .el-header {
        padding-top: 16px !important;
        height: 64px;

        button {
          height: 32px;
          width: 77px;
          border-radius: 4px !important;
          border: 1px solid #E1E1E1;
          background: none;
        }

        button:hover {
          color: #FFFFFF;
          background: #2972C8;
        }

        .el-select {
          * {
            height: 30px;
          }
        }
      }

      .el-main {
        padding: 0px 20px;

        .el-table--fit {
          min-height: calc(100vh - 260px) !important;
          height: calc(100vh - 260px) !important;
        }
      }
    }
  }
}
</style>
