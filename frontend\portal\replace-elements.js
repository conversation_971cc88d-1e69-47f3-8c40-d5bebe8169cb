const fs = require('fs');
const path = require('path');

// 需要替换的组件映射
const componentMap = {
  'el-row': 'base-row',
  'el-col': 'base-col',
  'el-button': 'base-button',
  'el-input': 'base-input',
  'el-form': 'base-form',
  'el-form-item': 'base-form-item',
  'el-container': 'base-container',
  'el-aside': 'base-aside',
  'el-main': 'base-main',
  'el-divider': 'base-divider',
  'el-avatar': 'base-avatar',
  'el-carousel': 'base-carousel',
  'el-carousel-item': 'base-carousel-item',
  'el-card': 'base-card',
  'el-timeline': 'base-timeline',
  'el-timeline-item': 'base-timeline-item',
  'el-select': 'base-select',
  'el-option': 'base-option',
  'el-checkbox': 'base-checkbox',
  'el-radio': 'base-radio',
  'el-radio-group': 'base-radio-group'
};

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    if (stat.isDirectory()) {
      walkDir(filePath, callback);
    } else if (file.endsWith('.vue')) {
      callback(filePath);
    }
  });
}

// 替换文件内容
function replaceInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // 替换组件标签
  Object.keys(componentMap).forEach(oldComponent => {
    const newComponent = componentMap[oldComponent];
    
    // 替换开始标签
    const openTagRegex = new RegExp(`<${oldComponent}(\\s|>)`, 'g');
    if (openTagRegex.test(content)) {
      content = content.replace(openTagRegex, `<${newComponent}$1`);
      hasChanges = true;
    }
    
    // 替换结束标签
    const closeTagRegex = new RegExp(`</${oldComponent}>`, 'g');
    if (closeTagRegex.test(content)) {
      content = content.replace(closeTagRegex, `</${newComponent}>`);
      hasChanges = true;
    }
  });
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated: ${filePath}`);
  }
}

// 开始处理
const srcDir = path.join(__dirname, 'src');
console.log('Starting to replace element-plus components...');

walkDir(srcDir, replaceInFile);

console.log('Replacement completed!');
