/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{x as t}from"./index.74d1ee23.js";const a=a=>t({url:"/authority/getAuthorityList",method:"post",data:a}),o=a=>t({url:"/authority/deleteAuthority",method:"post",data:a}),r=a=>t({url:"/authority/createAuthority",method:"post",data:a}),u=a=>t({url:"/authority/copyAuthority",method:"post",data:a}),e=a=>t({url:"/authority/setDataAuthority",method:"post",data:a}),h=a=>t({url:"/authority/updateAuthority",method:"put",data:a});export{r as a,u as c,o as d,a as g,e as s,h as u};
