<template class="consumer">
  <el-tabs type="card" class="demo-tabs">
    <el-tab-pane>
      <template #label>
        <span class="custom-tabs-label">
          <span>告警事件</span>
        </span>
      </template>
      <div
          style="padding-top: 10px;width: 220px;height: 100%;background-color: #FFFFFF;float: left"
      >
        <span
            style="float: left;color: #333333;padding-left: 5px;font-size: 16px;"
        >敏感数据</span>
        <div style="float: left">
          <el-icon style="margin-left: 5px;color: #333333" size="18px">
            <CirclePlusFilled/>
          </el-icon>
        </div>

        <el-icon style="float: right" size="18px">
          <Fold/>
        </el-icon>
        <base-radio-group class="concern-radio-group" v-model="userState" style="height: 100%;padding-top: 15px">
          <el-radio-button border="false" class="concern-radio" label="1">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <Briefcase/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">
              全部<span style="color: red">(5)</span>
            </span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="2">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <CreditCard/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">
              财务数据<span style="color: red">(5)</span>
            </span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="3">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <ScaleToOriginal/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">
              源代码<span style="color: red">(4)</span>
            </span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="4">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <MagicStick/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">
              设计文档<span style="color: red">(3)</span>
            </span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="5">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <DataLine/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">
              客户数据<span style="color: red">(2)</span>
            </span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="6">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <View/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">
              HR信息<span style="color: red">(1)</span>
            </span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="7">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <Van/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">供应商信息(0)</span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="8">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <Van/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">财务数据(0)</span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
          <el-radio-button border="false" class="concern-radio" label="9">
            <el-icon style="float: left;margin-right: 5px;font-size: 20px">
              <Help/>
            </el-icon>
            <span style="float: left;width: 10px;color: #333333;font-size: 12px">其它(25)</span>
            <el-icon style="float: right;margin-top: 5px">
              <DeleteFilled/>
            </el-icon>
            <span style="float: right;width: 10px;font-size: 10px;color: #333333;margin-right: 5px">5</span>
            <el-icon style="float: right;padding-right: 5px;margin-top: 5px">
              <Tools/>
            </el-icon>
          </el-radio-button>
        </base-radio-group>
      </div>
      <div style="width: calc(100% - 225px);float: right">
        <div style="background-color: #FFFFFF;padding-top: 5px;padding-bottom: 5px">
          <div style="width: 100%;height: 37px">
            <span
                style="padding-left: 5px;padding-top:25px;font-weight: 700;font-style: normal;font-size: 16px;color: #333333"
            >
            告警事件</span>
            <base-select
                class="m-2 date-select"
                size="small"
                style="width: 94px; float: right;height: 26px;font-size: 12px;margin-right: 5px"
                v-model="period"
                placeholder="最近90天"
            >
              <base-option
                  v-for="item in periods"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  class="date-option"
              />
            </base-select>
            <base-button
                style="float: right;height: 28px;margin-right: 5px;font-size: 12px"
                type="primary"
                color="#256EBF"
                :icon="Tools"
            >风险设置
            </base-button>
            <base-button
                style="float: right;height: 28px;margin-right: 5px;font-size: 12px"
                type="primary"
                color="#256EBF"
            >创建规则
            </base-button>
          </div>
        </div>
        <div style="background-color: #FFFFFF;height: 350px">
          <div>
            <div style="float: left">
              <div ref="container" style="width: 350px;height: 350px;"></div>
            </div>
            <div style="float: right;width: calc(100% - 350px);height: 340px">
              <div>
                <span style="font-weight: 700;font-size: 16px;font-style: normal;color: #333333">数据外发概览</span>
              </div>
              <div
                  style="float: left;border: 1px rgba(215, 215, 215, 1) solid; width: 32.5%;height: calc(100% - 30px);padding: 5px"
              >
                <el-icon style="color: #333333;margin-right: 5px">
                  <FolderOpened/>
                </el-icon>
                <span
                    style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: #333333"
                >
                  共享文件 50%</span>
                <br>
                <div style="height: calc(100% - 35px);"></div>
                <span
                    style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: rgba(85, 85, 85, 0.701960784313725)"
                >
                  2个共享服务器</span>
              </div>
              <div
                  style="float: left; width: 33%;height: calc(100% - 20px)"
              >
                <div
                    style="background-color: rgba(206, 233, 253, 1);border: rgba(215, 215, 215, 1) 1px solid;width: calc(100% - 12px);height: 200px;padding: 5px"
                >
                  <WechatOutlined style="font-size: 20px;color: #333333"/>
                  <span
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: #333333"
                  >
                    IM 文件传输 15%</span>
                  <br>
                  <div style="height: 160px;"></div>
                  <span
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: rgba(85, 85, 85, 0.701960784313725)"
                  >2个App</span>
                </div>
                <div
                    style="padding: 5px;width: calc(100% - 12px);height: 98px;border: rgba(215, 215, 215, 1) 1px solid"
                >
                  <UsbFilled style="font-size: 15px;color: #333333"/>
                  <span
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: #333333"
                  > U盘拷贝 60%</span>
                  <br>
                  <div style="height: calc(100% - 30px);"></div>
                  <div
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: rgba(85, 85, 85, 0.701960784313725)"
                  >2个服务
                  </div>
                </div>
              </div>
              <div
                  style="float: left; width: 33%;height: calc(100% - 20px)"
              >
                <div
                    style="border: rgba(215, 215, 215, 1) 1px solid;width: calc(100% - 12px);height: 200px;padding: 5px"
                >
                  <MailFilled style="font-size: 15px;color: #333333"/>
                  <span
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: #333333"
                  >
                    邮箱发送 10%</span>
                  <br>
                  <div style="height: 160px;"></div>
                  <span
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: rgba(85, 85, 85, 0.701960784313725)"
                  >3个邮箱服务器</span>
                </div>
                <div
                    style="background-color:rgba(242, 242, 242, 1);padding: 5px;width: calc(100% - 12px);height: 98px;border: rgba(215, 215, 215, 1) 1px solid"
                >
                  <CloudFilled style="font-size: 15px;color: #333333"/>
                  <span
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: #333333"
                  > 网盘上传 10%</span>
                  <br>
                  <div style="height: calc(100% - 30px);"></div>
                  <div
                      style="font-family: 'Microsoft YaHei UI';font-style: normal;font-weight: 700;font-size: 12px;color: rgba(85, 85, 85, 0.701960784313725)"
                  >3个App
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div style="padding: 5px;border: 1px solid rgb(215, 215, 215);background-color: #FFFFFF">
            <base-button icon @click="alarmExport" style="font-size: 12px;border: 0px">
              <template #icon>
                <ExportOutlined class="alarmExport"/>
              </template>
              导出
            </base-button>
            <base-button icon style="font-size: 12px;border: 0px">
              <template #icon>
                <el-icon size="16px">
                  <RefreshRight/>
                </el-icon>
              </template>
              刷新
            </base-button>
            <span
                style="margin-right: 15px;margin-top: 5px;float: right;font-size: 12px;color: #AAAAAA;font-weight: 400"
            >最近90天</span>
            <base-input
                v-model="searchUser"
                class="w-50 m-2 organize-search"
                placeholder="请输入用户名"
                :suffix-icon="Search"
                style="width: 200px;height: 26px;float: right;color: #AAAAAA;margin-right: 15px"
            />
            <base-button :icon="Filter" style="width: 83px;height:26px;float: right;margin-right: 5px;color: #AAAAAA">
              筛选
            </base-button>
            <base-select
                style="margin-right: 5px;
              width: 10%;
              height:34px;
              float: right;"
                v-model="state"
                class="alarmSelect"
                placeholder="全部状态"
            >
              <base-option
                  v-for="item in stateList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  class="risk-option"
                  :disabled="item.disabled"
              />
            </base-select>

          </div>
          <el-table
              :data="tableData"
              style="width: 100%; margin-bottom: 20px"
              stripe
              class="event-table"
          >
            <el-table-column type="expand">
              <template #default="props">
                <div m="4">
                  <div
                      v-for="(item,index) in props.row.description"
                      key="index"
                      style="height: 40px;line-height:40px"
                      :style="index%2===0?'':'background-color: #F6F6F6'"
                  >
                    <p
                        m="t-0 b-2"
                        style="padding-left: 10px;font-weight: 400;font-style: normal;font-size: 12px;color: rgba(127, 127, 127, 0.701960784313725)"
                    >{{ item }}
                    <p style="font-weight: 400;font-style: normal;font-size: 12px;color: rgba(127, 127, 127, 0.701960784313725);float: right"
                       m="t-0 b-2"
                    >{{ props.row.dateTime }}</p>
                    </p>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column>
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>事件等级</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <el-icon v-if="scope.row.id === 2" style="top: 3px;color: #F59A23;margin-right: 5px">
                    <WarningFilled/>
                  </el-icon>
                  <el-icon v-else style="top: 3px;color: #D9001B;margin-right: 5px">
                    <WarningFilled/>
                  </el-icon>
                  <span
                      style="font-weight: 400;font-style: normal;font-size: 12px;color: rgba(0, 0, 0, 0.701960784313725)"
                  >{{ scope.row.eventLevel }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="ruleName" label="规则名称">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>规则名称</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <p
                      style="font-weight: 400;font-style: normal;font-size: 12px;color: rgba(127, 127, 127, 0.701960784313725)"
                  >ID:{{ scope.row.ruleName.ID }}</p>
                  <p
                      style="font-weight: 400;font-style: normal;font-size: 12px;color: rgba(127, 127, 127, 0.701960784313725)"
                  >{{ scope.row.ruleName.name }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="dateTime" label="日期时间">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>日期时间</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <p
                      style="font-weight: 400;font-style: normal;font-size: 12px;color: rgba(127, 127, 127, 0.701960784313725)"
                  >{{ scope.row.dateTime }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="userName" label="用户名">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>用户名</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <p
                      style="font-weight: 400;font-style: normal;font-size: 12px;color: rgba(127, 127, 127, 0.701960784313725)"
                  >{{ scope.row.userName }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="outboundWay" label="外发途径">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>外发途径</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <p
                      style="font-weight: 400;font-style: normal;font-size: 12px;color: rgba(127, 127, 127, 0.701960784313725)"
                  >{{ scope.row.outboundWay }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="operation" label="操作">
              <template #header>
                <div style="text-align: center;font-size: 12px;font-weight: 700">
                  <span>操作</span>
                </div>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <el-link type="primary"
                           :underline="false"
                           style="
                       color: rgba(2, 167, 240, 0.996078431372549);
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700"
                           @click="details(scope.row)"
                  >
                    查看详情
                  </el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
              v-model:currentPage="currentPage4"
              v-model:page-size="pageSize4"
              :page-sizes="[100, 200, 300, 400]"
              :small="small"
              :disabled="disabled"
              :background="background"
              layout="total, sizes, prev, pager, next, jumper"
              :total="10000"
              style="float: right"
              class="risk-pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="告警规则">
      <template #label>
        <span class="custom-tabs-label">
          <span>告警规则</span>
        </span>
      </template>
      <div style="width: 100%;height: 43px;line-height: 43px;background-color: #FFFFFF">
        <span style="margin-left: 10px;color: #333333;font-weight: 700;font-size: 14px">告警规则</span>
        <base-button
            class="rule-button"
            style="margin-top: 7px;margin-right: 10px;padding: 5px 15px;font-size: 12px;background-color: #256EBF;float: right;height: 28px"
            type="primary"
            circle
            :icon="Setting"
        >
          推荐规则
        </base-button>
      </div>
      <div
          style="line-height: 43px;height: 43px;width: 100%;background-color: #FFFFFF;border-top: 1px #DCDCDC solid;border-bottom: 1px #DCDCDC solid"
      >
        <div style="width: 50%;float: left" class="rule-table-button">
          <base-button
              type="primary"
              :icon="CirclePlusFilled"
              color="#FFFF"
              style="font-size: 12px"
          >
            新增规则
          </base-button>
          <base-button
              type="primary"
              :icon="DeleteFilled"
              color="#FFFF"
              style="font-size: 12px"
          >
            删除
          </base-button>
          <base-button
              type="primary"
              :icon="RefreshRight"
              color="#FFFF"
              style="font-size: 12px"
          >
            刷新
          </base-button>
        </div>
        <div style="float: right">
          <base-select
              v-model="value"
              class="m-2 state-select"
              placeholder="Select"
              size="small"
          >
            <base-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </base-select>
          <base-button
              type="primary"
              :icon="Filter"
              color="#FFFF"
              class="alarm-filter"
              style="margin-right: 10px;height: 28px;border: #D7D7D7 1px solid;border-radius: 5px;font-size: 14px"
          >
            筛选
          </base-button>
          <base-input
              v-model="input3"
              class="w-50 m-2"
              style="font-size: 14px;margin-right: 5px;height: 28px;width: 200px"
              size="small"
              placeholder="请输入用户名"
              :suffix-icon="Search"
          />
        </div>
      </div>
      <div>
        <el-table class="rule-table" :data="alarmList" stripe style="width: 100%;height: 375px">
          <el-table-column prop="type" width="180">
            <template #header>
              <span style="font-size: 12px;font-weight: 700">规则ID</span>
            </template>
            <template #default="scope">
              <span style="font-size: 12px;font-weight: 400">ID:</span>
              <span style="font-size: 12px;font-weight: 400">{{ scope.row.id }} </span>
            </template>
          </el-table-column>
          <el-table-column prop="count" width="180">
            <template #header>
              <span style="font-size: 12px;font-weight: 700">规则名称</span>
            </template>
            <template #default="scope">
              <span style="font-size: 12px;color: #A2A2A2;font-weight: 400">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="riskEvent">
            <template #header>
              <span style="font-size: 12px;font-weight: 700">生效用户</span>
            </template>
            <template #default="scope">
              <span style="font-size: 12px;color: #A2A2A2;font-weight: 400">{{ scope.row.user }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="riskEvent">
            <template #header>
              <span style="font-size: 12px;font-weight: 700">规则描述</span>
            </template>
            <template #default="scope">
              <span style="font-size: 12px;color: #A2A2A2;font-weight: 400">{{ scope.row.describe }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="riskEvent">
            <template #header>
              <span style="font-size: 12px;font-weight: 700">操作</span>
            </template>
            <template #default="scope">
              <el-icon v-if="scope.row.state === '启用'" style="color: #008000;margin-right: 5px">
                <Check/>
              </el-icon>
              <StopOutlined v-else style="font-weight: 700;color: #FD3D3D;margin-right: 5px"/>
              <span style="font-size: 12px;font-weight: 400">{{ scope.row.state }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: 'DataSecurityOverview',
}
</script>
<script setup>
import { onMounted, ref } from 'vue'
import {
  RefreshRight,
  Search,
  Filter,
  Tools,
  Setting,
  CirclePlusFilled,
  DeleteFilled,
} from '@element-plus/icons-vue'
import { Pie, measureTextWidth } from '@antv/g2plot'

const state = ref('0')
const stateList = [{
  value: '0',
  label: '全部状态',
}, {
  value: '1',
  label: '未处理',
}, {
  value: '2',
  label: '已处理',
}]

const searchUser = ref('')
const alarmList = ref([{
  id: '1001',
  name: '源代码泄露',
  user: '全部用户',
  describe: '发送源代码到Github、个人U盘等',
  state: '启用',
}, {
  id: '1002',
  name: '源代码泄露',
  user: '',
  describe: 'U：公司客户数据',
  state: '启用',
}, {
  id: '1003',
  name: '源代码泄露',
  user: '',
  describe: 'U：公司客户数据',
  state: '禁用',
}])

const details = (data) => {
  console.log(data)
}

// 分页
const currentPage4 = ref(4)
const pageSize4 = ref(100)
const small = ref(true)
const background = ref(true)
const disabled = ref(false)
const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
}

const period = ref('1')
const periods = [{
  value: '1',
  label: '最近90天',
}, {
  value: '2',
  label: '最近30天',
}]

const userState = ref('1')

const data = [
  { type: '严重事件', value: 27 },
  { type: '高危事件', value: 25 },
  { type: '中危事件', value: 18 },
  { type: '低危事件', value: 15 },
]

const container = ref(null)
const renderPiePlot = () => {
  const piePlot = new Pie(container.value, {
    appendPadding: 10,
    data,
    angleField: 'value',
    colorField: 'type',
    color: ({ type }) => {
      if (type === '严重事件') return '#E14949'
      if (type === '高危事件') return '#FA8A03'
      if (type === '中危事件') return '#60AF25'
      return '#4A8BD7'
    },
    radius: 1,
    innerRadius: 0.75,
    label: {
      type: 'inner',
      offset: '-50%',
      content: '{value}',
      style: {
        textAlign: 'center',
        fontSize: 14,
      },
    },
    interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontSize: '16px',
        },
        content: '事件分布',
      },
    },
  })

  piePlot.render()
}
onMounted(renderPiePlot)

const tableData = [
  {
    id: 1,
    eventLevel: '严重',
    ruleName: {
      ID: '1001',
      name: '源代码泄露',
    },
    dateTime: '2022/10/15 10:01:22',
    userName: 'Andrew',
    outboundWay: 'U盘拷贝',
    description: [
      'Anco 发送邮件 附件：规划资料.zip  To Andrew',
      'Anco 压缩规划.ppt 到规划资料.zip',
      'Anco  下载产品规划.ppt到PC：XXXPC D:\\\\规划材料.ppt      from     svn:***********:2234',
    ],
  },
  {
    id: 2,
    eventLevel: '严重',
    ruleName: {
      ID: '1001',
      name: '源代码泄露',
    },
    dateTime: '2022/10/15 10:01:22',
    userName: 'Andrew',
    outboundWay: '网盘上传',
    description: [
      'Anco 发送邮件 附件：规划资料.zip  To Andrew',
      'Anco 压缩规划.ppt 到规划资料.zip',
      'Anco  下载产品规划.ppt到PC：XXXPC D:\\\\规划材料.ppt      from     svn:***********:2234',
    ],
  },
  {
    id: 3,
    eventLevel: '严重',
    ruleName: {
      ID: '1001',
      name: '源代码泄露',
    },
    dateTime: '2022/10/15 10:01:22',
    userName: 'Andrew',
    outboundWay: 'IM文件传输',
    description: [
      'Anco 发送邮件 附件：规划资料.zip  To Andrew',
      'Anco 压缩规划.ppt 到规划资料.zip',
      'Anco  下载产品规划.ppt到PC：XXXPC D:\\\\规划材料.ppt      from     svn:***********:2234',
    ],
  },
  {
    id: 4,
    eventLevel: '严重',
    ruleName: {
      ID: '1001',
      name: '源代码泄露',
    },
    dateTime: '2022/10/15 10:01:22',
    userName: 'Andrew',
    outboundWay: '--',
    description: [
      'Anco 发送邮件 附件：规划资料.zip  To Andrew',
      'Anco 压缩规划.ppt 到规划资料.zip',
      'Anco  下载产品规划.ppt到PC：XXXPC D:\\\\规划材料.ppt      from     svn:***********:2234',
    ],
  },
  {
    id: 5,
    eventLevel: '严重',
    ruleName: {
      ID: '1002',
      name: '敏感数据分片',
    },
    dateTime: '2022/10/15 10:01:22',
    userName: 'Andrew',
    outboundWay: '--',
    description: [
      'Anco 发送邮件 附件：规划资料.zip  To Andrew',
      'Anco 压缩规划.ppt 到规划资料.zip',
      'Anco  下载产品规划.ppt到PC：XXXPC D:\\\\规划材料.ppt      from     svn:***********:2234',
    ],
  },
]

const value = ref('0')

const options = [
  {
    value: '0',
    label: '全部状态',
  },
  {
    value: '1',
    label: '未处理',
  },
  {
    value: '2',
    label: '已处理',
  },
]
</script>
<style scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
  float: left;
}

.concern-radio {
  width: 100%;
  height: 40px;
  line-height: 40px;
}

.state-select {
  margin-right: 10px;
  height: 28px;
  width: 122px;
  font-size: 14px;
  color: #AAAAAA;
}

</style>
<style>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

.demo-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}

.demo-tabs .custom-tabs-label span {
  vertical-align: middle;
  margin-left: 4px;
}

.demo-tabs .el-tabs__item {
  font-size: 12px;
}

.demo-tabs .el-tabs__nav {
  background-color: #FFFFFF;
}

.demo-tabs .is-active {
  background-color: rgba(37, 110, 191, 1);
  color: #FFFFFF;
  font-size: 12px;
}

.alarm .el-card__body {
  padding: 10px 20px;
}

.alarmExport > svg {
  width: 16px;
  height: 16px;
}

.alarmSelect .el-input {
  height: 34px;
}

.alarmSelect .el-input__inner {
  color: #AAAAAA;
}

.alarm-table-row-style .el-table__cell div {
  line-height: 30px !important;
}

.alarm-table-header-style {
  height: 34px;
  font-size: 12px;
}

.admin-box .el-table td .cell {
  height: auto;
  line-height: 30px;
}

.admin-box .el-table td .cell div {
  height: auto;
}

.demo-tabs > .el-tabs__content {
  padding: 0px 0px;
}

.concern-radio span {
  width: 100%;
  height: 40px;
  line-height: 25px;
}

.rule-button > .el-icon {
  height: 20px;
  width: 20px;
}

.rule-button > .el-icon .icon {
  height: 20px;
  width: 20px;
}

.state-select > div div {
  height: 28px;
  width: 122px;
  font-size: 14px;
  color: #AAAAAA;
}

.state-select > div input {
  font-size: 14px;
  height: 28px !important;
  color: #AAAAAA;
}

.alarm-filter > span {
  color: #AAAAAA;
}

.alarm-filter > i {
  color: #AAAAAA;
}

.rule-table > div table thead tr th.el-table__cell {
  background-color: #FFFFFF !important;
}

.rule-table th, .el-table tr {
  background-color: #FFFFFF;
}

.rule-table-button > button i {
  font-size: 16px;
}
</style>
<style lang="scss">
.concern-radio-group {
  .is-active {
    background-color: #B2E4FB !important;
    --el-color-primary: #B2E4FB !important;

    .el-icon {
      color: #2A7EF2;
    }
  }
}

.date-select {
  height: 28px;

  div {
    height: 28px;

    .el-input__wrapper {
      height: 28px;
      padding: 0px 7px;
    }
  }

  input {
    height: 26px !important;
    font-size: 13px;
    color: #D7D7D7;
  }
}

.date-option {
  font-size: 13px;
  color: #D7D7D7;
}

.date-option:hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}

.date-option.hover {
  background: #1E90FF;
  color: #FFFFFF !important;
}

.alarmSelect {
  height: 20px;
  width: 125px !important;

  .el-input {
    height: 26px;
    width: 125px;
  }

  .el-input__inner {
    color: #AAAAAA;
  }
}

.event-table {
  th.is-leaf {
    background-color: #FFFFFF !important;
  }
}

.risk-pagination {
  float: right;

  .el-pager {
    li {
      background-color: #FFFFFF !important;
    }

    .is-active {
      background-color: #4E8DDA !important;
    }
  }

  .btn-prev {
    background-color: #FFFFFF !important;
  }

  .btn-next {
    background-color: #FFFFFF !important;
  }
}
</style>
