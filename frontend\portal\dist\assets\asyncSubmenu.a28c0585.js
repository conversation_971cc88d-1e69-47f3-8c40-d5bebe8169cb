/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{_ as e,K as t,r as a,y as n,h as o,o as s,f as u,w as l,d as r,j as c,E as i,g as f,e as m,t as d,F as p,Q as v}from"./index.74d1ee23.js";const h={key:0,class:"gva-subMenu"},y=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({c8e9c8aa:x.value,"6037b64a":b.value})));const y=e,I=a(y.theme.activeBackground),b=a(y.theme.activeText),x=a(y.theme.normalText);return n((()=>y.theme),(()=>{I.value=y.theme.activeBackground,b.value=y.theme.activeText,x.value=y.theme.normalText})),(t,a)=>{const n=o("component"),y=o("el-icon"),I=o("el-sub-menu");return s(),u(I,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(p,{key:1},[e.routerInfo.meta.icon?(s(),u(y,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)],64)):(s(),r("div",h,[e.routerInfo.meta.icon?(s(),u(y,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)]))])),default:l((()=>[v(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-547fcaa6"]]);export{y as default};
