/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{r as e,b as r,c,_ as t}from"./index.74d1ee23.js";import{J as p}from"./index-browser-esm.c2d3b5c9.js";const s=""+new URL("noBody.745c3d16.png",import.meta.url).href,a=t(Object.assign({name:"CustomPic"},{props:{picType:{type:String,required:!1,default:"avatar"},picSrc:{type:String,required:!1,default:""}},setup(t){const a=t,i=e("/auth/");e(s);const o=r();return c((()=>""===a.picSrc?""!==p("$..headerImg[0]",o.userInfo)[0]&&"http"===p("$..headerImg[0]",o.userInfo)[0].slice(0,4)?p("$..headerImg[0]",o.userInfo)[0]:i.value+p("$..headerImg[0]",o.userInfo)[0]:""!==a.picSrc&&"http"===a.picSrc.slice(0,4)?a.picSrc:i.value+a.picSrc)),c((()=>a.picSrc&&"http"!==a.picSrc.slice(0,4)?i.value+a.picSrc:a.picSrc)),(e,r)=>null}}),[["__scopeId","data-v-7ac42d76"]]);export{a as C};
