/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{_ as e,u as a,a as s,r as l,b as t,c as n,v as u,D as o,J as r,h as i,o as v,d as m,j as c,w as d,F as p,i as y,g,e as f,H as h,m as b,k as S,t as I,P as q,G as O,f as w,N,Q as k}from"./index.4982c0f9.js";import{J as C}from"./index-browser-esm.c2d3b5c9.js";const J={class:"router-history"},x=["tab"],j=e(Object.assign({name:"HistoryComponent"},{setup(e){const j=a(),V=s(),E=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),P=l([]),_=l(""),A=l(!1),T=t(),L=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),R=l(0),D=l(0),H=l(!1),$=l(!1),F=l(""),G=n((()=>C("$..defaultRouter[0]",T.userInfo)[0]||"dashboard")),Q=()=>{P.value=[{name:G.value,meta:{title:"总览"},query:{},params:{}}],V.push({name:G.value}),A.value=!1,sessionStorage.setItem("historys",JSON.stringify(P.value))},U=()=>{let e;const a=P.value.findIndex((a=>(E(a)===F.value&&(e=a),E(a)===F.value))),s=P.value.findIndex((e=>E(e)===_.value));P.value.splice(0,a),a>s&&V.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},X=()=>{let e;const a=P.value.findIndex((a=>(E(a)===F.value&&(e=a),E(a)===F.value))),s=P.value.findIndex((e=>E(e)===_.value));P.value.splice(a+1,P.value.length),a<s&&V.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},Y=()=>{let e;P.value=P.value.filter((a=>(E(a)===F.value&&(e=a),E(a)===F.value))),V.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},z=e=>{if(!P.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const s in e.query)if(e.query[s]!==a.query[s])return!1;for(const s in e.params)if(e.params[s]!==a.params[s])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,P.value.push(a)}window.sessionStorage.setItem("activeValue",E(e))},B=l({});u((()=>P.value),(()=>{B.value={},P.value.forEach((e=>{B.value[E(e)]=e}))}));const K=e=>{const a=B.value[e];V.push({name:a.name,query:a.query,params:a.params})},M=e=>{const a=P.value.findIndex((a=>E(a)===e));E(j)===e&&(1===P.value.length?V.push({name:G.value}):a<P.value.length-1?V.push({name:P.value[a+1].name,query:P.value[a+1].query,params:P.value[a+1].params}):V.push({name:P.value[a-1].name,query:P.value[a-1].query,params:P.value[a-1].params})),P.value.splice(a,1)};u((()=>A.value),(()=>{A.value?document.body.addEventListener("click",(()=>{A.value=!1})):document.body.removeEventListener("click",(()=>{A.value=!1}))})),u((()=>j),((e,a)=>{"Login"!==e.name&&"Reload"!==e.name&&(P.value=P.value.filter((e=>!e.meta.closeTab)),z(e),sessionStorage.setItem("historys",JSON.stringify(P.value)),_.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),u((()=>P.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(P.value))}),{deep:!0});return(()=>{r.on("closeThisPage",(()=>{M(L(j))})),r.on("closeAllPage",(()=>{Q()})),r.on("mobile",(e=>{$.value=e})),r.on("collapse",(e=>{H.value=e}));const e=[{name:G.value,meta:{title:"总览"},query:{},params:{}}];P.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?_.value=window.sessionStorage.getItem("activeValue"):_.value=E(j),z(j),"true"===window.sessionStorage.getItem("needCloseAll")&&(Q(),window.sessionStorage.removeItem("needCloseAll"))})(),o((()=>{r.off("collapse"),r.off("mobile")})),(e,a)=>{const s=i("el-tab-pane"),l=i("el-tabs");return v(),m("div",J,[c(l,{modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),closable:!(1===P.value.length&&e.$route.name===G.value),type:"card",onContextmenu:a[1]||(a[1]=O((e=>(e=>{if(1===P.value.length&&j.name===G.value)return!1;let a="";if(a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a){let s;A.value=!0,s=H.value?54:220,$.value&&(s=0),R.value=e.clientX-s,D.value=e.clientY+10,F.value=a.substring(4)}})(e)),["prevent"])),onTabChange:K,onTabRemove:M},{default:d((()=>[(v(!0),m(p,null,y(P.value,(e=>(v(),g(s,{key:L(e),label:e.meta.title,name:L(e),tab:e,class:"gva-tab"},{label:d((()=>[f("span",{tab:e,style:h({color:_.value===L(e)?b(T).activeColor:"#333"})},[f("i",{class:"dot",style:h({backgroundColor:_.value===L(e)?b(T).activeColor:"#ddd"})},null,4),S(" "+I(b(q)(e.meta.title,e)),1)],12,x)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),w("自定义右键菜单html代码"),N(f("ul",{style:h({left:R.value+"px",top:D.value+"px"}),class:"contextmenu"},[f("li",{onClick:Q},"关闭所有"),f("li",{onClick:U},"关闭左侧"),f("li",{onClick:X},"关闭右侧"),f("li",{onClick:Y},"关闭其他")],4),[[k,A.value]])])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/historyComponent/history.vue"]]);export{j as default};
