/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import o from"./index.91b402da.js";import{o as s,f as t}from"./index.2320e6b9.js";import"./localLogin.b992c365.js";import"./wechat.c1f13d45.js";import"./feishu.f938b652.js";import"./dingtalk.6d3289a1.js";import"./oauth2.a56e4a54.js";import"./sms.1bf9553b.js";import"./secondaryAuth.e4383672.js";import"./verifyCode.c72175ce.js";const i=Object.assign({name:"ClientNewLogin"},{setup:i=>(i,e)=>(s(),t(o))});export{i as default};
