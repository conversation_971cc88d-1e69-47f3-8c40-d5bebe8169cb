{"version": 3, "file": "coordinate.js", "sourceRoot": "", "sources": ["../../src/types/coordinate.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Transformations of coordinate\n */\nexport type Transformations = Array<\n  | {\n      /** send (x, y) to (-x, y) */\n      type: 'reflectX';\n    }\n  | {\n      /** send (x, y) to (x, -y) */\n      type: 'reflectY';\n    }\n  | {\n      /** send (x, y) to (y, x) */\n      type: 'transpose';\n    }\n>;\n"]}