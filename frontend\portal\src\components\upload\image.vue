<template>
  <div class="upload-image">
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileChange"
    >
    <div class="upload-area" @click="selectFile">
      <div v-if="imageUrl" class="image-preview">
        <img :src="imageUrl" alt="preview" />
        <div class="image-actions">
          <button @click.stop="removeImage">删除</button>
        </div>
      </div>
      <div v-else class="upload-placeholder">
        <base-icon name="plus" />
        <span>点击上传图片</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const fileInput = ref(null)
const imageUrl = ref(props.modelValue)

watch(() => props.modelValue, (newVal) => {
  imageUrl.value = newVal
})

const selectFile = () => {
  fileInput.value.click()
}

const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      imageUrl.value = e.target.result
      emit('update:modelValue', e.target.result)
      emit('change', file)
    }
    reader.readAsDataURL(file)
  }
}

const removeImage = () => {
  imageUrl.value = ''
  emit('update:modelValue', '')
  emit('change', null)
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}
</script>

<style scoped>
.upload-image {
  display: inline-block;
}

.upload-area {
  width: 100px;
  height: 100px;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.image-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.image-actions button {
  padding: 4px 8px;
  background: #fff;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 12px;
}

.upload-placeholder span {
  margin-top: 4px;
}
</style>
