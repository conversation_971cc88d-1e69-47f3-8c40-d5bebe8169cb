<template>
  <div :class="formItemClass">
    <label v-if="label" :class="labelClass" :style="labelStyle">
      {{ label }}
    </label>
    <div class="base-form-item__content">
      <slot></slot>
      <div v-if="errorMessage" class="base-form-item__error">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, ref, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  prop: {
    type: String,
    default: ''
  },
  rules: {
    type: [Object, Array],
    default: () => []
  },
  required: {
    type: Boolean,
    default: false
  },
  labelWidth: {
    type: String,
    default: ''
  }
})

const baseForm = inject('baseForm', {})
const errorMessage = ref('')
const initialValue = ref(null)

const formItemClass = computed(() => {
  const classes = ['base-form-item']

  if (errorMessage.value) {
    classes.push('base-form-item--error')
  }

  if (props.required || isRequired.value) {
    classes.push('base-form-item--required')
  }

  return classes.join(' ')
})

const labelClass = computed(() => {
  const classes = ['base-form-item__label']

  if (props.required || isRequired.value) {
    classes.push('base-form-item__label--required')
  }

  return classes.join(' ')
})

const labelStyle = computed(() => {
  const width = props.labelWidth || baseForm.labelWidth
  if (width && baseForm.labelPosition !== 'top') {
    return {
      width: width,
      minWidth: width
    }
  }
  return {}
})

const isRequired = computed(() => {
  const rules = getRules()
  return rules.some(rule => rule.required)
})

const getRules = () => {
  const formRules = baseForm.rules?.[props.prop] || []
  const itemRules = props.rules || []
  return [].concat(formRules, itemRules)
}

// 验证字段
const validate = (trigger, callback) => {
  if (!props.prop || !baseForm.model) {
    if (callback) callback()
    return true
  }

  const value = baseForm.model[props.prop]
  const rules = getRules()

  if (rules.length === 0) {
    if (callback) callback()
    return true
  }

  for (const rule of rules) {
    // 检查触发条件
    if (trigger && rule.trigger && rule.trigger !== trigger) {
      continue
    }

    if (rule.required && (value === undefined || value === null || value === '')) {
      const message = rule.message || `${props.label}是必填项`
      errorMessage.value = message
      if (callback) callback(message)
      return false
    }

    if (value !== undefined && value !== null && value !== '') {
      if (rule.min && String(value).length < rule.min) {
        const message = rule.message || `${props.label}长度不能少于${rule.min}个字符`
        errorMessage.value = message
        if (callback) callback(message)
        return false
      }

      if (rule.max && String(value).length > rule.max) {
        const message = rule.message || `${props.label}长度不能超过${rule.max}个字符`
        errorMessage.value = message
        if (callback) callback(message)
        return false
      }

      if (rule.pattern && !rule.pattern.test(String(value))) {
        const message = rule.message || `${props.label}格式不正确`
        errorMessage.value = message
        if (callback) callback(message)
        return false
      }

      if (rule.validator && typeof rule.validator === 'function') {
        try {
          const result = rule.validator(rule, value, (error) => {
            if (error) {
              errorMessage.value = error.message || error
              if (callback) callback(error.message || error)
            } else {
              errorMessage.value = ''
              if (callback) callback()
            }
          })

          if (result === false) {
            const message = rule.message || `${props.label}验证失败`
            errorMessage.value = message
            if (callback) callback(message)
            return false
          }
        } catch (error) {
          const message = rule.message || error.message || `${props.label}验证失败`
          errorMessage.value = message
          if (callback) callback(message)
          return false
        }
      }
    }
  }

  errorMessage.value = ''
  if (callback) callback()
  return true
}

// 重置字段
const resetField = () => {
  if (props.prop && baseForm.model && initialValue.value !== undefined) {
    baseForm.model[props.prop] = initialValue.value
  }
  errorMessage.value = ''
}

// 清除验证
const clearValidate = () => {
  errorMessage.value = ''
}

// 监听表单数据变化
if (props.prop && baseForm.model) {
  watch(() => baseForm.model[props.prop], () => {
    if (errorMessage.value) {
      validate('change')
    }
  })
}

// 组件挂载时注册到表单
onMounted(() => {
  if (props.prop && baseForm.model) {
    initialValue.value = baseForm.model[props.prop]
  }

  if (baseForm.addFormItem) {
    baseForm.addFormItem({
      prop: props.prop,
      validate,
      resetField,
      clearValidate
    })
  }
})

// 组件卸载时从表单移除
onUnmounted(() => {
  if (baseForm.removeFormItem) {
    baseForm.removeFormItem({
      prop: props.prop,
      validate,
      resetField,
      clearValidate
    })
  }
})

// 暴露方法
defineExpose({
  validate,
  resetField,
  clearValidate,
  prop: props.prop
})
</script>

<style scoped>
.base-form-item {
  display: flex;
  margin-bottom: 22px;
}

.base-form-item__content {
  flex: 1;
  position: relative;
}

.base-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  margin-top: 4px;
}

.base-form-item--error :deep(.base-input) {
  border-color: #f56c6c;
}

.base-form-item--error :deep(.base-input:focus) {
  border-color: #f56c6c;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

.base-form-item__label--required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.base-form-item__label {
  display: flex;
  align-items: center;
  margin-right: 12px;
  margin-bottom: 0;
  flex-shrink: 0;
  font-size: 14px;
  color: #606266;
}

/* 当标签在顶部时 */
:deep(.base-form--label-top) .base-form-item {
  flex-direction: column;
}

:deep(.base-form--label-top) .base-form-item__label {
  margin-right: 0;
  margin-bottom: 8px;
}

/* 内联表单 */
:deep(.base-form--inline) .base-form-item {
  display: inline-flex;
  margin-right: 16px;
  margin-bottom: 0;
  vertical-align: top;
}
</style>
