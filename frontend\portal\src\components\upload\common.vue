<template>
  <div class="upload-common">
    <input
      ref="fileInput"
      type="file"
      :accept="accept"
      style="display: none"
      @change="handleFileChange"
    >
    <div class="upload-area" @click="selectFile">
      <div v-if="fileList.length > 0" class="file-list">
        <div v-for="(file, index) in fileList" :key="index" class="file-item">
          <base-icon name="document" />
          <span class="file-name">{{ file.name }}</span>
          <button @click.stop="removeFile(index)">删除</button>
        </div>
      </div>
      <div v-else class="upload-placeholder">
        <base-icon name="plus" />
        <span>点击上传文件</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  accept: {
    type: String,
    default: '*'
  },
  multiple: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const fileInput = ref(null)
const fileList = ref([...props.modelValue])

watch(() => props.modelValue, (newVal) => {
  fileList.value = [...newVal]
})

const selectFile = () => {
  fileInput.value.click()
}

const handleFileChange = (event) => {
  const files = Array.from(event.target.files)
  if (files.length > 0) {
    if (props.multiple) {
      fileList.value.push(...files)
    } else {
      fileList.value = [files[0]]
    }
    emit('update:modelValue', fileList.value)
    emit('change', fileList.value)
  }
}

const removeFile = (index) => {
  fileList.value.splice(index, 1)
  emit('update:modelValue', fileList.value)
  emit('change', fileList.value)
}
</script>

<style scoped>
.upload-common {
  display: inline-block;
}

.upload-area {
  min-width: 200px;
  min-height: 100px;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  padding: 10px;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #606266;
}

.file-item button {
  padding: 2px 6px;
  background: #f56c6c;
  color: white;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 14px;
}

.upload-placeholder span {
  margin-top: 8px;
}
</style>
