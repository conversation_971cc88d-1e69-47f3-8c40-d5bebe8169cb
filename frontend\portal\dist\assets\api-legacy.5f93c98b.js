/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",o=a.toStringTag||"@@toStringTag";function l(t,a,u,o){var l=a&&a.prototype instanceof c?a:c,f=Object.create(l.prototype);return n(f,"_invoke",function(t,n,a){var u,o,l,c=0,f=a||[],s=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return u=t,o=0,l=e,p.n=n,i}};function d(t,n){for(o=t,l=n,r=0;!s&&c&&!a&&r<f.length;r++){var a,u=f[r],d=p.p,v=u[2];t>3?(a=v===n)&&(l=u[(o=u[4])?5:(o=3,3)],u[4]=u[5]=e):u[0]<=d&&((a=t<2&&d<u[1])?(o=0,p.v=n,p.n=u[1]):d<v&&(a=t<3||u[0]>n||n>v)&&(u[4]=t,u[5]=n,p.n=v,o=0))}if(a||t>1)return i;throw s=!0,n}return function(a,f,v){if(c>1)throw TypeError("Generator is already running");for(s&&1===f&&d(f,v),o=f,l=v;(r=o<2?e:l)||!s;){u||(o?o<3?(o>1&&(p.n=-1),d(o,l)):p.n=l:p.v=l);try{if(c=2,u){if(o||(a="next"),r=u[a]){if(!(r=r.call(u,l)))throw TypeError("iterator result is not an object");if(!r.done)return r;l=r.value,o<2&&(o=0)}else 1===o&&(r=u.return)&&r.call(u),o<2&&(l=TypeError("The iterator does not provide a '"+a+"' method"),o=1);u=e}else if((r=(s=p.n<0)?l:t.call(n,p))!==i)break}catch(r){u=e,o=1,l=r}finally{c=1}}return{value:r,done:s}}}(t,u,o),!0),f}var i={};function c(){}function f(){}function s(){}r=Object.getPrototypeOf;var p=[][u]?r(r([][u]())):(n(r={},u,(function(){return this})),r),d=s.prototype=c.prototype=Object.create(p);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,n(e,o,"GeneratorFunction")),e.prototype=Object.create(d),e}return f.prototype=s,n(d,"constructor",s),n(s,"constructor",f),f.displayName="GeneratorFunction",n(s,o,"GeneratorFunction"),n(d),n(d,o,"Generator"),n(d,u,(function(){return this})),n(d,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:l,m:v}})()}function n(e,t,r,a){var u=Object.defineProperty;try{u({},"",{})}catch(e){u=0}n=function(e,t,r,a){if(t)u?u(e,t,{value:r,enumerable:!a,configurable:!a,writable:!a}):e[t]=r;else{var o=function(t,r){n(e,t,(function(e){return this._invoke(t,r,e)}))};o("next",0),o("throw",1),o("return",2)}},n(e,t,r,a)}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}function o(e,t,n,r,a,u,o){try{var l=e[u](o),i=l.value}catch(e){return void n(e)}l.done?t(i):Promise.resolve(i).then(r,a)}function l(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var u=e.apply(t,n);function l(e){o(u,r,a,l,i,"next",e)}function i(e){o(u,r,a,l,i,"throw",e)}l(void 0)}))}}System.register(["./api-legacy.16c002e6.js","./stringFun-legacy.41a4a108.js","./warningBar-legacy.4145d360.js","./index-legacy.dbc04544.js"],(function(e,n){"use strict";var r,u,o,i,c,f,s,p,d,v,m,b,y,h,g,_,w,j,O,V,k,P,C,S=document.createElement("style");return S.textContent='@charset "UTF-8";.button-box[data-v-1107200c]{padding:10px 20px}.button-box .el-button[data-v-1107200c]{float:right}.warning[data-v-1107200c]{color:#dc143c}\n',document.head.appendChild(S),{setters:[function(e){r=e.g,u=e.d,o=e.a,i=e.u,c=e.c,f=e.b},function(e){s=e.t},function(e){p=e.W},function(e){d=e._,v=e.r,m=e.h,b=e.o,y=e.d,h=e.e,g=e.j,_=e.w,w=e.F,j=e.i,O=e.f,V=e.k,k=e.t,P=e.M,C=e.P}],execute:function(){var n={class:"gva-search-box"},S={class:"gva-table-box"},x={class:"gva-btn-list"},z={style:{"text-align":"right","margin-top":"8px"}},G={class:"gva-pagination"},T={class:"dialog-footer"},A=Object.assign({name:"Api"},{setup:function(e){var d=v([]),A=v({path:"",apiGroup:"",method:"",description:""}),U=v([{value:"POST",label:"创建",type:"success"},{value:"GET",label:"查看",type:""},{value:"PUT",label:"更新",type:"warning"},{value:"DELETE",label:"删除",type:"danger"}]),E=v(""),I=v({path:[{required:!0,message:"请输入api路径",trigger:"blur"}],apiGroup:[{required:!0,message:"请输入组名称",trigger:"blur"}],method:[{required:!0,message:"请选择请求方式",trigger:"blur"}],description:[{required:!0,message:"请输入api介绍",trigger:"blur"}]}),D=v(1),F=v(0),q=v(10),B=v([]),N=v({}),K=function(){N.value={}},L=function(){D.value=1,q.value=10,J()},M=function(e){q.value=e,J()},W=function(e){D.value=e,J()},H=function(e){var t=e.prop,n=e.order;t&&("ID"===t&&(t="id"),N.value.orderKey=s(t),N.value.desc="descending"===n),J()},J=function(){var e=l(t().m((function e(){var n;return t().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,r(a({page:D.value,pageSize:q.value},N.value));case 1:0===(n=e.v).code&&(B.value=n.data.list,F.value=n.data.total,D.value=n.data.page,q.value=n.data.pageSize);case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();J();var Q=function(e){d.value=e},R=v(!1),X=function(){var e=l(t().m((function e(){var n,r;return t().w((function(e){for(;;)switch(e.n){case 0:return n=d.value.map((function(e){return e.ID})),e.n=1,u({ids:n});case 1:0===(r=e.v).code&&(P({type:"success",message:r.msg}),B.value.length===n.length&&D.value>1&&D.value--,R.value=!1,J());case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),Y=v(null),Z=v("新增Api"),$=v(!1),ee=function(e){switch(e){case"addApi":Z.value="新增Api";break;case"edit":Z.value="编辑Api"}E.value=e,$.value=!0},te=function(){Y.value.resetFields(),A.value={path:"",apiGroup:"",method:"",description:""},$.value=!1},ne=function(){var e=l(t().m((function e(n){var r;return t().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o({id:n.ID});case 1:r=e.v,A.value=r.data.api,ee("edit");case 2:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),re=function(){var e=l(t().m((function e(){return t().w((function(e){for(;;)switch(e.n){case 0:Y.value.validate(function(){var e=l(t().m((function e(n){var r;return t().w((function(e){for(;;)switch(e.n){case 0:if(!n){e.n=6;break}r=E.value,e.n="addApi"===r?1:"edit"===r?3:5;break;case 1:return e.n=2,c(A.value);case 2:return 0===e.v.code&&P({type:"success",message:"添加成功",showClose:!0}),J(),te(),e.a(3,6);case 3:return e.n=4,i(A.value);case 4:return 0===e.v.code&&P({type:"success",message:"编辑成功",showClose:!0}),J(),te(),e.a(3,6);case 5:return P({type:"error",message:"未知操作",showClose:!0}),e.a(3,6);case 6:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),ae=function(){var e=l(t().m((function e(n){return t().w((function(e){for(;;)switch(e.n){case 0:C.confirm("此操作将永久删除所有角色下该api, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(l(t().m((function e(){return t().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,f(n);case 1:0===e.v.code&&(P({type:"success",message:"删除成功!"}),1===B.value.length&&D.value>1&&D.value--,J());case 2:return e.a(2)}}),e)}))));case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}();return function(e,t){var r=m("base-input"),a=m("base-form-item"),u=m("base-option"),o=m("base-select"),l=m("base-button"),i=m("base-form"),c=m("el-popover"),f=m("el-table-column"),s=m("el-table"),v=m("el-pagination"),P=m("el-dialog");return b(),y("div",null,[h("div",n,[g(i,{ref:"searchForm",inline:!0,model:N.value},{default:_((function(){return[g(a,{label:"路径"},{default:_((function(){return[g(r,{modelValue:N.value.path,"onUpdate:modelValue":t[0]||(t[0]=function(e){return N.value.path=e}),placeholder:"路径"},null,8,["modelValue"])]})),_:1}),g(a,{label:"描述"},{default:_((function(){return[g(r,{modelValue:N.value.description,"onUpdate:modelValue":t[1]||(t[1]=function(e){return N.value.description=e}),placeholder:"描述"},null,8,["modelValue"])]})),_:1}),g(a,{label:"API组"},{default:_((function(){return[g(r,{modelValue:N.value.apiGroup,"onUpdate:modelValue":t[2]||(t[2]=function(e){return N.value.apiGroup=e}),placeholder:"api组"},null,8,["modelValue"])]})),_:1}),g(a,{label:"请求"},{default:_((function(){return[g(o,{modelValue:N.value.method,"onUpdate:modelValue":t[3]||(t[3]=function(e){return N.value.method=e}),clearable:"",placeholder:"请选择"},{default:_((function(){return[(b(!0),y(w,null,j(U.value,(function(e){return b(),O(u,{key:e.value,label:"".concat(e.label,"(").concat(e.value,")"),value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),g(a,null,{default:_((function(){return[g(l,{size:"small",type:"primary",icon:"search",onClick:L},{default:_((function(){return t[13]||(t[13]=[V("查询")])})),_:1,__:[13]}),g(l,{size:"small",icon:"refresh",onClick:K},{default:_((function(){return t[14]||(t[14]=[V("重置")])})),_:1,__:[14]})]})),_:1})]})),_:1},8,["model"])]),h("div",S,[h("div",x,[g(l,{size:"small",type:"primary",icon:"plus",onClick:t[4]||(t[4]=function(e){return ee("addApi")})},{default:_((function(){return t[15]||(t[15]=[V("新增")])})),_:1,__:[15]}),g(c,{modelValue:R.value,"onUpdate:modelValue":t[7]||(t[7]=function(e){return R.value=e}),placement:"top",width:"160"},{reference:_((function(){return[g(l,{icon:"delete",size:"small",disabled:!d.value.length,style:{"margin-left":"10px"},onClick:t[6]||(t[6]=function(e){return R.value=!0})},{default:_((function(){return t[18]||(t[18]=[V("删除")])})),_:1,__:[18]},8,["disabled"])]})),default:_((function(){return[t[19]||(t[19]=h("p",null,"确定要删除吗？",-1)),h("div",z,[g(l,{size:"small",type:"primary",link:"",onClick:t[5]||(t[5]=function(e){return R.value=!1})},{default:_((function(){return t[16]||(t[16]=[V("取消")])})),_:1,__:[16]}),g(l,{size:"small",type:"primary",onClick:X},{default:_((function(){return t[17]||(t[17]=[V("确定")])})),_:1,__:[17]})])]})),_:1,__:[19]},8,["modelValue"])]),g(s,{data:B.value,onSortChange:H,onSelectionChange:Q},{default:_((function(){return[g(f,{type:"selection",width:"55"}),g(f,{align:"left",label:"id","min-width":"60",prop:"ID",sortable:"custom"}),g(f,{align:"left",label:"API路径","min-width":"150",prop:"path",sortable:"custom"}),g(f,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup",sortable:"custom"}),g(f,{align:"left",label:"API简介","min-width":"150",prop:"description",sortable:"custom"}),g(f,{align:"left",label:"请求","min-width":"150",prop:"method",sortable:"custom"},{default:_((function(e){return[h("div",null,k(e.row.method)+" / "+k((t=e.row.method,n=U.value.filter((function(e){return e.value===t}))[0],n&&"".concat(n.label))),1)];var t,n})),_:1}),g(f,{align:"left",fixed:"right",label:"操作",width:"200"},{default:_((function(e){return[g(l,{icon:"edit",size:"small",type:"primary",link:"",onClick:function(t){return ne(e.row)}},{default:_((function(){return t[20]||(t[20]=[V("编辑")])})),_:2,__:[20]},1032,["onClick"]),g(l,{icon:"delete",size:"small",type:"primary",link:"",onClick:function(t){return ae(e.row)}},{default:_((function(){return t[21]||(t[21]=[V("删除")])})),_:2,__:[21]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"]),h("div",G,[g(v,{"current-page":D.value,"page-size":q.value,"page-sizes":[10,30,50,100],total:F.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:W,onSizeChange:M},null,8,["current-page","page-size","total"])])]),g(P,{modelValue:$.value,"onUpdate:modelValue":t[12]||(t[12]=function(e){return $.value=e}),"before-close":te,title:Z.value},{footer:_((function(){return[h("div",T,[g(l,{size:"small",onClick:te},{default:_((function(){return t[22]||(t[22]=[V("取 消")])})),_:1,__:[22]}),g(l,{size:"small",type:"primary",onClick:re},{default:_((function(){return t[23]||(t[23]=[V("确 定")])})),_:1,__:[23]})])]})),default:_((function(){return[g(p,{title:"新增API，需要在角色管理内配置权限才可使用"}),g(i,{ref_key:"apiForm",ref:Y,model:A.value,rules:I.value,"label-width":"80px"},{default:_((function(){return[g(a,{label:"路径",prop:"path"},{default:_((function(){return[g(r,{modelValue:A.value.path,"onUpdate:modelValue":t[8]||(t[8]=function(e){return A.value.path=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),g(a,{label:"请求",prop:"method"},{default:_((function(){return[g(o,{modelValue:A.value.method,"onUpdate:modelValue":t[9]||(t[9]=function(e){return A.value.method=e}),placeholder:"请选择",style:{width:"100%"}},{default:_((function(){return[(b(!0),y(w,null,j(U.value,(function(e){return b(),O(u,{key:e.value,label:"".concat(e.label,"(").concat(e.value,")"),value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),g(a,{label:"api分组",prop:"apiGroup"},{default:_((function(){return[g(r,{modelValue:A.value.apiGroup,"onUpdate:modelValue":t[10]||(t[10]=function(e){return A.value.apiGroup=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),g(a,{label:"api简介",prop:"description"},{default:_((function(){return[g(r,{modelValue:A.value.description,"onUpdate:modelValue":t[11]||(t[11]=function(e){return A.value.description=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue","title"])])}}});e("default",d(A,[["__scopeId","data-v-1107200c"]]))}}}))}();
