/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
System.register(["./index-legacy.dbc04544.js"],(function(e,a){"use strict";var t,n,l,s,o,r,i,u,c,d,f,p,m,g,b,v=document.createElement("style");return v.textContent=".el-table__body tr.current-row>td{background-color:#cee9fd!important}.el-table--enable-row-hover .el-table__body tr:hover>td{background-color:#cee9fd}.table-row-style .el-table__cell .cell .cell{color:#256ebf!important;font-size:12px}\n",document.head.appendChild(v),{setters:[function(e){t=e.r,n=e.B,l=e.h,s=e.o,o=e.d,r=e.e,i=e.j,u=e.w,c=e.k,d=e.t,f=e.f,p=e.F,m=e.i,g=e.P,b=e.<PERSON>}],execute:function(){var a={class:"role"},v={class:"header"},y={style:{"text-align":"center"}},h={style:{"font-size":"12px"}},x={style:{"text-align":"center"}},w={style:{"font-size":"12px"}},_={style:{"text-align":"center"}},z={style:{"font-size":"12px"}},k={style:{"text-align":"center"}},V={style:{"text-align":"center"}},C={class:"dialog-footer"};e("default",Object.assign({name:"RoleManagement"},{setup:function(e){var S=t(""),U=[{empno:"0001",name:"销售人员",description:"销售",member:["test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa","test","aaa"],state:1},{empno:"0002",name:"ST组织",description:"ST",member:["aaa","bbb"],state:1},{empno:"0003",name:"测试",description:"test",member:["ccc","ddd"],state:1}],T=n({name:"",description:"",member:""}),P=[{value:"zs",label:"张三"},{value:"ls",label:"李四",disabled:!0},{value:"ww",label:"王五"},{value:"zl",label:"赵六"}],R=t("add"),j=t("新增角色"),B=t(!1),F=t([]),M=function(e){console.log(e),F.value=e},q=function(e){g.confirm("是否取消新增?").then((function(){e()})).catch((function(){}))},E=function(){console.log(T),R.value="add",j.value="新增角色",B.value=!1},$=t(4),H=t(100),L=t(!0),O=t(!0),A=t(!1),D=function(e){console.log("".concat(e," items per page"))},G=function(e){console.log("current page: ".concat(e))};return function(e,t){var n=l("base-button"),F=l("base-input"),I=l("el-table-column"),J=l("SuccessFilled"),K=l("el-icon"),N=l("Remove"),Q=l("el-link"),W=l("el-table"),X=l("el-pagination"),Y=l("base-form-item"),Z=l("base-option"),ee=l("base-select"),ae=l("base-form"),te=l("el-dialog");return s(),o("div",a,[r("div",v,[i(n,{icon:e.Plus,onClick:t[0]||(t[0]=function(e){return console.log(1),j.value="新增角色",R.value="add",void(B.value=!0)})},{default:u((function(){return t[10]||(t[10]=[c("新增角色")])})),_:1,__:[10]},8,["icon"]),i(n,{icon:e.RefreshRight},{default:u((function(){return t[11]||(t[11]=[c("刷新")])})),_:1,__:[11]},8,["icon"]),i(F,{modelValue:S.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return S.value=e}),class:"w-50 m-2 organize-search",placeholder:"Search","suffix-icon":e.Search,style:{width:"15%",float:"right"}},null,8,["modelValue","suffix-icon"])]),i(W,{ref:"multipleTableRef",data:U,name:"roleTable",stripe:"",style:{width:"100%","margin-top":"5px","min-width":"1200px"},"highlight-current-row":"","class-name":"table-row-style","row-class-name":"app-table-style",onSelectionChange:M},{default:u((function(){return[i(I,{type:"selection",width:"55"}),i(I,{prop:"name",label:"角色名称",width:"180"},{header:u((function(){return t[12]||(t[12]=[r("div",{style:{"text-align":"center"}},[r("span",{style:{"font-size":"12px","font-weight":"700"}},"角色名称")],-1)])})),default:u((function(e){return[r("div",y,[r("span",h,d(e.row.name),1)])]})),_:1}),i(I,{prop:"description",label:"描述",width:"180"},{header:u((function(){return t[13]||(t[13]=[r("div",{style:{"text-align":"center"}},[r("span",{style:{"font-size":"12px","font-weight":"700"}},"描述")],-1)])})),default:u((function(e){return[r("div",x,[r("span",w,d(e.row.description),1)])]})),_:1}),i(I,{prop:"member",label:"角色成员","show-overflow-tooltip":""},{header:u((function(){return t[14]||(t[14]=[r("div",{style:{"text-align":"center"}},[r("span",{style:{"font-size":"12px","font-weight":"700"}},"角色成员")],-1)])})),default:u((function(e){return[r("div",_,[r("span",z,d(e.row.member),1)])]})),_:1}),i(I,{prop:"state",label:"状态"},{header:u((function(){return t[15]||(t[15]=[r("div",{style:{"text-align":"center"}},[r("span",{style:{"font-size":"12px","font-weight":"700"}},"状态")],-1)])})),default:u((function(e){return[r("div",k,[1===e.row.state?(s(),f(K,{key:0,style:{color:"#52c41a"}},{default:u((function(){return[i(J)]})),_:1})):(s(),f(K,{key:1},{default:u((function(){return[i(N)]})),_:1}))])]})),_:1}),i(I,{prop:"operate",label:"操作"},{header:u((function(){return t[16]||(t[16]=[r("div",{style:{"text-align":"center"}},[r("span",{style:{"font-size":"12px","font-weight":"700"}},"操作")],-1)])})),default:u((function(e){return[r("div",V,[i(Q,{type:"primary",underline:!1,style:{color:"rgba(2, 167, 240, 0.996078431372549)","margin-right":"10px","font-size":"12px","font-style":"normal","font-weight":"700"},onClick:function(a){return t=e.$index,n=e.row,console.log(t,n),j.value="编辑角色",R.value="edit",T.name=n.name,T.description="描述",T.member="zs",void(B.value=!0);var t,n}},{default:u((function(){return t[17]||(t[17]=[c(" 编辑 ")])})),_:2,__:[17]},1032,["onClick"]),i(Q,{type:"primary",underline:!1,style:{color:"rgba(2, 167, 240, 0.996078431372549)","font-size":"12px","font-style":"normal","font-weight":"700"},onClick:function(a){return t=e.$index,n=e.row,console.log(t),console.log(n),void g.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个角色吗？</strong><br><strong>删除后关联的用户将无法访问此角色关联的应用，请谨慎操作。</strong>',"删除用户",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((function(){"0001"===n.empno?b({type:"success",message:"删除成功"}):b({type:"error",message:"删除失败"})})).catch((function(){b({type:"info",message:"取消删除"})}));var t,n}},{default:u((function(){return t[18]||(t[18]=[c(" 删除 ")])})),_:2,__:[18]},1032,["onClick"])])]})),_:1})]})),_:1},512),i(X,{currentPage:$.value,"onUpdate:currentPage":t[2]||(t[2]=function(e){return $.value=e}),"page-size":H.value,"onUpdate:pageSize":t[3]||(t[3]=function(e){return H.value=e}),"page-sizes":[100,200,300,400],small:L.value,disabled:A.value,background:O.value,layout:"total, sizes, prev, pager, next, jumper",total:1e4,style:{float:"right"},class:"risk-pagination",onSizeChange:D,onCurrentChange:G},null,8,["currentPage","page-size","small","disabled","background"]),i(te,{modelValue:B.value,"onUpdate:modelValue":t[9]||(t[9]=function(e){return B.value=e}),title:j.value,width:"30%","custom-class":"custom-dialog","before-close":q},{footer:u((function(){return[r("span",C,[i(n,{onClick:t[7]||(t[7]=function(e){return E()})},{default:u((function(){return t[19]||(t[19]=[c("取消")])})),_:1,__:[19]}),i(n,{color:"#256EBF",type:"primary",onClick:t[8]||(t[8]=function(e){return E()})},{default:u((function(){return t[20]||(t[20]=[c("确定")])})),_:1,__:[20]})])]})),default:u((function(){return[i(ae,{"label-position":"right","label-width":"100px",model:T,style:{"max-width":"500px"}},{default:u((function(){return[i(Y,{label:"角色名：",prop:"name",rules:[{required:!0,message:"角色不能为空",trigger:["blur"]}]},{default:u((function(){return[i(F,{modelValue:T.name,"onUpdate:modelValue":t[4]||(t[4]=function(e){return T.name=e}),style:{width:"calc(100% - 20px)"}},null,8,["modelValue"])]})),_:1}),i(Y,{label:"描述：",prop:"description"},{default:u((function(){return[i(F,{modelValue:T.description,"onUpdate:modelValue":t[5]||(t[5]=function(e){return T.description=e}),style:{width:"calc(100% - 20px)"}},null,8,["modelValue"])]})),_:1}),i(Y,{label:"角色成员：",rules:[{required:!0,message:"请选择成员",trigger:["blur"]}]},{default:u((function(){return[i(ee,{modelValue:T.member,"onUpdate:modelValue":t[6]||(t[6]=function(e){return T.member=e}),style:{width:"calc(100% - 20px)"},placeholder:"请选择"},{default:u((function(){return[(s(),o(p,null,m(P,(function(e){return i(Z,{key:e.value,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"])})),64))]})),_:1},8,["modelValue"])]})),_:1})]})),_:1},8,["model"])]})),_:1},8,["modelValue","title"])])}}}))}}}));
