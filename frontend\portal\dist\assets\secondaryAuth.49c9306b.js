/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import e from"./verifyCode.45c1e215.js";import{r as a,c as t,a as s,b as l,F as n,h as u,i,w as c,e as o,d as r,_ as d,j as h,o as v,t as y,k as f}from"./index.bfaf04e1.js";const m={class:"secondary-auth-overlay"},p={class:"secondary-auth-container"},_={key:0,class:"auth-selector"},b={class:"auth-methods"},k={class:"auth-method-content"},I={class:"icon","aria-hidden":"true"},g=["xlink:href"],j={class:"auth-method-name"},x={class:"selector-footer"},C=d(Object.assign({name:"SecondaryAuth"},{props:{authMethods:{type:Array,default:()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:!0},{type:"email",name:"邮箱验证",icon:"email",available:!0}]},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup(d,{emit:C}){const S=d,q=a(!0),w=a(null),A=t((()=>S.authMethods.filter((e=>e.available)))),M=e=>{w.value=e,q.value=!1};1===A.value.length&&M(A.value[0]);const N=C,O=()=>{N("cancel")},B=e=>{"client"===route.query.type&&(e.clientParams={type:"client",wp:route.query.wp||"50001"}),N("verification-success",e)};return(a,t)=>{const C=h("base-avatar"),S=h("base-card"),N=h("base-button");return v(),s("div",m,[l("div",p,[q.value?(v(),s("div",_,[t[3]||(t[3]=l("h2",{class:"title"},"请选择二次认证方式",-1)),l("div",b,[(v(!0),s(n,null,u(A.value,(e=>(v(),r(S,{key:e.type,class:"auth-method-card",onClick:a=>M(e)},{default:c((()=>[l("div",k,[i(C,null,{default:c((()=>[(v(),s("svg",I,[l("use",{"xlink:href":"#icon-auth-"+e.icon},null,8,g)]))])),_:2},1024),l("div",j,y(e.name),1)])])),_:2},1032,["onClick"])))),128))]),l("div",x,[i(N,{type:"info",onClick:t[0]||(t[0]=()=>O())},{default:c((()=>t[2]||(t[2]=[f("取消")]))),_:1,__:[2]})])])):o("",!0),!q.value&&w.value?(v(),r(e,{key:1,auth_info:d.authInfo,auth_id:d.authId,"user-name":d.userName,last_id:d.lastId,"secondary-type":w.value.type,onVerificationSuccess:B,onBack:t[1]||(t[1]=e=>q.value=!0),onCancel:O},null,8,["auth_info","auth_id","user-name","last_id","secondary-type"])):o("",!0)])])}}}),[["__scopeId","data-v-3e719deb"]]);export{C as default};
