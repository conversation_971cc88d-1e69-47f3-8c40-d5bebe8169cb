/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{r as e,_ as l,B as a,p as o,h as t,o as s,d as r,e as i,j as n,w as d,k as p,t as u,g as m,f as c,O as _,m as f,M as y}from"./index.74d1ee23.js";import{_ as g}from"./customTable.09ee0d92.js";import{u as v,_ as w}from"./customFrom.6bcc3d4d.js";import{g as b,u as x,c as V}from"./agents.575e30de.js";const h=l=>[{field:"name",label:"名称：",type:"input",placeholder:"请输入名称",rules:[{required:!0,message:"名称不能为空",trigger:"blur"}]},{field:"desc",label:"描述：",type:"input",placeholder:"组件描述"},{field:"type",label:"组件类型：",type:"select",placeholder:"请选择",options:e([{value:3,label:"gateway"},{value:2,label:"connector"}]),optionsLabe:"label",optionsValue:"value",optionsKey:"value",rules:[{required:!0,message:"组件类型不能为空",trigger:"blur"}]},{field:"platform",label:"设备类型：",type:"select",placeholder:"请选择",options:e([{value:"1",label:"windows"},{value:"2",label:"linux"}]),optionsLabe:"label",optionsValue:"value",optionsKey:"value",rules:[{required:!0,message:"设备类型不能为空",trigger:"blur"}]},{field:"installCommand",label:"安装命令：",type:"input",disabled:!0,isHidden:l,placeholder:"保存后生成安装命令"}],k={class:"agents"},C={style:{"background-color":"#FFFFFF",padding:"22px 20px 60px 20px"}},F={class:"header"},j={style:{float:"right"}},N={style:{"padding-left":"17px"}},z={style:{"text-align":"center"}},O={key:0,style:{color:"#252631"}},U={key:1,style:{color:"#252631"}},E={style:{"text-align":"center"}},I={key:0,style:{color:"#252631"}},B={key:1,style:{color:"#252631"}},L={style:{"text-align":"center"}},q={key:0},A={style:{"text-align":"center"}},H={style:{width:"15px","max-width":"20px",float:"left"}},P=["id"],R=l(Object.assign({name:"Agents"},{setup(l){const{toClipboard:R}=v(),S=e(),T=e(),D=e(""),K=e([]),M=e(1),G=e(50),J=e(0),Q=e(!1);let W={formItems:[],formValues:a({})};const X=e(!1),Y=e(!1),Z=e(!1),$=e(!1),ee=e(!1),le={propList:[{prop:"app_name",label:"名称",slotName:"app_name"},{prop:"platform",label:"系统类型",slotName:"platform",isHidden:Y},{prop:"display_app_type",label:"组件类型",slotName:"display_app_type"},{prop:"app_version",label:"版本",slotName:"app_version",isHidden:Z},{prop:"desc",label:"描述",slotName:"desc"},{prop:"app_status",label:"状态",slotName:"app_status"},{prop:"operate",label:"操作",slotName:"operate"}],isIndexColumn:!0,isOperationColumn:!1},ae=async()=>{const e={limit:G.value,offset:(M.value-1)*G.value},l=await b(e);console.log("getAgentsList"),console.log(l),0===l.data.code&&(K.value=l.data.data.rows,J.value=l.data.data.total_rows)};(async()=>{await ae()})();const oe=e("add"),te=()=>{const e=Object.keys(W.formValues);let l={};e.forEach((e=>{l[e]=""})),Object.assign(W.formValues,l)},se=()=>{te(),Q.value=!1},re=async e=>{console.log("agents"),console.log(W.formValues),console.log(S.value),console.log(e),await S.value.ruleFormRef.validate((async(e,l)=>{if(!e)return y({showClose:!0,message:"字段校验失败，请检查！",type:"error"}),"";console.log(W.formValues);const a={name:W.formValues.name,desc:W.formValues.desc,type:Number(W.formValues.type),platform:1===W.formValues.platform?"windows":"linux"};let o="";if(o="edit"===oe.value?await x(a):await V(a),200===o.status&&0===o.data.code)return y.success({message:"生成命令成功"}),te(),await ae(),void(Q.value=!1);y.error({message:"生成命令失败，请重试"})}))};return o("currentPage",M),o("pageSize",G),o("total",J),o("getTableData",ae),(e,l)=>{const a=t("base-button"),o=t("base-checkbox"),v=t("el-popover"),b=t("SuccessFilled"),x=t("el-icon"),V=t("CircleCloseFilled"),M=t("el-link"),G=t("base-divider"),J=t("Close"),te=t("el-drawer");return s(),r("div",k,[i("div",C,[i("div",F,[n(a,{style:{width:"100px"},icon:e.Plus,onClick:l[0]||(l[0]=e=>(oe.value="add",D.value="添加组件",W.formItems=h(!0),void(Q.value=!0)))},{default:d((()=>l[7]||(l[7]=[p("添加组件")]))),_:1,__:[7]},8,["icon"]),n(a,{style:{width:"77px"},icon:e.RefreshRight,onClick:ae},{default:d((()=>l[8]||(l[8]=[p("刷新")]))),_:1,__:[8]},8,["icon"]),i("div",j,[n(v,{"popper-class":"custom-popover","show-arrow":!1,width:20,trigger:"click"},{reference:d((()=>[n(a,{icon:e.Filter},{default:d((()=>l[9]||(l[9]=[p("筛选")]))),_:1,__:[9]},8,["icon"])])),default:d((()=>[l[15]||(l[15]=i("div",{style:{color:"#252631","font-weight":"400","border-bottom":"1px #EBEBEB solid","text-align":"center",height:"38px","line-height":"38px"}}," 选择显示项 ",-1)),i("div",N,[n(o,{style:{"margin-top":"10px",width:"100%"},modelValue:X.value,"onUpdate:modelValue":l[1]||(l[1]=e=>X.value=e)},{default:d((()=>l[10]||(l[10]=[p("MAC")]))),_:1,__:[10]},8,["modelValue"]),n(o,{style:{"margin-top":"10px",width:"100%"},modelValue:Y.value,"onUpdate:modelValue":l[2]||(l[2]=e=>Y.value=e)},{default:d((()=>l[11]||(l[11]=[p("系统类型")]))),_:1,__:[11]},8,["modelValue"]),n(o,{style:{"margin-top":"10px",width:"100%"},modelValue:Z.value,"onUpdate:modelValue":l[3]||(l[3]=e=>Z.value=e)},{default:d((()=>l[12]||(l[12]=[p("版本")]))),_:1,__:[12]},8,["modelValue"]),n(o,{style:{"margin-top":"10px",width:"100%"},modelValue:$.value,"onUpdate:modelValue":l[4]||(l[4]=e=>$.value=e)},{default:d((()=>l[13]||(l[13]=[p("CPU")]))),_:1,__:[13]},8,["modelValue"]),n(o,{style:{"margin-top":"10px","margin-bottom":"10px",width:"100%"},modelValue:ee.value,"onUpdate:modelValue":l[5]||(l[5]=e=>ee.value=e)},{default:d((()=>l[14]||(l[14]=[p("内存 ")]))),_:1,__:[14]},8,["modelValue"])])])),_:1,__:[15]})])]),n(g,_({"table-data":K.value},le,{ref_key:"customTable",ref:T}),{app_name:d((e=>[i("div",z,[e.row.app_name?(s(),r("span",O,u(e.row.app_name),1)):(s(),r("span",U,u(e.row.name),1))])])),display_app_type:d((e=>[i("div",E,[3===e.row.display_app_type?(s(),r("span",I,"gateway")):m("",!0),2===e.row.display_app_type?(s(),r("span",B,"Connector")):m("",!0)])])),app_status:d((e=>[i("div",L,[e.row.installed?e.row.online?(s(),c(x,{key:1,size:16,style:{color:"#52c41a"}},{default:d((()=>[n(b)])),_:1})):(s(),c(x,{key:2,size:16},{default:d((()=>[n(V,{color:"#D23030"})])),_:1})):(s(),r("span",q,"---"))])])),operate:d((e=>[i("div",A,[n(M,{underline:!1,style:{"font-size":"14px",color:"#2972C8"},onClick:l=>{return a=e.row,console.log("handleEdit"),console.log(a),oe.value="edit",D.value="编辑组件",W.formItems=h(!1),W.formValues.id=a.id,W.formValues.name=a.app_name||a.name,W.formValues.desc=a.desc,W.formValues.type=a.display_app_type,W.formValues.platform="windows"===a.app_plat?"1":"2",W.formValues.installCommand="windows"===a.platform?a.command_windows:a.command_linux,void(Q.value=!0);var a}},{default:d((()=>l[16]||(l[16]=[p(" 编辑 ")]))),_:2,__:[16]},1032,["onClick"]),n(G,{direction:"vertical"}),n(M,{underline:!1,style:{"font-size":"14px",color:"#2972C8"},onClick:l=>delete e.row.id},{default:d((()=>l[17]||(l[17]=[p("删除 ")]))),_:2,__:[17]},1032,["onClick"]),e.row.command_windows||e.row.command_linux?(s(),c(G,{key:0,direction:"vertical"})):m("",!0),e.row.command_windows||e.row.command_linux?(s(),c(M,{key:1,underline:!1,style:{"font-size":"14px",color:"#2972C8"},onClick:l=>(async e=>{if(console.log(e),e)try{await R(e.toString()),y.success({message:"复制成功"})}catch(l){y.error({message:"复制失败请重试"})}else y.error({message:"命令未生成，请保存后再复制"})})("windows"===e.row.platform?e.row.command_windows:e.row.command_linux)},{default:d((()=>l[18]||(l[18]=[p("复制安装命令 ")]))),_:2,__:[18]},1032,["onClick"])):m("",!0)])])),_:1},16,["table-data"]),n(te,{modelValue:Q.value,"onUpdate:modelValue":l[6]||(l[6]=e=>Q.value=e),title:D.value,direction:"rtl","show-close":!1,size:"40%"},{header:d((({close:e,titleId:l})=>[i("div",H,[n(a,{link:"",onClick:e},{default:d((()=>[n(x,null,{default:d((()=>[n(J)])),_:1})])),_:2},1032,["onClick"])]),i("span",{id:l,class:"titleClass"},u(D.value),9,P)])),footer:d((()=>[n(a,{color:"#256EBF",type:"primary",onClick:re},{default:d((()=>l[19]||(l[19]=[p("确定 ")]))),_:1,__:[19]}),n(a,{style:{"margin-left":"10px","margin-right":"90px"},onClick:se},{default:d((()=>l[20]||(l[20]=[p("取消")]))),_:1,__:[20]})])),default:d((()=>[n(w,_({ref_key:"customForm",ref:S},f(W),{formOptions:f(W).formValues,cancel:se,submitForm:re,isFooter:!1}),null,16,["formOptions"])])),_:1},8,["modelValue","title"])])])}}}),[["__scopeId","data-v-763e9f26"]]);export{R as default};
