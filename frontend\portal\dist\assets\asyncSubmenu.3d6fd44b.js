/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{_ as e,I as t,r as a,v as n,h as o,o as s,f as u,w as l,d as r,j as c,C as i,g as f,e as m,t as d,F as p,O as v}from"./index.2320e6b9.js";const I={key:0,class:"gva-subMenu"},b=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:<PERSON><PERSON>an},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({c8e9c8aa:y.value,"6037b64a":x.value})));const b=e,h=a(b.theme.activeBackground),x=a(b.theme.activeText),y=a(b.theme.normalText);return n((()=>b.theme),(()=>{h.value=b.theme.activeBackground,x.value=b.theme.activeText,y.value=b.theme.normalText})),(t,a)=>{const n=o("component"),b=o("el-icon"),h=o("el-sub-menu");return s(),u(h,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(p,{key:1},[e.routerInfo.meta.icon?(s(),u(b,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)],64)):(s(),r("div",I,[e.routerInfo.meta.icon?(s(),u(b,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)]))])),default:l((()=>[v(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-547fcaa6"]]);export{b as default};
