/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{_ as e,a,R as s,r as l,h as o,o as u,d as n,j as c,w as t,N as r,e as v,F as i,i as d,m as p,f as m,Q as b,T as f,C as g,g as h,S as k,J as y}from"./index.2320e6b9.js";import x from"./index.3ae34784.js";const I={class:"search-component"},_={class:"transition-box",style:{display:"inline-block"}},C={key:0,class:"user-box"},j={key:1,class:"user-box"},w={key:2,class:"user-box"},B={key:3,class:"user-box"},T=e(Object.assign({name:"BtnBox"},{setup(e){const T=a(),V=s(),q=l(""),F=()=>{T.push({name:q.value}),q.value=""},J=l(!1),L=l(!0),N=()=>{J.value=!1,setTimeout((()=>{L.value=!0}),500)},O=l(null),Q=async()=>{L.value=!1,J.value=!0,await k(),O.value.focus()},R=l(!1),S=()=>{R.value=!0,y.emit("reload"),setTimeout((()=>{R.value=!1}),500)},U=()=>{window.open("https://support.qq.com/product/371961")};return(e,a)=>{const s=o("base-option"),l=o("base-select");return u(),n("div",I,[c(f,{name:"el-fade-in-linear"},{default:t((()=>[r(v("div",_,[c(l,{ref_key:"searchInput",ref:O,modelValue:q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>q.value=e),filterable:"",placeholder:"请选择",onBlur:N,onChange:F},{default:t((()=>[(u(!0),n(i,null,d(p(V).routerList,(e=>(u(),m(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])],512),[[b,J.value]])])),_:1}),L.value?(u(),n("div",C,[v("div",{class:g(["gvaIcon gvaIcon-refresh",[R.value?"reloading":""]]),onClick:S},null,2)])):h("",!0),L.value?(u(),n("div",j,[v("div",{class:"gvaIcon gvaIcon-search",onClick:Q})])):h("",!0),L.value?(u(),n("div",w,[c(x,{class:"search-icon",style:{cursor:"pointer"}})])):h("",!0),L.value?(u(),n("div",B,[v("div",{class:"gvaIcon gvaIcon-customer-service",onClick:U})])):h("",!0)])}}}),[["__scopeId","data-v-97ccbcef"]]);export{T as default};
