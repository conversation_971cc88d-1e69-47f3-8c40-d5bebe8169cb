/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{r as e,l,S as t,y as a,h as o,o as r,f as n,w as u,j as s,e as i,m as d,R as c,d as p,F as f,i as m,k as y,t as v,A as h,U as g,p as b,O as k,J as V,E as _,g as x,M as C}from"./index.74d1ee23.js";const w={style:{"border-bottom":"1px #E4E4E4 solid","border-top":"1px #E4E4E4 solid"}},$={style:{width:"100%",height:"25px","border-bottom":"1px #E4E4E4 solid"}},E={class:"custom-tree-node"},T=["onClick"],S=Object.assign({name:"CustomTransfer"},{setup(h,{expose:g}){const b=e(),k=e(!1),V=e(""),_=l("groupCheck"),x=l("userCheck"),C=l("appGroupCheck"),S=l("appCheck"),U=l("getTreeData"),A=l("defaultProps"),O=l("treeData"),j=l("treeType"),L=l("treeTotal"),R=l("treeCurrentPage"),z=l("treePageSize"),F=l("dataType"),D=async e=>(console.log(e),e.parent.parent?await D(e.parent):e.parent),P=l("checkedData"),M=l("treeSearch"),N=l("treeLabel"),q=async e=>{console.log("add check"),console.log(e);for(const l of e.childNodes)if(l.checked){console.log("chec"),console.log(l);const e={id:l.data.id,name:l.data.name,type:j.value,dataType:F.value};"group"===j.value&&"user"===F.value&&_.value.push(e),"user"===j.value&&"user"===F.value&&x.value.push(e),"group"===j.value&&"resource"===F.value&&C.value.push(e),"user"===j.value&&"resource"===F.value&&S.value.push(e),P.value.push(e)}else console.log(l),l.childNodes.length>0&&await q(l)},K=async e=>{const l=b.value.getNode(e);console.log(b.value.getCheckedKeys().length),console.log(O.value),b.value.getCheckedKeys().length===O.value.length?k.value=!0:k.value=!1,P.value.length=0,"group"===j.value&&"user"===F.value&&(_.value.length=0,P.value.push(...x.value)),"user"===j.value&&"user"===F.value&&(x.value.length=0,P.value.push(..._.value)),"group"===j.value&&"resource"===F.value&&(C.value.length=0,P.value.push(...S.value)),"user"===j.value&&"resource"===F.value&&(S.value.length=0,P.value.push(...C.value));const t=await D(l);console.log("父节点"),console.log(t),await q(t)},H=async()=>{if(console.log("setCheckedKeys"),console.log(F.value),console.log(j.value),"user"===F.value){let e=[];switch(j.value){case"user":console.log(x.value),e=x.value.map((e=>e.id)),await b.value.setCheckedKeys(e,!1);break;case"group":console.log(_.value),e=_.value.map((e=>e.id)),await b.value.setCheckedKeys(e,!1)}}if("resource"===F.value){let e=[];switch(j.value){case"user":console.log(S.value),e=S.value.map((e=>e.id)),await b.value.setCheckedKeys(e,!1);break;case"group":console.log(C.value),e=C.value.map((e=>e.id)),console.log(e),console.log(e.length),await b.value.setCheckedKeys(e,!1)}}console.log("修改全选状态"),console.log(b.value.getCheckedKeys()),b.value.getCheckedKeys().length===O.value.length?k.value=!0:k.value=!1},I=()=>{U()};t((()=>{console.log("nextTick")})),a(O,((e,l)=>{console.log("watch"),console.log(_.value),console.log(x.value),console.log(C.value),console.log(S.value),console.log(b),"user"===F.value?P.value.push(..._.value,...x.value):P.value.push(...C.value,...S.value),"group"===j.value&&"user"===F.value&&(V.value="组织名称"),"user"===j.value&&"user"===F.value&&(V.value="用户名"),"group"===j.value&&"resource"===F.value&&(V.value="资源分组名称"),"user"===j.value&&"resource"===F.value&&(V.value="资源名称"),H()}),{immediate:!0,deep:!0});const Y=e=>{k.value?(console.log(O.value),b.value.setCheckedNodes(O.value,!1)):b.value.setCheckedNodes([]),K(O.value[0])},G=()=>{console.log("removeAll"),"user"===F.value&&(_.value.length=0,x.value.length=0),"resource"===F.value&&(C.value.length=0,S.value.length=0),P.value.length=0,H()};return g({groupCheck:_,userCheck:x,appGroupCheck:C,appCheck:S}),(e,l)=>{const t=o("el-radio-button"),a=o("base-radio-group"),h=o("base-checkbox"),g=o("base-input"),U=o("el-tree"),D=o("el-scrollbar"),q=o("el-pagination"),B=o("base-col"),J=o("el-link"),X=o("base-row");return r(),n(X,{class:"custom-transfer"},{default:u((()=>[s(B,{span:12,style:{"border-right":"1px #E4E4E4 solid"}},{default:u((()=>[i("div",null,[s(a,{modelValue:d(j),"onUpdate:modelValue":l[0]||(l[0]=e=>c(j)?j.value=e:null),size:"large"},{default:u((()=>[(r(!0),p(f,null,m(d(N),(e=>(r(),n(t,{key:e.value,class:"tree-label",label:e.value,onChange:I},{default:u((()=>[y(v(e.text),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])]),i("div",w,[s(h,{style:{margin:"2px 5px 5px 22px"},modelValue:k.value,"onUpdate:modelValue":l[1]||(l[1]=e=>k.value=e),onChange:Y},null,8,["modelValue"]),s(g,{style:{width:"calc(100% - 41px)",height:"23px"},modelValue:d(M),"onUpdate:modelValue":l[2]||(l[2]=e=>c(M)?M.value=e:null),placeholder:V.value,onChange:I},null,8,["modelValue","placeholder"])]),s(D,{height:"350px",wrapRef:"scroll"},{default:u((()=>[s(U,{data:d(O),ref_key:"treeRef",ref:b,"expand-on-click-node":!1,"check-on-click-node":"",props:d(A),"show-checkbox":"","node-key":"id",onCheck:K},null,8,["data","props"])])),_:1}),s(q,{small:"",currentPage:d(R),"onUpdate:currentPage":l[3]||(l[3]=e=>c(R)?R.value=e:null),"page-size":d(z),"onUpdate:pageSize":l[4]||(l[4]=e=>c(z)?z.value=e:null),layout:"prev, pager, next",total:d(L),"onUpdate:total":l[5]||(l[5]=e=>c(L)?L.value=e:null),onCurrentChange:I},null,8,["currentPage","page-size","total"])])),_:1}),s(B,{span:12},{default:u((()=>[s(D,{wrapRef:"scroll",style:{height:"400px"}},{default:u((()=>[i("div",$,[s(J,{underline:!1,style:{margin:"5px",float:"right"},onClick:G},{default:u((()=>l[6]||(l[6]=[y("清空")]))),_:1,__:[6]})]),i("div",null,[s(U,{data:d(P),props:d(A)},{default:u((({node:e,data:l})=>[i("span",E,[i("span",null,v(e.label),1),i("span",null,[i("a",{onClick:t=>((e,l)=>{console.log("remove");const t=e.parent,a=t.data.children||t.data;console.log(l);const o=a.findIndex((e=>e.id===l.id));a.splice(o,1),P.value=[...P.value],console.log(j.value),console.log(F.value),"group"===l.type&&"user"===l.dataType&&_.value.splice(_.value.indexOf(l),1),"user"===l.type&&"user"===l.dataType&&x.value.splice(x.value.indexOf(l),1),"group"===l.type&&"resource"===l.dataType&&C.value.splice(C.value.indexOf(l),1),"user"===l.type&&"resource"===l.dataType&&S.value.splice(S.value.indexOf(l),1),H()})(e,l)},"删除",8,T)])])])),_:1},8,["data","props"])])])),_:1})])),_:1})])),_:1})}}});var U={exports:{}};
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */const A=g(U.exports=function(){return function(){var e={686:function(e,l,t){t.d(l,{default:function(){return _}});var a=t(279),o=t.n(a),r=t(370),n=t.n(r),u=t(817),s=t.n(u);function i(e){try{return document.execCommand(e)}catch(l){return!1}}var d=function(e){var l=s()(e);return i("cut"),l},c=function(e,l){var t=function(e){var l="rtl"===document.documentElement.getAttribute("dir"),t=document.createElement("textarea");t.style.fontSize="12pt",t.style.border="0",t.style.padding="0",t.style.margin="0",t.style.position="absolute",t.style[l?"right":"left"]="-9999px";var a=window.pageYOffset||document.documentElement.scrollTop;return t.style.top="".concat(a,"px"),t.setAttribute("readonly",""),t.value=e,t}(e);l.container.appendChild(t);var a=s()(t);return i("copy"),t.remove(),a},p=function(e){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},t="";return"string"==typeof e?t=c(e,l):e instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null==e?void 0:e.type)?t=c(e.value,l):(t=s()(e),i("copy")),t};function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var m=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=e.action,t=void 0===l?"copy":l,a=e.container,o=e.target,r=e.text;if("copy"!==t&&"cut"!==t)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==f(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===t&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===t&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return r?p(r,{container:a}):o?"cut"===t?d(o):p(o,{container:a}):void 0};function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function v(e,l){for(var t=0;t<l.length;t++){var a=l[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function h(e,l){return(h=Object.setPrototypeOf||function(e,l){return e.__proto__=l,e})(e,l)}function g(e){var l=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var t,a,o,r=b(e);if(l){var n=b(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return a=this,!(o=t)||"object"!==y(o)&&"function"!=typeof o?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(a):o}}function b(e){return(b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function k(e,l){var t="data-clipboard-".concat(e);if(l.hasAttribute(t))return l.getAttribute(t)}var V=function(e){!function(e,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(l&&l.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),l&&h(e,l)}(r,e);var l,t,a,o=g(r);function r(e,l){var t;return function(e,l){if(!(e instanceof l))throw new TypeError("Cannot call a class as a function")}(this,r),(t=o.call(this)).resolveOptions(l),t.listenClick(e),t}return l=r,t=[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText,this.container="object"===y(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var l=this;this.listener=n()(e,"click",(function(e){return l.onClick(e)}))}},{key:"onClick",value:function(e){var l=e.delegateTarget||e.currentTarget,t=this.action(l)||"copy",a=m({action:t,container:this.container,target:this.target(l),text:this.text(l)});this.emit(a?"success":"error",{action:t,text:a,trigger:l,clearSelection:function(){l&&l.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(e){return k("action",e)}},{key:"defaultTarget",value:function(e){var l=k("target",e);if(l)return document.querySelector(l)}},{key:"defaultText",value:function(e){return k("text",e)}},{key:"destroy",value:function(){this.listener.destroy()}}],a=[{key:"copy",value:function(e){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return p(e,l)}},{key:"cut",value:function(e){return d(e)}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],l="string"==typeof e?[e]:e,t=!!document.queryCommandSupported;return l.forEach((function(e){t=t&&!!document.queryCommandSupported(e)})),t}}],t&&v(l.prototype,t),a&&v(l,a),r}(o()),_=V},828:function(e){if("undefined"!=typeof Element&&!Element.prototype.matches){var l=Element.prototype;l.matches=l.matchesSelector||l.mozMatchesSelector||l.msMatchesSelector||l.oMatchesSelector||l.webkitMatchesSelector}e.exports=function(e,l){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(l))return e;e=e.parentNode}}},438:function(e,l,t){var a=t(828);function o(e,l,t,a,o){var n=r.apply(this,arguments);return e.addEventListener(t,n,o),{destroy:function(){e.removeEventListener(t,n,o)}}}function r(e,l,t,o){return function(t){t.delegateTarget=a(t.target,l),t.delegateTarget&&o.call(e,t)}}e.exports=function(e,l,t,a,r){return"function"==typeof e.addEventListener?o.apply(null,arguments):"function"==typeof t?o.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return o(e,l,t,a,r)})))}},879:function(e,l){l.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},l.nodeList=function(e){var t=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===t||"[object HTMLCollection]"===t)&&"length"in e&&(0===e.length||l.node(e[0]))},l.string=function(e){return"string"==typeof e||e instanceof String},l.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},370:function(e,l,t){var a=t(879),o=t(438);e.exports=function(e,l,t){if(!e&&!l&&!t)throw new Error("Missing required arguments");if(!a.string(l))throw new TypeError("Second argument must be a String");if(!a.fn(t))throw new TypeError("Third argument must be a Function");if(a.node(e))return function(e,l,t){return e.addEventListener(l,t),{destroy:function(){e.removeEventListener(l,t)}}}(e,l,t);if(a.nodeList(e))return function(e,l,t){return Array.prototype.forEach.call(e,(function(e){e.addEventListener(l,t)})),{destroy:function(){Array.prototype.forEach.call(e,(function(e){e.removeEventListener(l,t)}))}}}(e,l,t);if(a.string(e))return function(e,l,t){return o(document.body,e,l,t)}(e,l,t);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},817:function(e){e.exports=function(e){var l;if("SELECT"===e.nodeName)e.focus(),l=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var t=e.hasAttribute("readonly");t||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),t||e.removeAttribute("readonly"),l=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var a=window.getSelection(),o=document.createRange();o.selectNodeContents(e),a.removeAllRanges(),a.addRange(o),l=a.toString()}return l}},279:function(e){function l(){}l.prototype={on:function(e,l,t){var a=this.e||(this.e={});return(a[e]||(a[e]=[])).push({fn:l,ctx:t}),this},once:function(e,l,t){var a=this;function o(){a.off(e,o),l.apply(t,arguments)}return o._=l,this.on(e,o,t)},emit:function(e){for(var l=[].slice.call(arguments,1),t=((this.e||(this.e={}))[e]||[]).slice(),a=0,o=t.length;a<o;a++)t[a].fn.apply(t[a].ctx,l);return this},off:function(e,l){var t=this.e||(this.e={}),a=t[e],o=[];if(a&&l)for(var r=0,n=a.length;r<n;r++)a[r].fn!==l&&a[r].fn._!==l&&o.push(a[r]);return o.length?t[e]=o:delete t[e],this}},e.exports=l,e.exports.TinyEmitter=l}},l={};function t(a){if(l[a])return l[a].exports;var o=l[a]={exports:{}};return e[a](o,o.exports,t),o.exports}return t.n=function(e){var l=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(l,{a:l}),l},t.d=function(e,l){for(var a in l)t.o(l,a)&&!t.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:l[a]})},t.o=function(e,l){return Object.prototype.hasOwnProperty.call(e,l)},t(686)}().default}()),O=e=>{const l=void 0===(null==e?void 0:e.appendToBody)||e.appendToBody;return{toClipboard:(e,t)=>new Promise(((a,o)=>{const r=document.createElement("button"),n=new A(r,{text:()=>e,action:()=>"copy",container:void 0!==t?t:document.body});n.on("success",(e=>{n.destroy(),a(e)})),n.on("error",(e=>{n.destroy(),o(e)})),l&&document.body.appendChild(r),r.click(),l&&document.body.removeChild(r)}))}},j={style:{color:"darkgrey","font-size":"20px"}},L=["onClick"],R=["placeholder"],z={style:{float:"right","margin-right":"5px"}},F={style:{width:"100%"}},D=["src"],P=["src"],M={key:0,class:"dialog-footer"},N={class:"dialog-footer"},q={name:"CustomFrom",props:{formValues:{type:Object,required:!0},rules:{type:Object,required:!1},formItems:{type:Array,default:()=>[]},isFooter:{type:Boolean,required:!1,default:!0},itemStyle:{type:Object,default:()=>({})},colLayout:{type:Object,default:()=>({xl:24,lg:24,md:12,sm:24})},cancel:{type:Function,required:!0},submitForm:{type:Function,required:!0},addAppAddress:{type:Function,required:!1},removeAppAddress:{type:Function,required:!1}}},K=Object.assign(q,{setup(t,{expose:a}){const d=e("allDay"),c=e(),h=t,g=l("getTreeData"),w=e(!1),$=e(),E=l("dataType"),T=e(),U=l("treeType"),A=l("treeLabel"),q=l("checkedData"),K=()=>{console.log(c.value),h.formValues.groupCheck=c.value.groupCheck,h.formValues.userCheck=c.value.userCheck,h.formValues.appGroupCheck=c.value.appGroupCheck,h.formValues.appCheck=c.value.appCheck,h.formValues.user=[...c.value.groupCheck,c.value.userCheck],h.formValues.resource=[...c.value.appGroupCheck,c.value.appCheck],w.value=!1},{toClipboard:H}=O();return b("checkedData",q),a({ruleFormRef:T}),(e,l)=>{const a=o("base-input"),h=o("base-button"),b=o("base-option"),O=o("base-select"),I=o("el-tree-select"),Y=o("base-checkbox"),G=o("base-radio"),B=o("base-radio-group"),J=o("el-date-picker"),X=o("el-time-picker"),Q=o("base-form-item"),W=o("el-table-column"),Z=o("CirclePlusFilled"),ee=o("el-icon"),le=o("RemoveFilled"),te=o("el-table"),ae=o("el-tag"),oe=o("ArrowDown"),re=o("base-icon"),ne=o("el-upload"),ue=o("base-col"),se=o("base-row"),ie=o("base-form"),de=o("el-dialog");return r(),p(f,null,[s(ie,{ref_key:"ruleFormRef",ref:T,"label-position":"left","label-width":"100px",style:{"max-width":"550px","margin-left":"30px"},model:t.formValues},{default:u((()=>[s(se,null,{default:u((()=>[(r(!0),p(f,null,m(t.formItems,(o=>(r(),n(ue,k({ref_for:!0},t.colLayout),{default:u((()=>[o.isHidden?x("",!0):(r(),n(Q,{key:0,label:o.rules?o.label:"  "+o.label,rules:o.rules,style:V(o.rules?"margin-right: 35px":"margin-left:1px !important;margin-right: 35px"),prop:o.field},{default:u((()=>["input"===o.type||"password"===o.type?(r(),p(f,{key:0},[s(a,{type:o.type,placeholder:o.placeholder,disabled:o.disabled,modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e,class:_({inputDisabled:o.disabled}),autocomplete:"new-passwprd"},null,8,["type","placeholder","disabled","modelValue","onUpdate:modelValue","class"]),o.disabled?(r(),n(h,{key:0,style:{"margin-left":"7px",float:"right"},onClick:e=>(async e=>{if(e)try{await H(e.toString()),C.success({message:"复制成功"})}catch(l){C.error({message:"复制失败请重试"})}else C.error({message:"命令未生成，请保存后再复制"})})(t.formValues[`${o.field}`])},{default:u((()=>l[5]||(l[5]=[y("复制 ")]))),_:2,__:[5]},1032,["onClick"])):x("",!0)],64)):x("",!0),"date"===o.type?(r(),n(O,{key:1,modelValue:d.value,"onUpdate:modelValue":l[0]||(l[0]=e=>d.value=e),class:"m-2",placeholder:"Select",size:"small"},{default:u((()=>[s(b,{label:"全天",value:"allDay"},{default:u((()=>l[6]||(l[6]=[y("全天")]))),_:1,__:[6]}),s(b,{label:"其它",value:"other"},{default:u((()=>l[7]||(l[7]=[y("其它")]))),_:1,__:[7]})])),_:1},8,["modelValue"])):x("",!0),"treeSelect"===o.type?(r(),n(I,{key:2,props:{value:"id",label:"name",children:"subGroups"},modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e,data:o.options.value,"check-strictly":"","render-after-expand":!1},null,8,["modelValue","onUpdate:modelValue","data"])):x("",!0),"select"===o.type?(r(),n(O,{key:3,placeholder:o.placeholder,style:{width:"100%"},modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e},{default:u((()=>[(r(!0),p(f,null,m(o.options.value,(e=>(r(),n(b,{key:e[`${o.optionsKey}`],label:e[`${o.optionsLabe}`],value:e[`${o.optionsValue}`]},null,8,["label","value"])))),128))])),_:2},1032,["placeholder","modelValue","onUpdate:modelValue"])):x("",!0),"multiSelect"===o.type?(r(),n(O,{key:4,placeholder:o.placeholder,multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"},modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e},{default:u((()=>[(r(!0),p(f,null,m(o.options.value,(e=>(r(),n(b,{key:e[`${o.optionsKey}`]||e.id,label:e[`${o.optionsLabe}`]||e.name,value:e[`${o.optionsValue}`]||e.name},null,8,["label","value"])))),128))])),_:2},1032,["placeholder","modelValue","onUpdate:modelValue"])):x("",!0),"checkbox"===o.type?(r(),n(Y,{key:5,modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e,size:"large"},null,8,["modelValue","onUpdate:modelValue"])):x("",!0),"radio"===o.type?(r(),n(B,{key:6,"text-color":"#252631",modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e,class:"ml-4"},{default:u((()=>[(r(!0),p(f,null,m(o.options,(e=>(r(),n(G,{size:"large",label:e.value,key:e.value},{default:u((()=>[y(v(e.label),1)])),_:2},1032,["label"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])):x("",!0),"datepicker"===o.type||"timepicker"===o.type?(r(),n(B,{key:7,modelValue:t.formValues[`${o.dateType}`],"onUpdate:modelValue":e=>t.formValues[`${o.dateType}`]=e,class:"ml-4"},{default:u((()=>["timepicker"===o.type?(r(),n(G,{key:0,label:"0",size:"large"},{default:u((()=>l[8]||(l[8]=[y("全天")]))),_:1,__:[8]})):(r(),n(G,{key:1,label:"0",size:"large"},{default:u((()=>l[9]||(l[9]=[y("永不过期")]))),_:1,__:[9]})),s(G,{label:"1",size:"large"},{default:u((()=>l[10]||(l[10]=[y("自定义")]))),_:1,__:[10]}),"1"===t.formValues[`${o.dateType}`]&&"datepicker"===o.type?(r(),n(J,{key:2,modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e,format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",type:"date",placeholder:"请选择日期",style:{width:"44%"}},null,8,["modelValue","onUpdate:modelValue"])):"1"===t.formValues[`${o.dateType}`]&&"timepicker"===o.type?(r(),n(X,{key:3,modelValue:t.formValues[`${o.field}`],"onUpdate:modelValue":e=>t.formValues[`${o.field}`]=e,"value-format":"hh:mm:ss","is-range":"",format:"hh:mm:ss","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"44%"}},null,8,["modelValue","onUpdate:modelValue"])):x("",!0)])),_:2},1032,["modelValue","onUpdate:modelValue"])):x("",!0),"table"===o.type?(r(),n(te,{key:8,data:t.formValues[`${o.field}`],style:{width:"100%"}},{default:u((()=>[s(W,{align:"center"},{header:u((()=>l[11]||(l[11]=[i("span",{style:{color:"red"}},"*",-1),i("span",null," 协议",-1)]))),default:u((e=>[s(Q,{prop:`${o.field}[${e.$index}].protocol`,rules:o.subRules.protocol},{default:u((()=>[s(O,{modelValue:t.formValues[`${o.field}`][e.$index].protocol,"onUpdate:modelValue":l=>t.formValues[`${o.field}`][e.$index].protocol=l,size:"small",placeholder:"请选择"},{default:u((()=>[(r(!0),p(f,null,m(o.options,(e=>(r(),n(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:2},1032,["prop","rules"])])),_:2},1024),s(W,{align:"center"},{header:u((()=>l[12]||(l[12]=[i("span",{style:{color:"red"}},"*",-1),i("span",null," 服务器地址",-1)]))),default:u((e=>[s(Q,{prop:`${o.field}[${e.$index}].address`,rules:o.subRules.address},{default:u((()=>[s(a,{modelValue:t.formValues[`${o.field}`][e.$index].address,"onUpdate:modelValue":l=>t.formValues[`${o.field}`][e.$index].address=l,size:"small",placeholder:"请输入地址"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["prop","rules"])])),_:2},1024),s(W,{align:"center"},{header:u((()=>l[13]||(l[13]=[i("span",{style:{color:"red"}},"*",-1),i("span",null," 端口",-1)]))),default:u((e=>[s(Q,{prop:`${o.field}[${e.$index}].port`,rules:o.subRules.port},{default:u((()=>[s(a,{modelValue:t.formValues[`${o.field}`][e.$index].port,"onUpdate:modelValue":l=>t.formValues[`${o.field}`][e.$index].port=l,onBlur:l=>t.formValues[`${o.field}`][e.$index].port=Number(t.formValues[`${o.field}`][e.$index].port),size:"small",placeholder:"请输入端口"},null,8,["modelValue","onUpdate:modelValue","onBlur"])])),_:2},1032,["prop","rules"])])),_:2},1024),s(W,{align:"center"},{header:u((()=>l[14]||(l[14]=[i("span",null,"操作",-1)]))),default:u((e=>[i("div",j,[s(ee,{onClick:t.addAppAddress},{default:u((()=>[s(Z)])),_:1},8,["onClick"]),t.formValues[`${o.field}`].length>1?(r(),n(ee,{key:0,onClick:l=>t.removeAppAddress(e.$index)},{default:u((()=>[s(le)])),_:2},1032,["onClick"])):x("",!0)])])),_:2},1024)])),_:2},1032,["data"])):x("",!0),"dialog"===o.type?(r(),p("div",{key:9,style:{width:"100%",border:"1px #DCDFE6 double","border-radius":"4px"},onClick:e=>{return l=o.treeType,t=o.title,a=o.dataType,console.log("open1"),q.value.length=0,U.value=l,$.value=t,E.value=a,A.value="user"===a?[{text:"组织",value:"group"},{text:"用户",value:"user"}]:[{text:"分组",value:"group"},{text:"资源",value:"user"}],g(),void(w.value=!0);var l,t,a}},[i("div",{class:"custom-div",style:{width:"calc(100% - 21px)",float:"left"},placeholder:o.placeholder,contenteditable:"false"},[(r(!0),p(f,null,m(t.formValues[`${o.groupCheck}`],(e=>(r(),n(ae,{key:e.id||e.user_group_id,type:"info",effect:"light"},{default:u((()=>[y(v(e.name||e.user_group_name),1)])),_:2},1024)))),128)),(r(!0),p(f,null,m(t.formValues[`${o.userCheck}`],(e=>(r(),n(ae,{key:e.id||e.user_id,type:"info"},{default:u((()=>[y(v(e.name||e.user_name),1)])),_:2},1024)))),128)),(r(!0),p(f,null,m(t.formValues[`${o.appGroupCheck}`],(e=>(r(),n(ae,{key:e.id||e.app_group_id,type:"info"},{default:u((()=>[y(v(e.name||e.app_group_name),1)])),_:2},1024)))),128)),(r(!0),p(f,null,m(t.formValues[`${o.appCheck}`],(e=>(r(),n(ae,{key:e.id||e.app_id,type:"info"},{default:u((()=>[y(v(e.name||e.app_name),1)])),_:2},1024)))),128))],8,R),i("span",z,[s(ee,null,{default:u((()=>[s(oe)])),_:1})])],8,L)):x("",!0),"imgRadio"===o.type?(r(),p(f,{key:10},[i("span",F,[s(B,{modelValue:t.formValues[`${o.field}`].type,"onUpdate:modelValue":e=>t.formValues[`${o.field}`].type=e},{default:u((()=>[s(G,{label:"1",size:"large"},{default:u((()=>l[15]||(l[15]=[y("图标库选择")]))),_:1,__:[15]}),s(G,{label:"2",size:"large"},{default:u((()=>l[16]||(l[16]=[y("自定义上传")]))),_:1,__:[16]})])),_:2},1032,["modelValue","onUpdate:modelValue"])]),i("span",null,["1"===t.formValues[`${o.field}`].type?(r(),p("img",{key:0,style:{height:"70px"},src:o.localIcon,class:"avatar",onClick:l[1]||(l[1]=(...l)=>e.iconSelect&&e.iconSelect(...l))},null,8,D)):x("",!0),"2"===t.formValues[`${o.field}`].type?(r(),n(ne,{key:1,style:{height:"70px",width:"70px"},class:"avatar-uploader",action:"127.0.0.1:3000","show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload},{default:u((()=>[t.formValues[`${o.field}`].iconUrl?(r(),p("img",{key:0,style:{height:"70px",width:"70px"},src:t.formValues[`${o.field}`].iconUrl,class:"avatar"},null,8,P)):(r(),n(ee,{key:1,class:"avatar-uploader-icon",style:{height:"70px",width:"70px"}},{default:u((()=>[s(re,{name:"plus"})])),_:1}))])),_:2},1032,["on-success","before-upload"])):x("",!0)])],64)):x("",!0)])),_:2},1032,["label","rules","style","prop"]))])),_:2},1040)))),256))])),_:1}),t.isFooter?(r(),p("span",M,[s(h,{onClick:t.cancel},{default:u((()=>l[17]||(l[17]=[y("取消")]))),_:1,__:[17]},8,["onClick"]),s(h,{color:"#256EBF",style:{"margin-left":"10px"},type:"primary",onClick:l[2]||(l[2]=e=>t.submitForm(T.value))},{default:u((()=>l[18]||(l[18]=[y("确定")]))),_:1,__:[18]})])):x("",!0)])),_:1},8,["model"]),s(de,{modelValue:w.value,"onUpdate:modelValue":l[4]||(l[4]=e=>w.value=e),title:$.value,width:"600px",draggable:""},{footer:u((()=>[i("span",N,[s(h,{onClick:l[3]||(l[3]=e=>w.value=!1)},{default:u((()=>l[19]||(l[19]=[y("取消")]))),_:1,__:[19]}),s(h,{type:"primary",onClick:K},{default:u((()=>l[20]||(l[20]=[y(" 确定 ")]))),_:1,__:[20]})])])),default:u((()=>[s(S,{ref_key:"treeCheck",ref:c},null,512)])),_:1},8,["modelValue","title"])],64)}}});export{K as _,O as u};
