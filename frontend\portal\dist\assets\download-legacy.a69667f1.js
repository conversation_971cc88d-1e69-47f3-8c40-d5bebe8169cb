/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,o,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.toStringTag||"@@toStringTag";function l(e,i,r,a){var l=i&&i.prototype instanceof u?i:u,c=Object.create(l.prototype);return n(c,"_invoke",function(e,n,i){var r,a,l,u=0,c=i||[],s=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,n){return r=e,a=0,l=t,f.n=n,d}};function p(e,n){for(a=e,l=n,o=0;!s&&u&&!i&&o<c.length;o++){var i,r=c[o],p=f.p,w=r[2];e>3?(i=w===n)&&(l=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=t):r[0]<=p&&((i=e<2&&p<r[1])?(a=0,f.v=n,f.n=r[1]):p<w&&(i=e<3||r[0]>n||n>w)&&(r[4]=e,r[5]=n,f.n=w,a=0))}if(i||e>1)return d;throw s=!0,n}return function(i,c,w){if(u>1)throw TypeError("Generator is already running");for(s&&1===c&&p(c,w),a=c,l=w;(o=a<2?t:l)||!s;){r||(a?a<3?(a>1&&(f.n=-1),p(a,l)):f.n=l:f.v=l);try{if(u=2,r){if(a||(i="next"),o=r[i]){if(!(o=o.call(r,l)))throw TypeError("iterator result is not an object");if(!o.done)return o;l=o.value,a<2&&(a=0)}else 1===a&&(o=r.return)&&o.call(r),a<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=t}else if((o=(s=f.n<0)?l:e.call(n,f))!==d)break}catch(o){r=t,a=1,l=o}finally{u=1}}return{value:o,done:s}}}(e,r,a),!0),c}var d={};function u(){}function c(){}function s(){}o=Object.getPrototypeOf;var f=[][r]?o(o([][r]())):(n(o={},r,(function(){return this})),o),p=s.prototype=u.prototype=Object.create(f);function w(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,n(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return c.prototype=s,n(p,"constructor",s),n(s,"constructor",c),c.displayName="GeneratorFunction",n(s,a,"GeneratorFunction"),n(p),n(p,a,"Generator"),n(p,r,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:w}})()}function n(e,t,o,i){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}n=function(e,t,o,i){if(t)r?r(e,t,{value:o,enumerable:!i,configurable:!i,writable:!i}):e[t]=o;else{var a=function(t,o){n(e,t,(function(e){return this._invoke(t,o,e)}))};a("next",0),a("throw",1),a("return",2)}},n(e,t,o,i)}function t(e,n,t,o,i,r,a){try{var l=e[r](a),d=l.value}catch(e){return void t(e)}l.done?n(d):Promise.resolve(d).then(o,i)}System.register(["./iconfont-legacy.37c53566.js","./system-legacy.b5128538.js","./index-legacy.dbc04544.js","./browser-legacy.a9ffd316.js"],(function(n,o){"use strict";var i,r,a,l,d,u,c,s,f,p,w,v,h,g,y,m,x=document.createElement("style");return x.textContent='@charset "UTF-8";.icon[data-v-4e1d348e]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.client[data-v-4e1d348e]{height:100vh;text-align:center;background:#FFFFFF;max-height:calc(100vh - 68px)}.client .el-main[data-v-4e1d348e]{height:100%}.client .el-main div div:hover .window-show[data-v-4e1d348e]{display:none}.client .el-main div div:hover .window-hidden[data-v-4e1d348e]{display:block!important}.client .el-main div div:hover .window-hidden span[data-v-4e1d348e]{margin-top:42px!important}\n',document.head.appendChild(x),{setters:[function(){},function(e){i=e.g},function(e){r=e._,a=e.r,l=e.h,d=e.V,u=e.o,c=e.d,s=e.e,f=e.j,p=e.w,w=e.k,v=e.f,h=e.g,g=e.W,y=e.M},function(e){m=e.b}],execute:function(){var o={class:"client"},x={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}},b={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},_={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px","margin-left":"39%",display:"none"}},k={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},F={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-left":"39%","margin-top":"60px",display:"none"}},j={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},O={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},S={__name:"download",setup:function(n){var r=a(""),S=a(""),E=a(!1),C=a(!1),L=a(!1),M=a(!1),R=a({windows:0,darwin:0}),T=function(e){return 100===e?"完成":"".concat(e,"%")},U=function(e,n){return new Promise((function(t,o){var i=new XMLHttpRequest;i.open("GET",e,!0),i.responseType="blob",i.onprogress=function(e){if(e.lengthComputable){var t=e.loaded/e.total*100;R.value[n]=Math.round(t)}},i.onload=function(){200===i.status?t(i.response):o(new Error("下载失败"))},i.onerror=function(){o(new Error("网络错误"))},i.send()}))},z=function(e,n){if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,n);else{var t=document.createElement("a"),o=document.querySelector("body");t.href=window.URL.createObjectURL(e),t.download=n,t.style.display="none",o.appendChild(t),t.click(),o.removeChild(t),window.URL.revokeObjectURL(t.href)}G()},G=function(){E.value=!1,C.value=!1,L.value=!1,M.value=!1,Object.keys(R.value).forEach((function(e){R.value[e]=0}))},I=a(!1),P=function(){var n,o=(n=e().m((function n(t){var o,a,l,d,u,c,s,f,p,w,v,h,g,x,b,_,k,F,j;return e().w((function(e){for(;;)switch(e.n){case 0:if("android"!==t&&"ios"!==t||!I.value){e.n=1;break}return e.a(2);case 1:return I.value=!0,(o={windows:E,darwin:C,ios:L,android:M}[t]).value=!0,e.p=2,e.n=3,i({platform:t});case 3:if(0!==(a=e.v).data.code){e.n=10;break}if("ios"!==t){e.n=5;break}return e.n=4,m.toDataURL(a.data.data.download_url);case 4:l=e.v,d=document.getElementById("ioscanvas"),S.value=l,d&&(u=d.getContext("2d"),(c=new Image).onload=function(){d.width=c.width,d.height=c.height,u.drawImage(c,0,0)},c.src=l),e.n=9;break;case 5:if("android"!==t){e.n=7;break}return s=window.location.port,f=new URL(a.data.data.download_url),s?f.toString().includes("asec-deploy")?p=a.data.data.download_url:(f.port=s,p=f.toString()):(f.port="",p=f.toString()),e.n=6,m.toDataURL(p);case 6:w=e.v,v=document.getElementById("canvas"),r.value=w,v&&(h=v.getContext("2d"),(g=new Image).onload=function(){v.width=g.width,v.height=g.height,h.drawImage(g,0,0)},g.src=w),e.n=9;break;case 7:return x=window.location.port,b=new URL(a.data.data.download_url),x?(b.toString().includes("asec-deploy")?_=a.data.data.download_url:(b.port=x,_=b.toString()),k=a.data.data.latest_filename.replace(/@(\d+)/,"@".concat(x))):(b.port="",_=b.toString(),k=a.data.data.latest_filename),e.n=8,U(_,t);case 8:F=e.v,z(F,k);case 9:e.n=11;break;case 10:throw new Error(a.data.msg);case 11:e.n=13;break;case 12:e.p=12,j=e.v,y({type:"error",message:j.message||"下载失败，请联系管理员"});case 13:return e.p=13,o.value=!1,e.f(13);case 14:return e.a(2)}}),n,null,[[2,12,13,14]])})),function(){var e=this,o=arguments;return new Promise((function(i,r){var a=n.apply(e,o);function l(e){t(a,i,r,l,d,"next",e)}function d(e){t(a,i,r,l,d,"throw",e)}l(void 0)}))});return function(e){return o.apply(this,arguments)}}();return function(e,n){var t=l("el-link"),i=l("el-progress"),r=l("base-main"),a=d("loading");return u(),c("div",null,[s("div",o,[f(r,null,{default:p((function(){return[s("div",x,[s("div",{style:{float:"left","margin-right":"5%",width:"209px",height:"209px",background:"#F1F8FF"},onClick:n[0]||(n[0]=function(e){return P("windows")})},[(u(),c("svg",b,n[6]||(n[6]=[s("use",{"xlink:href":"#icon-windows"},null,-1)]))),(u(),c("svg",_,n[7]||(n[7]=[s("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),n[10]||(n[10]=s("br",null,null,-1)),f(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p((function(){return n[8]||(n[8]=[w("Windows客户端")])})),_:1,__:[8]}),f(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:p((function(){return n[9]||(n[9]=[w("点击下载Windows客户端")])})),_:1,__:[9]}),E.value?(u(),v(i,{key:0,percentage:R.value.windows,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):h("",!0)]),s("div",{style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onClick:n[1]||(n[1]=function(e){return P("darwin")})},[(u(),c("svg",k,n[11]||(n[11]=[s("use",{"xlink:href":"#icon-mac1"},null,-1)]))),(u(),c("svg",F,n[12]||(n[12]=[s("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),n[15]||(n[15]=s("br",null,null,-1)),f(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p((function(){return n[13]||(n[13]=[w("Mac客户端")])})),_:1,__:[13]}),f(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:p((function(){return n[14]||(n[14]=[w("点击下载Mac客户端")])})),_:1,__:[14]}),C.value?(u(),v(i,{key:0,percentage:R.value.darwin,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):h("",!0)]),g((u(),c("div",{"element-loading-text":"下载码生成中...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onMousemove:n[2]||(n[2]=function(e){return P("ios")}),onMouseleave:n[3]||(n[3]=function(e){return I.value=!1})},[(u(),c("svg",j,n[16]||(n[16]=[s("use",{"xlink:href":"#icon-ios"},null,-1)]))),n[18]||(n[18]=s("br",null,null,-1)),f(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p((function(){return n[17]||(n[17]=[w("iOS客户端")])})),_:1,__:[17]}),n[19]||(n[19]=s("div",{id:"ios",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[s("canvas",{id:"ioscanvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[a,L.value]]),g((u(),c("div",{"element-loading-text":"下载码生成中...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF"},onMousemove:n[4]||(n[4]=function(e){return P("android")}),onMouseleave:n[5]||(n[5]=function(e){return I.value=!1})},[(u(),c("svg",O,n[20]||(n[20]=[s("use",{"xlink:href":"#icon-Android"},null,-1)]))),n[22]||(n[22]=s("br",null,null,-1)),f(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p((function(){return n[21]||(n[21]=[w("Android客户端")])})),_:1,__:[21]}),n[23]||(n[23]=s("div",{id:"android",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[s("canvas",{id:"canvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[a,M.value]])])]})),_:1})])])}}};n("default",r(S,[["__scopeId","data-v-4e1d348e"]]))}}}))}();
