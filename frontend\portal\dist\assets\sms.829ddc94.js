/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{r as e,l as t,b as a,h as s,o as n,d as i,e as l,t as o,m as u,j as r,w as c,f as d,g as p,n as _,M as m,k as f,_ as v}from"./index.4982c0f9.js";const y={class:"sms"},h={key:0,style:{"margin-bottom":"20px"}},g={key:1,style:{"margin-bottom":"20px"}},b={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},k={style:{"text-align":"center"}},x=v(Object.assign({name:"Sms"},{props:{auth_info:{type:Object,default:function(){return{}}},auth_id:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}}},emits:["verification-success","back"],setup(v,{emit:x}){const w=e(""),I=t("userName");t("last_id");const N=t("isSecondary"),S=v,j=x,q=e(60);let C;const P=()=>{clearInterval(C)},V=async()=>{if(!S.auth_info.notPhone)return;const e={uniq_key:S.auth_info.uniqKey,idp_id:S.auth_id},t=await _(e);200===t.status&&-1!==t.data.code?(q.value=60,C=setInterval((()=>{q.value--,0===q.value&&P()}),1e3)):(m({showClose:!0,message:t.data.msg,type:"error"}),q.value=0)};V();const z=a(),K=async()=>{const e={uniq_key:S.auth_info.uniqKey,auth_code:w.value,user_name:S.userName||I.value,idp_id:S.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"},t=await z.LoginIn(e,"accessory");-1!==t.code&&j("verification-success",t)},O=()=>{j("back"),N&&(N.value=!1)};return(e,t)=>{const a=s("base-button"),_=s("base-input");return n(),i("div",y,[t[3]||(t[3]=l("div",{style:{top:"10px","margin-bottom":"25px","text-align":"center"}},[l("span",{class:"title"},"短信认证")],-1)),l("div",null,[v.auth_info.notPhone?(n(),i("div",h,"验证码已发送至您账号("+o(v.userName||u(I))+")关联的手机，请注意查收",1)):(n(),i("div",g,"您的账号("+o(v.userName||u(I))+")未关联手机号码，请联系管理员！",1)),v.auth_info.notPhone?(n(),i("div",b,[r(_,{modelValue:w.value,"onUpdate:modelValue":t[0]||(t[0]=e=>w.value=e),placeholder:"短信验证码",class:"input-with-select"},{append:c((()=>[r(a,{type:"info",disabled:q.value>0,onClick:V},{default:c((()=>[f("重新发送 "+o(q.value>0?`(${q.value}秒)`:""),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])):d("v-if",!0),l("div",k,[v.auth_info.notPhone?(n(),p(a,{key:0,type:"primary",size:"large",disabled:!w.value,onClick:K},{default:c((()=>t[1]||(t[1]=[f("确 定 ")]))),_:1,__:[1]},8,["disabled"])):d("v-if",!0),r(a,{type:"info",size:"large",onClick:O},{default:c((()=>t[2]||(t[2]=[f("取 消 ")]))),_:1,__:[2]})])])])}}}),[["__scopeId","data-v-3c142bac"],["__file","D:/asec-platform/frontend/portal/src/view/login/sms/sms.vue"]]);export{x as default};
