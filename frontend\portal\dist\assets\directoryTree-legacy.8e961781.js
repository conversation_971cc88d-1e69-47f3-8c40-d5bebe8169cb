/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,o,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",c=r.toStringTag||"@@toStringTag";function l(e,r,i,c){var l=r&&r.prototype instanceof a?r:a,s=Object.create(l.prototype);return t(s,"_invoke",function(e,t,r){var i,c,l,a=0,s=r||[],d=!1,f={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(e,t){return i=e,c=0,l=n,f.n=t,u}};function p(e,t){for(c=e,l=t,o=0;!d&&a&&!r&&o<s.length;o++){var r,i=s[o],p=f.p,y=i[2];e>3?(r=y===t)&&(l=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=n):i[0]<=p&&((r=e<2&&p<i[1])?(c=0,f.v=t,f.n=i[1]):p<y&&(r=e<3||i[0]>t||t>y)&&(i[4]=e,i[5]=t,f.n=y,c=0))}if(r||e>1)return u;throw d=!0,t}return function(r,s,y){if(a>1)throw TypeError("Generator is already running");for(d&&1===s&&p(s,y),c=s,l=y;(o=c<2?n:l)||!d;){i||(c?c<3?(c>1&&(f.n=-1),p(c,l)):f.n=l:f.v=l);try{if(a=2,i){if(c||(r="next"),o=i[r]){if(!(o=o.call(i,l)))throw TypeError("iterator result is not an object");if(!o.done)return o;l=o.value,c<2&&(c=0)}else 1===c&&(o=i.return)&&o.call(i),c<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),c=1);i=n}else if((o=(d=f.n<0)?l:e.call(t,f))!==u)break}catch(o){i=n,c=1,l=o}finally{a=1}}return{value:o,done:d}}}(e,i,c),!0),s}var u={};function a(){}function s(){}function d(){}o=Object.getPrototypeOf;var f=[][i]?o(o([][i]())):(t(o={},i,(function(){return this})),o),p=d.prototype=a.prototype=Object.create(f);function y(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,t(e,c,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=d,t(p,"constructor",d),t(d,"constructor",s),s.displayName="GeneratorFunction",t(d,c,"GeneratorFunction"),t(p),t(p,c,"Generator"),t(p,i,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:y}})()}function t(e,n,o,r){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}t=function(e,n,o,r){if(n)i?i(e,n,{value:o,enumerable:!r,configurable:!r,writable:!r}):e[n]=o;else{var c=function(n,o){t(e,n,(function(e){return this._invoke(n,o,e)}))};c("next",0),c("throw",1),c("return",2)}},t(e,n,o,r)}function n(e,t,n,o,r,i,c){try{var l=e[i](c),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(o,r)}System.register(["./iconfont-legacy.37c53566.js","./index-legacy.dbc04544.js"],(function(t,o){"use strict";var r,i,c,l,u,a,s,d,f,p,y,h,v=document.createElement("style");return v.textContent='@charset "UTF-8";.icon{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden;font-size:14px}.custom-tree-node{width:100%;line-height:33px}.custom-tree-node .tree-operate{visibility:hidden;position:absolute;right:1px}.custom-tree-node:hover .tree-operate{visibility:visible}.custom-tree-node{flex:1;align-items:center;justify-content:space-between;font-size:14px;padding-right:8px}.custom-tree-node div{width:200px;height:100%}.policy-tree .el-tree-node{overflow:hidden;max-width:200px}\n',document.head.appendChild(v),{setters:[function(){},function(e){r=e.l,i=e.h,c=e.o,l=e.d,u=e.f,a=e.w,s=e.e,d=e.j,f=e.t,p=e.g,y=e.m,h=e._}],execute:function(){var o={style:{width:"200px",height:"calc(100% - 50px)"}},v=["onClick"],g={style:{position:"absolute","margin-left":"17px"}},m={class:"tree-operate"},b=Object.assign({name:"DirectoryTree"},{props:{loadOperate:{type:Boolean,default:!0},edit:{type:Function,required:!1},append:{type:Function,required:!1},loadNode:{type:Function,required:!1},treeProps:{type:Object,required:!0}},setup:function(t){var h=r("cascaderKey"),b=r("treeRef"),x=r("open"),w=r("getTableData"),k=function(){var t,o=(t=e().m((function t(n){return e().w((function(e){for(;;)switch(e.n){case 0:return console.log("handleNodeClick"),console.log(n),b.value===n.id?b.value="":b.value=n.id,e.n=1,w();case 1:return e.a(2)}}),t)})),function(){var e=this,o=arguments;return new Promise((function(r,i){var c=t.apply(e,o);function l(e){n(c,r,i,l,u,"next",e)}function u(e){n(c,r,i,l,u,"throw",e)}l(void 0)}))});return function(e){return o.apply(this,arguments)}}();return function(e,n){var r=i("Folder"),b=i("el-icon"),w=i("el-link"),j=i("el-tree");return c(),l("div",o,[(c(),u(j,{key:y(h),icon:e.CirclePlusFilled,style:{"font-size":"12px",height:"100%",overflow:"auto","max-height":"100%"},props:t.treeProps,load:t.loadNode,lazy:"","highlight-current":"","expand-on-click-node":!1,"node-key":"id",class:"policy-tree"},{default:a((function(n){var o=n.node,i=n.data;return[s("span",{class:"custom-tree-node",onClick:function(e){return k(i)}},[d(b,{class:"icon svg-icon"},{default:a((function(){return[d(r)]})),_:1}),s("span",g,f(o.label),1),s("span",m,[t.loadOperate?(c(),u(w,{key:0,underline:!1,icon:e.CirclePlus,onClick:function(e){return t.append(i)}},null,8,["icon","onClick"])):p("",!0),i.loadDel?p("",!0):(c(),u(w,{key:1,underline:!1,style:{"margin-left":"5px"},icon:e.DeleteFilled,onClick:function(e){return function(e,t){console.log("remove"),console.log(t),x(t.id)}(0,i)}},null,8,["icon","onClick"])),d(w,{underline:!1,style:{"margin-left":"5px"},icon:e.Edit,onClick:function(e){return t.edit(o,i)}},null,8,["icon","onClick"])])],8,v)]})),_:1},8,["icon","props","load"]))])}}});t("D",h(b,[["__scopeId","data-v-c8609b35"]]))}}}))}();
