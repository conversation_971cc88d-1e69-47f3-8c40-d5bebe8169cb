/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{b as e,a,u as o,a0 as s,r as l,H as n,N as t,c as i,S as c,p as r,h as d,V as u,o as v,f as m,w as p,e as f,E as g,j as h,J as y,d as b,T as w,F as x,i as k,k as C,t as F,m as j,Z as I,W as z,a1 as O,g as R,a2 as U,z as _}from"./index.74d1ee23.js";import{_ as M}from"./ASD.492c8837.js";import S from"./index.3ef31a62.js";import{C as A}from"./index.562be0ce.js";import"./index-browser-esm.c2d3b5c9.js";import"./index.2ee16a6f.js";import"./menuItem.1131fe90.js";import"./asyncSubmenu.a28c0585.js";
/*! js-cookie v3.0.5 | MIT */function B(e){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var s in o)e[s]=o[s]}return e}var D=function e(a,o){function s(e,s,l){if("undefined"!=typeof document){"number"==typeof(l=B({},o,l)).expires&&(l.expires=new Date(Date.now()+864e5*l.expires)),l.expires&&(l.expires=l.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var n="";for(var t in l)l[t]&&(n+="; "+t,!0!==l[t]&&(n+="="+l[t].split(";")[0]));return document.cookie=e+"="+a.write(s,e)+n}}return Object.create({set:s,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var o=document.cookie?document.cookie.split("; "):[],s={},l=0;l<o.length;l++){var n=o[l].split("="),t=n.slice(1).join("=");try{var i=decodeURIComponent(n[0]);if(s[i]=a.read(t,i),e===i)break}catch(c){}}return e?s[e]:s}},remove:function(e,a){s(e,"",B({},a,{expires:-1}))},withAttributes:function(a){return e(this.converter,B({},this.attributes,a))},withConverter:function(a){return e(B({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(a)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const N={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},T={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},L={class:"header-row"},E={class:"header-col"},J={class:"header-cont"},W={class:"header-content pd-0"},V={class:"breadcrumb-col"},$={class:"breadcrumb"},H={class:"user-col"},Z={class:"right-box"},q={class:"dp-flex justify-content-center align-items height-full width-full"},G={class:"header-avatar",style:{cursor:"pointer"}},K={style:{"margin-right":"9px",color:"#252631"}},P={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},Q={key:0,class:"dropdown-menu"},X=Object.assign({name:"Layout"},{setup(B){const X=e(),Y=a(),ee=o(),ae=s(),oe=l(!0),se=l(!1),le=l(!1),ne=l("7"),te=()=>{document.body.clientWidth;le.value=!1,se.value=!1,oe.value=!0};te();const ie=l(!1);n((()=>{t.emit("collapse",oe.value),t.emit("mobile",le.value),t.on("reload",ve),t.on("showLoading",(()=>{ie.value=!0})),t.on("closeLoading",(()=>{ie.value=!1})),window.onresize=()=>(te(),t.emit("collapse",oe.value),void t.emit("mobile",le.value)),X.loadingInstance&&X.loadingInstance.close()})),i((()=>"dark"===X.sideMode?"#fff":"light"===X.sideMode?"#273444":X.baseColor));const ce=i((()=>"dark"===X.sideMode?"#273444":"light"===X.sideMode?"#fff":X.sideMode)),re=i((()=>ee.meta.matched)),de=l(!0);let ue=null;const ve=async()=>{ue&&window.clearTimeout(ue),ue=window.setTimeout((async()=>{if(ee.meta.keepAlive)de.value=!1,await c(),de.value=!0;else{const e=ee.meta.title;Y.push({name:"Reload",params:{title:e}})}}),400)},me=l(!1),pe=l(!1),fe=()=>{oe.value=!oe.value,se.value=!oe.value,me.value=!oe.value,t.emit("collapse",oe.value)},ge=()=>{pe.value=!pe.value},he=()=>{Y.push({name:"person"})};return r("day",ne),(e,a)=>{const o=d("base-aside"),s=d("router-view"),n=d("base-main"),t=d("base-container"),i=u("loading");return v(),m(t,{class:"layout-cont"},{default:p((()=>[f("div",{class:g([[se.value?"openside":"hideside",le.value?"mobile":""],"layout-wrapper"])},[f("div",{class:g([[me.value?"shadowBg":""],"shadow-overlay"]),onClick:a[0]||(a[0]=e=>(me.value=!me.value,se.value=!!oe.value,void fe()))},null,2),h(o,{class:"main-cont main-left gva-aside",collapsed:oe.value},{default:p((()=>[f("div",{class:g(["tilte",[se.value?"openlogoimg":"hidelogoimg"]]),style:y({background:ce.value})},a[3]||(a[3]=[f("img",{alt:"",class:"logoimg",src:M},null,-1)]),6),h(S,{class:"aside"}),f("div",{class:"footer",style:y({background:ce.value})},[f("div",{class:"menu-total",onClick:fe},[oe.value?(v(),b("svg",N,a[4]||(a[4]=[f("use",{"xlink:href":"#icon-expand"},null,-1)]))):(v(),b("svg",T,a[5]||(a[5]=[f("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)])),_:1},8,["collapsed"]),h(n,{class:"main-cont main-right"},{default:p((()=>[h(w,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:p((()=>[f("div",{style:y({width:`calc(100% - ${le.value?"0px":oe.value?"54px":"220px"})`}),class:"topfix"},[f("div",L,[f("div",E,[f("header",J,[f("div",W,[a[10]||(a[10]=f("div",{class:"header-menu-col",style:{"z-index":"100"}},null,-1)),f("div",V,[f("nav",$,[(v(!0),b(x,null,k(re.value.slice(1,re.value.length),(e=>(v(),b("div",{key:e.path,class:"breadcrumb-item"},[C(F(j(I)(e.meta.topTitle||"",j(ee)))+" ",1),"总览"===e.meta.title?z((v(),b("select",{key:0,"onUpdate:modelValue":a[1]||(a[1]=e=>ne.value=e),class:"day-select form-select"},a[6]||(a[6]=[f("option",{value:"7"},"最近7天",-1),f("option",{value:"30"},"最近30天",-1),f("option",{value:"90"},"最近90天",-1)]),512)),[[O,ne.value]]):R("",!0)])))),128))])]),f("div",H,[f("div",Z,[f("div",{class:"dropdown",onClick:ge},[f("div",q,[f("span",G,[h(A),f("span",K,F(j(X).userInfo.displayName?j(X).userInfo.displayName:j(X).userInfo.name),1),(v(),b("svg",P,a[7]||(a[7]=[f("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),pe.value?(v(),b("div",Q,[f("div",{class:"dropdown-item",onClick:he},a[8]||(a[8]=[f("svg",{class:"icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-avatar"})],-1),C(" 个人信息 ")])),f("div",{class:"dropdown-item",onClick:a[2]||(a[2]=e=>(async()=>{document.location.protocol,document.location.host;const e={action:1,msg:"",platform:document.location.hostname},a=l({}),o=l("ws://127.0.0.1:50001"),s=navigator.platform;0!==s.indexOf("Mac")&&"MacIntel"!==s||(o.value="wss://127.0.0.1:50001");const n=async e=>{console.log(e,"0"),await a.value.send(e)},t=async()=>{console.log("socket断开链接"),await a.value.close()};console.log(`asecagent://?web=${JSON.stringify(e)}`),await X.LoginOut(),a.value=new WebSocket(o.value),a.value.onopen=async()=>{console.log("socket连接成功"),await n(JSON.stringify(e))},a.value.onmessage=async e=>{console.log(e),await t()},a.value.onerror=()=>{console.log("socket连接错误")},D.remove("asce_sms")})())},a[9]||(a[9]=[f("svg",{class:"icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-reading-lamp"})],-1),C(" 登 出 ")]))])):R("",!0)])])])])])])])],4)])),_:1}),de.value?z((v(),m(s,{key:0,"element-loading-text":"正在加载中",class:"admin-box"},{default:p((({Component:e})=>[f("div",null,[h(w,{mode:"out-in",name:"el-fade-in-linear"},{default:p((()=>[(v(),m(U,{include:j(ae).keepAliveRouters},[(v(),m(_(e)))],1032,["include"]))])),_:2},1024)])])),_:1})),[[i,ie.value]]):R("",!0)])),_:1})],2)])),_:1})}}});export{X as default};
