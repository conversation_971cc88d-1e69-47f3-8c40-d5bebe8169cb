/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{r as e,u as t,v as n,o,d as a,e as i,B as r}from"./index.2320e6b9.js";const s=Object.assign({name:"Wechat"},{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(s){const d=e(0),u=s,c=t(),l=async()=>{var e,t,n,o;const a=window.location.host;let i=`${window.location.protocol}//${a}/#/status`;if(null==(e=c.query)?void 0:e.redirect){const e=(null==(t=c.query)?void 0:t.redirect.indexOf("?"))>-1?null==(o=c.query)?void 0:o.redirect.substring((null==(n=c.query)?void 0:n.redirect.indexOf("?"))+1):"";i=i+"?"+e}else if(c.query){const e=new URLSearchParams;for(const[t,n]of Object.entries(c.query))e.append(t,n);i=i+"?"+e.toString()}await(async()=>{const e={type:"qiyewx",data:{idpId:u.auth_id}},t=await r(e);if(200===t.status)return t.data.uniqKey})(),setTimeout((()=>{window.getQRCode({id:"qr_login",appid:u.auth_info.wxCorpId,agentid:u.auth_info.wxAgentId,redirect_uri:encodeURIComponent(i+"&auth_type=qiyewx"),state:u.auth_id,href:"",lang:"zh"});const e=document.querySelector("iframe");e.contentWindow.location.href!==e.src?console.log("iframe已重新加载"):console.log("iframe未重新加载")}),100)};return l(),n(u,(async(e,t)=>{d.value++,await l()})),(e,t)=>(o(),a("div",{key:d.value},t[0]||(t[0]=[i("div",{id:"qr_login",slot:"content",class:"wechat-class"},null,-1)])))}});export{s as default};
