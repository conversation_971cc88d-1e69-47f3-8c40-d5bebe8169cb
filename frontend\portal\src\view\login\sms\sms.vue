<template>
  <div class="sms">
    <div style="top: 10px;margin-bottom: 25px;text-align: center">
      <span class="title">短信认证</span>
    </div>
    <div>
      <div v-if="auth_info.notPhone" style="margin-bottom: 20px">验证码已发送至您账号({{ userName || user_name }})关联的手机，请注意查收</div>
      <div v-else style="margin-bottom: 20px">您的账号({{ userName || user_name }})未关联手机号码，请联系管理员！</div>
      <div v-if="auth_info.notPhone" class="mt-4" style="margin-bottom: 25px">
        <base-input
          v-model="auth_code"
          placeholder="短信验证码"
          class="input-with-select"
        >
          <template #append>
            <base-button type="info" :disabled="count_down>0" @click="send_sms">重新发送
              {{ count_down > 0 ? `(${count_down}秒)` : '' }}
            </base-button>
          </template>
        </base-input>
      </div>
      <div style="text-align: center;">
        <base-button
            v-if="auth_info.notPhone"
          type="primary"
          size="large"
          :disabled="!auth_code"
          @click="verify"
        >确 定
        </base-button>
        <base-button
          type="info"
          size="large"
          @click="cancel"
        >取 消
        </base-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Sms'
}
</script>
<script setup>
import { inject, ref } from 'vue'
import { post_send_sms } from '@/api/sms'
import { useUserStore } from '@/pinia/modules/user'
import {ElMessage} from "element-plus";

const auth_code = ref('')
const user_name = inject('userName')
const last_id = inject('last_id')
const isSecondary = inject('isSecondary')
const props = defineProps({
  auth_info: {
    type: Object,
    default: function() {
      return {}
    },
  },
  auth_id: {
    type: String,
    default: function() {
      return ''
    }
  },
  userName: {
    type: String,
    default: function() {
      return ''
    }
  },
  lastId: {
    type: String,
    default: function() {
      return ''
    }
  }
})

const emit = defineEmits(['verification-success', 'back'])

const count_down = ref(60)

let timerId
const startCountDown = () => {
  count_down.value = 60
  timerId = setInterval(() => {
    count_down.value--
    if (count_down.value === 0) {
      stopCountDown()
    }
  }, 1000)
}

const stopCountDown = () => {
  clearInterval(timerId)
}

const send_sms = async() => {
  if (!props.auth_info.notPhone) return
  const query = {
    uniq_key: props.auth_info.uniqKey,
    idp_id: props.auth_id
  }
  const res = await post_send_sms(query)
  if (res.status === 200 && res.data.code !== -1) {
    startCountDown()
  } else {
    ElMessage({
      showClose: true,
      message: res.data.msg,
      type: 'error',
    })
    count_down.value = 0
  }
}

send_sms()

const userStore = useUserStore()
const verify = async() => {
  const query = {
    uniq_key: props.auth_info.uniqKey,
    auth_code: auth_code.value,
    user_name: props.userName || user_name.value,
    idp_id: props.auth_id,
    redirect_uri: 'hello world',
    grant_type: 'implicit',
    client_id: 'client_portal'
  }

  const res = await userStore.LoginIn(query, 'accessory')
  if (res.code === -1) {
    return
  }
  
  // 验证成功时发出事件
  emit('verification-success', res)
}

const cancel = () => {
  emit('back')
  const host = window.location.host
  const protocol = window.location.protocol
  isSecondary ? isSecondary.value = false : ''
  // location.href = `${protocol}//${host}/#/login?idp_id=${props.last_id}`
}
</script>
<style lang="scss" scoped>
.sms {
  .title {
    height: 60px;
    font-size: 24px;
    text-align: center;
  }
}
</style>