/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{x as e,r as l,h as a,o as t,d as o,e as s,j as i,w as r,k as u,t as d,m as n,f as p,M as c}from"./index.74d1ee23.js";import{f as v}from"./format.8e96a47e.js";import"./date.23f5a973.js";import"./dictionary.aa4d2fe8.js";import"./sysDictionary.2a470765.js";const m={class:"gva-search-box"},f={class:"gva-table-box"},g={class:"gva-btn-list"},_={style:{"text-align":"right","margin-top":"8px"}},y={class:"popover-box"},h={key:1},b={class:"popover-box"},w={key:1},k={style:{"text-align":"right","margin-top":"8px"}},C={class:"gva-pagination"},V=Object.assign({name:"SysOperationRecord"},{setup(V){const z=l(1),x=l(0),O=l(10),S=l([]),j=l({}),R=()=>{j.value={}},I=()=>{z.value=1,O.value=10,""===j.value.status&&(j.value.status=null),N()},U=e=>{O.value=e,N()},D=e=>{z.value=e,N()},N=async()=>{const l=await(a={page:z.value,pageSize:O.value,...j.value},e({url:"/sysOperationRecord/getSysOperationRecordList",method:"get",params:a}));var a;0===l.code&&(S.value=l.data.list,x.value=l.data.total,z.value=l.data.page,O.value=l.data.pageSize)};N();const A=l(!1),B=l([]),E=e=>{B.value=e},J=async()=>{const l=[];B.value&&B.value.forEach((e=>{l.push(e.ID)}));var a;0===(await(a={ids:l},e({url:"/sysOperationRecord/deleteSysOperationRecordByIds",method:"delete",data:a}))).code&&(c({type:"success",message:"删除成功"}),S.value.length===l.length&&z.value>1&&z.value--,A.value=!1,N())},L=async l=>{l.visible=!1;var a;0===(await(a={ID:l.ID},e({url:"/sysOperationRecord/deleteSysOperationRecord",method:"delete",data:a}))).code&&(c({type:"success",message:"删除成功"}),1===S.value.length&&z.value>1&&z.value--,N())},M=e=>{try{return JSON.parse(e)}catch(l){return e}};return(e,l)=>{const c=a("base-input"),V=a("base-form-item"),N=a("base-button"),P=a("base-form"),T=a("el-popover"),q=a("el-table-column"),F=a("el-tag"),G=a("warning"),H=a("el-icon"),K=a("el-table"),Q=a("el-pagination");return t(),o("div",null,[s("div",m,[i(P,{inline:!0,model:j.value},{default:r((()=>[i(V,{label:"请求方法"},{default:r((()=>[i(c,{modelValue:j.value.method,"onUpdate:modelValue":l[0]||(l[0]=e=>j.value.method=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),i(V,{label:"请求路径"},{default:r((()=>[i(c,{modelValue:j.value.path,"onUpdate:modelValue":l[1]||(l[1]=e=>j.value.path=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),i(V,{label:"结果状态码"},{default:r((()=>[i(c,{modelValue:j.value.status,"onUpdate:modelValue":l[2]||(l[2]=e=>j.value.status=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),i(V,null,{default:r((()=>[i(N,{size:"small",type:"primary",icon:"search",onClick:I},{default:r((()=>l[6]||(l[6]=[u("查询")]))),_:1,__:[6]}),i(N,{size:"small",icon:"refresh",onClick:R},{default:r((()=>l[7]||(l[7]=[u("重置")]))),_:1,__:[7]})])),_:1})])),_:1},8,["model"])]),s("div",f,[s("div",g,[i(T,{modelValue:A.value,"onUpdate:modelValue":l[5]||(l[5]=e=>A.value=e),placement:"top",width:"160"},{reference:r((()=>[i(N,{icon:"delete",size:"small",style:{"margin-left":"10px"},disabled:!B.value.length,onClick:l[4]||(l[4]=e=>A.value=!0)},{default:r((()=>l[10]||(l[10]=[u("删除")]))),_:1,__:[10]},8,["disabled"])])),default:r((()=>[l[11]||(l[11]=s("p",null,"确定要删除吗？",-1)),s("div",_,[i(N,{size:"small",type:"primary",link:"",onClick:l[3]||(l[3]=e=>A.value=!1)},{default:r((()=>l[8]||(l[8]=[u("取消")]))),_:1,__:[8]}),i(N,{size:"small",type:"primary",onClick:J},{default:r((()=>l[9]||(l[9]=[u("确定")]))),_:1,__:[9]})])])),_:1,__:[11]},8,["modelValue"])]),i(K,{ref:"multipleTable",data:S.value,style:{width:"100%"},"tooltip-effect":"dark","row-key":"ID",onSelectionChange:E},{default:r((()=>[i(q,{align:"left",type:"selection",width:"55"}),i(q,{align:"left",label:"操作人",width:"140"},{default:r((e=>[s("div",null,d(e.row.user.userName)+"("+d(e.row.user.nickName)+")",1)])),_:1}),i(q,{align:"left",label:"日期",width:"180"},{default:r((e=>[u(d(n(v)(e.row.CreatedAt)),1)])),_:1}),i(q,{align:"left",label:"状态码",prop:"status",width:"120"},{default:r((e=>[s("div",null,[i(F,{type:"success"},{default:r((()=>[u(d(e.row.status),1)])),_:2},1024)])])),_:1}),i(q,{align:"left",label:"请求IP",prop:"ip",width:"120"}),i(q,{align:"left",label:"请求方法",prop:"method",width:"120"}),i(q,{align:"left",label:"请求路径",prop:"path",width:"240"}),i(q,{align:"left",label:"请求",prop:"path",width:"80"},{default:r((e=>[s("div",null,[e.row.body?(t(),p(T,{key:0,placement:"left-start",trigger:"click"},{reference:r((()=>[i(H,{style:{cursor:"pointer"}},{default:r((()=>[i(G)])),_:1})])),default:r((()=>[s("div",y,[s("pre",null,d(M(e.row.body)),1)])])),_:2},1024)):(t(),o("span",h,"无"))])])),_:1}),i(q,{align:"left",label:"响应",prop:"path",width:"80"},{default:r((e=>[s("div",null,[e.row.resp?(t(),p(T,{key:0,placement:"left-start",trigger:"click"},{reference:r((()=>[i(H,{style:{cursor:"pointer"}},{default:r((()=>[i(G)])),_:1})])),default:r((()=>[s("div",b,[s("pre",null,d(M(e.row.resp)),1)])])),_:2},1024)):(t(),o("span",w,"无"))])])),_:1}),i(q,{align:"left",label:"按钮组"},{default:r((e=>[i(T,{modelValue:e.row.visible,"onUpdate:modelValue":l=>e.row.visible=l,placement:"top",width:"160"},{reference:r((()=>[i(N,{icon:"delete",size:"small",type:"primary",link:"",onClick:l=>e.row.visible=!0},{default:r((()=>l[14]||(l[14]=[u("删除")]))),_:2,__:[14]},1032,["onClick"])])),default:r((()=>[l[15]||(l[15]=s("p",null,"确定要删除吗？",-1)),s("div",k,[i(N,{size:"small",type:"primary",link:"",onClick:l=>e.row.visible=!1},{default:r((()=>l[12]||(l[12]=[u("取消")]))),_:2,__:[12]},1032,["onClick"]),i(N,{size:"small",type:"primary",onClick:l=>L(e.row)},{default:r((()=>l[13]||(l[13]=[u("确定")]))),_:2,__:[13]},1032,["onClick"])])])),_:2,__:[15]},1032,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"]),s("div",C,[i(Q,{"current-page":z.value,"page-size":O.value,"page-sizes":[10,30,50,100],total:x.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:D,onSizeChange:U},null,8,["current-page","page-size","total"])])])])}}});export{V as default};
