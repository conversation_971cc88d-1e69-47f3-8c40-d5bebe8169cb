/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
System.register(["./index-legacy.11b10372.js"],(function(e,n){"use strict";var t,r,u,i,o,l,a,c,f,d,s,v;return{setters:[function(e){t=e._,r=e.R,u=e.h,i=e.o,o=e.d,l=e.j,a=e.w,c=e.T,f=e.g,d=e.V,s=e.m,v=e.x}],execute:function(){var n=Object.assign({name:"RouterHolder"},{setup:function(e){var n=r();return function(e,t){var r=u("router-view");return i(),o("div",null,[l(r,null,{default:a((function(e){var t=e.Component;return[l(c,{mode:"out-in",name:"el-fade-in-linear"},{default:a((function(){return[(i(),f(d,{include:s(n).keepAliveRouters},[(i(),f(v(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}});e("default",t(n,[["__file","D:/asec-platform/frontend/portal/src/view/routerHolder.vue"]]))}}}));
