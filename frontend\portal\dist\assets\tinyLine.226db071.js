/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{T as e}from"./index.22c7594f.js";import{r as t,H as n,o as a,d as i}from"./index.74d1ee23.js";const r=Object.assign({name:"TinyLine"},{props:{data:{default:function(){return[]},type:Array}},setup(r){const s=r,l=t(null);let o;return n((()=>{o=new e(l.value,{height:35,autoFit:!0,data:s.data,smooth:!1,step:5,point:{size:1,shape:"circle",style:{fill:"#2972C8",stroke:"#2972C8",lineWidth:1}},lineStyle:{lineWidth:1}}),o.render()})),(e,t)=>(a(),i("div",{ref_key:"container",ref:l},null,512))}});export{r as default};
