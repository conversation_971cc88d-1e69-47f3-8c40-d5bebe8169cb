<template xmlns="http://www.w3.org/1999/html">
    <base-container class="layout-cont">
        <base-container
            :class="[isSider ? 'openside' : 'hideside', isMobile ? 'mobile' : '']">
            <base-row :class="[isShadowBg ? 'shadowBg' : '']"
                @click="changeShadow()" />
            <base-aside class="main-cont main-left gva-aside">
                <div class="tilte"
                    :class="[isSider ? 'openlogoimg' : 'hidelogoimg']"
                    :style="{ background: backgroundColor }">
                    <img alt class="logoimg" src="@/assets/ASD.png">
                </div>
                <div style="background: '#273444'">
                    <el-scrollbar style="height: calc(100vh - 110px)">
                        <transition
                            :duration="{ enter: 800, leave: 100 }"
                            mode="out-in"
                            name="el-fade-in-linear">
                            <el-menu
                                :collapse="isCollapse"
                                :collapse-transition="false"
                                :default-active="active"
                                :background-color="theme.background"
                                :active-text-color="theme.activeText"
                                class="el-menu-vertical"
                                unique-opened>
                                <el-menu-item index="1">
                                    <el-icon>
                                        <component
                                            class="iconfont icon-zuhu-kehuduanxiazai" />
                                    </el-icon>
                                    <span>客户端下载</span>
                                </el-menu-item>
                            </el-menu>
                        </transition>
                    </el-scrollbar>
                </div>
                <div class="footer"
                    :style="{ background: backgroundColor }">
                    <div class="menu-total" @click="totalCollapse">
                        <el-icon v-if="isCollapse" color="#FFFFFF"
                            size="14px">
                            <Expand />
                        </el-icon>
                        <el-icon v-else color="#FFFFFF" size="14px">
                            <Fold />
                        </el-icon>
                    </div>
                </div>
            </base-aside>
            <base-main class="main-cont main-right client">
                <div class="downloadWin">
                    <div
                        style="margin-bottom: 5%;float: left;margin-right: 5%;width: 205px;height: 209px;background: #F1F8FF;position: relative;"
                        @click="download('windows')"
                    >
                        <svg class="icon window-show"
                            aria-hidden="true"
                            style="font-size: 43px;margin-top: 60px">
                            <use xlink:href="#icon-windows" />
                        </svg>
                        <svg
                            class="icon window-hidden"
                            aria-hidden="true"
                            style="font-size: 43px;margin-bottom: 42px;margin-top: 60px;display: none">
                            <use xlink:href="#icon-xiazai" />
                        </svg>
                        <br>
                        <el-link class="window-show"
                            :underline="false"
                            style="margin-top: 42px">
                            Windows客户端
                        </el-link>
                        <el-link
                            class="window-hidden"
                            :underline="false"
                            style="margin-top: 42px;display: none">
                            点击下载Windows客户端
                        </el-link>
                        
                        <el-progress
                            v-if="windowsloading"
                            :percentage="downloadProgress"
                            :format="progressFormat"
                            :stroke-width="10"
                            style="margin-top: 20px;"
                        />
                        
                        <div v-if="downloadComplete" class="download-complete">下载完成</div>
                    </div>
                </div>
            </base-main>
        </base-container>
    </base-container>
</template>

<script>
export default {
    name: 'downloadWin',
}
</script>

<script setup>
import '../../assets/ali/iconfont.css'
import '@/assets/ali/iconfont'
import { emitter } from '@/utils/bus.js'
import { computed, ref, onMounted, nextTick, getCurrentInstance, provide } from 'vue'
import { getDownloadUrl } from '@/api/system'
import { ElMessage } from 'element-plus'
import QRCode from 'qrcode'

// 三种窗口适配
const isCollapse = ref(false)
const isSider = ref(true)
const isMobile = ref(false)
const active = ref('1')
const theme = ref({})
theme.value = {
    background: '#273444',
    activeBackground: '#4D70FF',
    activeText: '#fff',
    normalText: '#fff',
    hoverBackground: 'rgba(64, 158, 255, 0.08)',
    hoverText: '#fff',
}
const windowsloading = ref(false)
const downloadProgress = ref(0)
const downloadComplete = ref(false)
const maxRetries = 3
let retryCount = 0

const initPage = () => {
    const screenWidth = document.body.clientWidth
    if (screenWidth < 1000) {
        isMobile.value = false
        isSider.value = false
        isCollapse.value = true
    } else if (screenWidth >= 1000 && screenWidth < 1200) {
        isMobile.value = false
        isSider.value = false
        isCollapse.value = true
    } else {
        isMobile.value = false
        isSider.value = true
        isCollapse.value = false
    }
}

initPage()

const loadingFlag = ref(false)
onMounted(() => {
    emitter.emit('collapse', isCollapse.value)
    emitter.emit('mobile', isMobile.value)
    emitter.on('showLoading', () => {
        loadingFlag.value = true
    })
    emitter.on('closeLoading', () => {
        loadingFlag.value = false
    })
    window.onresize = () => {
        return (() => {
            initPage()
            emitter.emit('collapse', isCollapse.value)
            emitter.emit('mobile', isMobile.value)
        })()
    }
})

const backgroundColor = ref('#1f2a36')

const isShadowBg = ref(false)
const totalCollapse = () => {
    isCollapse.value = !isCollapse.value
    isSider.value = !isCollapse.value
    isShadowBg.value = !isCollapse.value
    emitter.emit('collapse', isCollapse.value)
}

const changeShadow = () => {
    isShadowBg.value = !isShadowBg.value
    isSider.value = !!isCollapse.value
    totalCollapse()
}

const progressFormat = (percentage) => {
    if (percentage === 100) return '下载完成'
    return `${percentage}%`
}

const download = async (type) => {
    if (type !== 'windows') return

    windowsloading.value = true
    downloadProgress.value = 0
    downloadComplete.value = false
    retryCount = 0

    try {
        const res = await getDownloadUrl({ platform: type })
        if (res.data.code === 0) {
            const newPort = window.location.port
            const serverUrl = new URL(res.data.data.download_url)

            // 修改下载 URL 的端口
            let modifiedDownloadUrl

            if (newPort) {
              //asec-deploy.oss-cn-guangzhou.aliyuncs.com 公有云下载不需要修改端口
              if (serverUrl.toString().includes('asec-deploy')) {
                modifiedDownloadUrl = res.data.data.download_url
              }else{
                serverUrl.port = newPort
                modifiedDownloadUrl = serverUrl.toString()
              }
            }else{
                serverUrl.port = ''
                modifiedDownloadUrl = serverUrl.toString()
            }
            
            // 修改文件名中的端口
            const modifiedFilename = newPort
                ? res.data.data.latest_filename.replace(/@(\d+)/, `@${newPort}`)
                : res.data.data.latest_filename

            await myDownLoad(modifiedDownloadUrl, modifiedFilename)
            downloadComplete.value = true
            ElMessage({
                type: 'success',
                message: '下载完成',
            })
        } else {
            throw new Error(res.data.msg)
        }
    } catch (error) {
        ElMessage({
            type: 'error',
            message: error.message || '下载失败，请联系管理员',
        })
    } finally {
        windowsloading.value = false
        setTimeout(() => {
            downloadComplete.value = false
        }, 3000)
    }
}

const myDownLoad = async (url, fileName) => {
    try {
        const blob = await getBolb(url)
        saveAs(blob, fileName)
    } catch (error) {
        if (retryCount < maxRetries && error.message === '网络连接超时') {
            retryCount++
            return myDownLoad(url, fileName)
        }
        throw new Error(`安装包下载失败，请检查网络连接或联系管理员。错误: ${error.message}`)
    }
}

const getBolb = (url) => {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        
        xhr.timeout = 5 * 60 * 1000

        let startTime = Date.now()

        xhr.onprogress = (event) => {
            if (event.lengthComputable) {
                const percentComplete = (event.loaded / event.total) * 100
                downloadProgress.value = Math.round(percentComplete)
            } else {
                const elapsedTime = (Date.now() - startTime) / 1000
                const downloadSpeed = event.loaded / elapsedTime
                const estimatedTotal = downloadSpeed * 60
                const estimatedProgress = (event.loaded / estimatedTotal) * 100
                downloadProgress.value = Math.min(99, Math.round(estimatedProgress))
            }
        }
        
        xhr.onload = () => {
            if (xhr.status === 200) {
                resolve(xhr.response)
            } else {
                reject(new Error(`HTTP 错误: ${xhr.status}`))
            }
        }
        
        xhr.onerror = () => {
            reject(new Error('网络错误'))
        }

        xhr.ontimeout = () => {
            reject(new Error('网络连接超时'))
        }
        
        xhr.send()
    })
}

const saveAs = (blob, filename) => {
    if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
    } else {
        const link = document.createElement('a')
        const body = document.querySelector('body')

        link.href = window.URL.createObjectURL(blob)
        link.download = filename

        link.style.display = 'none'
        body.appendChild(link)

        link.click()
        body.removeChild(link)

        window.URL.revokeObjectURL(link.href)
    }
}

</script>
<style lang="scss" scoped>
.dark {
    background-color: #273444 !important;
    color: #fff !important;
}

.light {
    background-color: #fff !important;
    color: #000 !important;
}

.icon-rizhi1 {
    span {
        margin-left: 5px;
    }
}

.day-select {
    height: 23px;
    width: 88px;
    margin-left: 15px;

    div {
        height: 23px;
        width: 88px;

        input {
            height: 23px;
            width: 50px;
            font-size: 12px;
            color: #2972C8;
        }
    }
}

.right-box {
    margin-top: 9px;
}

.hidelogoimg {
    overflow: hidden !important;
    width: 54px !important;
    padding-left: 9px !important;

    .logoimg {
        margin-left: 7px;
    }
}

*,
*::before,
*::after {
    box-sizing: border-box;
}

// .el-menu{
//     border-bottom: none;
// }

.el-menu--collapse {
    .el-menu-item.is-active {
        color: v-bind(activeBackground);
        opacity: 100%;
    }
}

.el-menu-item,
.el-sub-menu {
    color: v-bind(normalText);
    font-size: 14px;
    height: 44px;
    line-height: 40px;
    // margin: 6px 0px 6px 0px;
    color: #fff;
    border-left: 4px #273444 solid;
    width: 100%;
    justify-content: flex-start;
}

.hideside {
    .el-menu-item {
        padding: 0 12px;

        &.is-active {
            .iconfont {
                color: #4D70FF;
            }
        }

    }
}


.el-menu-item.is-active,
.el-sub-menu.is-active {
    background: #465566 !important;
    border-left: 4px #71BDDF solid;
    border-bottom: none;
    opacity: 100%;
}

.el-menu-item:hover {
    background: #303E4E !important;
}

.menu-info {
    .menu-contorl {
        line-height: 52px;
        font-size: 20px;
        display: table-cell;
        vertical-align: middle;
    }
}

.layout-cont .main-cont .menu-total {
    margin-left: 20px;
}

.client {
    text-align: center;
    background: #FFFFFF;
    max-height: calc(100vh);
    padding: 20% 25%;

    .downloadWin {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon {
            width: 1em;
            height: 1em;
            vertical-align: -0.15em;
            fill: currentColor;
            overflow: hidden;
        }

        div:hover {
            .window-show {
                display: none;
            }

            .window-hidden {
                display: initial !important;

                span {
                    margin-top: 42px !important;
                }
            }
        }
    }
}

.download-complete {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
  color: #4D70FF;
}
</style>