/*! 
 Build based on gin-vue-admin 
 Time : 1749566068000 */
import{_ as e,o as t,d as u,e as n,F as a,i as c,C as o,t as i}from"./index.2320e6b9.js";const r=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],s={class:"layout-aside"},m={class:"menu-wrapper"},d=["onClick"],l={class:"icon menu-item-icon","aria-hidden":"true"},h=["xlink:href"],p={class:"menu-item-title"};const g=e({name:"ClientMenu",data:()=>({currentRouteCode:"101"}),computed:{computedMenu(){return this.computedMenuFun()}},mounted(){this.$router.push({path:"/client/main",query:[]})},watch:{$route:{handler(e,t){if(logger.log("路由变化",e,t),e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun(){const e=[];return r&&r.forEach((t=>{if(t.meta&&t.meta.menu){const{name:u,icon:n,uiId:a}=t.meta.menu,c={name:u,icon:n,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:a};e.push(c)}})),e},changeMenu(e,t={},u=0){const n={...t,menuClick:!0};logger.log(e,n),this.$router.push({path:e,query:n}),this.currentRouteCode=this.cutOut(u)},routerInterceptor(e){const t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:e=>e&&e.length?e.substr(0,3):e}},[["render",function(e,r,g,C,f,M){return t(),u("div",s,[n("ul",m,[(t(!0),u(a,null,c(M.computedMenu,(e=>(t(),u("li",{key:e.code,class:o(["menu-item",M.cutOut(e.code)===f.currentRouteCode?"active-menu-item":""]),onClick:t=>M.changeMenu(e.url,e.params,e.code)},[(t(),u("svg",l,[n("use",{"xlink:href":"#"+e.icon+(M.cutOut(e.code)===f.currentRouteCode?"-active":"")},null,8,h)])),n("div",p,i(e.name),1)],10,d)))),128))])])}],["__scopeId","data-v-32753310"]]);export{g as default};
