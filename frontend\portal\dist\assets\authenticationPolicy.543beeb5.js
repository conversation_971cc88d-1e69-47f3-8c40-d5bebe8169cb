/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{D as e}from"./directoryTree.447276d1.js";import t from"./addPolicyForm.8d1db131.js";import{_ as a,r as l,p as o,h as s,o as n,d as i,j as r,w as p,e as d,k as c,t as u,f as g,g as f,P as m,M as y}from"./index.74d1ee23.js";import"./iconfont.2d75af05.js";const v={class:"common-layout"},h={style:{"text-align":"center"}},x={style:{"font-size":"12px"}},w={style:{"text-align":"center"}},b={style:{"font-size":"12px"}},_={style:{"text-align":"center"}},z={style:{"font-size":"12px"}},k={style:{"text-align":"center"}},P={style:{"font-size":"12px"}},C={style:{"text-align":"center",height:"20px","padding-top":"10px"}},j={style:{"text-align":"center"}},A=a(Object.assign({name:"AuthenticationPolicy"},{setup(a){const A=l(!1),S=l("group"),U=l("新增策略"),I=l(""),V=l("");o("checkedGroupId",V);const T=e=>{m.confirm("是否取消新增?").then((()=>{e()})).catch((()=>{}))},B=l({}),F=()=>{console.log("refresh"),console.log(V.value)},R=[{empno:"0001",name:"本地账户",description:"本地账户",scope:"销管团队",mode:"本地账户",state:1},{empno:"0001",name:"南京研发认证",description:"test",scope:"",mode:"LDAP/AD",state:0}],$=e=>{console.log(e)},D=l(4),E=l(100),L=l(!0),M=l(!0),O=l(!1),q=e=>{console.log(`${e} items per page`)},G=e=>{console.log(`current page: ${e}`)},H=()=>{A.value=!1};return(a,l)=>{const o=s("base-aside"),V=s("base-button"),J=s("base-input"),K=s("el-table-column"),N=s("SuccessFilled"),Q=s("el-icon"),W=s("Remove"),X=s("el-link"),Y=s("el-table"),Z=s("el-pagination"),ee=s("base-main"),te=s("base-container"),ae=s("el-dialog");return n(),i("div",v,[r(te,{style:{height:"100%"}},{default:p((()=>[r(o,{width:"200px",style:{"min-height":"calc(100vh - 200px)"}},{default:p((()=>[l[5]||(l[5]=d("div",{style:{height:"35px"}},[d("span",{class:"menu-label"},"用户目录")],-1)),r(e,{loadOperate:!1})])),_:1,__:[5]}),r(ee,null,{default:p((()=>[r(V,{style:{"font-size":"12px",height:"28px"},color:"#256EBF",type:"primary",icon:a.Plus,onClick:l[0]||(l[0]=e=>{return t="app",U.value="group"===t?"新增分组":"新增策略",S.value=t,B.value={group:"",name:"",description:"",webPortal:"",sdp:"",appAddress:[{protocol:"",serverAddress:"",port:""}],appIcon:"1",iconUrl:"",localIcon:"/src/assets/noBody.png"},void(A.value=!0);var t})},{default:p((()=>l[6]||(l[6]=[c("新增策略 ")]))),_:1,__:[6]},8,["icon"]),r(V,{style:{"font-size":"12px",height:"28px"},icon:a.RefreshRight,onClick:F},{default:p((()=>l[7]||(l[7]=[c("刷新 ")]))),_:1,__:[7]},8,["icon"]),r(J,{modelValue:I.value,"onUpdate:modelValue":l[1]||(l[1]=e=>I.value=e),class:"w-50 m-2 organize-search",placeholder:"请输入用户名","suffix-icon":a.Search,style:{width:"150px"}},null,8,["modelValue","suffix-icon"]),r(Y,{data:R,stripe:"",name:"appTable","header-row-class-name":"table-header","row-class-name":"app-table-style",style:{width:"100%","margin-top":"5px","min-width":"1200px"},onSelectionChange:$},{default:p((()=>[r(K,{type:"selection",width:"55"}),r(K,{prop:"name",label:"名称",width:"180"},{header:p((()=>l[8]||(l[8]=[d("div",{style:{"text-align":"center"}},[d("span",{style:{"font-size":"12px","font-weight":"700"}},"名称")],-1)]))),default:p((e=>[d("div",h,[d("span",x,u(e.row.name),1)])])),_:1}),r(K,{prop:"description",label:"描述",width:"180"},{header:p((()=>l[9]||(l[9]=[d("div",{style:{"text-align":"center"}},[d("span",{style:{"font-size":"12px","font-weight":"700"}},"描述")],-1)]))),default:p((e=>[d("div",w,[d("span",b,u(e.row.description),1)])])),_:1}),r(K,{prop:"scope",label:"用户范围"},{header:p((()=>l[10]||(l[10]=[d("div",{style:{"text-align":"center"}},[d("span",{style:{"font-size":"12px","font-weight":"700"}},"用户范围")],-1)]))),default:p((e=>[d("div",_,[d("span",z,u(e.row.scope),1)])])),_:1}),r(K,{prop:"mode",label:"认证方式"},{header:p((()=>l[11]||(l[11]=[d("div",{style:{"text-align":"center"}},[d("span",{style:{"font-size":"12px","font-weight":"700"}},"认证方式")],-1)]))),default:p((e=>[d("div",k,[d("span",P,u(e.row.mode),1)])])),_:1}),r(K,{prop:"state",label:"状态"},{header:p((()=>l[12]||(l[12]=[d("div",{style:{"text-align":"center"}},[d("span",{style:{"font-size":"12px","font-weight":"700"}},"状态")],-1)]))),default:p((e=>[d("div",C,[1===e.row.state?(n(),g(Q,{key:0,style:{color:"#52c41a"}},{default:p((()=>[r(N)])),_:1})):(n(),g(Q,{key:1},{default:p((()=>[r(W)])),_:1}))])])),_:1}),r(K,{prop:"operate",label:"操作"},{header:p((()=>l[13]||(l[13]=[d("div",{style:{"text-align":"center"}},[d("span",{style:{"font-size":"12px","font-weight":"700"}},"操作")],-1)]))),default:p((e=>[d("div",j,[r(X,{type:"primary",underline:!1,style:{color:"rgba(2, 167, 240, 0.996078431372549)","margin-right":"10px","font-size":"12px","font-style":"normal","font-weight":"700"},onClick:t=>{return a=e.$index,l=e.row,console.log(a,l),U.value="编辑策略",S.value="app",B.value.group="qywx",B.value.name="ERP",B.value.description="描述",B.value.webPortal="www.aaa.com",B.value.sdp="123456",B.value.appAddress=[{protocol:"http",serverAddress:"***********",port:"8080"}],B.value.appIcon="1",B.value.iconUrl="",B.value.localIcon="/src/assets/dashboard.png",void(A.value=!0);var a,l}},{default:p((()=>l[14]||(l[14]=[c(" 编辑 ")]))),_:2,__:[14]},1032,["onClick"]),r(X,{type:"primary",underline:!1,style:{color:"rgba(2, 167, 240, 0.996078431372549)","font-size":"12px","font-style":"normal","font-weight":"700"},onClick:t=>{return a=e.$index,l=e.row,console.log(a),console.log(l),void m.confirm('<strong>确定要删除选中的<i style="color: #0d84ff">1</i>个策略吗？</strong><br><strong>删除后用户将无法登录和访问应用，请谨慎操作。</strong>',"删除策略",{dangerouslyUseHTMLString:!0,confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((()=>{"0001"===l.empno?y({type:"success",message:"删除成功"}):y({type:"error",message:"删除失败"})})).catch((()=>{y({type:"info",message:"取消删除"})}));var a,l}},{default:p((()=>l[15]||(l[15]=[c(" 删除 ")]))),_:2,__:[15]},1032,["onClick"])])])),_:1})])),_:1}),r(Z,{currentPage:D.value,"onUpdate:currentPage":l[2]||(l[2]=e=>D.value=e),"page-size":E.value,"onUpdate:pageSize":l[3]||(l[3]=e=>E.value=e),"page-sizes":[100,200,300,400],small:L.value,disabled:O.value,background:M.value,layout:"total, sizes, prev, pager, next, jumper",total:1e4,style:{float:"right"},class:"risk-pagination",onSizeChange:q,onCurrentChange:G},null,8,["currentPage","page-size","small","disabled","background"])])),_:1})])),_:1}),A.value?(n(),g(ae,{key:0,modelValue:A.value,"onUpdate:modelValue":l[4]||(l[4]=e=>A.value=e),title:U.value,"custom-class":"custom-dialog",width:"31.7%","before-close":T},{default:p((()=>[r(t,{type:S.value,"form-data":B.value,onSubmitForm:H},null,8,["type","form-data"])])),_:1},8,["modelValue","title"])):f("",!0)])}}}),[["__scopeId","data-v-3071d543"]]);export{A as default};
