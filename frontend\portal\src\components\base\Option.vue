<template>
  <div 
    class="base-option"
    :class="{ 
      'is-selected': isSelected,
      'is-disabled': disabled 
    }"
    @click="handleClick"
  >
    <slot>{{ label }}</slot>
  </div>
</template>

<script>
export default {
  name: 'BaseOption',
  props: {
    value: {
      type: [String, Number, Boolean],
      required: true
    },
    label: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  inject: ['select'],
  computed: {
    isSelected() {
      return this.select.modelValue === this.value
    }
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      this.select.handleOptionClick(this.value, this.label || this.$el.textContent)
    }
  }
}
</script>

<style scoped>
.base-option {
  padding: 8px 12px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.base-option:hover {
  background-color: #f5f7fa;
}

.base-option.is-selected {
  color: #409eff;
  background-color: #f0f9ff;
}

.base-option.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.base-option.is-disabled:hover {
  background-color: transparent;
}
</style>
