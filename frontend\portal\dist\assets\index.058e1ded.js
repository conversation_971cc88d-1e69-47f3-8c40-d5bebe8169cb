/*! 
 Build based on gin-vue-admin 
 Time : 1749549075000 */
import{s as e,r as a,z as l,D as s,E as n,b as t,u as o,h as c,o as r,d as i,e as u,j as p,w as d,f as v,_ as m,F as f,i as g,g as b,k,t as y,C as _,G as h,H as w,M as W}from"./index.4982c0f9.js";const S={class:"person"},C={class:"el-search"},D={class:"category-title"},x={class:"apps-container"},U={key:0,class:"status-badge"},O={class:"icon-wrapper"},T={class:"tooltip-content text-center"},A={key:0},E={key:1},I={class:"app-info"},F={class:"app-name"},P=m(Object.assign({name:"AppPage"},{setup(m){const P=a(""),V=a(null),N=a([]),$=a([]),z=a("1"),B=a(!1),J=a("standard"),L=l([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),M=a(null),j=a(!1),q=(e,a="success",l=3e3)=>{W({message:e,type:a,duration:l})},H=async e=>new Promise(((a,l)=>{let s,n=!1;(async()=>{try{const t=await new Promise(((e,a)=>{if(M.value&&M.value.readyState===WebSocket.OPEN)return void e(M.value);const l=new WebSocket("ws://localhost:50001");j.value=!0,l.onopen=()=>{console.log("WebSocket Connected"),M.value=l,j.value=!1,e(l)},l.onmessage=e=>{const a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&q(a,"error")},l.onclose=()=>{console.log("WebSocket Disconnected"),M.value=null,j.value=!1},l.onerror=e=>{console.error("WebSocket Error:",e),j.value=!1,a(e)},setTimeout((()=>{j.value&&(j.value=!1,l.close(),a(new Error("连接超时")))}),5e3)})),o={action:3,msg:e};s=setTimeout((()=>{n||(t.close(),l(new Error("启动超时：未收到响应")))}),3e3),t.onmessage=e=>{n=!0,clearTimeout(s);const t=e.data;t.startsWith("Ok")?a():l(new Error(t))},t.send(JSON.stringify(o)),console.log("发送消息:",o)}catch(t){clearTimeout(s),l(t)}})()}));s((()=>{M.value&&(M.value.close(),M.value=null)}));const R=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let l=0;for(let s=0;s<e.length;s++)l+=e.charCodeAt(s);return a[l%a.length]},G=()=>{B.value=!0},X=e=>{V.value=parseInt(e),$.value=e?N.value.filter((a=>a.id===parseInt(e))):N.value},K=()=>{if(!P.value)return void($.value=N.value);const e=P.value.toLowerCase().trim();$.value=N.value.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))},Q=async()=>{try{const{data:a}=await e({url:"/console/v1/application/getuserapp",method:"get"});if(console.log("API返回数据:",a),0===a.code&&a.data){const e=a.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl})))})));console.log("格式化后的数据:",e),N.value=e,$.value=e,e.length>0&&(V.value=e[0].id,z.value=e[0].id.toString())}}catch(a){console.error("API调用出错:",a)}};n((()=>{Q()}));const Y=t(),Z=o().query,ee=new XMLHttpRequest;ee.open("GET",document.location,!1),ee.send(null);const ae=ee.getResponseHeader("X-Corp-ID"),le={action:0,msg:{token:Y.token.accessToken,refreshToken:Y.token.refreshToken,realm:ae||"default"},platform:document.location.hostname};{const e=Z.wp||50001,l=a({}),s=a(`ws://127.0.0.1:${e}`),n=navigator.platform;0!==n.indexOf("Mac")&&"MacIntel"!==n||(s.value=`wss://127.0.0.1:${e}`);const t=()=>{l.value=new WebSocket(s.value),l.value.onopen=()=>{console.log("socket连接成功"),o(JSON.stringify(le))},l.value.onmessage=e=>{console.log(e),c()},l.value.onerror=()=>{console.log("socket连接错误:"+s.value),window.location.href=`asecagent://?web=${JSON.stringify(le)}`}},o=e=>{console.log(e,"0"),l.value.send(e)},c=()=>{console.log("socket断开链接"),l.value.close()};console.log(`asecagent://?web=${JSON.stringify(le)}`),t()}return(e,a)=>{const l=c("base-input"),s=c("base-button"),n=c("base-option"),t=c("base-select"),o=c("el-header"),m=c("el-menu-item"),W=c("el-menu"),V=c("base-aside"),M=c("base-avatar"),j=c("el-tooltip"),Q=c("el-link"),Y=c("base-main"),Z=c("base-container");return r(),i("div",null,[u("div",S,[p(o,null,{default:d((()=>[a[3]||(a[3]=u("span",{class:"el-title"},"我的应用",-1)),u("span",C,[p(l,{class:"el-search-input",modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),placeholder:"搜索应用","prefix-icon":"Search",onInput:K,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),p(s,{class:"el-search-btn",icon:"Refresh",size:"small"}),p(t,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:J.value,"onUpdate:modelValue":a[1]||(a[1]=e=>J.value=e),placeholder:"Select",size:"small"},{default:d((()=>[(r(!0),i(f,null,g(L,(e=>(r(),b(n,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),v('\r\n        <div class="el-row">\r\n          <span class="el-recent-access">最新访问</span>\r\n          <span class="el-recent-data">\r\n            <span class="el-recent-item">\r\n              最新访问1\r\n              <el-icon class="el-recent-icon">\r\n                <Close />\r\n              </el-icon>\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问2\r\n              <el-icon class="el-recent-icon">\r\n                <Close />\r\n              </el-icon>\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问3\r\n              <el-icon class="el-recent-icon">\r\n                <Close />\r\n              </el-icon>\r\n            </span>\r\n            <el-icon class="el-recent-clear" title="清空">\r\n              <Delete />\r\n            </el-icon>\r\n          </span>\r\n        </div>\r\n        ')])),_:1,__:[3]}),v(" 主体内容区域：使用 el-container 实现左右布局 "),p(Z,null,{default:d((()=>[v(" 左侧分类导航 "),p(V,{width:"96px",class:"category-aside"},{default:d((()=>[p(W,{class:"category-menu",onSelect:X,"default-active":z.value},{default:d((()=>[p(m,{index:"0",onClick:a[2]||(a[2]=e=>X(null))},{default:d((()=>a[4]||(a[4]=[k(" 全部 ")]))),_:1,__:[4]}),(r(!0),i(f,null,g(N.value,(e=>(r(),b(m,{key:e.id,index:e.id.toString()},{default:d((()=>[k(y(e.name),1)])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),v(" 右侧应用列表 "),p(Y,{class:"app-main"},{default:d((()=>[(r(!0),i(f,null,g($.value,(e=>(r(),i("div",{key:e.id,class:"category-section"},[v(" 分类标题 "),u("h3",D,y(e.name),1),v(" 应用列表 "),u("div",x,[(r(!0),i(f,null,g(e.apps,(e=>(r(),i("div",{key:e.id,class:_(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(r(),i("div",U," 维护中 ")):v("v-if",!0),p(Q,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:h((a=>(async e=>{if(e.WebUrl&&!e.maint)if(e.WebUrl.toLowerCase().startsWith("cs:")){const l=e.WebUrl.substring(3);try{q("正在启动爱尔企业浏览器...","info"),await H(l),q("启动成功","success")}catch(a){q("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else window.open(e.WebUrl,"_blank")})(e)),["prevent"])},{default:d((()=>[u("div",O,[p(j,{effect:"light",placement:"bottom"},{content:d((()=>[u("div",T,[e.WebUrl?(r(),i("span",A,y(e.WebUrl),1)):(r(),i("span",E,"暂无访问地址"))])])),default:d((()=>[p(M,{shape:"square",size:48,src:e.icon,onError:G,style:w(!e.icon||B.value?`background-color: ${R(e.app_name)} !important`:"")},{default:d((()=>[k(y(!e.icon||B.value?e.app_name.slice(0,2):""),1)])),_:2},1032,["src","style"])])),_:2},1024)]),u("div",I,[u("div",F,y(e.app_name),1),a[5]||(a[5]=u("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])])),_:2},1032,["disabled","onClick"])],2)))),128))])])))),128))])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-f0c977cd"],["__file","D:/asec-platform/frontend/portal/src/view/app/index.vue"]]);export{P as default};
