<template>
  <div class="login-page">
    <div class="header">
      <img src="@/assets/logo_login.png" alt="公司logo" class="logo">
      <!-- <h1 class="company-name">安数达</h1>
      <span class="separator"></span> -->
      <span class="header-text">ASec安全平台</span>
    </div>
    <div class="content">
      <div class="left-panel">
        <!-- <h2 class="slogan">让办公无界，让数据无忧！</h2> -->
        <img src="@/assets/login_building.png" alt="宣传图" class="image">
          <!-- <div class="icons">
          <img src="@/assets/aq.png" alt="图标1">
          <img src="@/assets/sd.png" alt="图标2">
          <img src="@/assets/cj.png" alt="图标3">
        </div> -->
      </div>
      <div class="right-panel">
        <!-- 显示当前认证状态 -->
        <div v-if="!showSecondaryAuth" class="auth-status">
          <div class="status-icon">
            <svg class="icon" aria-hidden="true" style="height: 48px; width: 48px; color: #0082ef;">
              <use xlink:href="#icon-auth-qiyewx" />
            </svg>
          </div>
          <h3 class="status-title">正在登录</h3>
          <p class="status-message">正在处理信息...</p>
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        
        <!-- 如果需要二次认证，显示等待提示 -->
        <div v-else class="auth-waiting">
          <div class="waiting-icon">
            <svg class="icon" aria-hidden="true" style="height: 32px; width: 32px; color: #f4a261;">
              <use xlink:href="#icon-auth-verify_code" />
            </svg>
          </div>
          <h4 class="waiting-title">需要进行安全验证</h4>
          <p class="waiting-message">请完成二次身份验证以确保账户安全</p>
        </div>
      </div>
    </div>

    <!-- 新的辅助认证组件 -->
    <SecondaryAuth
      v-if="showSecondaryAuth"
      :auth-info="{ 
        uniqKey: uniqKey,
        contactType: contactType,
        hasContactInfo: hasContactInfo
      }"
      :auth-id="auth_id"
      :user-name="userName" 
      :last-id="last_id"
      :auth-methods="authMethods"
      @verification-success="handleSecondarySuccess"
      @cancel="handleCancelAuth"
    />
  </div>
</template>

<script>
export default {
  name: 'Status'
}
</script>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import { Loading } from '@/components/base'
import { computed, provide, ref } from 'vue'
// import Sms from '@/view/login/sms/sms.vue'
import SecondaryAuth from '@/view/login/secondaryAuth/secondaryAuth.vue'
// 使用轻量级 SVG 图标，已在 main.js 中全局加载

const route = useRoute()
const router = useRouter()
const auth_info = ref({})
let { code, state } = route.query
const { auth_type, redirect_url, type, wp } = route.query
const host = window.location.host
const protocol = window.location.protocol

const userStore = useUserStore()
const loadingInstance = ref(null)

// 二次认证相关变量
const showSecondaryAuth = ref(false)
const secondary = ref([])
const isSecondary = ref(false)
const uniqKey = ref('')
const auth_id = ref('')
const last_id = ref(Array.isArray(state) ? state[0] : state)
const userName = ref('')
const contactType = ref('phone') // 默认为手机
const hasContactInfo = ref(true) // 默认有联系信息

// 计算可用的认证方法
const authMethods = computed(() => {
  return [
    {
      type: 'sms',
      name: '短信验证',
      icon: 'duanxin',
      available: contactType.value === 'phone' 
    },
    {
      type: 'email',
      name: '邮箱验证',
      icon: 'email',
      available: contactType.value === 'email' 
    }
  ]
})

// 处理辅助认证成功
const handleSecondarySuccess = async (result) => {
  loadingInstance.value = Loading.service({
    fullscreen: true,
    text: '认证成功，正在跳转...',
  })
  
  try {
    // 构建跳转URL，保留客户端参数
    let targetUrl = redirect_url || '/'
    
    if (result.clientParams) {
      const params = new URLSearchParams()
      params.set('type', result.clientParams.type)
      if (result.clientParams.wp) {
        params.set('wp', result.clientParams.wp)
      }
      targetUrl += (targetUrl.includes('?') ? '&' : '?') + params.toString()
    }
    
    window.location.href = targetUrl
  } finally {
    loadingInstance.value?.close()
  }
}

// 处理取消认证
const handleCancelAuth = () => {
  showSecondaryAuth.value = false
  isSecondary.value = false
  
  // 构建返回登录页的URL，保留客户端参数
  const queryParams = new URLSearchParams()
  queryParams.set('idp_id', Array.isArray(state) ? state[0] : state)
  
  if (redirect_url) {
    queryParams.set('redirect', encodeURIComponent(redirect_url))
  }
  if (type === 'client') {
    queryParams.set('type', 'client')
    if (wp) {
      queryParams.set('wp', wp)
    }
  }
  
  const loginUrl = `/login?${queryParams.toString()}`
  
  // 统一使用 router.push 而不是 location.href
  router.push(loginUrl)
}

const init = async() => {
  loadingInstance.value = Loading.service({
    fullscreen: true,
    text: '登录中，请稍候...',
  })

  try {
    const query = {
      clientId: 'client_portal',
      grantType: 'implicit',
      redirect_uri: `${protocol}//${host}/#/status`,
      idpId: Array.isArray(state) ? state[0] : state,
      authWeb: {
        authWebCode: Array.isArray(code) ? code[0] : code,
      }
    }
    const res = await userStore.LoginIn(query, auth_type, last_id.value)
    
    if (res.code !== -1) {
      if (res.isSecondary) {
        // 设置二次认证相关信息
        isSecondary.value = res.isSecondary
        secondary.value = res.secondary
        auth_id.value = res.secondary[0].id
        uniqKey.value = res.uniqKey
        userName.value = res.userName
        // 设置联系方式信息
        contactType.value = res.contactType
        hasContactInfo.value = res.hasContactInfo || false
        
        // 设置认证信息
        auth_info.value.uniqKey = res.uniqKey
        auth_info.value.name = res.secondary[0].name
        auth_info.value.notPhone = res.notPhone
        
        // 显示新的辅助认证组件
        showSecondaryAuth.value = true
      }
    } else {
      let redirectUrl = `${protocol}//${host}/#/login?idp_id=${Array.isArray(state) ? state[0] : state}`
      if (redirect_url) {
        redirectUrl += `&redirect=${redirect_url}`
      }
      // 保留客户端参数
      if (type === 'client') {
        redirectUrl += `&type=client`
        if (wp) {
          redirectUrl += `&wp=${wp}`
        }
      }
      location.href = redirectUrl
    }
  } catch (error) {
    console.error('登录处理失败:', error)
    let redirectUrl = `${protocol}//${host}/#/login?idp_id=${Array.isArray(state) ? state[0] : state}`
    // 保留客户端参数
    if (type === 'client') {
      redirectUrl += `&type=client`
      if (wp) {
        redirectUrl += `&wp=${wp}`
      }
    }
    location.href = redirectUrl
  } finally {
    loadingInstance.value?.close()
  }
}

init()

// const getLoginType = computed(() => {
//   switch (auth_type) {
//     case 'sms':
//       return Sms
//     default:
//       return Sms
//   }
// })

const selectAuthType = (auth_infos) => {
  // 更新认证类型和相关信息
  auth_id.value = auth_infos.id
  
  if (auth_infos.type !== 'sms') {
    // 非短信认证类型的处理
    auth_info.value = { ...auth_infos.attrs }
  } else {
    // 短信认证类型的处理
    auth_info.value.authType = auth_infos.type
    auth_info.value.uniqKey = uniqKey.value
    auth_info.value.name = auth_infos.name
  }
  
  auth_info.value.name = auth_infos.name
  
  // 切换到新的辅助认证组件
  showSecondaryAuth.value = true
}

const secondary_data = computed(() => {
  return secondary.value.filter(item => item.id !== auth_id.value)
})

// 提供响应式数据给子组件
provide('userName', userName)
provide('isSecondary', isSecondary)
provide('contactType', contactType)
provide('hasContactInfo', hasContactInfo)
provide('uniqKey', uniqKey)
provide('last_id', last_id)
</script>

<style scoped>
</style>

<style lang="scss">
.login-page {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/login_background.png');
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  
  .header {
    height: 60px;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
  }
  
  .content {
    display: flex;
    height: calc(100% - 60px);
    
    .left-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 20px;
      margin-left: 310px;
      
      .image {
        width: 718px;
        height: 470px;
        margin-bottom: 20px;
      }
    }
  }
  
  .auth-class:hover {
    .el-avatar {
      border: 1px #204ED9 solid !important;
    }
  }
  
  .right-panel {
    width: auto;
    height: auto;
    min-height: 300px;
    box-sizing: border-box;
    min-width: 380px;
    max-width: 380px;
    margin-right: 310px;
    margin-top: auto;
    margin-bottom: auto;
    padding: 40px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: absolute;
    z-index: 2;
    top: 50%;
    left: 75%;
    transform: translate(-50%, -50%);
    .auth-status {
      text-align: center;
      padding: 20px 0;
      
      .status-icon {
        margin-bottom: 20px;
      }
      
      .status-title {
        font-size: 20px;
        color: #333;
        margin-bottom: 10px;
        font-weight: 500;
      }
      
      .status-message {
        color: #666;
        font-size: 14px;
        margin-bottom: 30px;
      }
      
      .loading-dots {
        display: flex;
        justify-content: center;
        gap: 8px;
        
        span {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #0082ef;
          animation: loading-bounce 1.4s ease-in-out infinite both;
          
          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
          &:nth-child(3) { animation-delay: 0s; }
        }
      }
    }
    .auth-waiting {
      text-align: center;
      padding: 30px 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px dashed #dee2e6;
      
      .waiting-icon {
        margin-bottom: 15px;
      }
      
      .waiting-title {
        font-size: 16px;
        color: #495057;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .waiting-message {
        color: #6c757d;
        font-size: 13px;
        line-height: 1.4;
      }
    }
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>