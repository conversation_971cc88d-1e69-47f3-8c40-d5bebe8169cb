/*! 
 Build based on gin-vue-admin 
 Time : 1749545265000 */
import{B as e,j as l,o as a,a as r,i as u,w as s,F as o,h as t,d as i}from"./index.bfaf04e1.js";const b=Object.assign({name:"UserSource",props:["type"]},{setup(b,{expose:n}){const p=e({user:[{label:"用户过滤",key:"userFilters",value:"(objectCategory=person)",rules:[{required:!0,validator:(e,l,a)=>{""!==l?"a"!==l?a():a(new Error("用户过滤规则填写错误")):a(new Error("用户过滤不能为空"))},trigger:"blur"}]},{label:"外部ID",key:"userExternalId",value:"objectGUID",rules:[{required:!0,message:"外部ID不能为空",trigger:"blur"}]},{label:"用户名",key:"name",value:"sAMAccountName"},{label:"显示名",key:"showName",value:"displayName"},{label:"描述",key:"description",value:"description"},{label:"所属组织架构",key:"organization",value:"ou"},{label:"所属群组",key:"group",value:"memberOf"},{label:"账号状态",key:"state",value:"userAccountControl"},{label:"过期时间",key:"expiration",value:"accountExpires"},{label:"手机号码",key:"mobileNo",value:"telephoneNumber"},{label:"电子邮箱",key:"email",value:"mail"}],organize:[{label:"组织架构过滤",key:"organizeFilters",value:"(ou=*)",rules:[{required:!0,message:"组织架构过滤不能为空",trigger:"blur"}]},{label:"外部ID",key:"organizeExternalId",value:"objectGUID",rules:[{required:!0,message:"外部ID不能为空",trigger:"blur"}]},{label:"组名",key:"organizeName",value:"ou"},{label:"描述",key:"organizeDescription",value:"description"}],group:[{label:"群组过滤",key:"groupFilters",value:"(&(CN=*)&(ObjectClass=group))",rules:[{required:!0,message:"群组过滤不能为空",trigger:"blur"}]},{label:"外部ID",key:"groupExternalId",value:"objectGUID",rules:[{required:!0,message:"外部ID不能为空",trigger:"blur"}]},{label:"群组名",key:"groupName",value:"cn"},{label:"描述",key:"groupDescription",value:"description"}]});return n({propertyfrom:p}),(e,n)=>{const g=l("base-input"),m=l("base-form-item"),d=l("base-form");return a(),r("div",null,[u(d,{"label-width":"100px",model:p,style:{"max-width":"550px"},ize:"small"},{default:s((()=>[(a(!0),r(o,null,t(p[b.type],((e,l)=>(a(),i(m,{key:e.key,label:e.label+"：",prop:b.type+"."+l+".value",rules:e.rules},{default:s((()=>[u(g,{modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,class:"el-cource-style"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["label","prop","rules"])))),128))])),_:1},8,["model"])])}}});export{b as default};
