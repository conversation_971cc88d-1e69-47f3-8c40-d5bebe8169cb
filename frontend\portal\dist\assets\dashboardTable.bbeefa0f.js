/*! 
 Build based on gin-vue-admin 
 Time : 1749546430000 */
import{q as a,_ as s,r as e,o as t,d as o,e as m,F as l,i,E as d,t as c}from"./index.74d1ee23.js";import{f as r}from"./date.23f5a973.js";const f=a.create();const n={class:"commit-table"},p={class:"log"},u={class:"flex-1 flex key-box"},v={class:"flex-5 flex message"},g={class:"flex-3 flex form"},h=s(Object.assign({name:"DashboardTable"},{setup(a){const s=e(!0),h=e([]);var x;return(x=0,f({url:"https://api.github.com/repos/flipped-aurora/gin-vue-admin/commits?page="+x,method:"get"})).then((({data:a})=>{s.value=!1,a.forEach(((a,s)=>{a.commit.message&&s<10&&h.value.push({from:r(a.commit.author.date,"yyyy-MM-dd"),title:a.commit.author.name,showDayAndMonth:!0,message:a.commit.message})}))})),(a,s)=>(t(),o("div",n,[s[0]||(s[0]=m("div",{class:"commit-table-title"}," 更新日志 ",-1)),m("div",p,[(t(!0),o(l,null,i(h.value,((a,s)=>(t(),o("div",{key:s,class:"log-item"},[m("div",u,[m("span",{class:d(["key",s<3&&"top"])},c(s+1),3)]),m("div",v,c(a.message),1),m("div",g,c(a.from),1)])))),128))])]))}}),[["__scopeId","data-v-df172550"]]);export{h as default};
